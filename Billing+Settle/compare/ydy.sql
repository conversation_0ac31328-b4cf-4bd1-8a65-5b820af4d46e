CREATE DEFINER="stludr"@"10.%" PROCEDURE "SEND2PROV_YDY"(
    inMonth          IN   VARCHAR2,

    outSysError OUT VARCHAR2(1000),
    outReturn OUT NUMBER(4)
  )
AS

    iv_bill_35   varchar2(8000);

    vSql      varchar2(10240);
    v_proc_name         VARCHAR2(30) := 'SEND2PROV_YDY';

BEGIN
    outSysError := '';
    outReturn := 0;



BEGIN


     --参照生产视图进行调整，rownum rn 改为 row_number() over(partition by x.prov_code ) rn， 去掉tab那一层
       iv_bill_35 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_35  ' ||
      'SELECT x.*,y.taxrate, y.minbalanceamount, y.maxbalanceamount, y.settlementclass,y.productclass, ''' || inMonth || ''' settlemonth ' ||
              'from  (select tab.*,row_number() over(partition by prov_code order by rownum) rn from' ||
              '(select a.prov_code, decode(a.pay_tag, ''1'', ''0'', ''2'', ''1'') pay_tag, a.customer_province_number, a.ec_id, a.customer_name, a.fee_seq, a.product_subs_id, substr(a.issue_time,1,8) issue_time, substr(a.expire_time,1,8) expire_time, '||
              'a.product_class, a.settlement_class, a.product_type, a.product_name, a.product_id, a.prod_charge_code, a.rateplan_id, a.rateplan_name,  '||
              'a.partner_name, a.partner_code, a.member_nums, a.billing_term,  a.fee_val, a.settle_fee, a.tax_rate, a.standard_sale_price, a.settle_price,  '||
              'a.par_settle_rate, a.par_res_settl_rate, a.settlement_type, a.par_settl_amount, a.fee_flag, a.busi_type, a.contract_main, a.city_code, a.creator_name, a.if_free_resource,  '||
              'b.settlement_rate, b.settlement_type P2Csettlement_type, b.settlement_amount, a.original_bill_month, a.discount_Type, a.settle_disvalue,  b.Prd_Settle_Disvalue ,  '||
          ' '''' SjProductName,'''' SjProductCode,'''' SalesBaseDiscount,'''' PVsettleRate, 0 PVsettleValue,'''' PVProductClass  '||
              'from stludr.sync_interface_mc_' || inMonth || ' a,  stludr.sync_interface_mc_p2c_' || inMonth || '  b  '||
              'where a.id  = b.id and a.file_name = b.file_name  '||
              'and a.status = 0 and b.status = 0 ' ||
              ' UNION ALL '||
              ' select b.prov_code, ''0'' pay_tag, NULL customer_province_number, NULL ec_id, NULL customer_name,NULL fee_seq, '||
              ' b.product_subs_id, substr(b.issue_time, 1, 8)  issue_time,substr(b.expire_time, 1, 8) expire_time,b.pv_product_class product_class, '||
              ' b.settlement_class,NULL product_type,b.product_name,b.product_id,NULL prod_charge_code,NULL rateplan_id,NULL rateplan_name, '||
              ' a.partner_name,a.partner_code,NULL member_nums,b.billing_term,b.fee_val,NULL settle_fee,to_char(b.tax_rate,''FM0.90''),NULL standard_sale_price, '||
              ' NULL settle_price,a.par_settle_rate,a.par_ressettl_rate,a.settlement_type,a.par_settl_amount,1 fee_flag, ''移动云'' busi_type,NULL contract_main, '||
              ' NULL city_code,NULL creator_name,NULL if_free_resource,b.settlement_rate,b.settlement_type P2Csettlement_type, b.settlement_amount, '||
              ' NULL original_bill_month,NULL discount_Type,NULL settle_disvalue,NULL Prd_Settle_Disvalue,NULL SjProductName,NULL SjProductCode,NULL SalesBaseDiscount, '||
              ' NULL PVsettleRate,0 PVsettleValue,NULL PVProductClass  '||
              ' from stludr.SYNC_INTERFACE_BC2C_PS b left join stludr.SYNC_INTERFACE_BC2C_PART a on a.product_subs_id = b.product_subs_id  and b.prov_code = a.prov_code '||
              ' where  b.ACCT_MONTH =' || inMonth || '' ||
              ' and b.status = 0 ' ||
              ' UNION ALL  ' ||
          'SELECT a.prov_code,''0'' pay_tag,a.customer_province_number,a.ec_id,a.customer_name,a.fee_seq,a.product_subs_id,substr(a.issue_time, 1, 8) issue_time,  ' ||
      'substr(a.expire_time, 1, 8) expire_time,a.product_class,''6'' settlement_class,a.product_type,a.product_name,a.product_id,a.prod_charge_code,a.rateplan_id,  ' ||
      'a.rateplan_name,'''' partner_name,'''' partner_code,'''' member_nums,a.billing_term,a.fee_val,a.settle_fee settle_fee,a.tax_rate,a.standard_sale_price,  ' ||
      'a.settle_price,'''' par_settle_rate,'''' par_res_settl_rate,'''' settlement_type,0 par_settl_amount,a.fee_flag fee_flag,a.busi_type,a.contract_main,  ' ||
      ' a.city_code,a.creator_name,'''' if_free_resource,'''' settlement_rate,'''' P2Csettlement_type,a.PV_SETTLE_VALUE settlement_amount,a.original_bill_month,'''' DISCOUNT_TYPE,  ' ||
          ''''' SETTLE_DISVALUE,'''' Prd_Settle_Disvalue,a.SJ_PRODUCT_NAME SjProductName,a.SJ_PRODUCT_CODE SjProductCode,a.SALES_BASE_DISCOUNT SalesBaseDiscount,  ' ||
      'a.PV_SETTLE_RATE PVsettleRate,a.PV_SETTLE_VALUE PVsettleValue,a.PV_PRODUCT_CLASS PVProductClass FROM sync_interface_pvs_' || inMonth || ' a WHERE a.status = 0  ' ||
          ' UNION ALL  ' ||
      ' SELECT a.prov_code,''0'' pay_tag,'''' customer_province_number,'''' ec_id,'''' customer_name,'''' fee_seq,a.product_subs_id,substr(a.issue_time, 1, 8) issue_time,  ' ||
      'substr(a.expire_time, 1, 8) expire_time,'''' product_class,''6'' settlement_class,'''' product_type,a.product_name,a.product_id,'''' prod_charge_code,  ' ||
      ''''' rateplan_id,'''' rateplan_name,'''' partner_name,'''' partner_code,'''' member_nums,a.billing_term,a.fee_val,0 settle_fee,a.tax_rate,'''' standard_sale_price,  ' ||
      ''''' settle_price,'''' par_settle_rate,'''' par_res_settl_rate,'''' settlement_type,0 par_settl_amount, 1 fee_flag,''移动云'' busi_type,'''' contract_main,'''' city_code,  ' ||
      ' '''' creator_name,'''' if_free_resource,'''' settlement_rate,'''' P2Csettlement_type,a.PV_SETTLE_VALUE settlement_amount,'''' original_bill_month,'''' DISCOUNT_TYPE,'''' SETTLE_DISVALUE,  ' ||
          ' '''' Prd_Settle_Disvalue,'''' SjProductName,'''' SjProductCode,'''' SalesBaseDiscount,a.PV_SETTLE_RATE PVsettleRate,a.PV_SETTLE_VALUE PVsettleValue,  ' ||
          'a.PV_PRODUCT_CLASS PVProductClass FROM sync_interface_pvs_toc_' || inMonth || ' a WHERE a.status = 0 ) tab) x  ' ||
              ' left join   ' ||
              ' (select l.prov_cd, decode(l.col_name, ''移动云SAAS'', ''1'', ''6'') settlementclass,  decode(l.col_name, ''移动云SAAS'', ''1'', ''PaaS'', ''2'', ''IaaS'', ''3'')  productclass,   ' ||
              ' to_char(l.taxrate / 100, ''FM0.00'') taxrate,    ' ||
              ' sum(case when (l.report_fee - l.settle_fee)>0 then l.report_fee - l.settle_fee else 0 end) minbalanceamount,    ' ||
              ' sum(case when (l.report_fee - l.settle_fee)<0 then l.report_fee - l.settle_fee else 0 end) maxbalanceamount   ' ||
              ' from (select settlemonth, prov_cd, col_name, taxrate, sum(report_fee) report_fee, sum(settle_fee) settle_fee, rep_num from rpt_p2c_limited
							         where rep_num = ''D313-5'' and col_name <> ''专属云'' and settlemonth = ''' || inMonth || ''' group by settlemonth, prov_cd, col_name, taxrate, rep_num) l   ' ||
              '  group by l.prov_cd, l.col_name, l.taxrate) y   ' ||
              ' on x.prov_code = y.prov_cd  ';


--插入前清理数据。

truncate table stludr.bill_35;

BEGIN


set @vSql := iv_bill_35;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
outSysError := 'OK';

COMMIT;
END;




















INSERT INTO stlusers.FILE_DB2F_TASK(TASK_ID,TASK_NAME,DB_NAME,DB_USER,BODY_SQL_TEXT,OUTGOING_DIR,FORMAT_ID,RUN_STYLE,DAY_OR_MONTH,FILE_LEN,DESCRIPTION,TASK_STATUS,ACCT_MONTH,TASK_TYPE,PROCESS_DATE)
VALUES(SEND2RELATION_PROV.NEXTVAL,'Settlesend','tst3','stludr','select a.prov_code, a.pay_tag, a.customer_province_number, a.ec_id, a.customer_name, a.fee_seq, a.product_subs_id, a.issue_time, a.expire_time,
       a.product_class, a.settlement_class, a.product_type, a.product_name, a.product_id, a.prod_charge_code, a.rateplan_id, a.rateplan_name,
       a.partner_name, a.partner_code, a.member_nums, billing_term,  a.fee_val, a.settle_fee, a.tax_rate, a.standard_sale_price, a.settle_price,
       a.par_settle_rate, a.par_res_settl_rate, a.settlement_type, a.par_settl_amount, a.fee_flag, busi_type, a.contract_main, a.city_code, a.creator_name, a.if_free_resource,
       a.settlement_rate, a.P2Csettlement_type, a.settlement_amount, a.original_bill_month, a.discount_Type, a.settle_disvalue,  a.Prd_Settle_Disvalue ,
       a.SjProductName, a.SjProductCode, a.SalesBaseDiscount, a.PVsettleRate, a.PVsettleValue, a.PVProductClass,a.TaxRate,
       CASE
        WHEN a.MinBalanceAmount = 0 AND a.MaxBalanceAmount = 0 THEN a.MinBalanceAmount+NVL(b.adjSettleValue,0)
        WHEN a.MinBalanceAmount is null  AND a.MaxBalanceAmount is null THEN NVL(b.adjSettleValue,0)
        WHEN a.MinBalanceAmount != 0  AND a.MaxBalanceAmount = 0 THEN a.MinBalanceAmount+NVL(b.adjSettleValue,0)
        WHEN a.MinBalanceAmount is not null  AND a.MaxBalanceAmount is null THEN NVL(a.MinBalanceAmount,0)+NVL(b.adjSettleValue,0)
        ELSE NVL(a.MinBalanceAmount,0)
       END AS MinBalanceAmount,
       CASE
        WHEN a.MaxBalanceAmount != 0  AND a.MinBalanceAmount = 0 THEN a.MaxBalanceAmount+NVL(b.adjSettleValue,0)
        WHEN a.MaxBalanceAmount is not null  AND a.MinBalanceAmount is null THEN NVL(a.MaxBalanceAmount,0)+NVL(b.adjSettleValue,0)
        ELSE NVL(a.MaxBalanceAmount,0)
       END AS MaxBalanceAmount,
       a.SettlementClass,
       a.ProductClass, a.rn from
      (select * from stludr.bill_35 a where MinBalanceAmount != 0 or MaxBalanceAmount != 0) a
      left  JOIN  (SELECT PROV_CODE ,PV_PRODUCT_CLASS,tax_rate,TO_NUMBER(-1*sum(PV_SETTLE_VALUE)) adjSettleValue FROM sync_interface_pvs_'|| inMonth || '  WHERE FEE_FLAG =''2''
      GROUP BY PROV_CODE ,PV_PRODUCT_CLASS,tax_rate) b
      ON a.prov_code=b.PROV_CODE AND a.PRODUCTCLASS  = b.PV_PRODUCT_CLASS WHERE  a.rn = 1
union all
select * from (
	     select a.prov_code, a.pay_tag, a.customer_province_number, a.ec_id, a.customer_name, a.fee_seq, a.product_subs_id, a.issue_time, a.expire_time,
	       a.product_class, a.settlement_class, a.product_type, a.product_name, a.product_id, a.prod_charge_code, a.rateplan_id, a.rateplan_name,
	       a.partner_name, a.partner_code, a.member_nums, billing_term,  a.fee_val, a.settle_fee, a.tax_rate, a.standard_sale_price, a.settle_price,
	       a.par_settle_rate, a.par_res_settl_rate, a.settlement_type, a.par_settl_amount, a.fee_flag, busi_type, a.contract_main, a.city_code, a.creator_name, a.if_free_resource,
	       a.settlement_rate, a.P2Csettlement_type, a.settlement_amount, a.original_bill_month, a.discount_Type, a.settle_disvalue,  a.Prd_Settle_Disvalue ,
	       a.SjProductName, a.SjProductCode, a.SalesBaseDiscount, a.PVsettleRate, a.PVsettleValue, a.PVProductClass,a.TaxRate,
	       CASE
	        WHEN a.MinBalanceAmount = 0 AND a.MaxBalanceAmount = 0 THEN a.MinBalanceAmount+NVL(b.adjSettleValue,0)
	        WHEN a.MinBalanceAmount is null  AND a.MaxBalanceAmount is null THEN NVL(b.adjSettleValue,0)
	        WHEN a.MinBalanceAmount != 0  AND a.MaxBalanceAmount = 0 THEN a.MinBalanceAmount+NVL(b.adjSettleValue,0)
	        WHEN a.MinBalanceAmount is not null  AND a.MaxBalanceAmount is null THEN NVL(a.MinBalanceAmount,0)+NVL(b.adjSettleValue,0)
	        ELSE NVL(a.MinBalanceAmount,0)
	       END AS MinBalanceAmount,
	       CASE
	        WHEN a.MaxBalanceAmount != 0  AND a.MinBalanceAmount = 0 THEN a.MaxBalanceAmount+NVL(b.adjSettleValue,0)
	        WHEN a.MaxBalanceAmount is not null  AND a.MinBalanceAmount is null THEN NVL(a.MaxBalanceAmount,0)+NVL(b.adjSettleValue,0)
	        ELSE NVL(a.MaxBalanceAmount,0)
	       END AS MaxBalanceAmount,
	       a.SettlementClass ,
	       a.ProductClass, a.rn from
	     (select * from stludr.bill_35 a where MinBalanceAmount =0 and  MaxBalanceAmount = 0) a
	      left  JOIN  (SELECT PROV_CODE ,PV_PRODUCT_CLASS,tax_rate,TO_NUMBER(-1*sum(PV_SETTLE_VALUE)) adjSettleValue FROM sync_interface_pvs_'|| inMonth || '   WHERE FEE_FLAG =''2''
	      GROUP BY PROV_CODE ,PV_PRODUCT_CLASS,tax_rate) b
	      ON a.prov_code=b.PROV_CODE AND a.ProductClass  = b.PV_PRODUCT_CLASS
) a WHERE  a.rn = 1 and (a.MinBalanceAmount!=0 or a.MaxBalanceAmount!=0)','$SETTLE_HOME/$SETTLE_APPID/data/mc/BILLLIST',38,NULL,NULL,5000,'BBOSS 移动云省专结算及合作伙伴结算文件下发-0001号文件（保底/上限）','C',inMonth,'EBILL',SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK(TASK_ID,TASK_NAME,DB_NAME,DB_USER,BODY_SQL_TEXT,OUTGOING_DIR,FORMAT_ID,RUN_STYLE,DAY_OR_MONTH,FILE_LEN,DESCRIPTION,TASK_STATUS,ACCT_MONTH,TASK_TYPE,PROCESS_DATE)
VALUES(SEND2RELATION_PROV.NEXTVAL,'Settlesend','tst3','stludr','select distinct a.prov_code, a.pay_tag, a.customer_province_number, a.ec_id, a.customer_name, a.fee_seq, a.product_subs_id, a.issue_time, a.expire_time,
       a.product_class, a.settlement_class, a.product_type, a.product_name, a.product_id, a.prod_charge_code, a.rateplan_id, a.rateplan_name,
       a.partner_name, a.partner_code, a.member_nums, billing_term,  a.fee_val, a.settle_fee, a.tax_rate, a.standard_sale_price, a.settle_price,
       a.par_settle_rate, a.par_res_settl_rate, a.settlement_type, a.par_settl_amount, a.fee_flag, busi_type, a.contract_main, a.city_code, a.creator_name, a.if_free_resource,
       a.settlement_rate, a.P2Csettlement_type, a.settlement_amount, a.original_bill_month, a.discount_Type, a.settle_disvalue,  a.Prd_Settle_Disvalue ,
       a.SjProductName, a.SjProductCode, a.SalesBaseDiscount, a.PVsettleRate, a.PVsettleValue, a.PVProductClass, a.rn from
     stludr.bill_35 a WHERE  a.rn <> 1
union all
      select distinct a.prov_code, a.pay_tag, a.customer_province_number, a.ec_id, a.customer_name, a.fee_seq, a.product_subs_id, a.issue_time, a.expire_time,
       a.product_class, a.settlement_class, a.product_type, a.product_name, a.product_id, a.prod_charge_code, a.rateplan_id, a.rateplan_name,
       a.partner_name, a.partner_code, a.member_nums, billing_term,  a.fee_val, a.settle_fee, a.tax_rate, a.standard_sale_price, a.settle_price,
       a.par_settle_rate, a.par_res_settl_rate, a.settlement_type, a.par_settl_amount, a.fee_flag, busi_type, a.contract_main, a.city_code, a.creator_name, a.if_free_resource,
       a.settlement_rate, a.P2Csettlement_type, a.settlement_amount, a.original_bill_month, a.discount_Type, a.settle_disvalue,  a.Prd_Settle_Disvalue ,
       a.SjProductName, a.SjProductCode, a.SalesBaseDiscount, a.PVsettleRate, a.PVsettleValue, a.PVProductClass, a.rn from (
	     select a.prov_code, a.pay_tag, a.customer_province_number, a.ec_id, a.customer_name, a.fee_seq, a.product_subs_id, a.issue_time, a.expire_time,
	       a.product_class, a.settlement_class, a.product_type, a.product_name, a.product_id, a.prod_charge_code, a.rateplan_id, a.rateplan_name,
	       a.partner_name, a.partner_code, a.member_nums, billing_term,  a.fee_val, a.settle_fee, a.tax_rate, a.standard_sale_price, a.settle_price,
	       a.par_settle_rate, a.par_res_settl_rate, a.settlement_type, a.par_settl_amount, a.fee_flag, busi_type, a.contract_main, a.city_code, a.creator_name, a.if_free_resource,
	       a.settlement_rate, a.P2Csettlement_type, a.settlement_amount, a.original_bill_month, a.discount_Type, a.settle_disvalue,  a.Prd_Settle_Disvalue ,
	       a.SjProductName, a.SjProductCode, a.SalesBaseDiscount, a.PVsettleRate, a.PVsettleValue, a.PVProductClass,a.tax_rate TaxRate,
	       CASE
	        WHEN a.MinBalanceAmount = 0 AND a.MaxBalanceAmount = 0 THEN a.MinBalanceAmount+NVL(b.adjSettleValue,0)
	        WHEN a.MinBalanceAmount is null  AND a.MaxBalanceAmount is null THEN NVL(b.adjSettleValue,0)
	        WHEN a.MinBalanceAmount != 0  AND a.MaxBalanceAmount = 0 THEN a.MinBalanceAmount+NVL(b.adjSettleValue,0)
	        WHEN a.MinBalanceAmount is not null  AND a.MaxBalanceAmount is null THEN NVL(a.MinBalanceAmount,0)+NVL(b.adjSettleValue,0)
	        ELSE NVL(a.MinBalanceAmount,0)
	       END AS MinBalanceAmount,
	       CASE
	        WHEN a.MaxBalanceAmount != 0  AND a.MinBalanceAmount = 0 THEN a.MaxBalanceAmount+NVL(b.adjSettleValue,0)
	        WHEN a.MaxBalanceAmount is not null  AND a.MinBalanceAmount is null THEN NVL(a.MaxBalanceAmount,0)+NVL(b.adjSettleValue,0)
	        ELSE NVL(a.MaxBalanceAmount,0)
	       END AS MaxBalanceAmount,
	       a.SettlementClass ,
	       a.ProductClass, a.rn from stludr.bill_35  a
	      left  JOIN  (SELECT PROV_CODE ,PV_PRODUCT_CLASS,tax_rate,TO_NUMBER(-1*sum(PV_SETTLE_VALUE)) adjSettleValue FROM sync_interface_pvs_'|| inMonth || '  WHERE FEE_FLAG =''2''
	      GROUP BY PROV_CODE ,PV_PRODUCT_CLASS,tax_rate) b
	      ON a.prov_code=b.PROV_CODE AND a.ProductClass  = b.PV_PRODUCT_CLASS) a WHERE  a.rn = 1 group by a.prov_code, a.pay_tag, a.customer_province_number, a.ec_id, a.customer_name, a.fee_seq, a.product_subs_id, a.issue_time, a.expire_time,
       a.product_class, a.settlement_class, a.product_type, a.product_name, a.product_id, a.prod_charge_code, a.rateplan_id, a.rateplan_name,
       a.partner_name, a.partner_code, a.member_nums, billing_term,  a.fee_val, a.settle_fee, a.tax_rate, a.standard_sale_price, a.settle_price,
       a.par_settle_rate, a.par_res_settl_rate, a.settlement_type, a.par_settl_amount, a.fee_flag, busi_type, a.contract_main, a.city_code, a.creator_name, a.if_free_resource,
       a.settlement_rate, a.P2Csettlement_type, a.settlement_amount, a.original_bill_month, a.discount_Type, a.settle_disvalue,  a.Prd_Settle_Disvalue ,
       a.SjProductName, a.SjProductCode, a.SalesBaseDiscount, a.PVsettleRate, a.PVsettleValue, a.PVProductClass, a.rn having sum(MinBalanceAmount)=0 and sum(MaxBalanceAmount)=0','$SETTLE_HOME/$SETTLE_APPID/data/mc/BILLLIST',41,NULL,NULL,5000,'BBOSS 移动云省专结算及合作伙伴结算文件下发-0002~9999号文件（无保底/上限）','C',inMonth,'EBILL',SYSDATE);

COMMIT;

outSysError := 'DB2F出口文件移动云结算数据已写入file_db2f_task表。';



SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
END;

END