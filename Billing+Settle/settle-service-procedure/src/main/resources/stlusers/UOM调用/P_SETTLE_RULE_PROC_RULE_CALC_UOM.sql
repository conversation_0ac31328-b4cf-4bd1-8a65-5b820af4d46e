/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：出账流程-结算规则计算总调用
**/
use stlusers;
DROP PROCEDURE IF EXISTS stlusers.P_SETTLE_RULE_PROC_RULE_CALC_UOM;
DELIMITER ;;
CREATE DEFINER="stlusers"@"10.%" PROCEDURE "P_SETTLE_RULE_PROC_RULE_CALC_UOM"(
    inMonth          IN   VARCHAR2,
    batch            IN   VARCHAR2,
    flag_version     IN   VARCHAR2,
    reserve1         IN   VARCHAR2,
    reserve2         IN   VARCHAR2,
    proc_out         OUT   VARCHAR2,
    outSysError      OUT  VARCHAR2,
    outReturn        OUT  NUMBER,
    outBL            OUT  NUMBER,
    outAR            OUT  NUMBER
)
AS
    ivNextMonth varchar2(6);
    v_proc_name   VARCHAR2(33) := 'P_SETTLE_RULE_PROC_RULE_CALC_UOM';
    P_ERRCODE   VARCHAR2(32);
    P_ERRMSG    VARCHAR2(1024);
BEGIN
    outSysError := 'OK';
    outReturn := 0;
    ivNextMonth := to_char(add_months(to_date(inMonth, 'yyyymm'), 1), 'yyyymm');
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
GET DIAGNOSTICS CONDITION 1 P_ERRCODE = RETURNED_SQLSTATE, P_ERRMSG = MESSAGE_TEXT;
outSysError := substr(P_ERRMSG, 1, 1000);
		    outReturn  := -1;
ROLLBACK;
--  日志写表
select ('exception: ' || outReturn || '|'  || P_ERRCODE || '|' || outSysError ) AS error_msg ;
END;

BEGIN
call P_SETTLE_RULE_PROC_TARIFF_PARAMETER(inMonth,batch,flag_version,reserve1,reserve2,proc_out, outSysError, outReturn,outBL,outAR);

-- call LOG_PROCEDURES('call P_SETTLE_RULE_PROC_PARTITION_BUILD(inMonth)', v_proc_name);
-- call P_SETTLE_RULE_PROC_PARTITION_BUILD(inMonth);
--
-- call LOG_PROCEDURES('清理当前账期数据：'||inMonth, v_proc_name);
-- set @vSql := 'ALTER TABLE STL_REPART_PARAMETER_T TRUNCATE PARTITION P_'|| ivNextMonth;
-- PREPARE STMT FROM @vSql;
-- EXECUTE STMT;
-- DEALLOCATE PREPARE STMT;

call P_SETTLE_RULE_PROC_REPART_PARAMETER(inMonth,batch,flag_version,reserve1,reserve2,proc_out, outSysError, outReturn,outBL,outAR);

END;

SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || outReturn;

END ;;
DELIMITER ;