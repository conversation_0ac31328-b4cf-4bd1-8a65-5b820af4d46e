/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：出账流程-恢复结算前状态（日志/任务/查重表清除）
  1. db2f日志表删除
  2. db2f任务表删除
  3. udr2mq日志表删除
  4. 查重表删除
**/
DROP PROCEDURE IF EXISTS stlusers.SETTLE_RESTORE;
DELIMITER ;;
CREATE DEFINER="stlusers"@"10.%" PROCEDURE "SETTLE_RESTORE"(inMonth in VARCHAR2, inBatch in NUMBER, outSysError out VARCHAR2, outReturn out int)
AS

    vSql      varchar2(10240);
    v_proc_name         VARCHAR2(30) := 'STLUSERS.SETTLE_RESTORE';
   	P_ERRCODE VARCHAR2(1024);
	P_ERRMSG VARCHAR2(2048);

BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
GET DIAGNOSTICS CONDITION 1
    P_ERRCODE = RETURNED_SQLSTATE, P_ERRMSG = MESSAGE_TEXT;
    outSysError := substr(P_ERRMSG, 1, 1000);
    outReturn  := -1;
ROLLBACK;
select ('exception: ' || outReturn || '|'  || P_ERRCODE || '|' || outSysError ) AS error_msg ;
call STLUDR.STL_ERROR_LOG(inMonth,P_ERRCODE,outSysError,'',v_proc_name,'');
SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = outSysError;
END;

BEGIN
        outSysError := '';
        outReturn := 0;

       set @vSql := 'delete from STLUDR.RVL_INFO_LOG where RPT_FILENAME = ''' || v_proc_name || '''';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;

if (inBatch not in (0, 1, 2)) then
            outReturn := 1;
            outSysError := '批次错误。现仅支持：预出账-0；一批-1；二批-2。';
call STLUDR.STL_INFO_LOG(inMonth, '', v_proc_name, outSysError);
else

            set @vSql := 'delete from file_db2f_log where to_char(create_time, ''yymmdd'') = to_char(sysdate, ''yymmdd'')';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call STLUDR.STL_INFO_LOG(inMonth, '', v_proc_name, 'file_db2f_log表数据删除成功');


set @vSql := 'delete from file_db2f_task where to_char(process_date, ''yymmdd'') = to_char(sysdate, ''yymmdd'')';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call STLUDR.STL_INFO_LOG(inMonth, '', v_proc_name, 'file_db2f_task表数据删除成功');


set @vSql := 'delete from udr2mq_log where to_char(rcv_tm, ''yymmdd'') = to_char(sysdate, ''yymmdd'')';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call STLUDR.STL_INFO_LOG(inMonth, '', v_proc_name, 'udr2mq_log表数据删除成功');


set @vSql := 'delete from file_validation_dupchk k where substr(k.KEY, LENGTH(SUBSTRING_INDEX(k.KEY, ''_'', 4)) + 2, 8) = to_char(sysdate, ''yyyymmdd'')';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call STLUDR.STL_INFO_LOG(inMonth, '', v_proc_name, 'file_validation_dupchk表数据删除成功');

commit;

outReturn := 0;
            outSysError := '日志表当天日志数据删除成功';
SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
call STLUDR.STL_INFO_LOG(inMonth, '', v_proc_name, outSysError);
end if;
END;

BEGIN

call FINISH_OR_NOT(inMonth, inBatch, inMonth, inMonth, inMonth, outSysError, outSysError, outReturn, outSysError, outSysError);
END;
END ;;
DELIMITER ;