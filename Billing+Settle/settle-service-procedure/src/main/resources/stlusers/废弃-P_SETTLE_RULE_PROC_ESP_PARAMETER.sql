DELIMITER ;;
CREATE DEFINER="stlusers"@"10.%" PROCEDURE "P_SETTLE_RULE_PROC_ESP_PARAMETER"(
    inMonth          IN   VARCHAR2,
    batch            IN   VARCHAR2,
    flag_version     IN   VARCHAR2,
    reserve1         IN   VARCHAR2,
    reserve2         IN   VARCHAR2,
    proc_out         IN   VARCHAR2,
    outSysError      OUT  VARCHAR2,
    outReturn        OUT  NUMBER,
    outBL            OUT  NUMBER,
    outAR            OUT  NUMBER
)
AS
    vSql VARCHAR2(10240);
    v_proc_name   VARCHAR2(33) := 'P_SETTLE_RULE_PROC_ESP_PARAMETER';
BEGIN

    outSysError := 'OK';
    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        outSysError := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg ;
    END;

    BEGIN
        -- 清空原表
        set @vSql := 'TRUNCATE TABLE STL_ESP_PARAMETER_T';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        outReturn := 0;
        outSysError := 'OK';
    END;

    BEGIN
        insert into stl_esp_parameter_t
        SELECT seq_esp_parameter_id.NEXTVAL id, datax.* FROM(
          SELECT
               'Esp' offer_code,
               a.productnumber product_code,
               1 ordre_mode,
               c.rule_id,
               d.rate_id,
               a.chargecode charge_item,
               row_number() over(PARTITION BY a.productnumber, a.chargecode order by a.settlementrate) - 1 calc_priority,
               a.settlementparty object_value,
               a.settlementtype sett_type,
               a.settlementrate / 100 rate_value,
               b.taxrate tax_rate,
               0 dest_source,
               0 route_flag,
               to_date(to_char(a.eff_date, 'yyyymm'), 'yyyymm') eff_date,
               add_months(to_date(to_char(a.exp_date, 'yyyymm'), 'yyyymm'), 1) - 1 exp_date
          from stl_esp_settle_t a,
               stl_esp_product_t b,
               STL_OFFER_T c,
               STL_RATE_T d
          where c.data_source = 9
           and c.rule_id = d.rule_id
           and a.productnumber = b.productnumber
           and a.chargecode = b.chargecode3
           and to_char(a.eff_date, 'yyyymmdd') >= to_char(b.eff_date, 'yyyymmdd')
           and to_char(a.exp_date, 'yyyymmdd') <= to_char(b.exp_date, 'yyyymmdd')
        ) datax;
    END;

    COMMIT;

    SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || outReturn;

END ;;
DELIMITER ;
