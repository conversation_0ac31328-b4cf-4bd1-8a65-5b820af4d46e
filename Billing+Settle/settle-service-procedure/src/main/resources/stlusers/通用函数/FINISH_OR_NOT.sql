/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：通用函数-查看结算流程是否已处理完成
 1. 统计应处理文件数、已处理文件数、未处理文件数
 2. 返回当前处理状态
**/
DROP PROCEDURE IF EXISTS stlusers.FINISH_OR_NOT;
DELIMITER ;;
CREATE DEFINER="stlusers"@"10.%" PROCEDURE "FINISH_OR_NOT"(
    inMonth in VARCHAR2, inBatch in NUMBER, flag_version in VARCHAR2,
    reserve1 in VARCHAR2, reserve2 in VARCHAR2,
    proc_out out VARCHAR2, outSysError out VARCHAR2, outReturn out NUMBER,
    outBL out VARCHAR2, outAR out VARCHAR2)
AS

    iv_file   int;
    iv_finish int;
    iv_diff   int;
    v_proc_name         VARCHAR2(30) := 'FINISH_OR_NOT';
    P_ERRCODE VARCHAR2(1024);
	P_ERRMSG VARCHAR2(1024);

BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
GET DIAGNOSTICS CONDITION 1
    P_ERRCODE = RETURNED_SQLSTATE, P_ERRMSG = MESSAGE_TEXT;
    outSysError := substr(P_ERRMSG, 1, 1000);
	outReturn  := -1;
ROLLBACK;
--  日志写表
select ('exception: ' || outReturn || '|'  || P_ERRCODE || '|' || outSysError ) AS error_msg ;
call STLUDR.STL_ERROR_LOG(inMonth,P_ERRCODE,outSysError,'',v_proc_name,'');
SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = outSysError;
END;

BEGIN
        outSysError := '';
        outReturn := 0;

       set @vSql := 'delete from STLUDR.RVL_INFO_LOG where RPT_FILENAME = ''' || v_proc_name || '''';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;

select a.cnt1, b.cnt2, a.cnt1 - b.cnt2 into iv_file, iv_finish, iv_diff from
                                                                            (select count(*) cnt1 from stlusers.udr2mq_log where acnt_ym = inMonth) a,
                                                                            (select count(*) cnt2 from stlusers.udr2mq_log where acnt_ym = inMonth and proc_status = '11000') b;

if (iv_file <> 0 and iv_diff = 0) then
              outReturn := 0;
              outSysError := '结算应处理文件' || iv_file || '个，已处理' || iv_finish || '个。文件已全部处理完成。';
else if (iv_file <> 0 and iv_diff <> 0) then
              outReturn := -1;
              outSysError := '结算应处理文件' || iv_file || '个，已处理' || iv_finish || '个。还有' || iv_diff || '个需要处理。';
else if (iv_file = 0) then
              outReturn := -1;
              outSysError := '你确定结算启动了吗？udr2mq_log表中没有' || inMonth || '账期的数据。';
end if;
end if;
end if;
commit;
call STLUDR.STL_INFO_LOG(inMonth, '', v_proc_name, outSysError);
SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn || ',outSysError=' || outSysError;

END;

END ;;
DELIMITER ;