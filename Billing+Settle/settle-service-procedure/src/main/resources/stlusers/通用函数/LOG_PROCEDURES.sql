/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：通用函数-日志-存储过程日志记录
**/
use stlusers;
DROP PROCEDURE IF EXISTS stlusers.LOG_PROCEDURES;
DELIMITER ;;
create definer = "stlusers" @"10.%" procedure stlusers."LOG_PROCEDURES"(
    inLogContent in varchar2, module in varchar2
)
-- 以较统一的格式打印日志并写入日志表
  as
    iv_Sysdate varchar2(50);

begin
select to_Char(SYSDATE, 'yyyy-mm-dd hh24:mi:ss.ff3') into iv_Sysdate
from dual;

insert into LOG_PROCEDURES(SYS_DATE, MODULE, LOG)
values(iv_Sysdate, module, inLogContent);

commit;

exception
when others then
select('[' || to_Char(SYSDATE, 'yyyy-mm-dd hh24:mi:ss') || ']' ||
       'Exception occurred when writing logs.');

select('[' || to_Char(SYSDATE, 'yyyy-mm-dd hh24:mi:ss') || ']' ||
    SQLCODE || ':' || SQLERRM);
END ;;
DELIMITER ;