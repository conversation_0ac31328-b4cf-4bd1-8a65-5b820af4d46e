/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：通用函数-获取下个月
**/
DROP PROCEDURE IF EXISTS stludr.f_Month;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "f_Month"(inMonth IN VARCHAR2(8),
                                    inDiff IN NUMBER,
                                    iv_NewMonth OUT VARCHAR2(8),
                                    outSysError    OUT VARCHAR2(1000),
                                    outReturn     OUT NUMBER(4)
    )
AS
    vSql      varchar2(10240);
    v_proc_name         VARCHAR2(30) := 'f_Month';
BEGIN



    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        outSysError := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg ;
    END;

    BEGIN
        iv_NewMonth := to_Char(add_Months(to_Date(inMonth, 'yyyymm'), inDiff), 'yyyymm');
        SELECT 'procedure ' || v_proc_name || ' completed successfully. iv_NewMonth=' || iv_NewMonth || ' outSysError=' || outSysError || ' outReturn=' || outReturn;

    END;
END ;;
DELIMITER ;