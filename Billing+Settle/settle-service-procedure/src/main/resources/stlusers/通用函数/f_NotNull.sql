/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：通用函数-非空判断
**/
DROP PROCEDURE IF EXISTS stludr.f_NotNull;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "f_NotNull"(
                                        Str1     IN      VARCHAR2,
                                        Str2     IN      VARCHAR2,
                                        out_Result OUT VARCHAR2(1000),
                                        outSysError    OUT VARCHAR2(1000),
                                        outReturn     OUT NUMBER(4)
                                      )
AS
    vSql      varchar2(10240);
    v_proc_name         VARCHAR2(30) := 'f_NotNull';
BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        outSysError := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg ;
        out_Result := ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError );
    END;

    BEGIN
        IF Str1 IS NOT NULL THEN
          out_Result := Str1;
          SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
        ELSE
          out_Result := Str2;
          SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
        END IF;

    END;

END ;;
DELIMITER ;