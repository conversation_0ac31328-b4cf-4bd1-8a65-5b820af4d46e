/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：通用函数-非零判断
**/
DROP PROCEDURE IF EXISTS stludr.f_NotZero;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "f_NotZero"(
    Num1     IN      NUMBER,
    Num2     IN      NUMBER,
    outSysError    OUT VARCHAR2(1000),
    outReturn     OUT NUMBER(4),
    out_Result OUT NUMBER
  )
AS

    vSql      varchar2(10240);
    v_proc_name         VARCHAR2(30) := 'f_NotZero';

BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        outSysError := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg;
    END;

    BEGIN
        IF Num1 <> 0 THEN
          out_Result := Num1;
          SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
    ELSE
          out_Result := Num2;
          SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
    END IF;

    END;
END ;;
DELIMITER ;