/**
 * 存储过程功能说明请勿删除
 存储过程实现功能：规则计算-海外CDNrepart规则计算
**/
use stlusers;
DROP PROCEDURE IF EXISTS stlusers.P_SETTLE_RULE_PROC_REPART_CDN;
DELIMITER ;;
CREATE DEFINER="stlusers"@"10.%" PROCEDURE stlusers.P_SETTLE_RULE_PROC_REPART_CDN(
                                   acctMonth IN VARCHAR2,
                                   outReturn OUT NUMBER,
                                   outSysError OUT VARCHAR2)
As

    nextMonth             VARCHAR2(6); --账期月下一月
    cdn_month_tbl         VARCHAR2(64); --CDN账期月UR表名
    cdn_next_month_tbl    VARCHAR2(64); --CDN账期月下月UR表名
    append_month_tbl      VARCHAR2(64); --CDN账期月UR表名
    append_next_month_tbl VARCHAR2(64); --CDN账期月下月UR表名
    vSql                  VARCHAR2(10240);
    P_ERRCODE   VARCHAR2(16);
    P_ERRMSG    VARCHAR2(1024);

    v_proc_name   VARCHAR2(36) := 'P_SETTLE_RULE_PROC_REPART_CDN';
BEGIN
    outSysError := '';
    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
GET DIAGNOSTICS CONDITION 1 P_ERRCODE = RETURNED_SQLSTATE, P_ERRMSG = MESSAGE_TEXT;
        outSysError := substr(P_ERRMSG, 1, 1000);
        outReturn := -1;
ROLLBACK;
--  日志写表
select ('exception: ' || outReturn || '|' || P_ERRCODE || '|' || outSysError) AS error_msg;
call LOG_PROCEDURES(outSysError, v_proc_name);
SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = outSysError;
END;
BEGIN
    cdn_month_tbl := 'stludr.ur_cdn_' || acctMonth || '_t';
    append_month_tbl := 'stludr.ur_cdnappend_' || acctMonth || '_t';

    ---- 账期月下月
SELECT to_char(add_months(to_date(acctMonth, 'yyyyMM'), 1), 'yyyyMM')
INTO nextMonth
FROM dual;

cdn_next_month_tbl := 'stludr.ur_cdn_' || nextMonth || '_t';
append_next_month_tbl := 'stludr.ur_cdnappend_' || nextMonth || '_t';

-- set @vSql := 'delete from STLUSERS.LOG_PROCEDURES where MODULE = ''P_SETTLE_RULE_PROC_REPART_CDN''';
-- SELECT @vSql;
-- PREPARE STMT FROM @vSql;
-- EXECUTE STMT;
-- DEALLOCATE PREPARE STMT;
-- COMMIT;
    ---- 开始计算海外CDN结算规则
    /*
     1.没有保底，无流量的时候计费不出帐，所以不计算无流量部分
     2.和王应确认海外订购的话单 流量类型只有海外，但是现在没有校验
     */
SET @P_TIME2 := SYSDATE;
call LOG_PROCEDURES('step0:开始进行海外CDN结算规则计算', v_proc_name);
select('step1: 清除中间表数据');
call LOG_PROCEDURES('step1: 清除中间表数据', v_proc_name);
---- 清除海外CDN省间结算规则
set @vSql := 'delete from stlusers.STL_CDN_RULE WHERE PRODUCT_CODE = ''2023999400085020'' AND ACCT_MONTH =' ||
                acctMonth;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;

---- 清除海外CDN华为结算规则
set @vSql := 'delete from stlusers.STL_CDN_HW_RULE WHERE PRODUCT_CODE = ''2023999400085020'' AND ACCT_MONTH =' ||
                acctMonth;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;

---- 清除海外CDN全网订购比例
set @vSql := 'delete from stlusers.stl_national_rate_hw where acct_month = ' || acctMonth ||
                          ' and biz_type in (''HW-SJ'', ''HW-HW'')  ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;

---- 清除海外CDN全网订购比例
set @vSql := 'delete from stlusers.stl_national_rate where acct_month = ' || acctMonth ||
                          ' and biz_type in (''HW-SJ'', ''HW-HW'')  ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;

---- CDN话单省间结算部分  rate_back_id =3 国际流量
select('step2: 计算客户本省海外月累计流量/全网客户全国海外月累计流量占比');
call LOG_PROCEDURES('step2: 计算客户本省海外月累计流量/全网客户全国海外月累计流量占比', v_proc_name);
set @vSql := 'insert into stlusers.stl_national_rate_hw ' ||
                'select null,acct_month, biz_type, prov_cd, prov_quantity, total_quantity, ' ||
                '''0'', ''hwsj'' ' ||
                'from (select ''' || acctMonth || ''' acct_month, ' ||
                '''HW-SJ'' biz_type, ec_code_prov prov_cd, a.prov_quantity, b.total_quantity ' ||
                'from (select ec_code_prov, sum(nvl(ACCU_VOLUME, 0) + nvl(ACCU_DURATION, 0)) prov_quantity ' ||
                'from (select * from ' || cdn_month_tbl || ' union all  select * from ' || cdn_next_month_tbl || ') ' ||
                'where rate_back_id = ''3'' ' ||
                'and substr(dup_time, 1, 6) = ' || acctMonth || ' ' ||
                'group by ec_code_prov) a, ' ||
                '(select sum(nvl(ACCU_VOLUME, 0) + nvl(ACCU_DURATION, 0)) total_quantity ' ||
                'from  (select * from ' || cdn_month_tbl || ' union all  select * from ' || cdn_next_month_tbl ||
                ') ' ||
                'where rate_back_id = ''3''  ' ||
                'and substr(dup_time, 1, 6) = ' || acctMonth || ' ' ||
                ') b) t ' ||
                'where total_quantity <> 0';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;

---- append 话单流量  FLOW_TYPE = '3'  国际流量
set @vSql := 'insert into stlusers.stl_national_rate_hw ' ||
                'select null,acct_month, biz_type, prov_cd, prov_quantity, total_quantity, ' ||
                '''0'',''hwsj'' ' ||
                'from (select ''' || acctMonth || ''' acct_month, ' ||
                '''HW-SJ'' biz_type, ec_code_prov prov_cd, a.prov_quantity, b.total_quantity ' ||
                'from (select ec_code_prov, sum(nvl(DOWN_FLOW, 0) + nvl(UP_FLOW, 0) + nvl(CONTENT_FLOW,0)) prov_quantity ' ||
                'from (select * from ' || append_month_tbl || ' union all  select * from ' || append_next_month_tbl ||
                ') ' ||
                'where FLOW_TYPE = ''3'' ' ||
                ' and substr(dup_time, 1, 6) = ' || acctMonth || ' ' ||
                'group by ec_code_prov) a, ' ||
                '(select sum(nvl(DOWN_FLOW, 0) + nvl(UP_FLOW, 0) + nvl(CONTENT_FLOW,0)) total_quantity ' ||
                'from (select * from ' || append_month_tbl || ' union all  select * from ' || append_next_month_tbl ||
                ') ' ||
                'where FLOW_TYPE = ''3'' ' ||
                'and substr(dup_time, 1, 6) = ' || acctMonth || ' ' ||
                ') b) t ' ||
                'where total_quantity <> 0';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;

----  计算省占全国流量占比  海外CDN省间结算规则
set @vSql := 'insert into stlusers.stl_national_rate ' ||
                'select null,ACCT_MONTH, ' ||
                'BIZ_TYPE, ' ||
                'PROV_CD, ' ||
                'sum(nvl(PROV_QUANTITY, 0)), ' ||
                'sum(nvl(TOTAL_QUANTITY, 0)), ' ||
                'round(sum(nvl(PROV_QUANTITY, 0)) / sum(nvl(TOTAL_QUANTITY, 1)), 12), ' ||
                'SERVICE_TYPE ' ||
                'from stlusers.stl_national_rate_hw ' ||
                'GROUP BY ACCT_MONTH,BIZ_TYPE,SERVICE_TYPE,PROV_CD';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;

update stlusers.stl_national_rate as nr1
    join (
    select
    ACCT_MONTH,
    sum(PROV_QUANTITY) as total_quantity
    from
    stlusers.stl_national_rate
    where
    BIZ_TYPE = 'HW-SJ'
    and ACCT_MONTH = acctMonth
    group by
    ACCT_MONTH
    ) as nr2 on
    nr1.ACCT_MONTH = nr2.ACCT_MONTH
    set
        nr1.TOTAL_QUANTITY = nr2.total_quantity
where
    nr1.BIZ_TYPE = 'HW-SJ';

UPDATE
    stlusers.stl_national_rate
SET
    RATE = round(nvl(PROV_QUANTITY, 0) / nvl(TOTAL_QUANTITY, 1) * 100, 12)
WHERE
    BIZ_TYPE = 'HW-SJ'
  AND ACCT_MONTH = acctMonth;
COMMIT;


---- 清除海外CDN全网订购比例 开始计算华为结算规则，只取华为平面流量
set @vSql := 'delete from stlusers.stl_national_rate_hw where acct_month = ' || acctMonth ||
                          ' and biz_type in (''HW-SJ'', ''HW-HW'')  ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;

---- CDN话单华为结算部分  rate_back_id =3 国际流量
select('step3: 计算客户华为海外月累计流量/全网客户全国海外月累计流量占比');
call LOG_PROCEDURES('step3: 计算客户华为海外月累计流量/全网客户全国海外月累计流量占比', v_proc_name);
set @vSql := 'insert into stlusers.stl_national_rate_hw ' ||
                'select null,acct_month, biz_type, prov_cd, prov_quantity, total_quantity, ' ||
                '''0'', ''hwhw'' ' ||
                'from (select ''' || acctMonth || ''' acct_month,' ||
                ' ''HW-HW'' biz_type, ec_code_prov prov_cd, a.prov_quantity, b.total_quantity ' ||
                'from (select ec_code_prov, sum(nvl(ACCU_VOLUME, 0) + nvl(ACCU_DURATION, 0)) prov_quantity ' ||
                'from (select * from ' || cdn_month_tbl || ' union all  select * from ' || cdn_next_month_tbl || ') ' ||
                'where rate_back_id = ''3'' and sub_group_num = ''1'' ' ||
                ' and substr(dup_time, 1, 6) = ' || acctMonth || ' ' ||
                'group by ec_code_prov) a, ' ||
                '(select sum(nvl(ACCU_VOLUME, 0) + nvl(ACCU_DURATION, 0)) total_quantity ' ||
                'from (select * from ' || cdn_month_tbl || ' union all  select * from ' || cdn_next_month_tbl || ') ' ||
                'where rate_back_id = ''3'' and sub_group_num = ''1'' ' ||
                ' and substr(dup_time, 1, 6) = ' || acctMonth || ' ' ||
                ') b) t ' ||
                'where total_quantity <> 0';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;

---- append 话单流量  FLOW_TYPE = '3'  国际流量
set @vSql := 'insert into stlusers.stl_national_rate_hw ' ||
                'select null,acct_month, biz_type, prov_cd, prov_quantity, total_quantity, ' ||
                '''0'',''hwhw'' ' ||
                'from (select ''' || acctMonth || ''' acct_month,' ||
                ' ''HW-HW'' biz_type, ec_code_prov prov_cd, a.prov_quantity, b.total_quantity ' ||
                'from (select ec_code_prov, sum(nvl(DOWN_FLOW, 0) +nvl(UP_FLOW, 0) + nvl(CONTENT_FLOW,0)) prov_quantity ' ||
                'from (select * from ' || append_month_tbl || ' union all  select * from ' || append_next_month_tbl ||
                ') ' ||
                'where FLOW_TYPE = ''3''  and distribution_plane = ''1'' ' ||
                ' and substr(dup_time, 1, 6) = ' || acctMonth || ' ' ||
                'group by ec_code_prov) a, ' ||
                '(select sum(nvl(DOWN_FLOW, 0) + nvl(UP_FLOW, 0) + nvl(CONTENT_FLOW,0)) total_quantity ' ||
                'from (select * from ' || append_month_tbl || ' union all  select * from ' || append_next_month_tbl ||
                ') ' ||
                'where FLOW_TYPE = ''3'' and distribution_plane = ''1'' ' ||
                 'and substr(dup_time, 1, 6) = ' || acctMonth || ' ' ||
                ') b) t ' ||
                'where total_quantity <> 0';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;

---- 计算省占全国流量占比  海外CDN 华为结算规则
set @vSql := 'insert into stlusers.stl_national_rate ' ||
                'select null,ACCT_MONTH, ' ||
                'BIZ_TYPE, ' ||
                'PROV_CD, ' ||
                'sum(nvl(PROV_QUANTITY, 0)), ' ||
                'sum(nvl(TOTAL_QUANTITY, 0)), ' ||
                'round(sum(nvl(PROV_QUANTITY, 0)) / sum(nvl(TOTAL_QUANTITY, 1)), 12), ' ||
                'SERVICE_TYPE ' ||
                'from stlusers.stl_national_rate_hw ' ||
                'GROUP BY ACCT_MONTH,BIZ_TYPE,SERVICE_TYPE,PROV_CD';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;

update stlusers.stl_national_rate as nr1
    join (
    select
    ACCT_MONTH,
    sum(PROV_QUANTITY) as total_quantity
    from
    stlusers.stl_national_rate
    where
    BIZ_TYPE = 'HW-HW'
    and ACCT_MONTH = acctMonth
    group by
    ACCT_MONTH
    ) as nr2 on
    nr1.ACCT_MONTH = nr2.ACCT_MONTH
    set
        nr1.TOTAL_QUANTITY = nr2.total_quantity
where
    nr1.BIZ_TYPE = 'HW-HW';
COMMIT;

UPDATE
    stlusers.stl_national_rate
SET
    RATE = round(nvl(PROV_QUANTITY, 0) / nvl(TOTAL_QUANTITY, 1) * 100, 12)
WHERE
    BIZ_TYPE = 'HW-HW'
  AND ACCT_MONTH = acctMonth;
COMMIT;

/*
 查询CDN话单   CDNAPPEND话单  获取订购信息，关联全国流量占比计算结果
 */
set @vSql := 'INSERT INTO stlusers.STL_CDN_RULE ' ||
                ' SELECT null,a.ACCT_MONTH,a.EC_CODE,a.ORDER_MODE,a.OFFER_CODE,a.PRODUCT_CODE,' ||
                ' a.OFFER_ORDER_ID,a.PRODUCT_ORDER_ID,b.PROV_CD,b.PROV_QUANTITY,b.TOTAL_QUANTITY,' ||
                ' b.RATE,0 DEST_SOURCE,'''' ' ||
                ' FROM (SELECT * FROM (SELECT ''' || acctMonth ||
                ''' ACCT_MONTH,EC_CODE,ORDER_MODE,OFFER_CODE,PRODUCT_CODE, ' ||
                ' OFFER_ORDER_ID,PRODUCT_ORDER_ID,EC_CODE_PROV,substr(dup_time, 1, 6) DUP_TIME' ||
                ' FROM ' || cdn_month_tbl ||
                ' WHERE rate_back_id = ''3'' ' ||
                ' UNION ALL ' ||
                ' SELECT ''' || acctMonth || ''' ACCT_MONTH,EC_CODE,ORDER_MODE,OFFER_CODE,PRODUCT_CODE, ' ||
                ' OFFER_ORDER_ID,PRODUCT_ORDER_ID,EC_CODE_PROV,substr(dup_time, 1, 6) DUP_TIME ' ||
                ' FROM ' || cdn_next_month_tbl ||
                ' WHERE rate_back_id = ''3'' ' ||
                ' UNION ALL ' ||
                ' SELECT ''' || acctMonth || ''' ACCT_MONTH,EC_CODE,ORDER_MODE,OFFER_CODE,PRODUCT_CODE, ' ||
                ' OFFER_ORDER_ID,PRODUCT_ORDER_ID,EC_CODE_PROV,substr(dup_time, 1, 6) DUP_TIME ' ||
                ' FROM  ' || append_month_tbl ||
                ' WHERE FLOW_TYPE = ''3'' ' ||
                ' UNION ALL ' ||
                ' SELECT ''' || acctMonth || ''' ACCT_MONTH,EC_CODE,ORDER_MODE,OFFER_CODE,PRODUCT_CODE, ' ||
                ' OFFER_ORDER_ID,PRODUCT_ORDER_ID,EC_CODE_PROV,substr(dup_time, 1, 6) DUP_TIME ' ||
                ' FROM ' || append_next_month_tbl ||
                ' WHERE FLOW_TYPE = ''3'') ' ||
                ' WHERE substr(dup_time, 1, 6) = ' || acctMonth ||
                ' GROUP BY PRODUCT_ORDER_ID,ACCT_MONTH,EC_CODE,ORDER_MODE,OFFER_CODE, ' ||
                ' PRODUCT_CODE,OFFER_ORDER_ID,PRODUCT_ORDER_ID,EC_CODE_PROV,DUP_TIME) a ' ||
                ' RIGHT JOIN stlusers.stl_national_rate b ' ||
                ' ON a.ACCT_MONTH = b.ACCT_MONTH '||
                '  where b.BIZ_TYPE = ''HW-SJ''  and b.ACCT_MONTH =' || acctMonth;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;

/*
查询CDN话单   CDNAPPEND话单  获取订购信息，关联全国流量占比计算结果   华为结算限制流量分发平面  华为
sub_group_num = '1'  distribution_plane = '1'
*/
set @vSql := 'INSERT INTO stlusers.STL_CDN_HW_RULE ' ||
                ' SELECT NULL,a.ACCT_MONTH,a.EC_CODE,a.ORDER_MODE,a.OFFER_CODE,a.PRODUCT_CODE,' ||
                ' a.OFFER_ORDER_ID,a.PRODUCT_ORDER_ID,b.PROV_CD,b.PROV_QUANTITY,b.TOTAL_QUANTITY,' ||
                ' b.RATE,98 DEST_SOURCE,'''' ' ||
                ' FROM (SELECT * FROM (SELECT ''' || acctMonth ||
                ''' ACCT_MONTH,EC_CODE,ORDER_MODE,OFFER_CODE,PRODUCT_CODE, ' ||
                ' OFFER_ORDER_ID,PRODUCT_ORDER_ID,EC_CODE_PROV,substr(dup_time, 1, 6) DUP_TIME ' ||
                ' FROM ' || cdn_month_tbl ||
                ' WHERE rate_back_id = ''3'' and sub_group_num = ''1''  ' ||
                ' UNION ALL' ||
                ' SELECT ''' || acctMonth || ''' ACCT_MONTH,EC_CODE,ORDER_MODE,OFFER_CODE,PRODUCT_CODE, ' ||
                ' OFFER_ORDER_ID,PRODUCT_ORDER_ID,EC_CODE_PROV,substr(dup_time, 1, 6) DUP_TIME ' ||
                ' FROM ' || cdn_next_month_tbl ||
                ' WHERE rate_back_id = ''3'' and sub_group_num = ''1''  ' ||
                ' UNION ALL ' ||
                ' SELECT ''' || acctMonth || ''' ACCT_MONTH,EC_CODE,ORDER_MODE,OFFER_CODE,PRODUCT_CODE, ' ||
                ' OFFER_ORDER_ID,PRODUCT_ORDER_ID,EC_CODE_PROV,substr(dup_time, 1, 6) DUP_TIME ' ||
                ' FROM  ' || append_month_tbl ||
                ' WHERE FLOW_TYPE = ''3'' and distribution_plane = ''1'' ' ||
                ' UNION ALL ' ||
                ' SELECT ''' || acctMonth || ''' ACCT_MONTH,EC_CODE,ORDER_MODE,OFFER_CODE,PRODUCT_CODE, ' ||
                ' OFFER_ORDER_ID,PRODUCT_ORDER_ID,EC_CODE_PROV,substr(dup_time, 1, 6) DUP_TIME ' ||
                ' FROM ' || append_next_month_tbl ||
                ' WHERE FLOW_TYPE = ''3'' and distribution_plane = ''1'') ' ||
                ' WHERE substr(dup_time, 1, 6) =' || acctMonth ||
                ' GROUP BY PRODUCT_ORDER_ID,ACCT_MONTH,EC_CODE,ORDER_MODE,OFFER_CODE, ' ||
                ' PRODUCT_CODE,OFFER_ORDER_ID,PRODUCT_ORDER_ID,EC_CODE_PROV,DUP_TIME) a ' ||
                ' RIGHT JOIN stlusers.stl_national_rate b ' ||
                ' ON a.ACCT_MONTH = b.ACCT_MONTH '||
                ' where b.BIZ_TYPE = ''HW-HW'' and b.ACCT_MONTH = ' || acctMonth ;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;

select('step4: Top55客户特殊处理');
call LOG_PROCEDURES('step4: Top55客户特殊处理', v_proc_name);
---- stl_cdn_rule和stl_cdn_hw_rule表更新istop55字段
update STL_CDN_RULE a
set istop55 = (select istop55
               from stl_serv_biz_code b
               where a.product_order_id = b.order_id
                 and b.SERVICE_CODE ='2023999400085020'
                 and acctMonth between to_char(b.effective_date, 'yyyymm') and to_char(b.expiry_date, 'yyyymm'))
where a.PRODUCT_CODE = '2023999400085020' and a.acct_month = acctMonth;
COMMIT;

update STL_CDN_HW_RULE a
set istop55 = (select istop55
               from stl_serv_biz_code b
               where a.product_order_id = b.order_id
                 and b.SERVICE_CODE ='2023999400085020'
                 and acctMonth between to_char(b.effective_date, 'yyyymm') and to_char(b.expiry_date, 'yyyymm'))
where a.PRODUCT_CODE = '2023999400085020' and a.acct_month = acctMonth;
COMMIT;

---- 更新受理模式3 top55订购的结算比例（*80%）
update STL_CDN_RULE
set ratio = round(ratio * 0.8, 12)
where order_mode = '3'
  and PRODUCT_CODE  = '2023999400085020'
  and (istop55 = '0' or istop55 is null)
  and acct_month = acctMonth;
COMMIT;

update STL_CDN_HW_RULE
set ratio = round(ratio * 0.8, 12)
where order_mode = '3'
  and PRODUCT_CODE = '2023999400085020'
  and (istop55 = '0' or istop55 is null)
  and acct_month = acctMonth;
COMMIT;

----插入20%部分
insert into STL_CDN_RULE
select distinct NULL,acctMonth,
                ec_code,
                order_mode,
                offer_code,
                product_code,
                offer_order_id,
                product_order_id,
                '${EC_PROV_CODE_07}',
                0,
                0,
                '20',
                '98',
                '0'
from STL_CDN_RULE
where order_mode = '3'
  and PRODUCT_CODE = '2023999400085020'
  and (istop55 = '0' or istop55 is null)
  and acct_month = acctMonth;
COMMIT;

insert into STL_CDN_HW_RULE
select distinct NULL,
                acctMonth,
                ec_code,
                order_mode,
                offer_code,
                product_code,
                offer_order_id,
                product_order_id,
                '${EC_PROV_CODE_07}',
                0,
                0,
                '20',
                '98',
                '0'
from STL_CDN_HW_RULE
where order_mode = '3'
  and PRODUCT_CODE = '2023999400085020'
  and (istop55 = '0' or istop55 is null)
  and acct_month = acctMonth;
COMMIT;

select('step5: 累计上限1000W逻辑处理');
call LOG_PROCEDURES('step5: 累计上限1000W特殊逻辑处理', v_proc_name);
---- CDN 1000W 导入数据到  "STLUSERS"."STL_CDN_RULE_1000"
set @vSql := 'INSERT INTO STL_CDN_RULE_1000  ' ||
                'SELECT null,k.* FROM STLUSERS.STL_CDN_RULE_1000 w RIGHT JOIN ( ' ||
                'SELECT ' ||
                '      a.ordermode order_mode,    ' ||
                '      a.ORGMONTH, ' ||
                '      a.pospecnumber offer_code, ' ||
                '      a.sospecnumber product_code, ' ||
                '      a.poid offer_order_id, ' ||
                '      a.soid product_order_id, ' ||
                '      a.NOTAXFEE, ' ||
                '      b.EFFECTIVE_DATE, ' ||
                '      b.EXPIRY_DATE, ' ||
                '      b.ISTOP55 ' ||
                '  FROM ' ||
                '      STLUSERS.STL_SERV_BIZ_CODE b  ' ||
                '  JOIN ' ||
                '      stludr.sync_interface_bl_' || acctMonth || ' a  ' ||
                '  ON  ' ||
                '      a.ORDERMODE = b.PROD_ORDER_MODE  ' ||
                '  AND  ' ||
                '      a.POID = b.PROD_ORDER_ID  ' ||
                '  AND  ' ||
                '      a.SOID = b.ORDER_ID  ' ||
                '  WHERE ' ||
                '      a.pospecnumber = ''50004'' AND a.SOSPECNUMBER =''2023999400085020'' ' ||
                '      AND a.ordermode = ''3''  ' ||
                '      AND b.ISTOP55 = ''1''  ' ||
                ' ) k  ' ||
                ' ON w.order_mode = k.order_mode ' ||
                ' AND w.ORGMONTH = k.ORGMONTH  ' ||
                ' AND w.offer_code = k.offer_code  ' ||
                ' AND w.product_code = k.product_code  ' ||
                ' AND w.offer_order_id = k.offer_order_id ' ||
                ' AND w.product_order_id = k.product_order_id ' ||
                ' AND w.NOTAXFEE = k.NOTAXFEE ' ||
                ' AND w.EFFECTIVE_DATE = k.EFFECTIVE_DATE ' ||
                ' AND w.EXPIRY_DATE = k.EXPIRY_DATE ' ||
                ' AND w.ISTOP55 = k.ISTOP55 ' ||
                ' WHERE  ' ||
                '   w.order_mode IS NULL  ' ||
                '   AND w.ORGMONTH IS NULL  ' ||
                '   AND w.offer_code IS NULL  ' ||
                '   AND w.product_code IS NULL  ' ||
                '   AND w.offer_order_id IS NULL  ' ||
                '   AND w.product_order_id IS NULL  ' ||
                '   AND w.NOTAXFEE IS NULL  ' ||
                '   AND w.EFFECTIVE_DATE IS NULL  ' ||
                '   AND w.EXPIRY_DATE IS NULL  ' ||
                '   AND w.ISTOP55 IS NULL  ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;
--------------------------------##########1000W以内规则计算################-----------------------------------
---- 1000以内 95%结給主办省 5%按流量占比给其他省
----  受理模式3   top55订购1000w以内的结算比例（*95%） 省间结算
MERGE INTO STL_CDN_RULE A
    USING(
        SELECT
            X.OFFER_ORDER_ID,
            X.PRODUCT_ORDER_ID
        FROM
            STL_CDN_RULE_1000 X
                JOIN CDN_SPECIAL_SETT_CONFIG Y ON X.PRODUCT_ORDER_ID=Y.PRODUCT_ORDER_ID
                JOIN
            (
                SELECT
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID,
                    MAX( ORGMONTH ) END_DATE,
                    CASE
                        WHEN MAX( EFFECTIVE_DATE ) = LAST_DAY(MAX( EFFECTIVE_DATE )) THEN
                            TRUNC(
                                    ADD_MONTHS(  MAX( EFFECTIVE_DATE ),
                                                 12 * TRUNC( ( MONTHS_BETWEEN(
                                                         TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))
                                                                 ) / 12, 0  ) + 1
                                    )
                                , 'MONTH')
                        ELSE
                            ADD_MONTHS(
                                    MAX( EFFECTIVE_DATE ),
                                    12 * TRUNC((MONTHS_BETWEEN( TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))  )/12,0)
                            )
                        END as START_DATE
                FROM
                    STL_CDN_RULE_1000
                GROUP BY
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID
            ) T
            ON  X.OFFER_ORDER_ID = T.OFFER_ORDER_ID
                AND X.PRODUCT_ORDER_ID = T.PRODUCT_ORDER_ID
                and X.product_code ='2023999400085020'
        WHERE
            X.ORGMONTH >= to_char(T.START_DATE, 'yyyymm' )
          AND X.ORGMONTH <= T.END_DATE
        GROUP BY
            X.OFFER_ORDER_ID,X.PRODUCT_ORDER_ID
        HAVING SUM(NOTAXFEE) <= 20000000000
    ) W
    ON (A.OFFER_ORDER_ID = W.OFFER_ORDER_ID AND A.PRODUCT_ORDER_ID = W.PRODUCT_ORDER_ID)
    WHEN MATCHED THEN
        UPDATE SET A.RATIO = ROUND(A.RATIO * 0.95, 12)
    WHERE A.ORDER_MODE = '3'
            AND A.ISTOP55 = '1'
            AND A.ACCT_MONTH = acctMonth;


----  受理模式3 top55订购1000w以内的结算比例（*95%） 华为结算
MERGE INTO STL_CDN_HW_RULE A
    USING(
        WITH HW_1000 AS (
            SELECT
                b.ORDER_MODE,
                b.ACCT_MONTH ORGMONTH,
                b.OFFER_CODE,
                b.PRODUCT_CODE,
                b.OFFER_ORDER_ID,
                b.PRODUCT_ORDER_ID,
                (b.CMCC_FEE + b.OTH_FEE) as NOTAXFEE,
                a.EFFECTIVE_DATE,
                a.ISTOP55
            FROM
                STLUSERS.STL_SERV_BIZ_CODE a
                    JOIN stludr.STL_CDN_ORDER_FEE b
                         ON a.PROD_ORDER_MODE = b.ORDER_MODE
                             AND a.PROD_ORDER_ID = b.OFFER_ORDER_ID
                             AND a.ORDER_ID = b.PRODUCT_ORDER_ID
            WHERE
                b.OFFER_CODE = '50004'
              AND b.product_code ='2023999400085020'
              AND b.ORDER_MODE = '3'
              AND a.ISTOP55 = '1'
        )
        SELECT
            X.OFFER_ORDER_ID,
            X.PRODUCT_ORDER_ID
        FROM
            HW_1000 X
                JOIN CDN_SPECIAL_SETT_CONFIG Y ON X.PRODUCT_ORDER_ID=Y.PRODUCT_ORDER_ID
                JOIN
            (
                SELECT
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID,
                    MAX( ORGMONTH ) END_DATE,
                    CASE
                        WHEN MAX( EFFECTIVE_DATE ) = LAST_DAY(MAX( EFFECTIVE_DATE )) THEN
                            TRUNC(
                                    ADD_MONTHS(  MAX( EFFECTIVE_DATE ),
                                                 12 * TRUNC( ( MONTHS_BETWEEN(
                                                         TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))
                                                                 ) / 12, 0  ) + 1
                                    )
                                , 'MONTH')
                        ELSE
                            ADD_MONTHS(
                                    MAX( EFFECTIVE_DATE ),
                                    12 * TRUNC((MONTHS_BETWEEN( TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE ))) )/12,0)
                            )
                        END as START_DATE
                FROM
                    HW_1000
                GROUP BY
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID
            ) T
            ON  X.OFFER_ORDER_ID = T.OFFER_ORDER_ID
                AND X.PRODUCT_ORDER_ID = T.PRODUCT_ORDER_ID
        WHERE
            X.ORGMONTH >= to_char(T.START_DATE, 'yyyymm' )
          AND X.ORGMONTH <= T.END_DATE
        GROUP BY
            X.OFFER_ORDER_ID,X.PRODUCT_ORDER_ID
        HAVING SUM(NOTAXFEE) <= 20000000000
    ) W
    ON (A.OFFER_ORDER_ID = W.OFFER_ORDER_ID AND A.PRODUCT_ORDER_ID = W.PRODUCT_ORDER_ID)
    WHEN MATCHED THEN
        UPDATE SET A.RATIO = ROUND(A.RATIO * 0.95, 12)
    WHERE A.ORDER_MODE = '3'
            AND A.ISTOP55 = '1'
            AND A.ACCT_MONTH = acctMonth;


-- 受理模式3 top55订购1000w以内的结算比例（*5%） 省间结算
INSERT INTO STL_CDN_RULE
SELECT DISTINCT
    null,
    acctMonth,
    x.ec_code,
    x.order_mode,
    x.offer_code,
    x.product_code,
    x.offer_order_id,
    x.product_order_id,
    '${EC_PROV_CODE_07}',
    0,
    0,
    '5',
    '98',
    '1'
FROM
    STL_CDN_RULE x join
    (
        SELECT
            X.OFFER_ORDER_ID,
            X.PRODUCT_ORDER_ID
        FROM
            STL_CDN_RULE_1000 X
                JOIN CDN_SPECIAL_SETT_CONFIG Y ON X.PRODUCT_ORDER_ID=Y.PRODUCT_ORDER_ID
                JOIN
            (
                SELECT
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID,
                    MAX( ORGMONTH ) END_DATE,
                    CASE
                        WHEN MAX( EFFECTIVE_DATE ) = LAST_DAY(MAX( EFFECTIVE_DATE )) THEN
                            TRUNC(
                                    ADD_MONTHS(  MAX( EFFECTIVE_DATE ),
                                                 12 * TRUNC( ( MONTHS_BETWEEN(
                                                         TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))
                                                                 ) / 12, 0  ) + 1
                                    )
                                , 'MONTH')
                        ELSE
                            ADD_MONTHS(
                                    MAX( EFFECTIVE_DATE ),
                                    12 * TRUNC((MONTHS_BETWEEN( TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE ))) )/12,0)
                            )
                        END as START_DATE
                FROM
                    STL_CDN_RULE_1000
                GROUP BY
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID
            ) T
            ON  X.OFFER_ORDER_ID = T.OFFER_ORDER_ID
                AND X.PRODUCT_ORDER_ID = T.PRODUCT_ORDER_ID
                and X.product_code ='2023999400085020'
        WHERE
            X.ORGMONTH >= to_char(T.START_DATE, 'yyyymm' )
          AND X.ORGMONTH <= T.END_DATE
        GROUP BY
            X.OFFER_ORDER_ID,X.PRODUCT_ORDER_ID
        HAVING SUM(NOTAXFEE) <= 20000000000
    ) t
    ON  x.OFFER_ORDER_ID = t.OFFER_ORDER_ID AND x.PRODUCT_ORDER_ID = t.PRODUCT_ORDER_ID
WHERE
    x.order_mode = '3'
  AND x.istop55 = '1'
  AND x.acct_month = acctMonth;

-- 受理模式3 top55订购1000w以内的结算比例（*5%） 华为  签约省
INSERT INTO STL_CDN_HW_RULE
SELECT DISTINCT
    NULL,
    acctMonth,
    x.ec_code,
    x.order_mode,
    x.offer_code,
    x.product_code,
    x.offer_order_id,
    x.product_order_id,
    '${EC_PROV_CODE_07}',
    0,
    0,
    '5',
    '98',
    '1'
FROM
    STL_CDN_HW_RULE x join
    (

        WITH HW_1000 AS (
            SELECT
                b.ORDER_MODE,
                b.ACCT_MONTH ORGMONTH,
                b.OFFER_CODE,
                b.PRODUCT_CODE,
                b.OFFER_ORDER_ID,
                b.PRODUCT_ORDER_ID,
                (b.CMCC_FEE + b.OTH_FEE) as NOTAXFEE,
                a.EFFECTIVE_DATE,
                a.ISTOP55
            FROM
                STLUSERS.STL_SERV_BIZ_CODE a
                    JOIN stludr.STL_CDN_ORDER_FEE b
                         ON a.PROD_ORDER_MODE = b.ORDER_MODE
                             AND a.PROD_ORDER_ID = b.OFFER_ORDER_ID
                             AND a.ORDER_ID = b.PRODUCT_ORDER_ID
            WHERE
                b.OFFER_CODE = '50004'
              AND b.product_code ='2023999400085020'
              AND b.ORDER_MODE = '3'
              AND a.ISTOP55 = '1'
        )
        SELECT
            X.OFFER_ORDER_ID,
            X.PRODUCT_ORDER_ID
        FROM
            HW_1000 X
                JOIN CDN_SPECIAL_SETT_CONFIG Y ON X.PRODUCT_ORDER_ID=Y.PRODUCT_ORDER_ID
                JOIN
            (
                SELECT
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID,
                    MAX( ORGMONTH ) END_DATE,
                    CASE
                        WHEN MAX( EFFECTIVE_DATE ) = LAST_DAY(MAX( EFFECTIVE_DATE )) THEN
                            TRUNC(
                                    ADD_MONTHS(  MAX( EFFECTIVE_DATE ),
                                                 12 * TRUNC( ( MONTHS_BETWEEN(
                                                         TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))
                                                                 ) / 12, 0  ) + 1
                                    )
                                , 'MONTH')
                        ELSE
                            ADD_MONTHS(
                                    MAX( EFFECTIVE_DATE ),
                                    12 * TRUNC((MONTHS_BETWEEN( TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))  )/12,0)
                            )
                        END as START_DATE
                FROM
                    HW_1000
                GROUP BY
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID
            ) T
            ON  X.OFFER_ORDER_ID = T.OFFER_ORDER_ID
                AND X.PRODUCT_ORDER_ID = T.PRODUCT_ORDER_ID
        WHERE
            X.ORGMONTH >= to_char(T.START_DATE, 'yyyymm' )
          AND X.ORGMONTH <= T.END_DATE
        GROUP BY
            X.OFFER_ORDER_ID,X.PRODUCT_ORDER_ID
        HAVING SUM(NOTAXFEE) <= 20000000000
    ) t
    ON  x.OFFER_ORDER_ID = t.OFFER_ORDER_ID AND x.PRODUCT_ORDER_ID = t.PRODUCT_ORDER_ID
WHERE
    x.order_mode = '3'
  AND x.istop55 = '1'
  AND x.acct_month = acctMonth;

--------------------------------##########超过1000W规则计算################-----------------------------------
----  受理模式3 top55订购超过1000w的结算比例 省间结算
MERGE INTO STL_CDN_RULE A
    USING (SELECT X.OFFER_ORDER_ID,
                  X.PRODUCT_ORDER_ID,
                  -- 1-(((SUM(X.NOTAXFEE)/20000000000) - 1) * 0.05) as PROVINCE31_RATIO
                  CASE
                      WHEN (SUM(X.NOTAXFEE) - 20000000000) <
                           SUM(CASE WHEN X.ORGMONTH = acctMonth THEN X.NOTAXFEE ELSE 0 END) THEN
                          (
                              ((SUM(X.NOTAXFEE) - 20000000000) /
                               SUM(CASE WHEN X.ORGMONTH = acctMonth THEN X.NOTAXFEE ELSE 1 END)) * 1
                              ) + (
                              (1 - (SUM(X.NOTAXFEE) - 20000000000) /
                                   SUM(CASE WHEN X.ORGMONTH = acctMonth THEN X.NOTAXFEE ELSE 1 END)) * 0.95
                              )
                      --第一次超出
                      ELSE
                          1
                      --除第一次超出外的第n次超出
                      END AS PROVINCE31_RATIO
           FROM STL_CDN_RULE_1000 X
                    JOIN CDN_SPECIAL_SETT_CONFIG Y ON X.PRODUCT_ORDER_ID=Y.PRODUCT_ORDER_ID
                    JOIN
                (SELECT OFFER_ORDER_ID,
                        PRODUCT_ORDER_ID,
                        MAX(ORGMONTH) END_DATE,
                        CASE
                            WHEN MAX(EFFECTIVE_DATE) = LAST_DAY(MAX(EFFECTIVE_DATE)) THEN
                                TRUNC(
                                        ADD_MONTHS(MAX(EFFECTIVE_DATE),
                                                   12 * TRUNC((MONTHS_BETWEEN(
                                                           TO_DATE(MAX(ORGMONTH), 'yyyymm'),
                                                           TRUNC(MAX(EFFECTIVE_DATE)))
                                                                  ) / 12, 0) + 1
                                        )
                                    , 'MONTH')
                            ELSE
                                ADD_MONTHS(
                                        MAX(EFFECTIVE_DATE),
                                        12 * TRUNC((MONTHS_BETWEEN(TO_DATE(MAX(ORGMONTH), 'yyyymm'),
                                                                   TRUNC(MAX(EFFECTIVE_DATE)))) / 12, 0)
                                )
                            END as    START_DATE
                 FROM STL_CDN_RULE_1000
                 GROUP BY OFFER_ORDER_ID,
                          PRODUCT_ORDER_ID) T
                ON X.OFFER_ORDER_ID = T.OFFER_ORDER_ID
                    AND X.PRODUCT_ORDER_ID = T.PRODUCT_ORDER_ID
                    and X.product_code ='2023999400085020'
           WHERE X.ORGMONTH >= to_char(T.START_DATE, 'yyyymm')
             AND X.ORGMONTH <= T.END_DATE
           GROUP BY X.OFFER_ORDER_ID, X.PRODUCT_ORDER_ID
           HAVING SUM(NOTAXFEE) > 20000000000) W
    ON (A.OFFER_ORDER_ID = W.OFFER_ORDER_ID AND A.PRODUCT_ORDER_ID = W.PRODUCT_ORDER_ID)
    WHEN MATCHED THEN
        UPDATE
            SET A.RATIO = ROUND(A.RATIO * W.PROVINCE31_RATIO, 12)
    WHERE A.ORDER_MODE = '3'
              AND A.ISTOP55 = '1'
              AND A.ACCT_MONTH = acctMonth;
---------------------------------------------------------------------------------------------
INSERT INTO STL_CDN_RULE
SELECT DISTINCT null,acctMonth,
                x.ec_code,
                x.order_mode,
                x.offer_code,
                x.product_code,
                x.offer_order_id,
                x.product_order_id,
                '${EC_PROV_CODE_07}',
                0,
                0,
                ROUND(t.sign_ratio * 100, 12),
                '98',
                '1'
FROM STL_CDN_RULE x
         JOIN (SELECT X.OFFER_ORDER_ID,
                      X.PRODUCT_ORDER_ID,
                      -- ((sum( NOTAXFEE )/20000000000) - 1) * 0.05  as SIGN_RATIO
                      (1 - (SUM(X.NOTAXFEE) - 20000000000) /
                           SUM(CASE WHEN X.ORGMONTH = acctMonth THEN X.NOTAXFEE ELSE 1 END)) *
                      0.05 as SIGN_RATIO
               FROM STL_CDN_RULE_1000 X
                        JOIN CDN_SPECIAL_SETT_CONFIG Y ON X.PRODUCT_ORDER_ID=Y.PRODUCT_ORDER_ID
                        JOIN
                    (SELECT OFFER_ORDER_ID,
                            PRODUCT_ORDER_ID,
                            MAX(ORGMONTH) END_DATE,
                            CASE
                                WHEN MAX(EFFECTIVE_DATE) = LAST_DAY(MAX(EFFECTIVE_DATE)) THEN
                                    TRUNC(
                                            ADD_MONTHS(MAX(EFFECTIVE_DATE),
                                                       12 * TRUNC((MONTHS_BETWEEN(
                                                               TO_DATE(MAX(ORGMONTH), 'yyyymm'),
                                                               TRUNC(MAX(EFFECTIVE_DATE)))
                                                                      ) / 12, 0) + 1
                                            )
                                        , 'MONTH')
                                ELSE
                                    ADD_MONTHS(
                                            MAX(EFFECTIVE_DATE),
                                            12 * TRUNC((MONTHS_BETWEEN(TO_DATE(MAX(ORGMONTH), 'yyyymm'),
                                                                       TRUNC(MAX(EFFECTIVE_DATE)))) / 12, 0)
                                    )
                                END as    START_DATE
                     FROM STL_CDN_RULE_1000
                     GROUP BY OFFER_ORDER_ID,
                              PRODUCT_ORDER_ID) T
                    ON X.OFFER_ORDER_ID = T.OFFER_ORDER_ID
                        AND X.PRODUCT_ORDER_ID = T.PRODUCT_ORDER_ID
                        and X.product_code ='2023999400085020'
               WHERE X.ORGMONTH >= to_char(T.START_DATE, 'yyyymm')
                 AND X.ORGMONTH <= T.END_DATE
               GROUP BY X.OFFER_ORDER_ID, X.PRODUCT_ORDER_ID
               HAVING SUM(X.NOTAXFEE) > 20000000000
                  AND (SUM(X.NOTAXFEE) - 20000000000) <
                      SUM(CASE WHEN X.ORGMONTH = acctMonth THEN X.NOTAXFEE ELSE 0 END)) t
              ON x.OFFER_ORDER_ID = t.OFFER_ORDER_ID
                  AND x.PRODUCT_ORDER_ID = t.PRODUCT_ORDER_ID
WHERE x.order_mode = '3'
  AND x.istop55 = '1'
  AND x.acct_month = acctMonth;

----  受理模式3 top55订购超过1000w的结算比例  华为结算
MERGE INTO STL_CDN_HW_RULE A
    USING (WITH HW_1000 AS (SELECT b.ORDER_MODE,
                                   b.ACCT_MONTH                ORGMONTH,
                                   b.OFFER_CODE,
                                   b.PRODUCT_CODE,
                                   b.OFFER_ORDER_ID,
                                   b.PRODUCT_ORDER_ID,
                                   (b.CMCC_FEE + b.OTH_FEE) as NOTAXFEE,
                                   a.EFFECTIVE_DATE,
                                   a.ISTOP55
                            FROM STLUSERS.STL_SERV_BIZ_CODE a
                                     JOIN stludr.STL_HWCDN_ORDER_FEE b
                                          ON a.PROD_ORDER_MODE = b.ORDER_MODE
                                              AND a.PROD_ORDER_ID = b.OFFER_ORDER_ID
                                              AND a.ORDER_ID = b.PRODUCT_ORDER_ID
                            WHERE b.OFFER_CODE = '50004'
                              AND b.product_code = '2023999400085020'
                              AND b.ORDER_MODE = '3'
                              AND a.ISTOP55 = '1')
           SELECT X.OFFER_ORDER_ID,
                  X.PRODUCT_ORDER_ID,
                  -- 1-(((SUM(X.NOTAXFEE)/20000000000) - 1) * 0.05) as PROVINCE31_RATIO
                  CASE
                      WHEN (SUM(X.NOTAXFEE) - 20000000000) <
                           SUM(CASE WHEN X.ORGMONTH = acctMonth THEN X.NOTAXFEE ELSE 0 END) THEN
                          (
                              ((SUM(X.NOTAXFEE) - 20000000000) /
                               SUM(CASE WHEN X.ORGMONTH = acctMonth THEN X.NOTAXFEE ELSE 1 END)) * 1
                              ) + (
                              (1 - (SUM(X.NOTAXFEE) - 20000000000) /
                                   SUM(CASE WHEN X.ORGMONTH = acctMonth THEN X.NOTAXFEE ELSE 1 END)) * 0.95
                              )
                      --第一次超出
                      ELSE
                          1
                      --除第一次超出外的第n次超出
                      END AS PROVINCE31_RATIO
           FROM HW_1000 X
                    JOIN CDN_SPECIAL_SETT_CONFIG Y ON X.PRODUCT_ORDER_ID=Y.PRODUCT_ORDER_ID
                    JOIN
                (SELECT OFFER_ORDER_ID,
                        PRODUCT_ORDER_ID,
                        MAX(ORGMONTH) END_DATE,
                        CASE
                            WHEN MAX(EFFECTIVE_DATE) = LAST_DAY(MAX(EFFECTIVE_DATE)) THEN
                                TRUNC(
                                        ADD_MONTHS(MAX(EFFECTIVE_DATE),
                                                   12 * TRUNC((MONTHS_BETWEEN(
                                                           TO_DATE(MAX(ORGMONTH), 'yyyymm'),
                                                           TRUNC(MAX(EFFECTIVE_DATE)))
                                                                  ) / 12, 0) + 1
                                        )
                                    , 'MONTH')
                            ELSE
                                ADD_MONTHS(
                                        MAX(EFFECTIVE_DATE),
                                        12 * TRUNC((MONTHS_BETWEEN(TO_DATE(MAX(ORGMONTH), 'yyyymm'),
                                                                   TRUNC(MAX(EFFECTIVE_DATE)))) / 12, 0)
                                )
                            END as    START_DATE
                 FROM HW_1000
                 GROUP BY OFFER_ORDER_ID,
                          PRODUCT_ORDER_ID) T
                ON X.OFFER_ORDER_ID = T.OFFER_ORDER_ID
                    AND X.PRODUCT_ORDER_ID = T.PRODUCT_ORDER_ID
           WHERE X.ORGMONTH >= to_char(T.START_DATE, 'yyyymm')
             AND X.ORGMONTH <= T.END_DATE
           GROUP BY X.OFFER_ORDER_ID, X.PRODUCT_ORDER_ID
           HAVING SUM(NOTAXFEE) > 20000000000) W
    ON (A.OFFER_ORDER_ID = W.OFFER_ORDER_ID AND A.PRODUCT_ORDER_ID = W.PRODUCT_ORDER_ID)
    WHEN MATCHED THEN
        UPDATE
            SET A.RATIO = ROUND(A.RATIO * W.PROVINCE31_RATIO, 12)
    WHERE A.ORDER_MODE = '3'
              AND A.ISTOP55 = '1'
              AND A.ACCT_MONTH = acctMonth;
---------------------------------------------------------------------------------------------
INSERT INTO STL_CDN_HW_RULE
SELECT DISTINCT NULL,acctMonth,
                x.ec_code,
                x.order_mode,
                x.offer_code,
                x.product_code,
                x.offer_order_id,
                x.product_order_id,
                '${EC_PROV_CODE_07}',
                0,
                0,
                ROUND(t.sign_ratio * 100, 12),
                '98',
                '1'
FROM STL_CDN_HW_RULE x
         JOIN CDN_SPECIAL_SETT_CONFIG Y ON x.PRODUCT_ORDER_ID=Y.PRODUCT_ORDER_ID
         JOIN (WITH HW_1000 AS (SELECT b.ORDER_MODE,
                                       b.ACCT_MONTH                ORGMONTH,
                                       b.OFFER_CODE,
                                       b.PRODUCT_CODE,
                                       b.OFFER_ORDER_ID,
                                       b.PRODUCT_ORDER_ID,
                                       (b.CMCC_FEE + b.OTH_FEE) as NOTAXFEE,
                                       a.EFFECTIVE_DATE,
                                       a.ISTOP55
                                FROM STLUSERS.STL_SERV_BIZ_CODE a
                                         JOIN stludr.STL_HWCDN_ORDER_FEE b
                                              ON a.PROD_ORDER_MODE = b.ORDER_MODE
                                                  AND a.PROD_ORDER_ID = b.OFFER_ORDER_ID
                                                  AND a.ORDER_ID = b.PRODUCT_ORDER_ID
                                WHERE b.OFFER_CODE = '50004'
                                  AND b.product_code = '2023999400085020'
                                  AND b.ORDER_MODE = '3'
                                  AND a.ISTOP55 = '1')
               SELECT X.OFFER_ORDER_ID,
                      X.PRODUCT_ORDER_ID,
                      -- ((sum( NOTAXFEE )/20000000000) - 1) * 0.05  as SIGN_RATIO
                      (1 - (SUM(X.NOTAXFEE) - 20000000000) /
                           SUM(CASE WHEN X.ORGMONTH = acctMonth THEN X.NOTAXFEE ELSE 1 END)) *
                      0.05 as SIGN_RATIO
               FROM HW_1000 X
                        JOIN
                    (SELECT OFFER_ORDER_ID,
                            PRODUCT_ORDER_ID,
                            MAX(ORGMONTH) END_DATE,
                            CASE
                                WHEN MAX(EFFECTIVE_DATE) = LAST_DAY(MAX(EFFECTIVE_DATE)) THEN
                                    TRUNC(
                                            ADD_MONTHS(MAX(EFFECTIVE_DATE),
                                                       12 * TRUNC((MONTHS_BETWEEN(
                                                               TO_DATE(MAX(ORGMONTH), 'yyyymm'),
                                                               TRUNC(MAX(EFFECTIVE_DATE)))) / 12, 0) + 1
                                            )
                                        , 'MONTH')
                                ELSE
                                    ADD_MONTHS(
                                            MAX(EFFECTIVE_DATE),
                                            12 * TRUNC((MONTHS_BETWEEN(TO_DATE(MAX(ORGMONTH), 'yyyymm'),
                                                                       TRUNC(MAX(EFFECTIVE_DATE)))) / 12, 0)
                                    )
                                END as    START_DATE
                     FROM HW_1000
                     GROUP BY OFFER_ORDER_ID,
                              PRODUCT_ORDER_ID) T
                    ON X.OFFER_ORDER_ID = T.OFFER_ORDER_ID
                        AND X.PRODUCT_ORDER_ID = T.PRODUCT_ORDER_ID
               WHERE X.ORGMONTH >= to_char(T.START_DATE, 'yyyymm')
                 AND X.ORGMONTH <= T.END_DATE
               GROUP BY X.OFFER_ORDER_ID, X.PRODUCT_ORDER_ID
               HAVING SUM(X.NOTAXFEE) > 20000000000
                  AND (SUM(X.NOTAXFEE) - 20000000000) <
                      SUM(CASE WHEN X.ORGMONTH = acctMonth THEN X.NOTAXFEE ELSE 0 END)) t
              ON x.OFFER_ORDER_ID = t.OFFER_ORDER_ID
                  AND x.PRODUCT_ORDER_ID = t.PRODUCT_ORDER_ID
WHERE x.order_mode = '3'
  AND x.istop55 = '1'
  AND x.acct_month = acctMonth;

select('step6: 开始入库正式数据（STL_REPART_PARAMETER_T）');
call LOG_PROCEDURES('step6: 开始入库正式数据（STL_REPART_PARAMETER_T）', v_proc_name);
---- BBOSS出账的CDN
---- 省间结算部分
INSERT INTO STL_REPART_PARAMETER_T(ID, OFFER_CODE, PRODUCT_CODE, PROD_INST_ID, SVC_INST_ID,
                                   ORDER_MODE, RULE_ID, RATE_ID, CALC_PRIORITY,
                                   OBJECT_VALUE, TARIFF_TYPE, RATE_VALUE, EFF_DATE,
                                   EXP_DATE, ACCT_MONTH, DEST_SOURCE, CHARGE_ITEM, ROUTE_FLAG)
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*, ft.FEETYPE, 1 route_flag
FROM (SELECT a.OFFER_CODE                                       offer_code,
             a.PRODUCT_CODE                                     product_code,
             a.OFFER_ORDER_ID                                   poid_inst_id,
             a.PRODUCT_ORDER_ID                                 svc_inst_id,
             a.ORDER_MODE                                       order_mode,
             b.RULE_ID                                          rule_id,
             c.RATE_ID                                          rate_id,
             row_number() over (PARTITION BY a.PRODUCT_ORDER_ID ORDER BY to_number(a.RATIO)) - 1
                                                                        calc_priority,
              a.SETT_PROV                                        object_value,
             '1'                                                tariff_type,
             a.RATIO / 100                                      rate_value,
             to_date(a.ACCT_MONTH, 'yyyymm')                    eff_date,
             add_months(to_date(
                                a.ACCT_MONTH, 'yyyymm'), 1) - 1 exp_date,
             a.ACCT_MONTH                                       acct_month,
             a.dest_source
      FROM STL_CDN_RULE a,
           STL_OFFER_T b,
           STL_RATE_T c
      WHERE a.OFFER_CODE = b.OFFER_CODE
        AND a.ORDER_MODE = b.ORDER_MODE
        AND b.PRODUCT_CODE = '2023999400085020'
        AND a.PRODUCT_CODE = b.PRODUCT_CODE
        AND b.RULE_ID = c.RULE_ID
        AND b.DATA_SOURCE = 1
        and a.offer_code = '50004'
        and b.dest_source = '0'
        AND a.ACCT_MONTH = acctMonth
        and to_date(acctMonth, 'YYYYMM') between b.eff_date and b.exp_date) data,
     (SELECT '16' feetype
      FROM DUAL
      UNION
      SELECT '4039'
      FROM DUAL
      UNION
      SELECT '66' feetype
      FROM DUAL
      UNION
      SELECT '8039'
      FROM DUAL) ft;
---- 华为结算部分
INSERT INTO STL_REPART_PARAMETER_T(ID, OFFER_CODE, PRODUCT_CODE, PROD_INST_ID, SVC_INST_ID,
                                   ORDER_MODE, RULE_ID, RATE_ID, CALC_PRIORITY,
                                   OBJECT_VALUE, TARIFF_TYPE, RATE_VALUE, EFF_DATE,
                                   EXP_DATE, ACCT_MONTH, DEST_SOURCE, CHARGE_ITEM, ROUTE_FLAG)
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*, ft.FEETYPE, 1 route_flag
FROM (SELECT a.OFFER_CODE                                       offer_code,
             a.PRODUCT_CODE                                     product_code,
             a.OFFER_ORDER_ID                                   poid_inst_id,
             a.PRODUCT_ORDER_ID                                 svc_inst_id,
             a.ORDER_MODE                                       order_mode,
             b.RULE_ID                                          rule_id,
             c.RATE_ID                                          rate_id,
             row_number() over (PARTITION BY a.PRODUCT_ORDER_ID ORDER BY to_number(a.RATIO)) - 1
                                                                        calc_priority,
              a.SETT_PROV                                        object_value,
             '1'                                                tariff_type,
             a.RATIO / 100                                      rate_value,
             to_date(a.ACCT_MONTH, 'yyyymm')                    eff_date,
             add_months(to_date(
                                a.ACCT_MONTH, 'yyyymm'), 1) - 1 exp_date,
             a.ACCT_MONTH                                       acct_month,
             a.dest_source
      FROM STL_CDN_HW_RULE a,
           STL_OFFER_T b,
           STL_RATE_T c
      WHERE a.OFFER_CODE = b.OFFER_CODE
        AND a.ORDER_MODE = b.ORDER_MODE
        AND b.PRODUCT_CODE = '2023999400085020'
        AND a.PRODUCT_CODE = b.PRODUCT_CODE
        AND b.RULE_ID = c.RULE_ID
        AND b.DATA_SOURCE = 1
        and a.offer_code = '50004'
        and b.dest_source = '98'
        AND a.ACCT_MONTH = acctMonth
        and to_date(acctMonth, 'YYYYMM') between b.eff_date and b.exp_date) data,
     (SELECT '116' feetype
      FROM DUAL) ft;
COMMIT;

select('step7: 开始入库海外增值结算规则（STL_REPART_PARAMETER_T）');
call LOG_PROCEDURES('step7: 开始入库海外增值结算规则（STL_REPART_PARAMETER_T', v_proc_name);
----  先删除海外增值已存在的规则   理论上不会有，因为没有话单
delete
from STL_REPART_PARAMETER_T
where SVC_INST_ID in
      (select soid
       from STLUSERS.stl_sync_attr
       where POSPECNUMBER = '50004'
         and SOSPECNUMBER = '2023999400085020'
         and ATTR_ID = '50004050003'
         and (attr_value !='' or attr_value is not null));
COMMIT;

-----------------------------------------------------------------------------------------------------
---- 省间结算部分
INSERT INTO STL_REPART_PARAMETER_T(ID, OFFER_CODE, PRODUCT_CODE, PROD_INST_ID, SVC_INST_ID,
                                   ORDER_MODE, RULE_ID, RATE_ID, CALC_PRIORITY, OBJECT_VALUE, TARIFF_TYPE,
                                   RATE_VALUE, EFF_DATE,
                                   EXP_DATE, ACCT_MONTH, CHARGE_ITEM, DEST_SOURCE, ROUTE_FLAG)
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, t.*, 1 ROUTE_FLAG
FROM (SELECT *
      FROM (WITH tmp AS (SELECT a.POID,
                                a.SOID,
                                b.CALC_PRIORITY,
                                b.OBJECT_VALUE,
                                b.RATE_VALUE,
                                b.ACCT_MONTH,
                                b.ORDER_MODE
                         FROM stl_sync_attr a,
                              (SELECT b.OFFER_CODE,
                                      b.SVC_INST_ID,
                                      b.CALC_PRIORITY,
                                      b.OBJECT_VALUE,
                                      b.RATE_VALUE,
                                      b.ACCT_MONTH,
                                      b.ORDER_MODE,
                                      b.CHARGE_ITEM
                               FROM stl_sync_attr a,
                                    STL_REPART_PARAMETER_T b
                               WHERE a.ATTR_VALUE = b.SVC_INST_ID
                                 AND b.TARIFF_TYPE = 1
                                 AND a.POSPECNUMBER = b.OFFER_CODE
                                 AND a.POSPECNUMBER = '50004'
                                 AND a.SOSPECNUMBER = '2023999400085020'
                                 AND b.CHARGE_ITEM NOT IN (SELECT '116' feetype
                                                           FROM DUAL
                                                           union all
                                                           SELECT '117' feetype
                                                           FROM DUAL
                                                           union all
                                                           select resource_specode
                                                           FROM stludr.cdn_vas_dict
                                                           union all
                                                           select resource_specode
                                                           FROM stludr.hwcdn_vas_dict)) b
                         WHERE a.ATTR_VALUE = b.SVC_INST_ID
                           AND a.POSPECNUMBER = b.OFFER_CODE
                           AND a.POSPECNUMBER = '50004'
                           AND a.SOSPECNUMBER = '2023999400085020'
                         GROUP BY a.POID,
                                  a.SOID,
                                  b.CALC_PRIORITY,
                                  b.OBJECT_VALUE,
                                  b.RATE_VALUE,
                                  b.ACCT_MONTH,
                                  b.ORDER_MODE)
            SELECT a.OFFER_CODE,
                   a.PRODUCT_CODE,
                   tmp.POID,
                   tmp.SOID,
                   a.ORDER_MODE,
                   a.RULE_ID,
                   '1'                                                  RATE_ID,
                   tmp.CALC_PRIORITY,
                   tmp.OBJECT_VALUE,
                   '1'                                                  TARIFF_TYPE,
                   tmp.RATE_VALUE,
                   to_date(tmp.ACCT_MONTH, 'yyyymm')                    eff_date,
                   add_months(to_date(tmp.ACCT_MONTH, 'yyyymm'), 1) - 1 exp_date,
                   tmp.ACCT_MONTH,
                   b.CHARGE_ITEM,
                   a.DEST_SOURCE
            FROM STL_OFFER_T a,
                 STL_RULE_ITEM_T b,
                 tmp
            WHERE a.RULE_ID = b.RULE_ID
              AND a.OFFER_CODE = '50004'
              AND a.PRODUCT_CODE = '2023999400085020'
              AND a.ORDER_MODE = tmp.ORDER_MODE
              AND b.CHARGE_ITEM IN ('16','66','4039','8039')
            ORDER BY tmp.SOID, b.CHARGE_ITEM) A
      UNION
      SELECT *
      FROM (
               ---- 华为结算部分
               WITH HW AS (SELECT a.POID,
                                  a.SOID,
                                  b.CALC_PRIORITY,
                                  b.OBJECT_VALUE,
                                  b.RATE_VALUE,
                                  b.ACCT_MONTH,
                                  b.ORDER_MODE
                           FROM stl_sync_attr a,
                                (SELECT b.OFFER_CODE,
                                        b.SVC_INST_ID,
                                        b.CALC_PRIORITY,
                                        b.OBJECT_VALUE,
                                        b.RATE_VALUE,
                                        b.ACCT_MONTH,
                                        b.ORDER_MODE,
                                        b.CHARGE_ITEM
                                 FROM stl_sync_attr a,
                                      STL_REPART_PARAMETER_T b
                                 WHERE a.ATTR_VALUE = b.SVC_INST_ID
                                   AND b.TARIFF_TYPE = 1
                                   AND a.POSPECNUMBER = b.OFFER_CODE
                                   AND a.POSPECNUMBER = '50004'
                                   AND a.SOSPECNUMBER = '2023999400085020'
                                   AND b.CHARGE_ITEM IN (SELECT '116' feetype
                                                         FROM DUAL
                                                         union all
                                                         SELECT '117' feetype
                                                         FROM DUAL
                                                         union all
                                                         select resource_specode
                                                         FROM stludr.hwcdn_vas_dict
                                                         union
                                                         select resource_specode
                                                         FROM stludr.cdn_vas_dict)) b
                           WHERE a.ATTR_VALUE = b.SVC_INST_ID
                             AND a.POSPECNUMBER = b.OFFER_CODE
                             AND a.POSPECNUMBER = '50004'
                             AND a.SOSPECNUMBER = '2023999400085020'
                           GROUP BY a.POID,
                                    a.SOID,
                                    b.CALC_PRIORITY,
                                    b.OBJECT_VALUE,
                                    b.RATE_VALUE,
                                    b.ACCT_MONTH,
                                    b.ORDER_MODE
                           ORDER BY a.SOID)
               SELECT a.OFFER_CODE,
                      a.PRODUCT_CODE,
                      HW.POID,
                      HW.SOID,
                      a.ORDER_MODE,
                      a.RULE_ID,
                      '1'                                                 RATE_ID,
                      HW.CALC_PRIORITY,
                      HW.OBJECT_VALUE,
                      '1'                                                 TARIFF_TYPE,
                      HW.RATE_VALUE,
                      to_date(HW.ACCT_MONTH, 'yyyymm')                    eff_date,
                      add_months(to_date(HW.ACCT_MONTH, 'yyyymm'), 1) - 1 exp_date,
                      HW.ACCT_MONTH,
                      b.CHARGE_ITEM,
                      a.DEST_SOURCE
               FROM STL_OFFER_T a,
                    STL_RULE_ITEM_T b,
                    HW
               WHERE a.RULE_ID = b.RULE_ID
                 AND a.OFFER_CODE = '50004'
                 AND a.PRODUCT_CODE = '2023999400085020'
                 AND a.RULE_ID IN ('1053', '1054', '1055')
                 AND a.ORDER_MODE = HW.ORDER_MODE
               ORDER BY HW.SOID, b.CHARGE_ITEM) B) t;
COMMIT;
select('step8: 海外CDN规则计算完成');

call LOG_PROCEDURES('completed successfully. 执行完成总耗时秒='||TIMESTAMPDIFF(SECOND, @P_TIME2, NOW()), v_proc_name);

outReturn := 0;
        outSysError := 'OK';
END;
END;;
DELIMITER ;