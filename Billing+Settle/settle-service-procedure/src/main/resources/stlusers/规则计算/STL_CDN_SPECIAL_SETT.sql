/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：CDN 特殊订购规则处理  P_SETTLE_RULE_PROC_REPART_PARAMETER 中调用
**/
use stlusers;
DROP PROCEDURE IF EXISTS stlusers.STL_CDN_SPECIAL_SETT;
DELIMITER ;;
CREATE DEFINER="stlusers"@"10.%" PROCEDURE "STL_CDN_SPECIAL_SETT"(acctMonth IN VARCHAR2,
                                   outReturn OUT NUMBER,
                                   outSysError OUT VARCHAR2)
As

    vSql                  VARCHAR2(10240);
    v_proc_name   VARCHAR2(32) :='STL_CDN_SPECIAL_SETT';
    P_ERRCODE   VARCHAR2(16);
    P_ERRMSG    VARCHAR2(1024);
begin
		outSysError := '';
    	outReturn := -1;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
GET DIAGNOSTICS CONDITION 1 P_ERRCODE = RETURNED_SQLSTATE, P_ERRMSG = MESSAGE_TEXT;
outSysError := substr(P_ERRMSG, 1, 1000);
        outReturn  := -1;
ROLLBACK;
select ('exception: ' || outReturn || '|'  || P_ERRCODE || '|' || outSysError || '|') AS error_msg ;
call LOG_PROCEDURES(outSysError, v_proc_name);
SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = outSysError;

END;

BEGIN

-- set @vSql := 'delete from STLUSERS.LOG_PROCEDURES where MODULE = ''' || v_proc_name || '''';
-- SELECT @vSql;
-- PREPARE STMT FROM @vSql;
-- EXECUTE STMT;
-- DEALLOCATE PREPARE STMT;
-- COMMIT;

select('step1: 清除临时表（CDN_SPECIAL_PARAMETER_T）数据');
call LOG_PROCEDURES('step1: 清除临时表（CDN_SPECIAL_PARAMETER_T）数据', v_proc_name);

set @vSql := 'delete from STLUSERS.CDN_SPECIAL_PARAMETER_T where ACCT_MONTH = ''' || acctMonth || '''';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;

select('step2: 清除临时表（STL_CDN_RULE_SPECIAL_SETT）数据');
call LOG_PROCEDURES('step2: 清除临时表（STL_CDN_RULE_SPECIAL_SETT）数据', v_proc_name);

set @vSql := 'delete from STLUSERS.STL_CDN_RULE_SPECIAL_SETT where ACCT_MONTH = ''' || acctMonth ||'''';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;

select('step3: 清除临时表（STL_CDN_HW_RULE_SPECIAL_SETT）数据');
call LOG_PROCEDURES('step3: 清除临时表（STL_CDN_HW_RULE_SPECIAL_SETT）数据', v_proc_name);

set @vSql := 'delete from STLUSERS.STL_CDN_HW_RULE_SPECIAL_SETT where ACCT_MONTH = ''' || acctMonth || '''';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;

select('step4: 备份当前账期结算规则数据到CDN_SPECIAL_PARAMETER_T表');
call LOG_PROCEDURES('step4: 备份当前账期结算规则数据到CDN_SPECIAL_PARAMETER_T表', v_proc_name);

set @vSql := 'insert into STLUSERS.CDN_SPECIAL_PARAMETER_T select * from ' ||
                ' STLUSERS.STL_REPART_PARAMETER_T where ACCT_MONTH = ''' || acctMonth || '''' ||
                ' and PROD_INST_ID in (select PRODUCT_ORDER_ID from STLUSERS.CDN_SPECIAL_SETT_CONFIG)';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;

select('step5: 删除STL_REPART_PARAMETER_T表符合配置得数据');
call LOG_PROCEDURES('step5: 删除STL_REPART_PARAMETER_T表符合配置得数据', v_proc_name);

set @vSql := 'delete from STLUSERS.STL_REPART_PARAMETER_T where SVC_INST_ID in(select PRODUCT_ORDER_ID from STLUSERS.CDN_SPECIAL_SETT_CONFIG)';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;

select('step6: 查询STL_CDN_RULE数据入库到STL_CDN_RULE_SPECIAL_SETT');
call LOG_PROCEDURES('step6: 查询STL_CDN_RULE数据入库到STL_CDN_RULE_SPECIAL_SETT', v_proc_name);

set @vSql := 'insert into STLUSERS.STL_CDN_RULE_SPECIAL_SETT select * from ' ||
                ' STLUSERS.STL_CDN_RULE where ACCT_MONTH = ''' || acctMonth || ''''||
                ' and SETT_PROV != ''${EC_PROV_CODE_07}'' '||
                ' and PRODUCT_ORDER_ID in (select PRODUCT_ORDER_ID from STLUSERS.CDN_SPECIAL_SETT_CONFIG)';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;

select('step7: 查询STL_CDN_HW_RULE数据入库到STL_CDN_HW_RULE_SPECIAL_SETT');
call LOG_PROCEDURES('step7: 查询STL_CDN_HW_RULE数据入库到STL_CDN_HW_RULE_SPECIAL_SETT', v_proc_name);

set @vSql := 'insert into STLUSERS.STL_CDN_HW_RULE_SPECIAL_SETT select * from ' ||
                ' STLUSERS.STL_CDN_HW_RULE where ACCT_MONTH = ''' || acctMonth || ''''||
                ' and SETT_PROV != ''${EC_PROV_CODE_07}'' '||
                ' and PRODUCT_ORDER_ID in (select PRODUCT_ORDER_ID from STLUSERS.CDN_SPECIAL_SETT_CONFIG)';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;

select('step8: 更新STL_CDN_RULE_SPECIAL_SETT表结算比例');
call LOG_PROCEDURES('step8: 更新STL_CDN_RULE_SPECIAL_SETT表结算比例', v_proc_name);

set @vSql := 'update STLUSERS.STL_CDN_RULE_SPECIAL_SETT set RATIO =  round (PROV_AMOUNT / TOTAL_AMOUNT,12)';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;

select('step9: 更新STL_CDN_HW_RULE_SPECIAL_SETT表结算比例');
call LOG_PROCEDURES('step9: 更新STL_CDN_HW_RULE_SPECIAL_SETT表结算比例', v_proc_name);

set @vSql := 'update STLUSERS.STL_CDN_HW_RULE_SPECIAL_SETT set RATIO =  round (PROV_AMOUNT / TOTAL_AMOUNT,12)';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;

select('step10: 省间结算规则入库');
call LOG_PROCEDURES('step10: 省间结算规则入库', v_proc_name);

INSERT INTO STLUSERS.STL_REPART_PARAMETER_T(ID, OFFER_CODE, PRODUCT_CODE, PROD_INST_ID, SVC_INST_ID,
                                            ORDER_MODE, RULE_ID, RATE_ID, CALC_PRIORITY,
                                            OBJECT_VALUE, TARIFF_TYPE, RATE_VALUE, EFF_DATE,
                                            EXP_DATE, ACCT_MONTH, DEST_SOURCE, CHARGE_ITEM, ROUTE_FLAG)
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*, ft.FEETYPE, 1 route_flag
FROM (SELECT a.OFFER_CODE                                       offer_code,
             a.PRODUCT_CODE                                     product_code,
             a.OFFER_ORDER_ID                                   poid_inst_id,
             a.PRODUCT_ORDER_ID                                 svc_inst_id,
             a.ORDER_MODE                                       order_mode,
             b.RULE_ID                                          rule_id,
             c.RATE_ID                                          rate_id,
             row_number() over (PARTITION BY a.PRODUCT_ORDER_ID ORDER BY to_number(a.RATIO)) - 1
                                                                        calc_priority,
              a.SETT_PROV                                        object_value,
             '1'                                                tariff_type,
             a.RATIO                                            rate_value,
             to_date(a.ACCT_MONTH, 'yyyymm')                    eff_date,
             add_months(to_date(
                                a.ACCT_MONTH, 'yyyymm'), 1) - 1 exp_date,
             a.ACCT_MONTH                                       acct_month,
             a.dest_source
      FROM STL_CDN_RULE_SPECIAL_SETT a,
           STL_OFFER_T b,
           STL_RATE_T c
      WHERE a.OFFER_CODE = b.OFFER_CODE
        AND a.ORDER_MODE = b.ORDER_MODE
        AND b.PRODUCT_CODE = -1
        --AND a.PRODUCT_CODE != '2023999400083948'
        AND b.RULE_ID = c.RULE_ID
        AND b.DATA_SOURCE = 1
        and a.offer_code = '50004'
        and b.dest_source = '0'
        AND a.ACCT_MONTH = acctMonth
        and to_date(acctMonth, 'YYYYMM') between b.eff_date and b.exp_date) data,
     (SELECT '16' feetype
      FROM DUAL
      UNION
      SELECT '1063'
      FROM DUAL
      UNION
      SELECT '4018'
      FROM DUAL
      UNION
      SELECT '1101'
      FROM DUAL
      UNION
      SELECT '1102'
      FROM DUAL
      UNION
      SELECT '1467' feetype
      FROM DUAL
      UNION
      SELECT '1468'
      FROM DUAL
      UNION
      SELECT '1469'
      FROM DUAL
      UNION
      SELECT '1470' feetype
      FROM DUAL) ft;

-- 华为结算部分
select('step11: 华为结算规则入库');
call LOG_PROCEDURES('step11: 华为结算规则入库', v_proc_name);

INSERT INTO STL_REPART_PARAMETER_T(ID, OFFER_CODE, PRODUCT_CODE, PROD_INST_ID, SVC_INST_ID,
                                   ORDER_MODE, RULE_ID, RATE_ID, CALC_PRIORITY,
                                   OBJECT_VALUE, TARIFF_TYPE, RATE_VALUE, EFF_DATE,
                                   EXP_DATE, ACCT_MONTH, DEST_SOURCE, CHARGE_ITEM, ROUTE_FLAG)
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*, ft.FEETYPE, 1 route_flag
FROM (SELECT a.OFFER_CODE                                       offer_code,
             a.PRODUCT_CODE                                     product_code,
             a.OFFER_ORDER_ID                                   poid_inst_id,
             a.PRODUCT_ORDER_ID                                 svc_inst_id,
             a.ORDER_MODE                                       order_mode,
             b.RULE_ID                                          rule_id,
             c.RATE_ID                                          rate_id,
             row_number() over (PARTITION BY a.PRODUCT_ORDER_ID ORDER BY to_number(a.RATIO)) - 1
                                                                        calc_priority,
              a.SETT_PROV                                        object_value,
             '1'                                                tariff_type,
             a.RATIO                                            rate_value,
             to_date(a.ACCT_MONTH, 'yyyymm')                    eff_date,
             add_months(to_date(
                                a.ACCT_MONTH, 'yyyymm'), 1) - 1 exp_date,
             a.ACCT_MONTH                                       acct_month,
             a.dest_source
      FROM STL_CDN_HW_RULE_SPECIAL_SETT a,
           STL_OFFER_T b,
           STL_RATE_T c
      WHERE a.OFFER_CODE = b.OFFER_CODE
        AND a.ORDER_MODE = b.ORDER_MODE
        AND b.PRODUCT_CODE = -1
        -- AND b.PRODUCT_CODE != '2023999400083948'
        AND b.RULE_ID = c.RULE_ID
        AND b.DATA_SOURCE = 1
        and a.offer_code = '50004'
        and b.dest_source = '98'
        AND a.ACCT_MONTH = acctMonth
        and to_date(acctMonth, 'YYYYMM') between b.eff_date and b.exp_date) data,
     (SELECT '116' feetype
      FROM DUAL
      union all
      select resource_specode
      From stludr.cdn_vas_dict) ft;
COMMIT;

outReturn := 0;
        outSysError := 'OK';

SELECT 'procedure STL_CDN_SPECIAL_SETT completed successfully. outReturn=' || outReturn;
call LOG_PROCEDURES('procedure STL_CDN_SPECIAL_SETT completed successfully. outReturn=' || outReturn, v_proc_name);
END;
END;;
DELIMITER ;