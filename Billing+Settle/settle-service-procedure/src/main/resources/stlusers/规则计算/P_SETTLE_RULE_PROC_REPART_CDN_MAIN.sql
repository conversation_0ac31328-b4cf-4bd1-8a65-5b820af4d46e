/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：规则计算-repart规则计算
**/
use stlusers;
DROP PROCEDURE IF EXISTS stlusers.P_SETTLE_RULE_PROC_REPART_CDN_MAIN;

DELIMITER ;;
CREATE or replace DEFINER="stlusers"@"10.%" PROCEDURE `stlusers`.P_SETTLE_RULE_PROC_REPART_CDN_MAIN(
        inMonth in VARCHAR2,
        inBatch IN VARCHAR2,
        flag_version IN VARCHAR2,
        reserve1 IN VARCHAR2,
        reserve2 IN VARCHAR2,
        proc_out OUT VARCHAR2,
        outSysError OUT VARCHAR2(1000),
        outReturn OUT NUMBER(4),
        outBL OUT VARCHAR2,
        outAR OUT VARCHAR2)
AS
    vSql VARCHAR2(10240);

    iv_prov_cd varchar2(3);
    ivNextMonth varchar2(6);
    P_ERRCODE   VARCHAR2(16);
    P_ERRMSG    VARCHAR2(1024);

    v_proc_name   VARCHAR2(36) := 'P_SETTLE_RULE_PROC_REPART_CDN_MAIN';

BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
GET DIAGNOSTICS CONDITION 1 P_ERRCODE = RETURNED_SQLSTATE, P_ERRMSG = MESSAGE_TEXT;
outSysError := substr(P_ERRMSG, 1, 1000);
        outReturn  := -1;
ROLLBACK;


select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg ;
call LOG_PROCEDURES(outSysError, v_proc_name);
SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = outSysError;

END;

BEGIN

         /*优化内容：
          alter table stlusers.STL_CDN_HW_RULE modify RATIO varchar(64);
          alter table stludr.cust_prod_info add index idx_pc_sc_am(product_code, service_code, acct_month);
          alter table stlusers.stl_national_rate add index idx_am_bt(acct_month, biz_type);
          alter table stludr.ur_cdn_202307_t add index idx_1(rate_back_id,sett_prov,ACCU_VOLUME,ACCU_DURATION);
          alter table stl_cdn_rule modify PRODUCT_CODE varchar(64);
          alter table stludr.ur_cdn_202307_t add key idx_accu_volume(accu_volume);
        alter table stludr.ur_cdn_202307_t add key idx_accu_duration(accu_duration);
        alter table stludr.ur_cdn_202307_t add index idx_rbi_sgn_dt (rate_back_id,sub_group_num,dup_time);

          */

        outSysError := 'OK';
        outReturn := 0;
        ivNextMonth := to_char(add_months(to_date(inMonth, 'yyyymm'), 1), 'yyyymm');
select concat('1_', now());


--开始时间
SET @P_TIME_CDNMAIN := SYSDATE;
call LOG_PROCEDURES('开始执行', v_proc_name);

select concat('2_', now());
call LOG_PROCEDURES('1.插入CDN结算规则', v_proc_name);

--插入CDN结算规则
set @vSql := 'TRUNCATE TABLE STL_CDN_RULE';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := 'TRUNCATE TABLE STL_CDN_HW_RULE';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

----计算全网订购比例
            set @vSql := 'delete from stl_national_rate where acct_month = ''' || inMonth || ''' and biz_type in (''CDN'', ''CDN-HW'')';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

select concat('3_', now());

truncate table stlusers.repart_parameter_tmp;

--当前月月表的汇总
set @vSql := 'insert into stlusers.repart_parameter_tmp(acct_month,ec_code,order_mode,offer_code,product_code, offer_order_id,product_order_id,sett_prov,rate_back_id,sub_group_num,dup_time,prov_quantity)
              select acct_month, ec_code,order_mode,offer_code, product_code,offer_order_id,product_order_id,sett_prov, rate_back_id,sub_group_num, substr(dup_time, 1, 6) dup_time, sum(nvl(accu_volume, 0) + nvl(accu_duration, 0)) prov_quantity
                from stludr.ur_cdn_' || inMonth || '_t ' ||
               'where (accu_volume <> 0 or accu_duration<>0)
               group by acct_month,ec_code,order_mode, offer_code, product_code,offer_order_id, product_order_id,sett_prov, rate_back_id, sub_group_num,substr(dup_time, 1, 6)';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

--下月月表当前月数据汇总（华为结算使用）
--月表中的字段acct_month就是账期月份，而dup_time会含少量上月数据。使用acct_month来区分临时表中的数据是当前月还是下个月的。
set @vSql := 'insert into stlusers.repart_parameter_tmp(acct_month,ec_code,order_mode,offer_code,product_code, offer_order_id,product_order_id,sett_prov,rate_back_id,sub_group_num,dup_time,prov_quantity)
select acct_month, ec_code, order_mode,offer_code, product_code, offer_order_id, product_order_id, sett_prov, rate_back_id, ''1'','||inMonth||',
       sum(nvl(ACCU_VOLUME, 0) + nvl(ACCU_DURATION, 0)) PROV_QUANTITY
  from stludr.ur_cdn_' || ivNextMonth || '_t
 where sub_group_num = ''1''
   and dup_time like ''' || inMonth || '%''
   and (accu_volume <> 0 or accu_duration <> 0)
 group by acct_month,ec_code,order_mode, offer_code, product_code, offer_order_id, product_order_id, sett_prov,rate_back_id';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

/*
create table stlusers.repart_parameter_tmp_1(sett_prov varchar(3), prov_quantity bigint) comment 'shard=shard2';
*/

truncate table stlusers.repart_parameter_tmp_1;
--中间表数据量不大，去掉游标循环
/*open cur_prov;
loop
fetch cur_prov into iv_prov_cd;
                exit when cur_prov%NOTFOUND;
                ----省间结算部分
                set @vSql := 'insert into stlusers.repart_parameter_tmp_1 ' ||chr(10)||
                         'select '''||iv_prov_cd||''', sum(nvl(ACCU_VOLUME, 0) + nvl(ACCU_DURATION, 0)) prov_quantity ' ||chr(10)||
                         'from stludr.ur_cdn_' || inMonth || '_t ' ||chr(10)||
                         'where rate_back_id = ''1'' and sett_prov='''||iv_prov_cd||''' having prov_quantity > 0';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


end loop;
close cur_prov;*/

--省间结算部分  使用中间表的数据来计算
set @vSql := 'insert into stlusers.repart_parameter_tmp_1 (sett_prov, prov_quantity)
                         select sett_prov, sum(prov_quantity) prov_quantity
                         from repart_parameter_tmp
                         where rate_back_id = ''1''  and acct_month=' || inMonth || ' group by sett_prov having prov_quantity > 0';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := 'insert into stl_national_rate ' ||chr(10)||
                     'select null,acct_month, biz_type, prov_cd, prov_quantity, total_quantity, ' ||chr(10)||
                      'round(prov_quantity / total_quantity * 100, 12) rate, ''jcyw'' ' ||chr(10)||
                 'from (select ''' || inMonth || ''' acct_month, ''CDN'' biz_type, sett_prov prov_cd, a.prov_quantity, b.total_quantity ' ||chr(10)||
                         'from stlusers.repart_parameter_tmp_1 a, ' ||chr(10)||
                              '(select sum(prov_quantity) total_quantity from stlusers.repart_parameter_tmp_1) b) t ' ||chr(10)||
                'where total_quantity <> 0 and prov_quantity is not null';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;



call LOG_PROCEDURES('2.省间结算部分超低时延', v_proc_name);

---- 省间结算部分超低时延
set @vSql := 'insert into stl_national_rate ' ||chr(10)||
                 'select null,acct_month, biz_type, prov_cd, prov_quantity, total_quantity, ' ||chr(10)||
                  'round(prov_quantity / total_quantity * 100, 12) rate,''cdsy'' ' ||chr(10)||
             'from (select ''' || inMonth || ''' acct_month, ''CDN'' biz_type, sett_prov prov_cd, a.prov_quantity, b.total_quantity ' ||chr(10)||
                     'from (select sett_prov, sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0)) prov_quantity ' ||chr(10)||
                             'from stludr.ur_cdnappend_' || inMonth || '_t ' ||chr(10)||
                            'where FLOW_TYPE = ''1'' ' ||
                            'group by sett_prov) a, ' ||
                          '(select sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0)) total_quantity ' ||chr(10)||
                             'from stludr.ur_cdnappend_' || inMonth || '_t ' ||chr(10)||
                            'where FLOW_TYPE = ''1'') b) t ' ||chr(10)||
            'where total_quantity <> 0';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call LOG_PROCEDURES('3.省间结算部分全站加速  和需求沟通都是各算各的  总流量和分省流量都是全站加速的', v_proc_name);

----省间结算部分全站加速  和需求沟通都是各算各的  总流量和分省流量都是全站加速的
set @vSql :=  'insert into stl_national_rate ' ||
                 'select null,acct_month, biz_type, prov_cd, prov_quantity, total_quantity, ' ||
                 'round(prov_quantity / total_quantity * 100, 12) rate,''qzjs'' ' ||
                 'from (select ''' || inMonth ||
                 ''' acct_month, ''CDN'' biz_type, sett_prov prov_cd, a.prov_quantity, b.total_quantity ' ||
                 'from (select sett_prov, sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0) + nvl(UP_FLOW,0)) prov_quantity ' ||
                 'from stludr.ur_cdnappend_' || inMonth || '_t ' ||
                 'where FLOW_TYPE = ''1'' and service_type in(''1'',''2'') ' ||
                 'group by sett_prov) a, ' ||
                 '(select sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0) + nvl(UP_FLOW,0)) total_quantity ' ||
                 'from stludr.ur_cdnappend_' || inMonth || '_t ' ||
                 'where FLOW_TYPE = ''1'' and service_type in(''1'',''2'') ) b) t ' ||
                 'where total_quantity <> 0';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

select concat('4_', now());

truncate table stlusers.repart_parameter_tmp_1;


call LOG_PROCEDURES('4.华为结算部分', v_proc_name);

--华为结算部分
set @vSql := 'insert into stlusers.repart_parameter_tmp_1  (sett_prov, prov_quantity)
           select sett_prov, sum(prov_quantity) prov_quantity from stlusers.repart_parameter_tmp
            where rate_back_id = ''1'' and sub_group_num = ''1'' and dup_time = ''' || inMonth || ''' group by sett_prov
            having prov_quantity is not null';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


set @vSql := 'insert into stl_national_rate ' ||
                     'select null,acct_month, biz_type, prov_cd, prov_quantity, total_quantity, ' ||
                      'round(prov_quantity / total_quantity * 100, 12) rate,''jcyw'' ' ||
                 'from (select ''' || inMonth || ''' acct_month, ''CDN-HW'' biz_type, sett_prov prov_cd, a.prov_quantity, b.total_quantity ' ||
                         'from stlusers.repart_parameter_tmp_1 a, ' ||
                              '(select sum(prov_quantity) total_quantity from stlusers.repart_parameter_tmp_1) b) t ' ||
                'where total_quantity <> 0';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;



call LOG_PROCEDURES('5.华为结算部分超低时延', v_proc_name);
----华为结算部分超低时延
set @vSql := 'insert into stl_national_rate ' ||
                 'select null,acct_month, biz_type, prov_cd, prov_quantity, total_quantity, ' ||
                  'round(prov_quantity / total_quantity * 100, 12) rate,''cdsy'' ' ||
             'from (select ''' || inMonth || ''' acct_month, ''CDN-HW'' biz_type, sett_prov prov_cd, a.prov_quantity, b.total_quantity ' ||
                     'from (select sett_prov, sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0)) prov_quantity ' ||
                             'from (select * from stludr.ur_cdnappend_' || inMonth || '_t ' ||
                                    'union all ' ||
                                   'select * from stludr.ur_cdnappend_' || ivNextMonth || '_t) ' ||
                            'where FLOW_TYPE = ''1'' and service_type = ''3'' and DISTRIBUTION_PLANE = ''1'' ' ||
                              'and substr(dup_time, 1, 6) = ''' || inMonth || ''' ' ||
                            'group by sett_prov) a, ' ||
                          '(select sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0)) total_quantity ' ||
                             'from (select * from stludr.ur_cdnappend_' || inMonth || '_t ' ||
                                    'union all ' ||
                                   'select * from stludr.ur_cdnappend_' || ivNextMonth || '_t) ' ||
                            'where FLOW_TYPE = ''1''  and service_type = ''3'' and DISTRIBUTION_PLANE = ''1'' ' ||
                              'and substr(dup_time, 1, 6) = ''' || inMonth || ''') b) t ' ||
            'where total_quantity <> 0';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call LOG_PROCEDURES('6.华为结算部分全站加速', v_proc_name);

----华为结算部分全站加速
set @vSql := 'insert into stl_national_rate ' ||
                 'select null,acct_month, biz_type, prov_cd, prov_quantity, total_quantity, ' ||
                 'round(prov_quantity / total_quantity * 100, 12) rate,''qzjs'' ' ||
                 'from (select ''' || inMonth ||
                 ''' acct_month, ''CDN-HW'' biz_type, sett_prov prov_cd, a.prov_quantity, b.total_quantity ' ||
                 'from (select sett_prov, sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0) + nvl(UP_FLOW,0)) prov_quantity ' ||
                 'from (select * from stludr.ur_cdnappend_' || inMonth || '_t ' ||
                 'union all ' ||
                 'select * from stludr.ur_cdnappend_' || ivNextMonth || '_t) ' ||
                 'where FLOW_TYPE = ''1'' and service_type in(''1'',''2'') and DISTRIBUTION_PLANE = ''1'' ' ||
                 'and substr(dup_time, 1, 6) = ''' || inMonth || ''' ' ||
                 'group by sett_prov) a, ' ||
                 '(select sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0) + nvl(UP_FLOW,0)) total_quantity ' ||
                 'from (select * from stludr.ur_cdnappend_' || inMonth || '_t ' ||
                 'union all ' ||
                 'select * from stludr.ur_cdnappend_' || ivNextMonth || '_t) ' ||
                 'where FLOW_TYPE = ''1'' and service_type in(''1'',''2'') and DISTRIBUTION_PLANE = ''1'' ' ||
                 'and substr(dup_time, 1, 6) = ''' || inMonth || ''') b) t ' ||
                 'where total_quantity <> 0';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
select concat('5_', now());

/*CREATE TABLE repart_parameter_tmp_2 (
ACCT_MONTH char(6), EC_CODE varchar(32), ORDER_MODE decimal(2,0), OFFER_CODE varchar(64), PRODUCT_CODE varchar(64),
OFFER_ORDER_ID decimal(15,0), PRODUCT_ORDER_ID decimal(15,0), SETT_PROV varchar(3), prov_amount bigint) comment 'shard=shard2';
*/

truncate table repart_parameter_tmp_2;

call LOG_PROCEDURES('7.含网内流量订购的比例计算', v_proc_name);

----含网内流量订购的比例计算
--省间结算部分
set @vSql := 'INSERT INTO stlusers.repart_parameter_tmp_2 ' ||
              '(acct_month, ec_code, order_mode, offer_code, product_code, offer_order_id, product_order_id, sett_prov, prov_amount)' ||
               'SELECT ''' || inMonth || ''', ' ||
                     'EC_CODE, ' ||
                     'ORDER_MODE, ' ||
                     'OFFER_CODE, ' ||
                     'PRODUCT_CODE, ' ||
                     'OFFER_ORDER_ID, ' ||
                     'PRODUCT_ORDER_ID, ' ||
                     'SETT_PROV, ' ||
                     'sum(prov_quantity) prov_amount ' ||
                'FROM  stlusers.repart_parameter_tmp ' ||
               'where rate_back_id = ''1'' and PRODUCT_CODE !=''****************'' and dup_time='''||inMonth ||
               ''' GROUP BY  EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID,SETT_PROV';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

truncate table stlusers.repart_parameter_tmp_2_1;

-- 备份一下网内流量的订购
insert into stlusers.repart_parameter_tmp_2_1 select * from repart_parameter_tmp_2;

set @vSql := 'INSERT INTO stlusers.STL_CDN_RULE ' ||
                         'SELECT NULL,a.ACCT_MONTH, a.EC_CODE,  a.ORDER_MODE,  a.OFFER_CODE, a.PRODUCT_CODE, a.OFFER_ORDER_ID, a.PRODUCT_ORDER_ID, a.SETT_PROV, a.prov_amount, b.total_amount, ' ||
                                  /*'case when (ssbc.istop55 = ''0'' or ssbc.istop55 is null) and a.order_mode = ''3'' then round(a.prov_amount / b.total_amount * 100 * 0.8, 7) '
                                'else round(a.prov_amount / b.total_amount * 100, 12) end ratio ,*/
                           ' round(a.prov_amount / b.total_amount * 100, 12), 0 dest_source, ssbc.istop55 ' ||
                           'FROM repart_parameter_tmp_2 a ' ||
                          'inner join(SELECT ACCT_MONTH, ' ||
                                        'EC_CODE, ' ||
                                        'ORDER_MODE, ' ||
                                        'OFFER_CODE, ' ||
                                        'PRODUCT_CODE, ' ||
                                        'OFFER_ORDER_ID, ' ||
                                        'PRODUCT_ORDER_ID, ' ||
                                        'sum(nvl(prov_amount, 0)) total_amount ' ||
                                   'FROM repart_parameter_tmp_2 ' ||
                                  'GROUP BY ACCT_MONTH, EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID) b ' ||
                          '   on a.ACCT_MONTH = b.ACCT_MONTH AND a.ORDER_MODE = b.ORDER_MODE AND a.OFFER_ORDER_ID = b.OFFER_ORDER_ID ' ||
                            '    AND a.PRODUCT_ORDER_ID = b.PRODUCT_ORDER_ID AND b.total_amount <> 0 '
                           'left join stl_serv_biz_code ssbc on a.product_order_id=ssbc.order_id and ''' || inMonth || ''' between to_char(ssbc.effective_date, ''yyyymm'') and to_char(ssbc.expiry_date, ''yyyymm'')';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

call LOG_PROCEDURES('8.含网内流量订购的比例计算，省间结算部分超低时延', v_proc_name);

---- 省间结算部分超低时延
set @vSql := 'INSERT INTO stlusers.STL_CDN_RULE ' ||
       'SELECT NULL,a.ACCT_MONTH, ' ||
             'a.EC_CODE, ' ||
             'a.ORDER_MODE, ' ||
             'a.OFFER_CODE, ' ||
             'a.PRODUCT_CODE, ' ||
             'a.OFFER_ORDER_ID, ' ||
             'a.PRODUCT_ORDER_ID, ' ||
             'a.SETT_PROV, ' ||
             'a.prov_amount, ' ||
             'b.total_amount, ' ||
             'round(a.prov_amount / b.total_amount * 100, 12) ratio, ' ||
             '0 DEST_SOURCE, ' ||
             ''''' ' ||
        'FROM (SELECT ACCT_MONTH, ' ||
                     'EC_CODE, ' ||
                     'ORDER_MODE, ' ||
                     'OFFER_CODE, ' ||
                     'PRODUCT_CODE, ' ||
                     'OFFER_ORDER_ID, ' ||
                     'PRODUCT_ORDER_ID, ' ||
                     'SETT_PROV, ' ||
                     'sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0)) prov_amount ' ||
                'FROM stludr.UR_CDNAPPEND_' || inMonth || '_T ' ||
               'where FLOW_TYPE = ''1'' and service_type = ''3'' and PRODUCT_CODE !=''****************''' ||  -- 排除海外CDN
               'GROUP BY ACCT_MONTH, EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID, SETT_PROV) a, ' ||
             '(SELECT ACCT_MONTH, ' ||
                     'EC_CODE, ' ||
                     'ORDER_MODE, ' ||
                     'OFFER_CODE, ' ||
                     'PRODUCT_CODE, ' ||
                     'OFFER_ORDER_ID, ' ||
                     'PRODUCT_ORDER_ID, ' ||
                     'sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0)) total_amount ' ||
                'FROM stludr.UR_CDNAPPEND_' || inMonth || '_T ' ||
               'where FLOW_TYPE = ''1'' and SERVICE_TYPE = ''3'' and PRODUCT_CODE !=''****************''' ||  -- 排除海外CDN
               'GROUP BY ACCT_MONTH, EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID) b ' ||
       'WHERE a.ACCT_MONTH = b.ACCT_MONTH ' ||
         'AND a.ORDER_MODE = b.ORDER_MODE ' ||
         'AND a.OFFER_ORDER_ID = b.OFFER_ORDER_ID ' ||
         'AND a.PRODUCT_ORDER_ID = b.PRODUCT_ORDER_ID ' ||
         'AND b.total_amount <> 0 ' ||
       'ORDER BY ACCT_MONTH, PRODUCT_ORDER_ID';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call LOG_PROCEDURES('9.含网内流量订购的比例计算，省间结算部分全站加速', v_proc_name);

-- 省间结算部分全站加速
set @vSql :=  'INSERT INTO stlusers.STL_CDN_RULE ' ||
                 'SELECT NULL,a.ACCT_MONTH, ' ||
                 'a.EC_CODE, ' ||
                 'a.ORDER_MODE, ' ||
                 'a.OFFER_CODE, ' ||
                 'a.PRODUCT_CODE, ' ||
                 'a.OFFER_ORDER_ID, ' ||
                 'a.PRODUCT_ORDER_ID, ' ||
                 'a.SETT_PROV, ' ||
                 'a.prov_amount, ' ||
                 'b.total_amount, ' ||
                 'round(a.prov_amount / b.total_amount * 100, 12) ratio, ' ||
                 '0 DEST_SOURCE, ' ||
                 ''''' ' ||
                 'FROM (SELECT ACCT_MONTH, ' ||
                 'EC_CODE, ' ||
                 'ORDER_MODE, ' ||
                 'OFFER_CODE, ' ||
                 'PRODUCT_CODE, ' ||
                 'OFFER_ORDER_ID, ' ||
                 'PRODUCT_ORDER_ID, ' ||
                 'SETT_PROV, ' ||
                 'sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0) + nvl(UP_FLOW,0)) prov_amount ' ||
                 'FROM stludr.UR_CDNAPPEND_' || inMonth || '_T ' ||
                 'where FLOW_TYPE = ''1'' and service_type in(''1'',''2'') and PRODUCT_CODE !=''****************''' ||  -- 排除海外CDN
                 'GROUP BY ACCT_MONTH, EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID, SETT_PROV) a, ' ||
                 '(SELECT ACCT_MONTH, ' ||
                 'EC_CODE, ' ||
                 'ORDER_MODE, ' ||
                 'OFFER_CODE, ' ||
                 'PRODUCT_CODE, ' ||
                 'OFFER_ORDER_ID, ' ||
                 'PRODUCT_ORDER_ID, ' ||
                 'sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0) + nvl(UP_FLOW,0)) total_amount ' ||
                 'FROM stludr.UR_CDNAPPEND_' || inMonth || '_T ' ||
                 'where FLOW_TYPE = ''1'' and SERVICE_TYPE in(''1'',''2'') and PRODUCT_CODE !=''****************''' ||  -- 排除海外CDN
                 'GROUP BY ACCT_MONTH, EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID) b ' ||
                 'WHERE a.ACCT_MONTH = b.ACCT_MONTH ' ||
                 'AND a.ORDER_MODE = b.ORDER_MODE ' ||
                 'AND a.OFFER_ORDER_ID = b.OFFER_ORDER_ID ' ||
                 'AND a.PRODUCT_ORDER_ID = b.PRODUCT_ORDER_ID ' ||
                 'AND b.total_amount <> 0 ' ||
                 'ORDER BY ACCT_MONTH, PRODUCT_ORDER_ID';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


select concat('6_', now());

truncate table repart_parameter_tmp_2;

call LOG_PROCEDURES('10.含网内流量订购的比例计算，华为结算部分', v_proc_name);

----含网内流量订购的比例计算

set @vSql := 'INSERT INTO stlusers.repart_parameter_tmp_2 ' ||
               '(acct_month, ec_code, order_mode, offer_code, product_code, offer_order_id, product_order_id, sett_prov, prov_amount)' ||
               'SELECT ''' || inMonth || ''' ACCT_MONTH, EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, ' ||
                          'PRODUCT_ORDER_ID,SETT_PROV, sum(PROV_QUANTITY) prov_amount ' ||
               '  FROM stlusers.repart_parameter_tmp '||
               ' where rate_back_id = ''1'' and PRODUCT_CODE !=''****************''  and sub_group_num = ''1'' and dup_time = ''' || inMonth || ''' '||
               ' GROUP BY EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID,SETT_PROV';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

--华为结算部分
set @vSql := 'INSERT INTO stlusers.STL_CDN_HW_RULE ' ||
                         'SELECT NULL,a.ACCT_MONTH, a.EC_CODE, a.ORDER_MODE, a.OFFER_CODE, a.PRODUCT_CODE, a.OFFER_ORDER_ID, a.PRODUCT_ORDER_ID, ' ||
                                'a.SETT_PROV, a.prov_amount, b.total_amount, ' ||
                                /*'case when (ssbc.istop55 = ''0'' or ssbc.istop55 is null) and a.order_mode = ''3'' then round(a.prov_amount / b.total_amount * 100 * 0.8, 7) '
                                'else round(a.prov_amount / b.total_amount * 100, 12) end ratio, */
                                'round(a.prov_amount / b.total_amount * 100, 12), 98 DEST_SOURCE, ssbc.istop55 ' ||
                           'FROM (SELECT ACCT_MONTH, ' ||
                                        'EC_CODE, ' ||
                                        'ORDER_MODE, ' ||
                                        'OFFER_CODE, ' ||
                                        'PRODUCT_CODE, ' ||
                                        'OFFER_ORDER_ID, ' ||
                                        'PRODUCT_ORDER_ID, ' ||
                                        'SETT_PROV, ' ||
                                        'prov_amount ' ||
                                   'FROM stlusers.repart_parameter_tmp_2 ) a ' ||
                           'INNER JOIN (SELECT ACCT_MONTH, ' ||
                                        'EC_CODE, ' ||
                                        'ORDER_MODE, ' ||
                                        'OFFER_CODE, ' ||
                                        'PRODUCT_CODE, ' ||
                                        'OFFER_ORDER_ID, ' ||
                                        'PRODUCT_ORDER_ID, ' ||
                                        'sum(prov_amount) total_amount ' ||
                                   'FROM stlusers.repart_parameter_tmp_2 ' ||
                                  'GROUP BY ACCT_MONTH, EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID) b ' ||
                          '  ON a.ACCT_MONTH = b.ACCT_MONTH  AND a.ORDER_MODE = b.ORDER_MODE  AND a.OFFER_ORDER_ID = b.OFFER_ORDER_ID ' ||
                          '     AND a.PRODUCT_ORDER_ID = b.PRODUCT_ORDER_ID AND b.total_amount <> 0 '||
                          'left join stl_serv_biz_code ssbc on a.product_order_id=ssbc.order_id and ''' || inMonth || ''' between to_char(ssbc.effective_date, ''yyyymm'') and to_char(ssbc.expiry_date, ''yyyymm'')';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

call LOG_PROCEDURES('11.含网内流量订购的比例计算，华为结算部分超低时延', v_proc_name);

--华为结算部分超低时延
set @vSql := 'INSERT INTO stlusers.STL_CDN_HW_RULE ' ||
      'SELECT NULL,a.ACCT_MONTH, ' ||
             'a.EC_CODE, ' ||
             'a.ORDER_MODE, ' ||
             'a.OFFER_CODE, ' ||
             'a.PRODUCT_CODE, ' ||
             'a.OFFER_ORDER_ID, ' ||
             'a.PRODUCT_ORDER_ID, ' ||
             'a.SETT_PROV, ' ||
             'a.prov_amount, ' ||
             'b.total_amount, ' ||
             'round(a.prov_amount / b.total_amount * 100, 12) ratio, ' ||
             '98 DEST_SOURCE, ' ||
             ''''' ' ||
        'FROM (SELECT ''' || inMonth || ''' ACCT_MONTH, ' ||
                     'EC_CODE, ' ||
                     'ORDER_MODE, ' ||
                     'OFFER_CODE, ' ||
                     'PRODUCT_CODE, ' ||
                     'OFFER_ORDER_ID, ' ||
                     'PRODUCT_ORDER_ID, ' ||
                     'SETT_PROV, ' ||
                     'sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0)) prov_amount ' ||
                'FROM (select * from stludr.ur_cdnappend_' || inMonth || '_t ' ||
                       'union all ' ||
                      'select * from stludr.ur_cdnappend_' || ivNextMonth || '_t) ' ||
               'where FLOW_TYPE = ''1'' and service_type = ''3'' and DISTRIBUTION_PLANE = ''1'' ' ||
                 ' and PRODUCT_CODE !=''****************'' ' ||
                 ' and substr(dup_time, 1, 6) = ''' || inMonth || ''' ' ||
               'GROUP BY EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID, SETT_PROV) a, ' ||
             '(SELECT ''' || inMonth || ''' ACCT_MONTH, ' ||
                     'EC_CODE, ' ||
                     'ORDER_MODE, ' ||
                     'OFFER_CODE, ' ||
                     'PRODUCT_CODE, ' ||
                     'OFFER_ORDER_ID, ' ||
                     'PRODUCT_ORDER_ID, ' ||
                     'sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0)) total_amount ' ||
                'FROM (select * from stludr.ur_cdnappend_' || inMonth || '_t ' ||
                       'union all ' ||
                      'select * from stludr.ur_cdnappend_' || ivNextMonth || '_t) ' ||
               'where FLOW_TYPE = ''1'' and service_type = ''3'' and DISTRIBUTION_PLANE = ''1'' ' ||
               	 ' and PRODUCT_CODE !=''****************'' ' ||
                 ' and substr(dup_time, 1, 6) = ''' || inMonth || ''' ' ||
               'GROUP BY EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID) b ' ||
       'WHERE a.ACCT_MONTH = b.ACCT_MONTH ' ||
         'AND a.ORDER_MODE = b.ORDER_MODE ' ||
         'AND a.OFFER_ORDER_ID = b.OFFER_ORDER_ID ' ||
         'AND a.PRODUCT_ORDER_ID = b.PRODUCT_ORDER_ID ' ||
         'AND b.total_amount <> 0 ' ||
       'ORDER BY ACCT_MONTH, PRODUCT_ORDER_ID';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

call LOG_PROCEDURES('12.含网内流量订购的比例计算，华为结算部分全站加速', v_proc_name);

-- 华为结算部分全站加速

set @vSql := 'INSERT INTO stlusers.STL_CDN_HW_RULE ' ||
                 'SELECT NULL,a.ACCT_MONTH, ' ||
                 'a.EC_CODE, ' ||
                 'a.ORDER_MODE, ' ||
                 'a.OFFER_CODE, ' ||
                 'a.PRODUCT_CODE, ' ||
                 'a.OFFER_ORDER_ID, ' ||
                 'a.PRODUCT_ORDER_ID, ' ||
                 'a.SETT_PROV, ' ||
                 'a.prov_amount, ' ||
                 'b.total_amount, ' ||
                 'round(a.prov_amount / b.total_amount * 100, 12) ratio, ' ||
                 '98 DEST_SOURCE, ' ||
                 ''''' ' ||
                 'FROM (SELECT ''' || inMonth || ''' ACCT_MONTH, ' ||
                 'EC_CODE, ' ||
                 'ORDER_MODE, ' ||
                 'OFFER_CODE, ' ||
                 'PRODUCT_CODE, ' ||
                 'OFFER_ORDER_ID, ' ||
                 'PRODUCT_ORDER_ID, ' ||
                 'SETT_PROV, ' ||
                 'sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0) + nvl(UP_FLOW,0)) prov_amount ' ||
                 'FROM (select * from stludr.ur_cdnappend_' || inMonth || '_t ' ||
                 'union all ' ||
                 'select * from stludr.ur_cdnappend_' || ivNextMonth || '_t) ' ||
                 'where FLOW_TYPE = ''1'' and service_type in(''1'',''2'') and DISTRIBUTION_PLANE = ''1'' ' ||
                 ' and PRODUCT_CODE !=''****************'' ' ||
                 ' and substr(dup_time, 1, 6) = ''' || inMonth || ''' ' ||
                 'GROUP BY EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID, SETT_PROV) a, ' ||
                 '(SELECT ''' || inMonth || ''' ACCT_MONTH, ' ||
                 'EC_CODE, ' ||
                 'ORDER_MODE, ' ||
                 'OFFER_CODE, ' ||
                 'PRODUCT_CODE, ' ||
                 'OFFER_ORDER_ID, ' ||
                 'PRODUCT_ORDER_ID, ' ||
                 'sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0) + nvl(UP_FLOW,0)) total_amount ' ||
                 'FROM (select * from stludr.ur_cdnappend_' || inMonth || '_t ' ||
                 'union all ' ||
                 'select * from stludr.ur_cdnappend_' || ivNextMonth || '_t) ' ||
                 'where FLOW_TYPE = ''1'' and service_type in(''1'',''2'') and DISTRIBUTION_PLANE = ''1'' ' ||
                 ' and PRODUCT_CODE !=''****************'' ' ||
                 ' and substr(dup_time, 1, 6) = ''' || inMonth || ''' ' ||
                 'GROUP BY EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID) b ' ||
                 'WHERE a.ACCT_MONTH = b.ACCT_MONTH ' ||
                 'AND a.ORDER_MODE = b.ORDER_MODE ' ||
                 'AND a.OFFER_ORDER_ID = b.OFFER_ORDER_ID ' ||
                 'AND a.PRODUCT_ORDER_ID = b.PRODUCT_ORDER_ID ' ||
                 'AND b.total_amount <> 0 ' ||
                 'ORDER BY ACCT_MONTH, PRODUCT_ORDER_ID';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

select concat('7_', now());

/*CREATE TABLE repart_parameter_tmp_3 (
  ORDER_MODE decimal(2,0) , EC_CODE varchar(32) , OFFER_CODE varchar(64) , PRODUCT_CODE varchar(64) ,
  OFFER_ORDER_ID decimal(15,0) , PRODUCT_ORDER_ID decimal(15,0) ) comment 'shard=shard2';
*/
--repart_parameter_tmp_5 此临时表废弃
----仅含网外流量订购的比例计算
--省间结算部分
call LOG_PROCEDURES('13.仅含网外流量订购的比例计算', v_proc_name);

truncate table repart_parameter_tmp_3;
--truncate table repart_parameter_tmp_5;


set @vSql := 'insert into repart_parameter_tmp_3 ' ||
    '(order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id)' ||
    '(select distinct order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||
      'from repart_parameter_tmp where rate_back_id = ''2'' and PRODUCT_CODE !=''****************'' ) ' ||
     'minus ' ||
    '(select distinct order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||
      'from repart_parameter_tmp where rate_back_id = ''1'' and PRODUCT_CODE !=''****************'' ) ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


set @vSql := 'insert into stl_cdn_rule ' ||chr(10)||
            'select NULL,''' || inMonth || ''', a.ec_code, a.order_mode, a.offer_code, a.product_code, a.offer_order_id, a.product_order_id, b.prov_cd sett_prov, ' ||chr(10)||
                 'b.prov_quantity prov_amount, b.total_quantity total_amount, '||chr(10)||
                /*'case when (ssbc.istop55 = ''0'' or ssbc.istop55 is null) and a.order_mode = ''3'' then round(b.rate * 0.8, 12) else b.rate end ratio, */
			 'b.rate,0, ssbc.istop55 ' ||chr(10)||
             'from repart_parameter_tmp_3 a ' ||chr(10)||
             'cross join (select prov_cd, prov_quantity, total_quantity, rate from stl_national_rate where acct_month = ''' || inMonth || ''' and biz_type = ''CDN'' and SERVICE_TYPE = ''jcyw'') b'||chr(10)||
             'left join stl_serv_biz_code ssbc on a.product_order_id=ssbc.order_id and ''' || inMonth || ''' between to_char(ssbc.effective_date, ''yyyymm'') and to_char(ssbc.expiry_date, ''yyyymm'')';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
-- 省间结算部分超低时延
set @vSql := 'insert into stl_cdn_rule '||chr(10)||
      'select NULL,''' || inMonth || ''', a.ec_code, a.order_mode, a.offer_code, a.product_code, a.offer_order_id, a.product_order_id, b.prov_cd sett_prov, ' ||chr(10)||
             'b.prov_quantity prov_amount, b.total_quantity total_amount, b.rate ratio, ''0'', '''' '||chr(10)||
        'from (select distinct order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id '||chr(10)||
                'from stludr.ur_cdnappend_' || inMonth || '_t ' ||chr(10)||
               'where FLOW_TYPE = ''2'' and service_type = ''3'' and PRODUCT_CODE !=''****************'' and (DOWN_FLOW <> 0 or CONTENT_FLOW <> 0 )' ||chr(10)||
                  'minus ' ||chr(10)||
              'select distinct order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id '||chr(10)||
                'from stludr.ur_cdnappend_' || inMonth || '_t ' ||chr(10)||
               'where FLOW_TYPE in(''1'',''3'') and service_type = ''3'' and PRODUCT_CODE !=''****************'' and (DOWN_FLOW <> 0 or CONTENT_FLOW <> 0 )) a ' ||chr(10)||
               ' cross join ' ||chr(10)||
             '(select prov_cd, prov_quantity, total_quantity, rate ' ||chr(10)||
                'from stl_national_rate ' ||chr(10)||
               'where acct_month = ''' || inMonth || ''' ' ||chr(10)||
                 'and biz_type = ''CDN'' and SERVICE_TYPE = ''cdsy'') b';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

-- 省间结算部分全站加速
set @vSql := 'insert into stl_cdn_rule '||chr(10)||
                 'select NULL,''' || inMonth ||
                 ''', a.ec_code, a.order_mode, a.offer_code, a.product_code, a.offer_order_id, a.product_order_id, b.prov_cd sett_prov, '||chr(10)||
                 'b.prov_quantity prov_amount, b.total_quantity total_amount, b.rate ratio, ''0'', '''' '||chr(10)||
                 'from (select distinct order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||chr(10)||
                 'from stludr.ur_cdnappend_' || inMonth || '_t ' ||
                 'where FLOW_TYPE = ''2'' and service_type in(''1'',''2'') and (DOWN_FLOW <> 0 or CONTENT_FLOW <> 0  or UP_FLOW<> 0) '||chr(10)||
                 ' minus ' ||
                 'select distinct order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||chr(10)||
                 'from stludr.ur_cdnappend_' || inMonth || '_t  ' ||chr(10)||
                 ' where FLOW_TYPE in(''1'',''3'') and service_type in(''1'',''2'') and PRODUCT_CODE !=''****************'' and (DOWN_FLOW <> 0 or CONTENT_FLOW <> 0 or UP_FLOW<> 0)) a ' ||chr(10)||
                 ' cross join ' ||chr(10)||
                 '(select prov_cd, prov_quantity, total_quantity, rate ' ||chr(10)||
                 'from stl_national_rate ' ||chr(10)||
                 'where acct_month = ''' || inMonth || ''' ' ||chr(10)||
                 'and biz_type = ''CDN'' and SERVICE_TYPE = ''qzjs'') b';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
select concat('8_', now());

truncate table repart_parameter_tmp_3;


set @vSql := 'insert into repart_parameter_tmp_3 ' ||
              '(order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id)' ||
              '(select distinct order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||
                'from repart_parameter_tmp where rate_back_id = ''2'' and PRODUCT_CODE !=''****************'' and sub_group_num = ''1'' and dup_time = ''' || inMonth || ''' )'||
               'minus ' ||
              '(select distinct order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||
                'from repart_parameter_tmp where rate_back_id = ''1'' and PRODUCT_CODE !=''****************'' and sub_group_num = ''1'' and dup_time = ''' || inMonth || ''') ';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := 'insert into stl_cdn_hw_rule ' ||chr(10)||
            'select NULL,''' || inMonth || ''', a.ec_code, a.order_mode, a.offer_code, a.product_code, a.offer_order_id, a.product_order_id, b.prov_cd sett_prov, ' ||chr(10)||
                 'b.prov_quantity prov_amount, b.total_quantity total_amount, '||chr(10)||
                 /*'case when (ssbc.istop55 = ''0'' or ssbc.istop55 is null) and a.order_mode = ''3'' then round(b.rate * 0.8, 12) else b.rate end ratio ,*/
 				 'b.rate,''98'', ssbc.istop55 ' ||chr(10)||
            'from repart_parameter_tmp_3 a ' ||chr(10)||
            'cross join (select prov_cd, prov_quantity, total_quantity, rate from stl_national_rate where acct_month = ''' || inMonth || ''' and biz_type = ''CDN-HW'' and SERVICE_TYPE = ''jcyw'') b'||chr(10)||
            'left join stl_serv_biz_code ssbc on a.product_order_id=ssbc.order_id and ''' || inMonth || ''' between to_char(ssbc.effective_date, ''yyyymm'') and to_char(ssbc.expiry_date, ''yyyymm'')';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

--  华为结算部分超低时延
set @vSql :=  'insert into stl_cdn_hw_rule ' ||
      'select NULL,''' || inMonth || ''', a.ec_code, a.order_mode, a.offer_code, a.product_code, a.offer_order_id, a.product_order_id, b.prov_cd sett_prov, ' ||
             'b.prov_quantity prov_amount, b.total_quantity total_amount, b.rate ratio, ''98'', '''' ' ||
        'from (select distinct order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||
                'from (select * from stludr.ur_cdnappend_' || inMonth || '_t ' ||
                       'union all ' ||
                      'select * from stludr.ur_cdnappend_' || ivNextMonth || '_t) ' ||
               'where FLOW_TYPE = ''2'' and service_type = ''3'' and PRODUCT_CODE !=''****************'' and DISTRIBUTION_PLANE = ''1'' and (DOWN_FLOW <> 0 or CONTENT_FLOW <> 0) ' ||
                 'and substr(dup_time, 1, 6) = ''' || inMonth || '''' ||
                  ' minus ' ||
              'select distinct order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||
                'from (select * from stludr.ur_cdnappend_' || inMonth || '_t ' ||
                       'union all ' ||
                      'select * from stludr.ur_cdnappend_' || ivNextMonth || '_t) ' ||
               'where FLOW_TYPE in(''1'',''3'') and service_type = ''3'' and PRODUCT_CODE !=''****************'' and DISTRIBUTION_PLANE = ''1'' and (DOWN_FLOW <> 0 or CONTENT_FLOW <> 0) ' ||
                 'and substr(dup_time, 1, 6) = ''' || inMonth || ''') a ' ||
                 'cross join ' ||
             '(select prov_cd, prov_quantity, total_quantity, rate ' ||
                'from stl_national_rate ' ||
               'where acct_month = ''' || inMonth || ''' ' ||
                 'and biz_type = ''CDN-HW'' and SERVICE_TYPE = ''cdsy'' ) b';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

-- 华为结算部分全站加速
set @vSql :=  'insert into stl_cdn_hw_rule ' ||
                 'select NULL,''' || inMonth ||
                 ''', a.ec_code, a.order_mode, a.offer_code, a.product_code, a.offer_order_id, a.product_order_id, b.prov_cd sett_prov, ' ||
                 'b.prov_quantity prov_amount, b.total_quantity total_amount, b.rate ratio, ''98'', '''' ' ||
                 'from (select distinct order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||
                 'from (select * from stludr.ur_cdnappend_' || inMonth || '_t ' ||
                 'union all ' ||
                 'select * from stludr.ur_cdnappend_' || ivNextMonth || '_t) ' ||
                 'where FLOW_TYPE = ''2'' and PRODUCT_CODE !=''****************'' and service_type in(''1'',''2'') and DISTRIBUTION_PLANE = ''1'' and (DOWN_FLOW <> 0 or CONTENT_FLOW <> 0 or UP_FLOW<> 0) ' ||
                 'and substr(dup_time, 1, 6) = ''' || inMonth || '''' ||
                 ' minus ' ||
                 'select distinct order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||
                 'from (select * from stludr.ur_cdnappend_' || inMonth || '_t ' ||
                 'union all ' ||
                 'select * from stludr.ur_cdnappend_' || ivNextMonth || '_t) ' ||
                 'where FLOW_TYPE in( ''1'',''3'') and PRODUCT_CODE !=''****************'' and service_type in(''1'',''2'') and DISTRIBUTION_PLANE = ''1'' and (DOWN_FLOW <> 0 or CONTENT_FLOW <> 0 or UP_FLOW<> 0) ' ||
                 'and substr(dup_time, 1, 6) = ''' || inMonth || ''') a ' ||
                 'cross join ' ||
                 '(select prov_cd, prov_quantity, total_quantity, rate ' ||
                 'from stl_national_rate ' ||
                 'where acct_month = ''' || inMonth || ''' ' ||
                 'and biz_type = ''CDN-HW'' and SERVICE_TYPE = ''qzjs'') b';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

select concat('9_', now());

/*CREATE TABLE repart_parameter_tmp_4 (
 ORDER_MODE decimal(2,0), OFFER_CODE varchar(64), PRODUCT_CODE varchar(64), OFFER_ORDER_ID decimal(15,0), PRODUCT_ORDER_ID decimal(15,0)) comment 'shard=shard2';*/
call LOG_PROCEDURES('14.无流量订购的比例计算', v_proc_name);

----无流量订购的比例计算
--省间结算部分
truncate table repart_parameter_tmp_4;


set @vSql := 'insert into repart_parameter_tmp_4 ' ||
                '(order_mode, offer_code, product_code, offer_order_id, product_order_id) '||
                'select distinct order_mode, offer_code, product_code, offer_order_id, product_order_id from stlusers.repart_parameter_tmp '||
                ' where  product_code not in (''****************'',''****************'') and acct_month=''' || inMonth || '''';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := 'insert into stl_cdn_rule ' ||chr(10)||
            'select NULL,''' || inMonth || ''', '''', a.order_mode, a.offer_code, a.product_code, a.offer_order_id, a.product_order_id, b.prov_cd sett_prov, ' ||chr(10)||
                 'b.prov_quantity prov_amount, b.total_quantity total_amount, '||chr(10)||
                 /*'case when (ssbc.istop55 = ''0'' or ssbc.istop55 is null) and a.order_mode = ''3'' then round(b.rate * 0.8, 12) else b.rate end ratio,*/
                 'b.rate,0, ssbc.istop55 ' ||chr(10)||
            'from
            (((select distinct ordermode order_mode, pospecnumber offer_code, sospecnumber product_code, ' ||chr(10)||
                         'poid offer_order_id, soid product_order_id,to_char(accountid)    ACCOUNTID ' ||chr(10)||
                    'from stludr.sync_interface_bl_' || inMonth || ' ' ||chr(10)||
                   'where pospecnumber = ''50004'' and SOSPECNUMBER not in (''****************'',''****************'') ' ||chr(10)||
                   'union all ' ||chr(10)||
                  'select distinct decode(a.prov_code, ''000'', ''1'', ''3'') order_mode, a.product_code offer_code, ' ||chr(10)||
                         'a.service_code product_code, a.prod_order_id offer_order_id, a.order_id product_order_id, ''1'' ACCOUNTID ' ||chr(10)||
                    'from stludr.cust_prod_info a ' ||chr(10)||
                   'where a.product_code = ''9200397'' ' ||chr(10)||
                     'and a.service_code = ''9200397'' ' ||chr(10)||
                     'and a.acct_month = ''' || inMonth || ''') ' ||chr(10)||
                   'minus ' ||chr(10)||
                  'select distinct to_char(u.order_mode), offer_code, product_code, to_char(offer_order_id), to_char(product_order_id),to_char(s.ACCOUNTID) ' ||chr(10)||
                    'from repart_parameter_tmp_4 u,stludr.sync_interface_bl_' || inMonth || ' s  where  u.PRODUCT_ORDER_ID =s.SOID ' ||' ) ' ||chr(10)||
                     '  minus ' ||
               ' select distinct to_char(u.order_mode), offer_code, u.product_code, to_char(offer_order_id), to_char(product_order_id),''1'' '||chr(10)||
               '  from stludr.ur_cdn_' || inMonth || '_t u,stludr.cust_prod_info s ' ||chr(10)||
               ' where u.PRODUCT_ORDER_ID =s.prod_order_id and s.acct_month = '|| inMonth || ' and (accu_volume <> 0 or accu_duration <> 0) ' ||chr(10)||
                     'minus ' ||
              'select distinct to_char(order_mode), offer_code, product_code, to_char(offer_order_id), to_char(product_order_id),to_char(accountid) ACCOUNTID' ||chr(10)||
                'from stludr.ur_cdnappend_' || inMonth || '_t u,stludr.sync_interface_bl_' || inMonth || ' s ' ||chr(10)||
               ' where u.PRODUCT_ORDER_ID =s.SOID and (DOWN_FLOW <> 0  or CONTENT_FLOW <> 0 or UP_FLOW <> 0 )) a ' ||chr(10)||
                     'inner join ' ||chr(10)||
                 '(select prov_cd, prov_quantity, total_quantity, rate,SERVICE_TYPE
      from stlusers.stl_national_rate
      where acct_month = ''' || inMonth || ''' and biz_type = ''CDN''  ) b  ON  b.SERVICE_TYPE  = (case a.ACCOUNTID when ''1'' THEN ''jcyw''
                                 WHEN ''5'' THEN ( CASE a.product_code
                                                   when ''****************'' then ''qzjs''
                                                   ELSE  ''cdsy'' END)
                                 ELSE ''jcyw''
                                 END)  '||chr(10)||
                 'left join stl_serv_biz_code ssbc on a.product_order_id=ssbc.order_id and ''' || inMonth || ''' between to_char(ssbc.effective_date, ''yyyymm'') and to_char(ssbc.expiry_date, ''yyyymm'')';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

select concat('10_', now());

truncate table repart_parameter_tmp_4;


--华为结算部分
set @vSql := 'insert into repart_parameter_tmp_4 ' ||chr(10)||
        '(order_mode, offer_code, product_code, offer_order_id, product_order_id) '||
        ' select distinct order_mode, offer_code, product_code, offer_order_id, product_order_id from stlusers.repart_parameter_tmp '||
        ' where product_code not in (''****************'',''****************'') and sub_group_num = ''1'' and dup_time = ''' || inMonth ||'''';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := 'insert into stl_cdn_hw_rule ' ||chr(10)||
            'select NULL,''' || inMonth || ''', '''', a.order_mode, a.offer_code, a.product_code, a.offer_order_id, a.product_order_id, b.prov_cd sett_prov, ' ||chr(10)||
                 'b.prov_quantity prov_amount, b.total_quantity total_amount, '||chr(10)||
                /*'case when (ssbc.istop55 = ''0'' or ssbc.istop55 is null) and a.order_mode = ''3'' then round(b.rate * 0.8, 12) else b.rate end ratio,*/
 				'b.rate,''98'', ssbc.istop55 ' ||chr(10)||
            'from (((select distinct ordermode order_mode, pospecnumber offer_code, sospecnumber product_code, ' ||chr(10)||
                         'poid offer_order_id, soid product_order_id ,to_char(accountid)    ACCOUNTID' ||chr(10)||
                    'from stludr.sync_interface_bl_' || inMonth || ' ' ||chr(10)||
                   'where pospecnumber = ''50004'' and SOSPECNUMBER not in (''****************'',''****************'') ' ||chr(10)||
                   'union all ' ||chr(10)||
                  'select distinct decode(a.prov_code, ''000'', ''1'', ''3'') order_mode, a.product_code offer_code, ' ||chr(10)||
                         'a.service_code product_code, a.prod_order_id offer_order_id, a.order_id product_order_id ,''1'' ACCOUNTID ' ||chr(10)||
                    'from stludr.cust_prod_info a ' ||chr(10)||
                   'where a.product_code = ''9200397'' ' ||chr(10)||
                     'and a.service_code = ''9200397'' ' ||chr(10)||
                     'and a.acct_month = ''' || inMonth || ''') ' ||chr(10)||
                   'minus ' ||chr(10)||
                  'select distinct to_char(u.order_mode), offer_code, product_code, to_char(offer_order_id), to_char(product_order_id),to_char(accountid)    ACCOUNTID  ' ||chr(10)||
                    'from repart_parameter_tmp_4 u,stludr.sync_interface_bl_' || inMonth || ' s where u.PRODUCT_ORDER_ID =s.SOID ) ' ||chr(10)||
                    '  minus ' ||chr(10)||
               ' select distinct to_char(u.order_mode), offer_code, u.product_code, to_char(offer_order_id), to_char(product_order_id),''1'' '||chr(10)||
               '  from stludr.ur_cdn_' || inMonth || '_t u,stludr.cust_prod_info s ' ||chr(10)||
                 ' where u.PRODUCT_ORDER_ID =s.prod_order_id and s.acct_month = '|| inMonth || ' and (accu_volume <> 0 or accu_duration <> 0) ' ||chr(10)||
                    'minus '  ||chr(10)||
              'select distinct to_char(order_mode), offer_code, product_code, to_char(offer_order_id), to_char(product_order_id),to_char(accountid)    ACCOUNTID   ' ||chr(10)||
                'from (select * from stludr.ur_cdnappend_' || inMonth || '_t u,stludr.sync_interface_bl_' || inMonth || ' s  ' ||chr(10)||
               'where u.PRODUCT_ORDER_ID =s.SOID '  ||chr(10)||
                       'union all  ' ||chr(10)||
                      'select * from stludr.ur_cdnappend_' || ivNextMonth || '_t u,stludr.sync_interface_bl_' || inMonth || ' s   ' ||chr(10)||
               'where u.PRODUCT_ORDER_ID =s.SOID '|| ')  ' ||chr(10)||
               'where (DOWN_FLOW <> 0 or CONTENT_FLOW <> 0 or UP_FLOW <> 0) and DISTRIBUTION_PLANE = ''1'' ' ||
                 'and substr(dup_time, 1, 6) = ''' || inMonth || ''') a ' ||
                     'inner join ' ||chr(10)||
                 '(select prov_cd, prov_quantity, total_quantity, rate,SERVICE_TYPE '||
     ' from stlusers.stl_national_rate '||
     ' where acct_month = ''' || inMonth || ''' and biz_type = ''CDN-HW'' ) b  ON  b.SERVICE_TYPE  = (case a.ACCOUNTID when ''1'' THEN ''jcyw'' '||
                                ' WHEN ''5'' THEN ( CASE a.product_code
                                                   when ''****************'' then ''qzjs''
                                                   ELSE  ''cdsy'' END)
                                 ELSE ''jcyw''
                                 END) '||chr(10)||
                 ' left join stl_serv_biz_code ssbc on a.product_order_id=ssbc.order_id and ''' || inMonth || ''' between to_char(ssbc.effective_date, ''yyyymm'') and to_char(ssbc.expiry_date, ''yyyymm'')';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

select concat('11_', now());

call LOG_PROCEDURES('15.stl_cdn_rule和stl_cdn_hw_rule表更新istop55字段', v_proc_name);

----stl_cdn_rule和stl_cdn_hw_rule表更新istop55字段

update STL_CDN_RULE a
set istop55 = (select istop55
               from stl_serv_biz_code b
               where a.product_order_id = b.order_id
                 and inMonth between to_char(b.effective_date, 'yyyymm') and to_char(b.expiry_date, 'yyyymm'))
where a.acct_month = inMonth;
commit;

update STL_CDN_HW_RULE a
set istop55 = (select istop55
               from stl_serv_biz_code b
               where a.product_order_id = b.order_id
                 and inMonth between to_char(b.effective_date, 'yyyymm') and to_char(b.expiry_date, 'yyyymm'))
where a.acct_month = inMonth;
commit;
select concat('12_', now());


----更新受理模式3 top55订购的结算比例（*80%）
update STL_CDN_RULE
set ratio = round(ratio * 0.8, 12)
where order_mode = '3'
  and (istop55 = '0' or istop55 is null)
  and acct_month = inMonth;
commit;

update STL_CDN_HW_RULE
set ratio = round(ratio * 0.8, 12)
where order_mode = '3'
  and (istop55 = '0' or istop55 is null)
  and acct_month = inMonth;
commit;
select concat('13_', now());


----插入20%部分
insert into STL_CDN_RULE
select distinct NULL,inMonth, ec_code, order_mode, offer_code, product_code, offer_order_id, product_order_id, '${EC_PROV_CODE_07}', NULL, NULL, '20', '98', '0'
from STL_CDN_RULE
where order_mode = '3' and (istop55 = '0' or istop55 is null)
  and acct_month = inMonth;

COMMIT;

insert into STL_CDN_HW_RULE
select distinct null, inMonth, ec_code, order_mode, offer_code, product_code, offer_order_id, product_order_id, '${EC_PROV_CODE_07}', NULL, NULL, '20', '98', '0'
from STL_CDN_HW_RULE
where order_mode = '3' and (istop55 = '0' or istop55 is null)
  and acct_month = inMonth;

COMMIT;
select concat('14_', now());

--CDN 1000W 导入数据到  "STLUSERS"."STL_CDN_RULE_1000"

call LOG_PROCEDURES('16.CDN 1000W逻辑计算', v_proc_name);

set @vSql := 'INSERT INTO STL_CDN_RULE_1000  '  ||
                   'SELECT null,k.* FROM STLUSERS.STL_CDN_RULE_1000 w RIGHT JOIN ( '||
                        'SELECT '  ||
                        '      a.ordermode order_mode,    '  ||
                        '      a.ORGMONTH, '  ||
                        '      a.pospecnumber offer_code, '  ||
                        '      a.sospecnumber product_code, '  ||
                        '      a.poid offer_order_id, '  ||
                        '      a.soid product_order_id, '  ||
                        '      a.NOTAXFEE/100 NOTAXFEE, '  ||
                        '      b.EFFECTIVE_DATE, '  ||
                        '      b.EXPIRY_DATE, '  ||
                        '      b.ISTOP55 '  ||
                        '  FROM '  ||
                        '      STLUSERS.STL_SERV_BIZ_CODE b  '  ||
                        '  JOIN '  ||
                        '      stludr.sync_interface_bl_' ||inMonth|| ' a  '  ||
                        '  ON  '  ||
                        '      a.ORDERMODE = b.PROD_ORDER_MODE  '  ||
                        '  AND  '  ||
                        '      a.POID = b.PROD_ORDER_ID  '  ||
                        '  AND  '  ||
                        '      a.SOID = b.ORDER_ID  '  ||
                        '  WHERE '  ||
                        '      a.pospecnumber = ''50004''  '  ||
                        ' AND a.SOSPECNUMBER not in (''****************'',''****************'' )'  ||
                        '      AND a.ordermode = ''3''  '  ||
                        '      AND b.ISTOP55 = ''1''  '  ||
                   ' ) k  ' ||
                   ' ON w.order_mode = k.order_mode ' ||
                   ' AND w.ORGMONTH = k.ORGMONTH  ' ||
                   ' AND w.offer_code = k.offer_code  ' ||
                   ' AND w.product_code = k.product_code  ' ||
                   ' AND w.offer_order_id = k.offer_order_id ' ||
                   ' AND w.product_order_id = k.product_order_id ' ||
                   ' AND w.NOTAXFEE = k.NOTAXFEE ' ||
                   ' AND w.EFFECTIVE_DATE = k.EFFECTIVE_DATE ' ||
                   ' AND w.EXPIRY_DATE = k.EXPIRY_DATE ' ||
                   ' AND w.ISTOP55 = k.ISTOP55 ' ||
                   ' WHERE  ' ||
                   '  w.order_mode IS NULL  ' ||
                   '  AND w.ORGMONTH IS NULL  ' ||
                   '  AND w.offer_code IS NULL  ' ||
                   '  AND w.product_code IS NULL  ' ||
                   '  AND w.offer_order_id IS NULL  ' ||
                   '  AND w.product_order_id IS NULL  ' ||
                   '  AND w.NOTAXFEE IS NULL  ' ||
                   '  AND w.EFFECTIVE_DATE IS NULL  ' ||
                   '  AND w.EXPIRY_DATE IS NULL  ' ||
                   '  AND w.ISTOP55 IS NULL  ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;



--cdn_upper_limit NUMBER(20) := 20000000000;


--受理模式3   top55订购1000w以内的结算比例（*95%） 非华为   31省
MERGE INTO STL_CDN_RULE A
    USING(
        SELECT
            X.OFFER_ORDER_ID,
            X.PRODUCT_ORDER_ID
        FROM
            STL_CDN_RULE_1000 X
                JOIN CDN_SPECIAL_SETT_CONFIG Y ON X.PRODUCT_ORDER_ID=Y.PRODUCT_ORDER_ID
                JOIN
            (
                SELECT
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID,
                    MAX( ORGMONTH ) END_DATE,
                    CASE
                        WHEN MAX( EFFECTIVE_DATE ) = LAST_DAY(MAX( EFFECTIVE_DATE )) THEN
                            TRUNC(
                                    ADD_MONTHS(  MAX( EFFECTIVE_DATE ),
                                                 12 * TRUNC( ( MONTHS_BETWEEN(
                                                         TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))
                                                                 ) / 12, 0  ) + 1
                                    )
                                , 'MONTH')
                        ELSE
                            ADD_MONTHS(
                                    MAX( EFFECTIVE_DATE ),
                                    12 * TRUNC((MONTHS_BETWEEN( TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))  )/12,0)
                            )
                        END as START_DATE
                FROM
                    STL_CDN_RULE_1000
                GROUP BY
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID
            ) T
            ON  X.OFFER_ORDER_ID = T.OFFER_ORDER_ID
                AND X.PRODUCT_ORDER_ID = T.PRODUCT_ORDER_ID
                AND X.PRODUCT_CODE not in ('****************','****************' )
        WHERE
            X.ORGMONTH >= to_char(T.START_DATE, 'yyyymm' )
          AND X.ORGMONTH <= T.END_DATE
        GROUP BY
            X.OFFER_ORDER_ID,X.PRODUCT_ORDER_ID
        HAVING SUM(NOTAXFEE) <= 20000000000
    ) W
    ON (A.OFFER_ORDER_ID = W.OFFER_ORDER_ID AND A.PRODUCT_ORDER_ID = W.PRODUCT_ORDER_ID)
    WHEN MATCHED THEN
        UPDATE SET A.RATIO = ROUND(A.RATIO * 0.95, 12)
    WHERE A.ORDER_MODE = '3'
            AND A.ISTOP55 = '1'
            AND A.ACCT_MONTH = INMONTH;


--受理模式3 top55订购1000w以内的结算比例（*95%） 华为  31省
MERGE INTO STL_CDN_HW_RULE A
    USING(
        WITH HW_1000 AS (
            SELECT
                b.ORDER_MODE,
                b.ACCT_MONTH ORGMONTH,
                b.OFFER_CODE,
                b.PRODUCT_CODE,
                b.OFFER_ORDER_ID,
                b.PRODUCT_ORDER_ID,
                (b.CMCC_FEE + b.OTH_FEE) as NOTAXFEE,
                a.EFFECTIVE_DATE,
                a.ISTOP55
            FROM
                STLUSERS.STL_SERV_BIZ_CODE a
                    JOIN stludr.STL_CDN_ORDER_FEE b
                         ON a.PROD_ORDER_MODE = b.ORDER_MODE
                             AND a.PROD_ORDER_ID = b.OFFER_ORDER_ID
                             AND a.ORDER_ID = b.PRODUCT_ORDER_ID
            WHERE
                b.OFFER_CODE = '50004'
              AND b.product_code not in('****************','****************')
              AND b.ORDER_MODE = '3'
              AND a.ISTOP55 = '1'
        )
        SELECT
            X.OFFER_ORDER_ID,
            X.PRODUCT_ORDER_ID
        FROM
            HW_1000 X
                JOIN CDN_SPECIAL_SETT_CONFIG Y ON X.PRODUCT_ORDER_ID=Y.PRODUCT_ORDER_ID
                JOIN
            (
                SELECT
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID,
                    MAX( ORGMONTH ) END_DATE,
                    CASE
                        WHEN MAX( EFFECTIVE_DATE ) = LAST_DAY(MAX( EFFECTIVE_DATE )) THEN
                            TRUNC(
                                    ADD_MONTHS(  MAX( EFFECTIVE_DATE ),
                                                 12 * TRUNC( ( MONTHS_BETWEEN(
                                                         TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))
                                                                 ) / 12, 0  ) + 1
                                    )
                                , 'MONTH')
                        ELSE
                            ADD_MONTHS(
                                    MAX( EFFECTIVE_DATE ),
                                    12 * TRUNC((MONTHS_BETWEEN( TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE ))) )/12,0)
                            )
                        END as START_DATE
                FROM
                    HW_1000
                GROUP BY
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID
            ) T
            ON  X.OFFER_ORDER_ID = T.OFFER_ORDER_ID
                AND X.PRODUCT_ORDER_ID = T.PRODUCT_ORDER_ID
        WHERE
            X.ORGMONTH >= to_char(T.START_DATE, 'yyyymm' )
          AND X.ORGMONTH <= T.END_DATE
        GROUP BY
            X.OFFER_ORDER_ID,X.PRODUCT_ORDER_ID
        HAVING SUM(NOTAXFEE) <= 20000000000
    ) W
    ON (A.OFFER_ORDER_ID = W.OFFER_ORDER_ID AND A.PRODUCT_ORDER_ID = W.PRODUCT_ORDER_ID)
    WHEN MATCHED THEN
        UPDATE SET A.RATIO = ROUND(A.RATIO * 0.95, 12)
    WHERE A.ORDER_MODE = '3'
            AND A.ISTOP55 = '1'
            AND A.ACCT_MONTH = INMONTH;


--受理模式3 top55订购1000w以内的结算比例（*5%） 非华为  签约省  国产decimal不能插'' oracle是''; 改为0
INSERT INTO STL_CDN_RULE
SELECT DISTINCT
    NULL,
    inMonth,
    x.ec_code,
    x.order_mode,
    x.offer_code,
    x.product_code,
    x.offer_order_id,
    x.product_order_id,
    '${EC_PROV_CODE_07}',
    0,
    0,
    '5',
    '98',
    '1'
FROM
    STL_CDN_RULE x join
    (
        SELECT
            X.OFFER_ORDER_ID,
            X.PRODUCT_ORDER_ID
        FROM
            STL_CDN_RULE_1000 X
                JOIN CDN_SPECIAL_SETT_CONFIG Y ON X.PRODUCT_ORDER_ID=Y.PRODUCT_ORDER_ID
                JOIN
            (
                SELECT
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID,
                    MAX( ORGMONTH ) END_DATE,
                    CASE
                        WHEN MAX( EFFECTIVE_DATE ) = LAST_DAY(MAX( EFFECTIVE_DATE )) THEN
                            TRUNC(
                                    ADD_MONTHS(  MAX( EFFECTIVE_DATE ),
                                                 12 * TRUNC( ( MONTHS_BETWEEN(
                                                         TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))
                                                                 ) / 12, 0  ) + 1
                                    )
                                , 'MONTH')
                        ELSE
                            ADD_MONTHS(
                                    MAX( EFFECTIVE_DATE ),
                                    12 * TRUNC((MONTHS_BETWEEN( TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE ))) )/12,0)
                            )
                        END as START_DATE
                FROM
                    STL_CDN_RULE_1000
                GROUP BY
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID
            ) T
            ON  X.OFFER_ORDER_ID = T.OFFER_ORDER_ID
                AND X.PRODUCT_ORDER_ID = T.PRODUCT_ORDER_ID
                AND X.PRODUCT_CODE not in ('****************','****************' )
        WHERE
            X.ORGMONTH >= to_char(T.START_DATE, 'yyyymm' )
          AND X.ORGMONTH <= T.END_DATE
        GROUP BY
            X.OFFER_ORDER_ID,X.PRODUCT_ORDER_ID
        HAVING SUM(NOTAXFEE) <= 20000000000
    ) t
    ON  x.OFFER_ORDER_ID = t.OFFER_ORDER_ID AND x.PRODUCT_ORDER_ID = t.PRODUCT_ORDER_ID
WHERE
    x.order_mode = '3'
  AND x.istop55 = '1'
  AND x.acct_month = inMonth;


--受理模式3 top55订购1000w以内的结算比例（*5%） 华为  签约省    国产decimal不能插'' oracle是''; 改为0
INSERT INTO STL_CDN_HW_RULE
SELECT DISTINCT
    null,
    inMonth,
    x.ec_code,
    x.order_mode,
    x.offer_code,
    x.product_code,
    x.offer_order_id,
    x.product_order_id,
    '${EC_PROV_CODE_07}',
    0,
    0,
    '5',
    '98',
    '1'
FROM
    STL_CDN_HW_RULE x join
    (

        WITH HW_1000 AS (
            SELECT
                b.ORDER_MODE,
                b.ACCT_MONTH ORGMONTH,
                b.OFFER_CODE,
                b.PRODUCT_CODE,
                b.OFFER_ORDER_ID,
                b.PRODUCT_ORDER_ID,
                (b.CMCC_FEE + b.OTH_FEE) as NOTAXFEE,
                a.EFFECTIVE_DATE,
                a.ISTOP55
            FROM
                STLUSERS.STL_SERV_BIZ_CODE a
                    JOIN stludr.STL_CDN_ORDER_FEE b
                         ON a.PROD_ORDER_MODE = b.ORDER_MODE
                             AND a.PROD_ORDER_ID = b.OFFER_ORDER_ID
                             AND a.ORDER_ID = b.PRODUCT_ORDER_ID
            WHERE
                b.OFFER_CODE = '50004'
              AND b.product_code not in('****************','****************')
              AND b.ORDER_MODE = '3'
              AND a.ISTOP55 = '1'
        )
        SELECT
            X.OFFER_ORDER_ID,
            X.PRODUCT_ORDER_ID
        FROM
            HW_1000 X
                JOIN CDN_SPECIAL_SETT_CONFIG Y ON X.PRODUCT_ORDER_ID=Y.PRODUCT_ORDER_ID
                JOIN
            (
                SELECT
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID,
                    MAX( ORGMONTH ) END_DATE,
                    CASE
                        WHEN MAX( EFFECTIVE_DATE ) = LAST_DAY(MAX( EFFECTIVE_DATE )) THEN
                            TRUNC(
                                    ADD_MONTHS(  MAX( EFFECTIVE_DATE ),
                                                 12 * TRUNC( ( MONTHS_BETWEEN(
                                                         TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))
                                                                 ) / 12, 0  ) + 1
                                    )
                                , 'MONTH')
                        ELSE
                            ADD_MONTHS(
                                    MAX( EFFECTIVE_DATE ),
                                    12 * TRUNC((MONTHS_BETWEEN( TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))  )/12,0)
                            )
                        END as START_DATE
                FROM
                    HW_1000
                GROUP BY
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID
            ) T
            ON  X.OFFER_ORDER_ID = T.OFFER_ORDER_ID
                AND X.PRODUCT_ORDER_ID = T.PRODUCT_ORDER_ID
        WHERE
            X.ORGMONTH >= to_char(T.START_DATE, 'yyyymm' )
          AND X.ORGMONTH <= T.END_DATE
        GROUP BY
            X.OFFER_ORDER_ID,X.PRODUCT_ORDER_ID
        HAVING SUM(NOTAXFEE) <= 20000000000
    ) t
    ON  x.OFFER_ORDER_ID = t.OFFER_ORDER_ID AND x.PRODUCT_ORDER_ID = t.PRODUCT_ORDER_ID
WHERE
    x.order_mode = '3'
  AND x.istop55 = '1'
  AND x.acct_month = inMonth;




--cdn 1000w金额分割
--受理模式3 top55订购超过1000w的结算比例 非华为  31省
MERGE INTO STL_CDN_RULE A
    USING(
        SELECT
            X.OFFER_ORDER_ID,
            X.PRODUCT_ORDER_ID,
            -- 1-(((SUM(X.NOTAXFEE)/20000000000) - 1) * 0.05) as PROVINCE31_RATIO
            CASE
                WHEN (SUM(X.NOTAXFEE)-20000000000) < SUM(CASE WHEN X.ORGMONTH = inMonth THEN X.NOTAXFEE ELSE 0 END) THEN
                    (
                        ((SUM(X.NOTAXFEE)-20000000000)/SUM(CASE WHEN X.ORGMONTH = inMonth THEN X.NOTAXFEE ELSE 1 END)) * 1
                        ) + (
                        (1 - (SUM(X.NOTAXFEE)-20000000000)/SUM(CASE WHEN X.ORGMONTH = inMonth THEN X.NOTAXFEE ELSE 1 END)) * 0.95
                        )
                --第一次超出
                ELSE
                    1
                --除第一次超出外的第n次超出
                END  AS PROVINCE31_RATIO
        FROM
            STL_CDN_RULE_1000 X
                JOIN CDN_SPECIAL_SETT_CONFIG Y ON X.PRODUCT_ORDER_ID=Y.PRODUCT_ORDER_ID
                JOIN
            (
                SELECT
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID,
                    MAX( ORGMONTH ) END_DATE,
                    CASE
                        WHEN MAX( EFFECTIVE_DATE ) = LAST_DAY(MAX( EFFECTIVE_DATE )) THEN
                            TRUNC(
                                    ADD_MONTHS(  MAX( EFFECTIVE_DATE ),
                                                 12 * TRUNC( ( MONTHS_BETWEEN(
                                                         TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))
                                                                 ) / 12, 0  ) + 1
                                    )
                                , 'MONTH')
                        ELSE
                            ADD_MONTHS(
                                    MAX( EFFECTIVE_DATE ),
                                    12 * TRUNC((MONTHS_BETWEEN( TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE ))) )/12,0)
                            )
                        END as START_DATE
                FROM
                    STL_CDN_RULE_1000
                GROUP BY
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID
            ) T
            ON  X.OFFER_ORDER_ID = T.OFFER_ORDER_ID
                AND X.PRODUCT_ORDER_ID = T.PRODUCT_ORDER_ID
                AND X.PRODUCT_CODE not in ('****************','****************' )
        WHERE
            X.ORGMONTH >= to_char(T.START_DATE, 'yyyymm' )
          AND X.ORGMONTH <= T.END_DATE
        GROUP BY
            X.OFFER_ORDER_ID,X.PRODUCT_ORDER_ID
        HAVING SUM(NOTAXFEE)  > 20000000000
    ) W
    ON (A.OFFER_ORDER_ID = W.OFFER_ORDER_ID AND A.PRODUCT_ORDER_ID = W.PRODUCT_ORDER_ID)
    WHEN MATCHED THEN
        UPDATE SET A.RATIO = ROUND(A.RATIO * W.PROVINCE31_RATIO, 12)
    WHERE A.ORDER_MODE = '3'
            AND A.ISTOP55 = '1'
            AND A.ACCT_MONTH = INMONTH;


--受理模式3 top55订购超过1000w的结算比例 华为  31省
MERGE INTO STL_CDN_HW_RULE A
    USING(
        WITH HW_1000 AS (
            SELECT
                b.ORDER_MODE,
                b.ACCT_MONTH ORGMONTH,
                b.OFFER_CODE,
                b.PRODUCT_CODE,
                b.OFFER_ORDER_ID,
                b.PRODUCT_ORDER_ID,
                (b.CMCC_FEE + b.OTH_FEE) as NOTAXFEE,
                a.EFFECTIVE_DATE,
                a.ISTOP55
            FROM
                STLUSERS.STL_SERV_BIZ_CODE a
                    JOIN stludr.STL_CDN_ORDER_FEE b
                         ON a.PROD_ORDER_MODE = b.ORDER_MODE
                             AND a.PROD_ORDER_ID = b.OFFER_ORDER_ID
                             AND a.ORDER_ID = b.PRODUCT_ORDER_ID
            WHERE
                b.OFFER_CODE = '50004'
              AND b.product_code not in('****************','****************')
              AND b.ORDER_MODE = '3'
              AND a.ISTOP55 = '1'
        )
        SELECT
            X.OFFER_ORDER_ID,
            X.PRODUCT_ORDER_ID,
            -- 1-(((SUM(X.NOTAXFEE)/20000000000) - 1) * 0.05) as PROVINCE31_RATIO
            CASE
                WHEN (SUM(X.NOTAXFEE)-20000000000) < SUM(CASE WHEN X.ORGMONTH = inMonth THEN X.NOTAXFEE ELSE 0 END) THEN
                    (
                        ((SUM(X.NOTAXFEE)-20000000000)/SUM(CASE WHEN X.ORGMONTH = inMonth THEN X.NOTAXFEE ELSE 1 END)) * 1
                        ) + (
                        (1 - (SUM(X.NOTAXFEE)-20000000000)/SUM(CASE WHEN X.ORGMONTH = inMonth THEN X.NOTAXFEE ELSE 1 END)) * 0.95
                        )
                --第一次超出
                ELSE
                    1
                --除第一次超出外的第n次超出
                END  AS PROVINCE31_RATIO
        FROM
            HW_1000 X
                JOIN CDN_SPECIAL_SETT_CONFIG Y ON X.PRODUCT_ORDER_ID=Y.PRODUCT_ORDER_ID
                JOIN
            (
                SELECT
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID,
                    MAX( ORGMONTH ) END_DATE,
                    CASE
                        WHEN MAX( EFFECTIVE_DATE ) = LAST_DAY(MAX( EFFECTIVE_DATE )) THEN
                            TRUNC(
                                    ADD_MONTHS(  MAX( EFFECTIVE_DATE ),
                                                 12 * TRUNC( ( MONTHS_BETWEEN(
                                                         TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))
                                                                 ) / 12, 0  ) + 1
                                    )
                                , 'MONTH')
                        ELSE
                            ADD_MONTHS(
                                    MAX( EFFECTIVE_DATE ),
                                    12 * TRUNC((MONTHS_BETWEEN( TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE ))) )/12,0)
                            )
                        END as START_DATE
                FROM
                    HW_1000
                GROUP BY
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID
            ) T
            ON  X.OFFER_ORDER_ID = T.OFFER_ORDER_ID
                AND X.PRODUCT_ORDER_ID = T.PRODUCT_ORDER_ID
        WHERE
            X.ORGMONTH >= to_char(T.START_DATE, 'yyyymm' )
          AND X.ORGMONTH <= T.END_DATE
        GROUP BY
            X.OFFER_ORDER_ID,X.PRODUCT_ORDER_ID
        HAVING SUM(NOTAXFEE) > 20000000000
    ) W
    ON (A.OFFER_ORDER_ID = W.OFFER_ORDER_ID AND A.PRODUCT_ORDER_ID = W.PRODUCT_ORDER_ID)
    WHEN MATCHED THEN
        UPDATE SET A.RATIO = ROUND(A.RATIO * W.PROVINCE31_RATIO, 12)
    WHERE A.ORDER_MODE = '3'
            AND A.ISTOP55 = '1'
            AND A.ACCT_MONTH = INMONTH;


--受理模式3 top55订购超过1000w的结算比例 非华为  签约省
INSERT INTO STL_CDN_RULE
SELECT DISTINCT
    NULL,
    inMonth,
    x.ec_code,
    x.order_mode,
    x.offer_code,
    x.product_code,
    x.offer_order_id,
    x.product_order_id,
    '${EC_PROV_CODE_07}',
    0,
    0,
    ROUND(t.sign_ratio* 100, 12),
    '98',
    '1'
FROM STL_CDN_RULE x
         JOIN (
    SELECT
        X.OFFER_ORDER_ID,
        X.PRODUCT_ORDER_ID,
        -- ((sum( NOTAXFEE )/20000000000) - 1) * 0.05  as SIGN_RATIO
        (1 - (SUM(X.NOTAXFEE)-20000000000)/SUM(CASE WHEN X.ORGMONTH = inMonth THEN X.NOTAXFEE ELSE 1 END)) * 0.05 as SIGN_RATIO
    FROM
        STL_CDN_RULE_1000 X
            JOIN CDN_SPECIAL_SETT_CONFIG Y ON X.PRODUCT_ORDER_ID=Y.PRODUCT_ORDER_ID
            JOIN
        (
            SELECT
                OFFER_ORDER_ID,
                PRODUCT_ORDER_ID,
                MAX( ORGMONTH ) END_DATE,
                CASE
                    WHEN MAX( EFFECTIVE_DATE ) = LAST_DAY(MAX( EFFECTIVE_DATE )) THEN
                        TRUNC(
                                ADD_MONTHS(  MAX( EFFECTIVE_DATE ),
                                             12 * TRUNC( ( MONTHS_BETWEEN(
                                                     TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))
                                                             ) / 12, 0  ) + 1
                                )
                            , 'MONTH')
                    ELSE
                        ADD_MONTHS(
                                MAX( EFFECTIVE_DATE ),
                                12 * TRUNC((MONTHS_BETWEEN( TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))  )/12,0)
                        )
                    END as START_DATE
            FROM
                STL_CDN_RULE_1000
            GROUP BY
                OFFER_ORDER_ID,
                PRODUCT_ORDER_ID
        ) T
        ON  X.OFFER_ORDER_ID = T.OFFER_ORDER_ID
            AND X.PRODUCT_ORDER_ID = T.PRODUCT_ORDER_ID
            AND X.PRODUCT_CODE not in ('****************','****************' )
    WHERE
        X.ORGMONTH >= to_char(T.START_DATE, 'yyyymm' )
      AND X.ORGMONTH <= T.END_DATE
    GROUP BY
        X.OFFER_ORDER_ID,X.PRODUCT_ORDER_ID
    HAVING SUM(X.NOTAXFEE) > 20000000000
       AND  (SUM(X.NOTAXFEE)-20000000000) < SUM(CASE WHEN X.ORGMONTH = inMonth THEN X.NOTAXFEE ELSE 0 END)
) t
              ON x.OFFER_ORDER_ID = t.OFFER_ORDER_ID
                  AND x.PRODUCT_ORDER_ID = t.PRODUCT_ORDER_ID
WHERE
    x.order_mode = '3'
  AND x.istop55 = '1'
  AND x.acct_month = inMonth;


--受理模式3 top55订购超过1000w的结算比例 华为  签约省
INSERT INTO STL_CDN_HW_RULE
SELECT DISTINCT
    null,
    inMonth,
    x.ec_code,
    x.order_mode,
    x.offer_code,
    x.product_code,
    x.offer_order_id,
    x.product_order_id,
    '${EC_PROV_CODE_07}',
    0,
    0,
    ROUND(t.sign_ratio* 100, 12),
    '98',
    '1'
FROM STL_CDN_HW_RULE x
         JOIN CDN_SPECIAL_SETT_CONFIG Y ON x.PRODUCT_ORDER_ID=Y.PRODUCT_ORDER_ID
         JOIN (
    WITH HW_1000 AS (
        SELECT
            b.ORDER_MODE,
            b.ACCT_MONTH ORGMONTH,
            b.OFFER_CODE,
            b.PRODUCT_CODE,
            b.OFFER_ORDER_ID,
            b.PRODUCT_ORDER_ID,
            (b.CMCC_FEE + b.OTH_FEE) as NOTAXFEE,
            a.EFFECTIVE_DATE,
            a.ISTOP55
        FROM
            STLUSERS.STL_SERV_BIZ_CODE a
                JOIN stludr.STL_CDN_ORDER_FEE b
                     ON a.PROD_ORDER_MODE = b.ORDER_MODE
                         AND a.PROD_ORDER_ID = b.OFFER_ORDER_ID
                         AND a.ORDER_ID = b.PRODUCT_ORDER_ID
        WHERE
            b.OFFER_CODE = '50004'
          AND b.product_code not in('****************','****************')
          AND b.ORDER_MODE = '3'
          AND a.ISTOP55 = '1'
    )
    SELECT
        X.OFFER_ORDER_ID,
        X.PRODUCT_ORDER_ID,

        -- ((sum( NOTAXFEE )/20000000000) - 1) * 0.05  as SIGN_RATIO
        (1 - (SUM(X.NOTAXFEE)-20000000000)/SUM(CASE WHEN X.ORGMONTH = inMonth THEN X.NOTAXFEE ELSE 1 END)) * 0.05 as SIGN_RATIO
    FROM
        HW_1000 X
            JOIN
        (
            SELECT
                OFFER_ORDER_ID,
                PRODUCT_ORDER_ID,
                MAX( ORGMONTH ) END_DATE,
                CASE
                    WHEN MAX( EFFECTIVE_DATE ) = LAST_DAY(MAX( EFFECTIVE_DATE )) THEN
                        TRUNC(
                                ADD_MONTHS(  MAX( EFFECTIVE_DATE ),
                                             12 * TRUNC( ( MONTHS_BETWEEN(
                                                     TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))) / 12, 0  ) + 1
                                )
                            , 'MONTH')
                    ELSE
                        ADD_MONTHS(
                                MAX( EFFECTIVE_DATE ),
                                12 * TRUNC((MONTHS_BETWEEN( TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))) / 12,0)
                        )
                    END as START_DATE
            FROM
                HW_1000
            GROUP BY
                OFFER_ORDER_ID,
                PRODUCT_ORDER_ID
        ) T
        ON  X.OFFER_ORDER_ID = T.OFFER_ORDER_ID
            AND X.PRODUCT_ORDER_ID = T.PRODUCT_ORDER_ID
    WHERE
        X.ORGMONTH >= to_char(T.START_DATE, 'yyyymm' )
      AND X.ORGMONTH <= T.END_DATE
    GROUP BY
        X.OFFER_ORDER_ID,X.PRODUCT_ORDER_ID
    HAVING SUM(X.NOTAXFEE) > 20000000000
       AND  (SUM(X.NOTAXFEE)-20000000000) < SUM(CASE WHEN X.ORGMONTH = inMonth THEN X.NOTAXFEE ELSE 0 END)
) t
              ON  x.OFFER_ORDER_ID = t.OFFER_ORDER_ID
                  AND x.PRODUCT_ORDER_ID = t.PRODUCT_ORDER_ID
WHERE
    x.order_mode = '3'
  AND x.istop55 = '1'
  AND x.acct_month = inMonth;

commit;

call LOG_PROCEDURES('17.BBOSS出账的CDN结算规则{省间结算部分}入STL_REPART_PARAMETER_T', v_proc_name);

----BBOSS出账的CDN
--省间结算部分
INSERT INTO STL_REPART_PARAMETER_T(ID,OFFER_CODE,PRODUCT_CODE,PROD_INST_ID,SVC_INST_ID,
                                   ORDER_MODE,RULE_ID,RATE_ID,CALC_PRIORITY,
                                   OBJECT_VALUE,TARIFF_TYPE,RATE_VALUE,EFF_DATE,
                                   EXP_DATE,ACCT_MONTH,DEST_SOURCE,CHARGE_ITEM,ROUTE_FLAG)
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*, ft.FEETYPE, 1 route_flag
FROM
    (SELECT a.OFFER_CODE offer_code, a.PRODUCT_CODE product_code,
            a.OFFER_ORDER_ID poid_inst_id, a.PRODUCT_ORDER_ID svc_inst_id, a.ORDER_MODE order_mode, b.RULE_ID rule_id,
            c.RATE_ID rate_id, row_number() over(PARTITION BY a.PRODUCT_ORDER_ID ORDER BY to_number(a.RATIO)) - 1
                 calc_priority, a.SETT_PROV object_value, '1' tariff_type,
            a.RATIO / 100 rate_value, to_date(a.ACCT_MONTH, 'yyyymm') eff_date, add_months(to_date(
                                                                                                   a.ACCT_MONTH, 'yyyymm'), 1) - 1 exp_date, a.ACCT_MONTH acct_month,a.dest_source
     FROM STL_CDN_RULE a,
          STL_OFFER_T b,
          STL_RATE_T c
     WHERE a.OFFER_CODE = b.OFFER_CODE
       AND a.ORDER_MODE = b.ORDER_MODE
       AND b.PRODUCT_CODE = -1
       AND b.RULE_ID = c.RULE_ID
       AND b.DATA_SOURCE = 1
       and a.offer_code = '50004'
       and b.dest_source = '0'
       AND a.ACCT_MONTH = inMonth
       and to_date(inMonth, 'YYYYMM') between b.eff_date and b.exp_date) data,
    (SELECT '16' feetype FROM DUAL UNION SELECT '1063' FROM dual
     UNION SELECT '1101' FROM DUAL UNION SELECT '1102' FROM DUAL UNION SELECT '1467' feetype FROM dual UNION SELECT '4018' feetype FROM DUAL
     UNION SELECT '1468' FROM DUAL UNION SELECT '1469' FROM DUAL UNION SELECT '1470' feetype FROM DUAL) ft;
select concat('15_', now());
call LOG_PROCEDURES('18.BBOSS出账的CDN结算规则{华为结算部分}入STL_REPART_PARAMETER_T', v_proc_name);

--华为结算部分
INSERT INTO STL_REPART_PARAMETER_T(ID,OFFER_CODE,PRODUCT_CODE,PROD_INST_ID,SVC_INST_ID,
                                   ORDER_MODE,RULE_ID,RATE_ID,CALC_PRIORITY,
                                   OBJECT_VALUE,TARIFF_TYPE,RATE_VALUE,EFF_DATE,
                                   EXP_DATE,ACCT_MONTH,DEST_SOURCE,CHARGE_ITEM,ROUTE_FLAG)
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*, ft.FEETYPE, 1 route_flag
FROM
    (SELECT a.OFFER_CODE offer_code, a.PRODUCT_CODE product_code,
            a.OFFER_ORDER_ID poid_inst_id, a.PRODUCT_ORDER_ID svc_inst_id, a.ORDER_MODE order_mode, b.RULE_ID rule_id,
            c.RATE_ID rate_id, row_number() over(PARTITION BY a.PRODUCT_ORDER_ID ORDER BY to_number(a.RATIO)) - 1
                 calc_priority, a.SETT_PROV object_value, '1' tariff_type,
            a.RATIO / 100 rate_value, to_date(a.ACCT_MONTH, 'yyyymm') eff_date, add_months(to_date(
                                                                                                   a.ACCT_MONTH, 'yyyymm'), 1) - 1 exp_date, a.ACCT_MONTH acct_month,a.dest_source
     FROM STL_CDN_HW_RULE a,
          STL_OFFER_T b,
          STL_RATE_T c
     WHERE a.OFFER_CODE = b.OFFER_CODE
       AND a.ORDER_MODE = b.ORDER_MODE
       AND b.PRODUCT_CODE = -1
       AND b.RULE_ID = c.RULE_ID
       AND b.DATA_SOURCE = 1
       and a.offer_code = '50004'
       and b.dest_source = '98'
       AND a.ACCT_MONTH = inMonth
       and to_date(inMonth, 'YYYYMM') between b.eff_date and b.exp_date) data,
    (SELECT '116' feetype FROM DUAL
     union all
     select resource_specode From stludr.cdn_vas_dict) ft;
select concat('16_', now());
---- EBOSS出账的CDN
-- 省间结算部分（互联网客户、苏研直签客户）
call LOG_PROCEDURES('19.EBOSS出账的CDN入STL_REPART_PARAMETER_T', v_proc_name);

INSERT INTO STL_REPART_PARAMETER_T(ID,OFFER_CODE,PRODUCT_CODE,PROD_INST_ID,SVC_INST_ID,
                                   ORDER_MODE,RULE_ID,RATE_ID,CALC_PRIORITY,
                                   OBJECT_VALUE,TARIFF_TYPE,RATE_VALUE,EFF_DATE,
                                   EXP_DATE,ACCT_MONTH,DEST_SOURCE,CHARGE_ITEM,ROUTE_FLAG)
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*, ft.FEETYPE, 1 route_flag
FROM
    (SELECT a.OFFER_CODE offer_code, a.PRODUCT_CODE product_code,
            a.OFFER_ORDER_ID poid_inst_id, a.PRODUCT_ORDER_ID svc_inst_id, a.ORDER_MODE order_mode, b.RULE_ID rule_id,
            c.RATE_ID rate_id, row_number() over(PARTITION BY a.PRODUCT_ORDER_ID ORDER BY to_number(a.RATIO)) - 1
                 calc_priority, a.SETT_PROV object_value, '1' tariff_type,
            a.RATIO / 100 rate_value, to_date(a.ACCT_MONTH, 'yyyymm') eff_date, add_months(to_date(
                                                                                                   a.ACCT_MONTH, 'yyyymm'), 1) - 1 exp_date, a.ACCT_MONTH acct_month,'98' dest_source
     FROM STL_CDN_RULE a,
          STL_OFFER_T b,
          STL_RATE_T c
     WHERE a.OFFER_CODE = b.OFFER_CODE
       AND a.ORDER_MODE = b.ORDER_MODE
       AND b.PRODUCT_CODE = '9200397'
       AND b.RULE_ID = c.RULE_ID
       AND b.DATA_SOURCE = 1
       and a.offer_code = '9200397'
       and b.route_code = '0'
       AND a.ACCT_MONTH = inMonth
       and to_date(inMonth, 'YYYYMM') between b.eff_date and b.exp_date) data,
    (SELECT '16' feetype FROM DUAL) ft;
commit;
select concat('17_', now());
--省间结算部分（后付费政企客户）
INSERT INTO STL_REPART_PARAMETER_T(ID,OFFER_CODE,PRODUCT_CODE,PROD_INST_ID,SVC_INST_ID,
                                   ORDER_MODE,RULE_ID,RATE_ID,CALC_PRIORITY,
                                   OBJECT_VALUE,TARIFF_TYPE,RATE_VALUE,EFF_DATE,
                                   EXP_DATE,ACCT_MONTH,DEST_SOURCE,CHARGE_ITEM,ROUTE_FLAG)
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*, ft.FEETYPE, 1 route_flag
FROM
    (SELECT a.OFFER_CODE offer_code, a.PRODUCT_CODE product_code,
            a.OFFER_ORDER_ID poid_inst_id, a.PRODUCT_ORDER_ID svc_inst_id, a.ORDER_MODE order_mode, b.RULE_ID rule_id,
            c.RATE_ID rate_id, row_number() over(PARTITION BY a.PRODUCT_ORDER_ID ORDER BY to_number(a.RATIO)) - 1
                   calc_priority, a.SETT_PROV object_value, '1' tariff_type,
            a.RATIO / 100 rate_value, to_date(a.ACCT_MONTH, 'yyyymm') eff_date, add_months(to_date(
                                                                                                   a.ACCT_MONTH, 'yyyymm'), 1) - 1 exp_date, a.ACCT_MONTH acct_month, '0' dest_source
     FROM STL_CDN_RULE a,
          STL_OFFER_T b,
          STL_RATE_T c
     WHERE a.OFFER_CODE = b.OFFER_CODE
       AND a.ORDER_MODE = b.ORDER_MODE
       AND b.PRODUCT_CODE = '9200397'
       AND b.RULE_ID = c.RULE_ID
       AND b.DATA_SOURCE = 1
       and a.offer_code = '9200397'
       and b.route_code = '0'
       AND a.ACCT_MONTH = inMonth
       and to_date(inMonth, 'YYYYMM') between b.eff_date and b.exp_date) data,
    (SELECT '17' feetype FROM DUAL) ft;
select concat('18_', now());
--华为结算部分
INSERT INTO STL_REPART_PARAMETER_T(ID,OFFER_CODE,PRODUCT_CODE,PROD_INST_ID,SVC_INST_ID,
                                   ORDER_MODE,RULE_ID,RATE_ID,CALC_PRIORITY,
                                   OBJECT_VALUE,TARIFF_TYPE,RATE_VALUE,EFF_DATE,
                                   EXP_DATE,ACCT_MONTH,DEST_SOURCE,CHARGE_ITEM,ROUTE_FLAG)
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*, '98', ft.FEETYPE, 1 route_flag
FROM
    (SELECT a.OFFER_CODE offer_code, a.PRODUCT_CODE product_code,
            a.OFFER_ORDER_ID poid_inst_id, a.PRODUCT_ORDER_ID svc_inst_id, a.ORDER_MODE order_mode, b.RULE_ID rule_id,
            c.RATE_ID rate_id, row_number() over(PARTITION BY a.PRODUCT_ORDER_ID ORDER BY to_number(a.RATIO)) - 1
                 calc_priority, a.SETT_PROV object_value, '1' tariff_type,
            a.RATIO / 100 rate_value, to_date(a.ACCT_MONTH, 'yyyymm') eff_date, add_months(to_date(
                                                                                                   a.ACCT_MONTH, 'yyyymm'), 1) - 1 exp_date, a.ACCT_MONTH acct_month
     FROM STL_CDN_HW_RULE a,
          STL_OFFER_T b,
          STL_RATE_T c
     WHERE a.OFFER_CODE = b.OFFER_CODE
       AND a.ORDER_MODE = b.ORDER_MODE
       AND b.PRODUCT_CODE = '9200397'
       AND b.RULE_ID = c.RULE_ID
       AND b.DATA_SOURCE = 1
       and a.offer_code = '9200397'
       and b.route_code = '3'
       AND a.ACCT_MONTH = inMonth
       and to_date(inMonth, 'YYYYMM') between b.eff_date and b.exp_date) data,
    (SELECT '116' feetype FROM DUAL union all
     SELECT '117' feetype FROM DUAL union all
     select resource_specode From stludr.cdn_vas_dict) ft;
commit;

-- call LOG_PROCEDURES('开始处理CDN特殊订购结算规则,Call STL_CDN_SPECIAL_SETT', v_proc_name);

-- call STL_CDN_SPECIAL_SETT(inMonth,outReturn,outSysError);


-- CDN 直播源站类产品结算规则
BEGIN
call LOG_PROCEDURES('20.开始处理直播源站类产品结算规则', v_proc_name);

-- 先删除直播源产品的规则
delete from STL_REPART_PARAMETER_T where offer_code = '50004' and product_code = '****************' and acct_month = inMonth;
INSERT INTO STL_REPART_PARAMETER_T(ID,OFFER_CODE,PRODUCT_CODE,PROD_INST_ID,SVC_INST_ID,
                                   ORDER_MODE,RULE_ID,RATE_ID,CALC_PRIORITY,OBJECT_VALUE,TARIFF_TYPE,RATE_VALUE,EFF_DATE,
                                   EXP_DATE,ACCT_MONTH,CHARGE_ITEM,DEST_SOURCE,ROUTE_FLAG)
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, t.* ,1 ROUTE_FLAG
FROM
    (
        SELECT * FROM (
                          WITH tmp AS (
                              SELECT
                                  a.POID,
                                  a.SOID,
                                  b.CALC_PRIORITY,
                                  b.OBJECT_VALUE ,
                                  b.RATE_VALUE ,
                                  b.ACCT_MONTH ,
                                  b.ORDER_MODE
                              FROM
                                  stl_sync_attr a,
                                  (
                                      SELECT b.OFFER_CODE,b.SVC_INST_ID,b.CALC_PRIORITY,b.OBJECT_VALUE,b.RATE_VALUE,b.ACCT_MONTH,b.ORDER_MODE,b.CHARGE_ITEM
                                      FROM stl_sync_attr a,STL_REPART_PARAMETER_T b WHERE
                                          a.ATTR_VALUE = b.SVC_INST_ID
                                                                                      AND b.TARIFF_TYPE = 1
                                                                                      AND b.ACCT_MONTH = inMonth
                                                                                      AND a.POSPECNUMBER = b.OFFER_CODE
                                                                                      AND a.POSPECNUMBER = '50004'
                                                                                      AND a.SOSPECNUMBER = '****************'
                                                                                      AND b.CHARGE_ITEM NOT IN (
                                              SELECT '116' feetype FROM DUAL union all
                                              SELECT '117' feetype FROM DUAL union all
                                              SELECT '4039' feetype FROM DUAL union all
                                              SELECT '501' feetype FROM DUAL union all
                                              select resource_specode FROM stludr.cdn_vas_dict)
                                  ) b
                              WHERE
                                  a.ATTR_VALUE = b.SVC_INST_ID
                                AND a.POSPECNUMBER = b.OFFER_CODE
                                AND a.POSPECNUMBER = '50004'
                                AND a.SOSPECNUMBER = '****************'
                              GROUP BY
                                  a.POID,
                                  a.SOID,
                                  b.CALC_PRIORITY,
                                  b.OBJECT_VALUE ,
                                  b.RATE_VALUE ,
                                  b.ACCT_MONTH,
                                  b.ORDER_MODE
                          )
                          SELECT a.OFFER_CODE,a.PRODUCT_CODE ,tmp.POID,tmp.SOID ,a.ORDER_MODE,a.RULE_ID , '1' RATE_ID ,
                                 tmp.CALC_PRIORITY,tmp.OBJECT_VALUE ,'1' TARIFF_TYPE,tmp.RATE_VALUE,
                                 to_date(tmp.ACCT_MONTH, 'yyyymm') eff_date, add_months(to_date(tmp.ACCT_MONTH, 'yyyymm'), 1) - 1 exp_date,
                                 tmp.ACCT_MONTH,b.CHARGE_ITEM,a.DEST_SOURCE
                          FROM  STL_OFFER_T a,STL_RULE_ITEM_T b ,tmp
                          WHERE
                              a.RULE_ID =b.RULE_ID
                            AND a.OFFER_CODE ='50004'
                            AND a.PRODUCT_CODE ='****************'
                            AND a.ORDER_MODE =tmp.ORDER_MODE
                            AND b.CHARGE_ITEM IN ('3970','7970')
                          ORDER BY tmp.SOID,b.CHARGE_ITEM
                      ) A
        UNION ALL
        SELECT * FROM (
                          -- 华为结算部分
                          WITH HW AS (
                              SELECT
                                  a.POID,
                                  a.SOID,
                                  b.CALC_PRIORITY,
                                  b.OBJECT_VALUE ,
                                  b.RATE_VALUE ,
                                  b.ACCT_MONTH ,
                                  b.ORDER_MODE
                              FROM
                                  stl_sync_attr a,
                                  (
                                      SELECT b.OFFER_CODE,b.SVC_INST_ID,b.CALC_PRIORITY,b.OBJECT_VALUE,b.RATE_VALUE,b.ACCT_MONTH,b.ORDER_MODE,b.CHARGE_ITEM
                                      FROM stl_sync_attr a,STL_REPART_PARAMETER_T b WHERE
                                          a.ATTR_VALUE = b.SVC_INST_ID
                                                                                      AND b.TARIFF_TYPE = 1
                                                                                      AND b.ACCT_MONTH = inMonth
                                                                                      AND a.POSPECNUMBER = b.OFFER_CODE
                                                                                      AND a.POSPECNUMBER = '50004'
                                                                                      AND a.SOSPECNUMBER = '****************'
                                                                                      AND b.CHARGE_ITEM IN (
                                              SELECT '116' feetype FROM DUAL union all
                                              SELECT '117' feetype FROM DUAL union all
                                              select resource_specode FROM stludr.cdn_vas_dict)
                                  ) b
                              WHERE
                                  a.ATTR_VALUE = b.SVC_INST_ID
                                AND a.POSPECNUMBER = b.OFFER_CODE
                                AND a.POSPECNUMBER = '50004'
                                AND a.SOSPECNUMBER = '****************'
                              GROUP BY
                                  a.POID,
                                  a.SOID,
                                  b.CALC_PRIORITY,
                                  b.OBJECT_VALUE ,
                                  b.RATE_VALUE ,
                                  b.ACCT_MONTH,
                                  b.ORDER_MODE
                              ORDER BY a.SOID
                          )
                          SELECT a.OFFER_CODE,a.PRODUCT_CODE ,HW.POID,HW.SOID ,a.ORDER_MODE,a.RULE_ID , '1' RATE_ID ,
                                 HW.CALC_PRIORITY,HW.OBJECT_VALUE ,'1' TARIFF_TYPE,HW.RATE_VALUE,
                                 to_date(HW.ACCT_MONTH, 'yyyymm') eff_date, add_months(to_date(HW.ACCT_MONTH, 'yyyymm'), 1) - 1 exp_date,
                                 HW.ACCT_MONTH,b.CHARGE_ITEM,a.DEST_SOURCE
                          FROM  STL_OFFER_T a ,STL_RULE_ITEM_T b,HW
                          WHERE
                              a.RULE_ID =b.RULE_ID
                            AND a.OFFER_CODE ='50004'
                            AND a.PRODUCT_CODE ='****************'
                            AND a.RULE_ID IN ('666','667','668')
                            AND a.ORDER_MODE =HW.ORDER_MODE
                          ORDER BY HW.SOID ,b.CHARGE_ITEM
                      ) B
    ) t;
COMMIT;

END;


COMMIT;

call LOG_PROCEDURES('call P_SETTLE_RULE_PROC_REPART_CDN', v_proc_name);
call P_SETTLE_RULE_PROC_REPART_CDN(inMonth,outReturn,outSysError);

SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
call LOG_PROCEDURES('completed successfully. 执行完成总耗时秒='||TIMESTAMPDIFF(SECOND, @P_TIME_CDNMAIN, NOW()), v_proc_name);

END;

END;;
DELIMITER ;
