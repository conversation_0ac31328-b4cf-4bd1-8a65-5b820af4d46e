/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：规则计算-tariff规则计算
**/
DROP PROCEDURE IF EXISTS stlusers.P_SETTLE_RULE_PROC_TARIFF_PARAMETER;
DELIMITER //
CREATE OR REPLACE  DEFINER="stlusers"@"10.%" PROCEDURE "P_SETTLE_RULE_PROC_TARIFF_PARAMETER"(
    inMonth          IN   VARCHAR2,
    batch            IN   VARCHAR2,
    flag_version     IN   VARCHAR2,
    reserve1         IN   VARCHAR2,
    reserve2         IN   VARCHAR2,
    proc_out         IN   VARCHAR2,
    outSysError      OUT  VARCHAR2,
    outReturn        OUT  NUMBER,
    outBL            OUT  NUMBER,
    outAR            OUT  NUMBER
)
AS
    iv_Sql VARCHAR2(10240);
    iv_Seq NUMBER(12);
    iv_Poid VARCHAR2(20);
    iv_Soid VARCHAR2(20);
    iv_Feetype VARCHAR2(4);
    iv_RuleStartMonth CHAR(6);
    iv_RuleEndMonth CHAR(6);
    dyn_Select VARCHAR2(1024);
    v_proc_name  VARCHAR2(36) := 'P_SETTLE_RULE_PROC_TARIFF_PARAMETER';

BEGIN
    outSysError := 'OK';
    outReturn := 0;
















    DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT,@p3 = MYSQL_ERRNO;
outSysError := substr(@p2, 1, 1000);
        outReturn  := -1;
ROLLBACK;



select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError || '|' || @p3 ) AS error_msg ;
END;


BEGIN




        set @vSql := 'TRUNCATE TABLE STL_TARIFF_PARAMETER_T';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
        outSysError := 'OK';
BEGIN



select 1;

INSERT INTO stl_tariff_parameter_t
select seq_tariff_parameter_id.nextval id, offer_code, product_code, poid_inst_id, svc_inst_id, order_mode, rule_id, rate_id, charge_item, object_value, tariff_type, decode(PRIORITY, NULL,calc_priority-1, '',calc_priority-1, PRIORITY) calc_priority

     , rate_value, eff_date, exp_date, dest_source, route_flag from (
                                                                        SELECT /*+ hash_join(a,b,c,d,e) join_order(c,b,d,e,a)*/a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
                                                                                                                               a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
                                                                                                                               e.RATE_ID rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value,
                                                                                                                               a.SETTLEMEASURE tariff_type, row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH ORDER BY a.SETTLERATE) calc_priority,
                                                                                IFNULL(decode(a.SETTLEMEASURE, '1', round(a.SETTLERATE / a.SUMSETTLERATE, 15), '2',
                                                                                              a.SETTLEFEE),'') rate_value, decode(a.ORDERTYPE, '1', decode(floor(a.RULESTARTMONTH / 201501),
                                                                                                                                                           0, to_date('201501', 'yyyymm'), decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))),
                                                                                                                                  decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))) eff_date,
                                                                                                                               decode(floor(a.ruleendmonth / to_char(b.exp_date, 'YYYYMM')), 0, date_sub(date_add(to_date(a.RULEENDMONTH, 'yyyymm'), interval 1 month), interval 1 day), b.exp_date) exp_date,
                                                                                                                               decode(a.sospecnumber, '50001', 1, 0) dest_source, 1 route_flag, a.PRIORITY
                                                                        FROM STL_SYNC_RULE a,
                                                                             STL_OFFER_T b,
                                                                             STL_RATE_T c,
                                                                             STL_RULE_ITEM_T d,
                                                                             STL_TARIFF_RATE_T e
                                                                        WHERE a.POSPECNUMBER = b.OFFER_CODE
                                                                          AND b.PRODUCT_CODE = -1
                                                                          AND a.ORDERTYPE = b.ORDER_MODE
                                                                          AND b.RULE_ID = c.RULE_ID
                                                                          AND b.RULE_ID = d.RULE_ID
                                                                          AND a.FEETYPE = d.CHARGE_ITEM
                                                                          AND a.SETTLEMEASURE = e.TARIFF_TYPE
                                                                          AND c.RATE_ID = e.RATE_ID
                                                                          AND a.POSPECNUMBER NOT IN ('1010403', '1010402', '50021')
                                                                          AND a.SETTLEMEASURE = 1
                                                                          AND b.DATA_SOURCE = 1);

select 2;

























INSERT INTO stl_tariff_parameter_t


SELECT /*+ hash_join(a) no_index(a idx_sospecnumber) join_order(c,b,d,e,a) */ SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
                                                                              a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
                                                                              e.RATE_ID rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value,
                                                                              a.SETTLEMEASURE tariff_type,
                                                                              decode(a.PRIORITY, NULL,row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH ORDER BY a.SETTLERATE) - 1, '',row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH ORDER BY a.SETTLERATE) - 1,a.PRIORITY) calc_priority,
                                                                              decode(a.SETTLEMEASURE, '1', round(a.SETTLERATE / a.SUMSETTLERATE, 27), '2',a.SETTLEFEE) rate_value,

                                                                              decode(a.ORDERTYPE, '1', decode(floor(a.RULESTARTMONTH / 201501),0, to_date('201501', 'yyyymm'), decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))),decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))) eff_date,

                                                                              decode(floor(a.ruleendmonth / to_char(b.exp_date, 'YYYYMM')), 0, date_sub(date_add(to_date(a.RULEENDMONTH, 'yyyymm'), interval 1 month), interval 1 day), b.exp_date) exp_date,
                                                                              0 dest_source, 1 route_flag
FROM STL_SYNC_RULE a,
     STL_OFFER_T b,
     STL_RATE_T c,
     STL_RULE_ITEM_T d,
     STL_TARIFF_RATE_T e
WHERE a.POSPECNUMBER = b.OFFER_CODE
  AND a.SOSPECNUMBER = b.PRODUCT_CODE
  AND a.ORDERTYPE = b.ORDER_MODE
  AND b.RULE_ID = c.RULE_ID
  AND b.RULE_ID = d.RULE_ID
  AND a.FEETYPE = d.CHARGE_ITEM
  AND a.SETTLEMEASURE = e.TARIFF_TYPE
  AND c.RATE_ID = e.RATE_ID
  AND a.POSPECNUMBER NOT IN ('1010403', '1010800', '50021','50120')
  and (a.sospecnumber not in (select product_code from stl_config_db2f where type_nm = 'LP' and column_nm = 'product_code')
    or a.sospecnumber is NULL)
  and a.SOSPECNUMBER !='2023999400085449'


          AND a.SETTLEMEASURE = 1
          AND b.DATA_SOURCE = 1;

select 3;
--插入实收历史数据（2015年前）
INSERT INTO stl_tariff_parameter_t select seq_tariff_parameter_id.nextval id, t.* FROM STL_TARIFF_PARAMETER_T_HIS t;
select 4;
--按商品（不包含全网IDC-1010403、智慧店铺-50075,智慧园区-50076,高精度定位-50091），固定值
INSERT INTO stl_tariff_parameter_t
SELECT /*+ hash_join(a,b,c,d,e)*/seq_tariff_parameter_id.nextval id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
                                 a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
                                 e.RATE_ID rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value,
                                 decode(a.SETTLEMEASURE, '3', '2', a.settlemeasure) tariff_type, 0 calc_priority,
                                 decode(a.SETTLEMEASURE, '1', round(a.SETTLERATE / a.SUMSETTLERATE, 15),a.SETTLEFEE) rate_value,
                                 to_date(a.RULESTARTMONTH, 'yyyymm') eff_date, date_sub(date_add(to_date(a.RULEENDMONTH, 'yyyymm'), interval 1 month), interval 1 day) exp_date, 0 dest_source, 1 route_flag
FROM STL_SYNC_RULE a,
     STL_OFFER_T b,
     STL_RATE_T c,
     STL_RULE_ITEM_T d,
     STL_TARIFF_RATE_T e
WHERE a.POSPECNUMBER = b.OFFER_CODE
  AND b.PRODUCT_CODE = -1
  AND a.ORDERTYPE = b.ORDER_MODE
  AND b.RULE_ID = c.RULE_ID
  AND b.RULE_ID = d.RULE_ID
  AND a.FEETYPE = d.CHARGE_ITEM
  AND decode(a.SETTLEMEASURE, '3', '2', a.settlemeasure) = e.TARIFF_TYPE
  AND c.RATE_ID = e.RATE_ID
  AND a.POSPECNUMBER NOT IN ('1010403', '50075','50076','50091')
  AND a.SETTLEMEASURE in (2, 3)
  AND a.SETTLEFEE > 0
  AND b.DATA_SOURCE = 1;

select 5;

INSERT INTO stl_tariff_parameter_t
SELECT /*+ hash_join(a,b,c,d,e)*/seq_tariff_parameter_id.nextval id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
                                 a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
                                 e.RATE_ID rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value,
                                 decode(a.SETTLEMEASURE, '3', '2', a.settlemeasure) tariff_type, 1 calc_priority,
                                 decode(a.SETTLEMEASURE, '1', to_char(round(a.SETTLERATE / a.SUMSETTLERATE, 15)),
                                        '$') rate_value, to_date(a.RULESTARTMONTH, 'yyyymm') eff_date, date_sub(date_add(to_date(a.RULEENDMONTH, 'yyyymm'), interval 1 month), interval 1 day) exp_date, 0 dest_source, 1 route_flag
FROM STL_SYNC_RULE a,
     STL_OFFER_T b,
     STL_RATE_T c,
     STL_RULE_ITEM_T d,
     STL_TARIFF_RATE_T e
WHERE a.POSPECNUMBER = b.OFFER_CODE
  AND b.PRODUCT_CODE = -1
  AND a.ORDERTYPE = b.ORDER_MODE
  AND b.RULE_ID = c.RULE_ID
  AND b.RULE_ID = d.RULE_ID
  AND a.FEETYPE = d.CHARGE_ITEM
  AND decode(a.SETTLEMEASURE, '3', '2', a.settlemeasure) = e.TARIFF_TYPE
  AND c.RATE_ID = e.RATE_ID
  AND a.POSPECNUMBER NOT IN ('1010403', '50075','50076','50091')
  AND a.SETTLEMEASURE in (2, 3)
  AND a.SETTLEFEE <= 0
  AND b.DATA_SOURCE = 1;

select 6;
--按产品（不包含全网IDC-1010403、智慧店铺-50075、智慧园区-50076、 OnePower工业互联网-50097、高精度定位-50091）、中移急救、


INSERT INTO stl_tariff_parameter_t
SELECT /*+ hash_join(a,b,c,d,e)*/seq_tariff_parameter_id.nextval id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
                                 a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
                                 e.RATE_ID rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value,
                                 decode(a.SETTLEMEASURE, '3', '2', a.settlemeasure) tariff_type, 0 calc_priority,
                                 decode(a.SETTLEMEASURE, '1', round(a.SETTLERATE / a.SUMSETTLERATE, 15),
                                        a.SETTLEFEE) rate_value, to_date(a.RULESTARTMONTH, 'yyyymm') eff_date, date_sub(date_add(to_date(a.RULEENDMONTH, 'yyyymm'), interval 1 month), interval 1 day) exp_date, 0 dest_source, 1 route_flag
FROM STL_SYNC_RULE a,
     STL_OFFER_T b,
     STL_RATE_T c,
     STL_RULE_ITEM_T d,
     STL_TARIFF_RATE_T e
WHERE a.POSPECNUMBER = b.OFFER_CODE
  AND a.sospecnumber = b.PRODUCT_CODE
  AND a.ORDERTYPE = b.ORDER_MODE
  AND b.RULE_ID = c.RULE_ID
  AND b.RULE_ID = d.RULE_ID
  AND a.FEETYPE = d.CHARGE_ITEM
  AND decode(a.SETTLEMEASURE, '3', '2', a.settlemeasure) = e.TARIFF_TYPE
  AND c.RATE_ID = e.RATE_ID
  AND a.POSPECNUMBER NOT IN ('1010403', '50075','50076','50097','50008','50074','50104','50068','50091','60006')
  AND a.sospecnumber !='2023999400085449'
         AND a.SETTLEMEASURE in (2, 3)
         AND a.SETTLEFEE >= 0
         AND b.DATA_SOURCE = 1;

select 7;

INSERT INTO stl_tariff_parameter_t
SELECT /*+ hash_join(a,b,c,d,e)*/seq_tariff_parameter_id.nextval id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
                                 a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
                                 e.RATE_ID rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value,
                                 decode(a.SETTLEMEASURE, '3', '2', a.settlemeasure) tariff_type, 1 calc_priority,
                                 decode(a.SETTLEMEASURE, '1', to_char(round(a.SETTLERATE / a.SUMSETTLERATE, 15)), '$') rate_value,
                                 to_date(a.RULESTARTMONTH, 'yyyymm') eff_date, date_sub(date_add(to_date(a.RULEENDMONTH, 'yyyymm'), interval 1 month), interval 1 day) exp_date,
                                 0 dest_source, 1 route_flag
FROM STL_SYNC_RULE a,
     STL_OFFER_T b,
     STL_RATE_T c,
     STL_RULE_ITEM_T d,
     STL_TARIFF_RATE_T e
WHERE a.POSPECNUMBER = b.OFFER_CODE
  AND a.sospecnumber = b.PRODUCT_CODE
  AND a.ORDERTYPE = b.ORDER_MODE
  AND b.RULE_ID = c.RULE_ID
  AND b.RULE_ID = d.RULE_ID
  AND a.FEETYPE = d.CHARGE_ITEM
  AND decode(a.SETTLEMEASURE, '3', '2', a.settlemeasure) = e.TARIFF_TYPE
  AND c.RATE_ID = e.RATE_ID
  AND a.POSPECNUMBER NOT IN ('1010403', '50075','50076','50097','50008','50074','50104','50068','50091')
  AND a.SETTLEMEASURE in (2, 3)
  AND a.SETTLEFEE < 0
  AND b.DATA_SOURCE = 1;

END;
select 8;
--插入全网IDC业务数据
BEGIN

      --受理模式1
INSERT INTO stl_tariff_parameter_t
select seq_tariff_parameter_id.nextval id, offer_code, product_code, poid_inst_id, svc_inst_id, order_mode, rule_id, rate_id, charge_item, object_value, tariff_type,  decode(PRIORITY, NULL,calc_priority-1, '',calc_priority-1, PRIORITY) calc_priority

     , rate_value, eff_date, exp_date, dest_source, route_flag from (
                                                                        SELECT /*+ hash_join(a,b,c,d,e)*/a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
                                                                                                         a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
                                                                                                         c.RATE_ID rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value, a.SETTLEMEASURE tariff_type,
                                                                                                         row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH ORDER BY a.SETTLERATE) calc_priority, a.PRIORITY,  '1' rate_value,
                                                                                                         to_date(a.RULESTARTMONTH, 'yyyymm') eff_date, date_sub(date_add(to_date(a.RULEENDMONTH, 'yyyymm'), interval 1 month), interval 1 day) exp_date,
                                                                                                         decode(a.COOPERATIVEPROV, '000', '1', '0') dest_source,
                                                                                                         decode(a.COOPERATIVEPROV, '000', '0', '1') route_flag
                                                                        FROM STL_SYNC_RULE a,
                                                                             STL_OFFER_T b,
                                                                             STL_RATE_T c,
                                                                             STL_RULE_ITEM_T d,
                                                                             STL_TARIFF_RATE_T e
                                                                        WHERE a.POSPECNUMBER = b.OFFER_CODE
                                                                          AND b.PRODUCT_CODE = -1
                                                                          AND a.ORDERTYPE = b.ORDER_MODE
                                                                          AND b.RULE_ID = c.RULE_ID
                                                                          AND b.RULE_ID = d.RULE_ID
                                                                          AND a.FEETYPE = d.CHARGE_ITEM
                                                                          AND c.RATE_ID = e.RATE_ID
                                                                          AND e.TARIFF_TYPE = 1
                                                                          AND a.POSPECNUMBER IN ('1010403')
                                                                          AND a.SETTLERATE <> 5
                                                                          AND a.ORDERTYPE = '1'
                                                                          AND b.DATA_SOURCE = 1);

select 9;
--受理模式3
INSERT INTO stl_tariff_parameter_t
select seq_tariff_parameter_id.nextval id, offer_code, product_code, poid_inst_id, svc_inst_id, order_mode, rule_id, rate_id, charge_item, object_value, tariff_type,  decode(PRIORITY, NULL,calc_priority-1, '',calc_priority-1, PRIORITY) calc_priority

     , rate_value, eff_date, exp_date, dest_source, route_flag from (
                                                                        SELECT /*+ hash_join(a,b,c,d,e)*/a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
                                                                                                         a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
                                                                                                         c.RATE_ID rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value,
                                                                                                         a.SETTLEMEASURE tariff_type, row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH ORDER BY a.SETTLERATE) calc_priority, a.PRIORITY,
                                                                                                         round(a.SETTLERATE / a.SUMSETTLERATE, 15)
                                                                                                             rate_value, to_date(a.RULESTARTMONTH, 'yyyymm') eff_date, date_sub(date_add(to_date(a.RULEENDMONTH, 'yyyymm'), interval 1 month), interval 1 day) exp_date, 0 dest_source, 1 route_flag
                                                                        FROM STL_SYNC_RULE a,
                                                                             STL_OFFER_T b,
                                                                             STL_RATE_T c,
                                                                             STL_RULE_ITEM_T d,
                                                                             STL_TARIFF_RATE_T e
                                                                        WHERE a.POSPECNUMBER = b.OFFER_CODE
                                                                          AND b.PRODUCT_CODE = -1
                                                                          AND a.ORDERTYPE = b.ORDER_MODE
                                                                          AND b.RULE_ID = c.RULE_ID
                                                                          AND b.RULE_ID = d.RULE_ID
                                                                          AND a.FEETYPE = d.CHARGE_ITEM
                                                                          AND e.RATE_ID = c.RATE_ID
                                                                          AND e.TARIFF_TYPE = 1
                                                                          AND a.POSPECNUMBER IN ('1010403')
                                                                          AND a.ORDERTYPE = '3'
                                                                          AND b.DATA_SOURCE = 1);
END;
select 10;
--插入IDC业务数据
BEGIN
INSERT INTO stl_tariff_parameter_t
select seq_tariff_parameter_id.nextval id, offer_code, product_code, poid_inst_id, svc_inst_id, order_mode, rule_id, rate_id, charge_item, object_value, tariff_type,  decode(PRIORITY, NULL,calc_priority-1, '',calc_priority-1, PRIORITY) calc_priority

     , rate_value, eff_date, exp_date, dest_source, route_flag from (
                                                                        SELECT /*+ hash_join(a,b,c,d,e)*/a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
                                                                                                         a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
                                                                                                         e.RATE_ID rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value,
                                                                                                         a.SETTLEMEASURE tariff_type, row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH
             ORDER BY a.SETTLERATE) calc_priority, a.PRIORITY,
                                                                                                         IFNULL(decode(a.SETTLEMEASURE, '1', round(a.SETTLERATE / a.SUMSETTLERATE, 15), '2',
                                                                                                                       a.SETTLEFEE) ,'') rate_value, decode(a.ORDERTYPE, '1', decode(floor(a.RULESTARTMONTH / 201501),
                                                                                                                                                                                     0, to_date('201501', 'yyyymm'), to_date(a.RULESTARTMONTH, 'yyyymm')),
                                                                                                                                                            to_date(a.RULESTARTMONTH, 'yyyymm')) eff_date, date_sub(date_add(to_date(a.RULEENDMONTH, 'yyyymm'), interval 1 month), interval 1 day) exp_date,
                                                                                                         decode(a.ORDERTYPE, '1', decode(a.COOPERATIVEPROV, '000', 1, 0), 0) dest_source,
                                                                                                         decode(a.ORDERTYPE, '1', decode(a.COOPERATIVEPROV, '000', 0, 1), 1) route_flag
                                                                        FROM STL_SYNC_RULE a,
                                                                             stlusers.STL_OFFER_T b,
                                                                             stlusers.STL_RATE_T c,
                                                                             stlusers.STL_RULE_ITEM_T d,
                                                                             stlusers.STL_TARIFF_RATE_T e
                                                                        WHERE a.POSPECNUMBER = b.OFFER_CODE
                                                                          AND a.SOSPECNUMBER = b.PRODUCT_CODE
                                                                          AND a.ORDERTYPE = b.ORDER_MODE
                                                                          AND b.RULE_ID = c.RULE_ID
                                                                          AND b.RULE_ID = d.RULE_ID
                                                                          AND a.FEETYPE = d.CHARGE_ITEM
                                                                          AND a.SETTLEMEASURE = e.TARIFF_TYPE
                                                                          AND c.RATE_ID = e.RATE_ID
                                                                          AND a.POSPECNUMBER in ('1010800')
                                                                          AND a.SETTLEMEASURE = 1
                                                                          AND b.DATA_SOURCE = 1
                                                                          AND a.SETTLERATE <> 0);
END;
select 11;
--插入移动云SaaS类产品结算规则
BEGIN
INSERT INTO stl_tariff_parameter_t
select seq_tariff_parameter_id.nextval id, offer_code, product_code, poid_inst_id, svc_inst_id, order_mode, rule_id, rate_id, charge_item, object_value, tariff_type,  decode(PRIORITY, NULL,calc_priority-1, '',calc_priority-1, PRIORITY) calc_priority

     , rate_value, eff_date, exp_date, dest_source, route_flag from (
                                                                        SELECT /*+ hash_join(a,b,c,d,e)*/a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
                                                                                                         a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
                                                                                                         e.RATE_ID rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value,
                                                                                                         a.SETTLEMEASURE tariff_type, row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH
             ORDER BY a.SETTLERATE) calc_priority, a.PRIORITY,
                                                                                                         IFNULL(decode(a.SETTLEMEASURE, '1', round(a.SETTLERATE / a.SUMSETTLERATE, 15), '2',
                                                                                                                       a.SETTLEFEE) ,'') rate_value, decode(a.ORDERTYPE, '1', decode(floor(a.RULESTARTMONTH / 201501),
                                                                                                                                                                                     0, to_date('201501', 'yyyymm'), to_date(a.RULESTARTMONTH, 'yyyymm')),
                                                                                                                                                            to_date(a.RULESTARTMONTH, 'yyyymm')) eff_date, date_sub(date_add(to_date(a.RULEENDMONTH, 'yyyymm'), interval 1 month), interval 1 day) exp_date,
                                                                                                         decode(a.attrname, '主办省', 98, 4) dest_source,
                                                                                                         0 route_flag
                                                                        FROM STL_SYNC_RULE a,
                                                                             stlusers.STL_OFFER_T b,
                                                                             stlusers.STL_RATE_T c,
                                                                             stlusers.STL_RULE_ITEM_T d,
                                                                             stlusers.STL_TARIFF_RATE_T e
                                                                        WHERE a.POSPECNUMBER = b.OFFER_CODE
                                                                          AND a.SOSPECNUMBER = b.PRODUCT_CODE
                                                                          AND a.ORDERTYPE = b.ORDER_MODE
                                                                          AND b.RULE_ID = c.RULE_ID
                                                                          AND b.RULE_ID = d.RULE_ID
                                                                          AND a.FEETYPE = d.CHARGE_ITEM
                                                                          AND a.SETTLEMEASURE = e.TARIFF_TYPE
                                                                          AND c.RATE_ID = e.RATE_ID
                                                                          AND a.POSPECNUMBER in ('1010402')
                                                                          and (a.sospecnumber in (select product_code from stl_config_db2f where type_nm = 'LP' and column_nm = 'product_code'))
                                                                          AND a.SETTLEMEASURE = 1
                                                                          AND b.DATA_SOURCE = 3
                                                                          AND a.SETTLERATE <> 0);
END;
select 12;
--插入智慧店铺结算规则
BEGIN
INSERT INTO stl_tariff_parameter_t
select seq_tariff_parameter_id.nextval id, offer_code, product_code, poid_inst_id, svc_inst_id, order_mode, rule_id, rate_id, charge_item, object_value, tariff_type,  decode(PRIORITY, NULL,calc_priority-1, '',calc_priority-1, PRIORITY) calc_priority

     , rate_value, eff_date, exp_date, dest_source, route_flag from (
                                                                        SELECT /*+ hash_join(a,b,c,d,e)*/a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
                                                                                                         a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
                                                                                                         2 rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value,
                                                                                                         2 tariff_type, row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH
             ORDER BY a.SETTLERATE) calc_priority, a.PRIORITY,
                                                                                                         a.SETTLEFEE rate_value, decode(a.ORDERTYPE, '1', decode(floor(a.RULESTARTMONTH / 201501),
                                                                                                                                                                 0, to_date('201501', 'yyyymm'), decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))),
                                                                                                                                        decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))) eff_date,
                                                                                                         decode(floor(a.ruleendmonth / to_char(b.exp_date, 'YYYYMM')), 0, date_sub(date_add(to_date(a.RULEENDMONTH, 'yyyymm'), interval 1 month), interval 1 day), b.exp_date) exp_date, 98 dest_source, 1 route_flag
                                                                        FROM STL_SYNC_RULE a,
                                                                             STL_OFFER_T b,
                                                                             STL_RATE_T c,
                                                                             STL_RULE_ITEM_T d
                                                                        WHERE a.POSPECNUMBER = b.OFFER_CODE
                                                                          AND a.SOSPECNUMBER = b.PRODUCT_CODE
                                                                          AND a.ORDERTYPE = b.ORDER_MODE
                                                                          AND b.RULE_ID = c.RULE_ID
                                                                          AND b.RULE_ID = d.RULE_ID
                                                                          AND a.FEETYPE = d.CHARGE_ITEM
                                                                          AND a.POSPECNUMBER = '50075'
                                                                          AND a.SETTLEMEASURE = 3
                                                                          and c.rate_type = 2
                                                                          AND b.DATA_SOURCE = 1);
END;

select 14;
--OnePower工业互联网平台结算规则、移动千里眼、OneHealth平台、智慧园区、OneCity
--排除OnePower业务所有云端产品
BEGIN

INSERT INTO stl_tariff_parameter_t
select seq_tariff_parameter_id.nextval id, offer_code, product_code, poid_inst_id, svc_inst_id, order_mode, rule_id, rate_id, charge_item, object_value, tariff_type,  decode(PRIORITY, NULL,calc_priority-1, '',calc_priority-1, PRIORITY) calc_priority

     , rate_value, eff_date, exp_date, dest_source, route_flag from (
                                                                        SELECT /*+ hash_join(a,b,c,d) join_order(c,b,d,a) */a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
                                                                                                                            a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
                                                                                                                            2 rate_id,  a.feetype charge_item, a.COOPERATIVEPROV object_value,
                                                                                                                            2 tariff_type,row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH ORDER BY a.SETTLERATE) calc_priority, a.PRIORITY,
                                                                                                                            a.SETTLEFEE rate_value, decode(a.ORDERTYPE, '1', decode(floor(a.RULESTARTMONTH / 201501),
                                                                                                                                                                                    0, to_date('201501', 'yyyymm'), decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))),
                                                                                                                                                           decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))) eff_date,
                                                                                                                            decode(floor(a.ruleendmonth / to_char(b.exp_date, 'YYYYMM')), 0, date_sub(date_add(to_date(a.RULEENDMONTH, 'yyyymm'), interval 1 month), interval 1 day), b.exp_date) exp_date, 98 dest_source, 1 route_flag
                                                                        FROM STL_SYNC_RULE a,
                                                                             STL_OFFER_T b,
                                                                             STL_RATE_T c,
                                                                             STL_RULE_ITEM_T d
                                                                        WHERE a.POSPECNUMBER = b.OFFER_CODE
                                                                          AND a.SOSPECNUMBER = b.PRODUCT_CODE
                                                                          AND a.ORDERTYPE = b.ORDER_MODE
                                                                          AND b.RULE_ID = c.RULE_ID
                                                                          AND b.RULE_ID = d.RULE_ID
                                                                          AND a.FEETYPE = d.CHARGE_ITEM
                                                                          AND a.POSPECNUMBER in ('50097','50008','50104','50076','50068')
                                                                          AND a.feetype not in (select charge_item from stludr.stl_onepower_yunduan_config)
                                                                          AND a.SETTLEMEASURE in('2','3')
                                                                          and c.rate_type = 2
                                                                          AND b.DATA_SOURCE = 1);

END;
select 15;
--OnePower云端产品按天结算
BEGIN


     set @iv_Sql :=  'INSERT INTO stl_tariff_parameter_t  '||
      'SELECT /*+ hash_join(a,b,c,d,e)*/seq_tariff_parameter_id.nextval id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,  '||
             'a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,  '||
             '2 rate_id,  a.feetype charge_item, a.COOPERATIVEPROV object_value,   '||
             '2 tariff_type, row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH ORDER BY a.SETTLERATE) calc_priority,   '||
             'round(a.SETTLEFEE * (e.eff_days/to_number(to_char(last_day(to_date('''|| inMonth || ''',''yyyymm'')),''dd'')))) rate_value, decode(a.ORDERTYPE, ''1'', decode(floor(a.RULESTARTMONTH / 201501),  '||
             '0, to_date(''201501'', ''yyyymm''), decode(floor(a.rulestartmonth / to_char(b.eff_date, ''YYYYMM'')), 0, b.eff_date, to_date(a.rulestartmonth, ''YYYYMM''))),  '||
             'decode(floor(a.rulestartmonth / to_char(b.eff_date, ''YYYYMM'')), 0, b.eff_date, to_date(a.rulestartmonth, ''YYYYMM''))) eff_date,   '||
             'decode(floor(a.ruleendmonth / to_char(b.exp_date, ''YYYYMM'')), 0, date_sub(date_add(to_date(a.RULEENDMONTH, ''yyyymm''), interval 1 month), interval 1 day), b.exp_date) exp_date, 98 dest_source, 1 route_flag '||
        'FROM STL_SYNC_RULE a,   '||
             'STL_OFFER_T b,     '||
             'STL_RATE_T c,     '||
             'STL_RULE_ITEM_T d,    '||
             'stludr.sync_onepower_' ||inMonth|| '  e,   '||
             'stludr.stl_onepower_yunduan_config  f '||
       'WHERE a.POSPECNUMBER = b.OFFER_CODE   '||
         'AND a.SOSPECNUMBER = b.PRODUCT_CODE   '||
         'AND a.ORDERTYPE = b.ORDER_MODE    '||
         'AND b.RULE_ID = c.RULE_ID     '||
         'AND b.RULE_ID = d.RULE_ID    '||
         'AND a.FEETYPE = d.CHARGE_ITEM   '||
         'and a.ordertype = e.ordermode    '||
         'and a.customernumber = e.customernumber   '||
         'and a.pospecnumber = e.pospecnumber   '||
         'and a.sospecnumber = e.sospecnumber    '||
         'and a.poid = e.poid    '||
         'and a.soid = e.soid    '||
         'and a.feetype = e.feetype  '||
         'and a.POSPECNUMBER = f.offer_code  '||
         'and a.SOSPECNUMBER = f.product_code   '||
         'and a.feetype = f.charge_item   '||
         'AND a.POSPECNUMBER in (''50097'')   '||
         'AND a.SETTLEMEASURE in(''2'',''3'')   '||
         'and c.rate_type = 2   '||
         'AND b.DATA_SOURCE = 1 ';


SELECT 'iv_Sql='||  @iv_Sql;
PREPARE STMT FROM @iv_Sql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

END;

    --和医疗  叠加包操作
select 15.1;
BEGIN

INSERT INTO stl_tariff_parameter_t
select seq_tariff_parameter_id.nextval id, tt.* from(
                                                        SELECT /*+ hash_join(a,b,c,d)*/  a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
                                                                                         a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
                                                                                         2 rate_id,  a.feetype charge_item, a.COOPERATIVEPROV object_value,
                                                                                         2 tariff_type, row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH ORDER BY a.SETTLERATE) calc_priority,
                                                                fee rate_value, decode(a.ORDERTYPE, '1', decode(floor(a.RULESTARTMONTH / 201501),
                                                                                                                0, to_date('201501', 'yyyymm'), decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))),
                                                                                       decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))) eff_date,
                                                                                         decode(floor(a.ruleendmonth / to_char(b.exp_date, 'YYYYMM')), 0,
                                                                                                date_sub(date_add(to_date(a.RULEENDMONTH, 'yyyymm'), interval 1 month), interval 1 day), b.exp_date) exp_date, 98 dest_source, 1 route_flag
                                                        FROM (select sum(settlefee) fee,customernumber, customername, pospecnumber, pospecname,  sospecnumber, sospecname, poid, soid, settlemeasure, feetype, settlerate,
                                                                     settleitem, cooperativeprov, rulestartmonth, ruleendmonth, ordertype, outprov, priority,
                                                                     sumsettlerate, attrname, settle_unit from STL_SYNC_RULE  where sospecnumber='5007401' and  feetype not in ('1990','1991','2000')
                                                              group by customernumber, customername, pospecnumber, pospecname,  sospecnumber, sospecname, poid, soid, settlemeasure, feetype, settlerate,
                                                                       settleitem, cooperativeprov, rulestartmonth, ruleendmonth, ordertype, outprov, priority,
                                                                       sumsettlerate, attrname, settle_unit) a,
                                                             STL_OFFER_T b,
                                                             STL_RATE_T c,
                                                             STL_RULE_ITEM_T d
                                                        WHERE a.POSPECNUMBER = b.OFFER_CODE
                                                          AND a.SOSPECNUMBER = b.PRODUCT_CODE
                                                          AND a.ORDERTYPE = b.ORDER_MODE
                                                          AND b.RULE_ID = c.RULE_ID
                                                          AND b.RULE_ID = d.RULE_ID
                                                          AND a.FEETYPE = d.CHARGE_ITEM
                                                          AND a.POSPECNUMBER = '50074'
                                                          AND a.SETTLEMEASURE in('2')
                                                          and c.rate_type = 2
                                                          AND b.DATA_SOURCE = 1
                                                        group  by a.POSPECNUMBER, a.SOSPECNUMBER, a.POID, a.SOID, a.ORDERTYPE, b.RULE_ID, a.feetype, a.COOPERATIVEPROV,
                                                                  a.PRIORITY, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH,a.SETTLERATE, b.eff_date, b.exp_date, fee) tt;


END;


select 17;
--插入CP结算规则
BEGIN
      --按商品
INSERT INTO stl_tariff_parameter_t
select seq_tariff_parameter_id.nextval id, offer_code, product_code, poid_inst_id, svc_inst_id, order_mode, rule_id, rate_id, charge_item, object_value, tariff_type,  decode(PRIORITY, NULL,calc_priority-1, '',calc_priority-1, PRIORITY) calc_priority

     , rate_value, eff_date, exp_date, dest_source, route_flag from (
                                                                        SELECT /*+ hash_join(a,b,c,d)*/a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
                                                                                                       a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
                                                                                                       c.RATE_ID rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value,
                                                                                                       a.SETTLEMEASURE tariff_type, row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH
             ORDER BY a.SETTLERATE) calc_priority, a.PRIORITY,
                                                                                                       IFNULL(decode(a.SETTLEMEASURE, '1', round(a.SETTLERATE / a.SUMSETTLERATE, 15), '2',
                                                                                                                     a.SETTLEFEE) ,'') rate_value, to_date(a.RULESTARTMONTH, 'yyyymm') eff_date, date_sub(date_add(to_date(a.RULEENDMONTH, 'yyyymm'), interval 1 month), interval 1 day) exp_date,
                                                                                                       decode(a.attrname, '基地省', 7, 0) dest_source,
                                                                                                       1 route_flag
                                                                        FROM STL_SYNC_RULE a,
                                                                             STL_OFFER_T b,
                                                                             STL_RATE_T c,
                                                                             STL_RULE_ITEM_T d
                                                                        WHERE a.POSPECNUMBER = b.OFFER_CODE
                                                                          AND b.PRODUCT_CODE = -1
                                                                          AND a.ORDERTYPE = b.ORDER_MODE
                                                                          AND b.RULE_ID = c.RULE_ID
                                                                          AND b.RULE_ID = d.RULE_ID
                                                                          AND a.FEETYPE = d.CHARGE_ITEM
                                                                          AND b.DATA_SOURCE = 5);
select 18;
--按产品
INSERT INTO stl_tariff_parameter_t
select seq_tariff_parameter_id.nextval id, offer_code, product_code, poid_inst_id, svc_inst_id, order_mode, rule_id, rate_id, charge_item, object_value, tariff_type,  decode(PRIORITY, NULL,calc_priority-1, '',calc_priority-1, PRIORITY) calc_priority

     , rate_value, eff_date, exp_date, dest_source, route_flag from (
                                                                        SELECT /*+ hash_join(a,b,c,d,e)*/a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
                                                                                                         a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
                                                                                                         c.RATE_ID rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value,
                                                                                                         a.SETTLEMEASURE tariff_type, row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH
             ORDER BY a.SETTLERATE) calc_priority, a.PRIORITY,
                                                                                                         IFNULL(decode(a.SETTLEMEASURE, '1', round(a.SETTLERATE / a.SUMSETTLERATE, 15), '2',
                                                                                                                       a.SETTLEFEE) ,'') rate_value, to_date(a.RULESTARTMONTH, 'yyyymm') eff_date,
                                                                                                         date_sub(date_add(to_date(a.RULEENDMONTH, 'yyyymm'), interval 1 month), interval 1 day) exp_date,
                                                                                                         decode(a.attrname, '基地省', 7, 0) dest_source,
                                                                                                         1 route_flag
                                                                        FROM STL_SYNC_RULE a,
                                                                             STL_OFFER_T b,
                                                                             STL_RATE_T c,
                                                                             STL_RULE_ITEM_T d
                                                                        WHERE a.POSPECNUMBER = b.OFFER_CODE
                                                                          AND a.SOSPECNUMBER = b.PRODUCT_CODE
                                                                          AND a.ORDERTYPE = b.ORDER_MODE
                                                                          AND b.RULE_ID = c.RULE_ID
                                                                          AND b.RULE_ID = d.RULE_ID
                                                                          AND a.FEETYPE = d.CHARGE_ITEM
                                                                          AND b.DATA_SOURCE = 5);
END;

select 19;
--插入智能路由功能费和停机保号费
BEGIN

      --按产品属性填写的比例计算
INSERT INTO stl_tariff_parameter_t
select seq_tariff_parameter_id.nextval id, offer_code, product_code, poid_inst_id, svc_inst_id, order_mode, rule_id, rate_id, charge_item, object_value, tariff_type,  decode(PRIORITY, NULL,calc_priority-1, '',calc_priority-1, PRIORITY) calc_priority

     , rate_value, eff_date, exp_date, dest_source, route_flag from (
                                                                        SELECT /*+ hash_join(a,b,c,d,e)*/a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
                                                                                                         a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
                                                                                                         e.RATE_ID rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value,
                                                                                                         a.SETTLEMEASURE tariff_type, row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH
             ORDER BY a.SETTLERATE) calc_priority, a.PRIORITY,
                                                                                                         decode(a.SETTLEMEASURE, '1', round(a.SETTLERATE / a.SUMSETTLERATE, 27),
                                                                                                                a.SETTLEFEE) rate_value,

                                                                                                         decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM')) eff_date,
                                                                                                         decode(floor(a.ruleendmonth / to_char(b.exp_date, 'YYYYMM')), 0, date_sub(date_add(to_date(a.RULEENDMONTH, 'yyyymm'), interval 1 month), interval 1 day), b.exp_date) exp_date, 0 dest_source, 1 route_flag
                                                                        FROM STL_SYNC_RULE a,
                                                                             STL_OFFER_T b,
                                                                             STL_RATE_T c,
                                                                             STL_RULE_ITEM_T d,
                                                                             STL_TARIFF_RATE_T e
                                                                        WHERE a.POSPECNUMBER = b.OFFER_CODE
                                                                          AND a.SOSPECNUMBER = b.PRODUCT_CODE
                                                                          AND a.ORDERTYPE = b.ORDER_MODE
                                                                          AND b.RULE_ID = c.RULE_ID
                                                                          AND b.RULE_ID = d.RULE_ID
                                                                          AND a.FEETYPE = d.CHARGE_ITEM
                                                                          AND a.SETTLEMEASURE = e.TARIFF_TYPE
                                                                          AND c.RATE_ID = e.RATE_ID
                                                                          AND a.POSPECNUMBER = '50021'
                                                                          and a.sospecnumber = '5002101'
                                                                          and a.feetype = '02'
                                                                          AND b.DATA_SOURCE = 1);
select 20;
--按产品属性填写的接入省平均分配
truncate table stl_ir95_rule;

insert into stl_ir95_rule(order_mode, pospecnumber, sospecnumber, poid, soid, in_prov, rulestartmonth, ruleendmonth, prov_count, settle_rate)
with t1 as (select ordertype, pospecnumber, sospecnumber, poid, soid, cooperativeprov, rulestartmonth, ruleendmonth
            from stl_sync_rule
            where pospecnumber = '50021'),
     t2 as(select /*+ no_merge(t1)*/ soid, count(*) as count from t1 GROUP BY soid)
select /*+ no_merge(t1, t2) hash_join(t1, t2)*/ t1.ordertype, t1.pospecnumber, t1.sospecnumber, t1.poid, t1.soid, t1.cooperativeprov, t1.rulestartmonth, t1.ruleendmonth, t2.count, round(1 / t2.count, 7)
from t1, t2 where t1.soid = t2.soid;

select 21;


--停机保号费
INSERT INTO stl_tariff_parameter_t
SELECT /*+ hash_join(a,b,c,d,e)*/seq_tariff_parameter_id.nextval id, a.POSPECNUMBER offer_code, a.sospecnumber product_code,
                                 a.POID poid_inst_id, a.SOID svc_inst_id, a.order_mode order_mode, b.RULE_ID rule_id,
                                 e.RATE_ID rate_id, '50' charge_item, a.in_prov object_value,
                                 1 tariff_type, 0 calc_priority,
                                 a.settle_rate rate_value,
                                 decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM')) eff_date,
                                 decode(floor(a.ruleendmonth / to_char(b.exp_date, 'YYYYMM')), 0, date_sub(date_add(to_date(a.RULEENDMONTH, 'yyyymm'), interval 1 month), interval 1 day), b.exp_date) exp_date, 0 dest_source, 1 route_flag
FROM STL_IR95_RULE a,
     STL_OFFER_T b,
     STL_RATE_T c,
     STL_RULE_ITEM_T d,
     STL_TARIFF_RATE_T e
WHERE a.POSPECNUMBER = b.OFFER_CODE
  and a.sospecnumber = b.product_code
  AND a.order_mode = b.ORDER_MODE
  AND b.RULE_ID = c.RULE_ID
  AND b.RULE_ID = d.RULE_ID
  and d.charge_item = '50'
  AND e.TARIFF_TYPE = 1
  and e.match_mode = 2
  and a.order_mode = 1
  AND c.RATE_ID = e.RATE_ID
  AND a.POSPECNUMBER = '50021'
  and a.sospecnumber = '5002101'
  AND b.DATA_SOURCE = 1;
select 22;
--功能费
INSERT INTO stl_tariff_parameter_t
SELECT /*+ hash_join(a,b,c,d,e)*/seq_tariff_parameter_id.nextval id, a.POSPECNUMBER offer_code, a.sospecnumber product_code,
                                 a.POID poid_inst_id, a.SOID svc_inst_id, a.order_mode order_mode, b.RULE_ID rule_id,
                                 e.RATE_ID rate_id, '02' charge_item, a.in_prov object_value,
                                 1 tariff_type, 0 calc_priority,
                                 a.settle_rate rate_value,

                                 decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM')) eff_date,
                                 decode(floor(a.ruleendmonth / to_char(b.exp_date, 'YYYYMM')), 0, date_sub(date_add(to_date(a.RULEENDMONTH, 'yyyymm'), interval 1 month), interval 1 day), b.exp_date) exp_date, 0 dest_source, 1 route_flag
FROM STL_IR95_RULE a,
     STL_OFFER_T b,
     STL_RATE_T c,
     STL_RULE_ITEM_T d,
     STL_TARIFF_RATE_T e
WHERE a.POSPECNUMBER = b.OFFER_CODE
  and a.sospecnumber = b.product_code
  AND a.order_mode = b.ORDER_MODE
  AND b.RULE_ID = c.RULE_ID
  AND b.RULE_ID = d.RULE_ID
  and d.charge_item = '02'
  AND e.TARIFF_TYPE = 1
  and e.match_mode = 2
  and a.order_mode = 1
  AND c.RATE_ID = e.RATE_ID
  AND a.POSPECNUMBER = '50021'
  and a.sospecnumber <> '5002101'
  AND b.DATA_SOURCE = 1;

select 23;

INSERT INTO stl_tariff_parameter_t
SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
       a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
       e.RATE_ID rate_id, a.FEETYPE  charge_item, a.COOPERATIVEPROV object_value,
       a.SETTLEMEASURE tariff_type, decode(a.PRIORITY, NULL,
                                           row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH
                                                     ORDER BY a.SETTLERATE) - 1,'',row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH ORDER BY a.SETTLERATE) - 1, a.PRIORITY) calc_priority,
       decode(a.SETTLEMEASURE, '1', round(a.SETTLERATE / a.SUMSETTLERATE, 27), '2',
              a.SETTLEFEE) rate_value, decode(a.ORDERTYPE, '1', decode(floor(a.RULESTARTMONTH / 201501),
                                                                       0, to_date('201501', 'yyyymm'), decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))),
                                              decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))) eff_date,
       decode(floor(a.ruleendmonth / to_char(b.exp_date, 'YYYYMM')), 0, date_sub(date_add(to_date(a.RULEENDMONTH, 'yyyymm'), interval 1 month), interval 1 day), b.exp_date) exp_date, 0 dest_source, 1 route_flag
FROM STL_SYNC_RULE a,
     STL_OFFER_T b,
     STL_RATE_T c,
     STL_RULE_ITEM_T d,
     STL_TARIFF_RATE_T e
WHERE a.POSPECNUMBER = b.OFFER_CODE
  AND a.SOSPECNUMBER = b.PRODUCT_CODE
  AND a.ORDERTYPE = b.ORDER_MODE
  AND b.RULE_ID = c.RULE_ID
  AND b.RULE_ID = d.RULE_ID
  AND a.FEETYPE = d.CHARGE_ITEM
  AND a.SETTLEMEASURE = e.TARIFF_TYPE
  AND c.RATE_ID = e.RATE_ID
  AND a.POSPECNUMBER ='50051'
  AND a.SOSPECNUMBER ='2023999400085449'
  AND a.SETTLEMEASURE = 1
  AND b.DATA_SOURCE = 1;

INSERT INTO stl_tariff_parameter_t
SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
       a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
       e.RATE_ID rate_id, a.FEETYPE  charge_item, a.COOPERATIVEPROV object_value,
       a.SETTLEMEASURE tariff_type, decode(a.PRIORITY, NULL,
                                           row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH
                                                     ORDER BY a.SETTLERATE) - 1, '',row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH ORDER BY a.SETTLERATE) - 1,a.PRIORITY) calc_priority,
       decode(a.SETTLEMEASURE, '1', round(a.SETTLERATE / a.SUMSETTLERATE, 27), '2',
              a.SETTLEFEE) rate_value, decode(a.ORDERTYPE, '1', decode(floor(a.RULESTARTMONTH / 201501),
                                                                       0, to_date('201501', 'yyyymm'), decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))),
                                              decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))) eff_date,
       decode(floor(a.ruleendmonth / to_char(b.exp_date, 'YYYYMM')), 0, date_sub(date_add(to_date(a.RULEENDMONTH, 'yyyymm'), interval 1 month), interval 1 day), b.exp_date) exp_date, 0 dest_source, 1 route_flag
FROM STL_SYNC_RULE a,
     STL_OFFER_T b,
     STL_RATE_T c,
     STL_RULE_ITEM_T d,
     STL_TARIFF_RATE_T e
WHERE a.POSPECNUMBER = b.OFFER_CODE
  AND a.SOSPECNUMBER = b.PRODUCT_CODE
  AND a.ORDERTYPE = b.ORDER_MODE
  AND b.RULE_ID = c.RULE_ID
  AND b.RULE_ID = d.RULE_ID
  AND a.FEETYPE = d.CHARGE_ITEM
  AND a.SETTLEMEASURE = e.TARIFF_TYPE
  AND c.RATE_ID = e.RATE_ID
  AND c.rate_type = e.tariff_type
  AND a.POSPECNUMBER ='50051'
  AND a.SOSPECNUMBER ='2023999400085449'
  AND a.SETTLEMEASURE = 2 AND a.FEETYPE NOT IN ('1850','2164','2165')
  AND b.DATA_SOURCE = 1;
select 24;

BEGIN

          set @iv_Sql :=  'INSERT INTO stl_tariff_parameter_t  '||
                          'SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,  '||
                          'a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,  '||
                          '2 rate_id,  a.feetype charge_item, a.COOPERATIVEPROV object_value,   '||
                          '2 tariff_type, decode(a.PRIORITY, NULL,   '||
                          'row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH  '||
                          'ORDER BY a.SETTLERATE) - 1,'''',row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH ORDER BY a.SETTLERATE) - 1, a.PRIORITY) calc_priority,   '||
                          'round(a.SETTLEFEE * (e.eff_days/to_number(to_char(last_day(to_date('''|| inMonth || ''',''yyyymm'')),''dd'')))) rate_value, decode(a.ORDERTYPE, ''1'', decode(floor(a.RULESTARTMONTH / 201501),  '||
                          '0, to_date(''201501'', ''yyyymm''), decode(floor(a.rulestartmonth / to_char(b.eff_date, ''YYYYMM'')), 0, b.eff_date, to_date(a.rulestartmonth, ''YYYYMM''))),  '||
                          'decode(floor(a.rulestartmonth / to_char(b.eff_date, ''YYYYMM'')), 0, b.eff_date, to_date(a.rulestartmonth, ''YYYYMM''))) eff_date,   '||
                          'decode(floor(a.ruleendmonth / to_char(b.exp_date, ''YYYYMM'')), 0, date_sub(date_add(to_date(a.RULEENDMONTH, ''yyyymm''), interval 1 month), interval 1 day), b.exp_date) exp_date, 0 dest_source, 1 route_flag '||
                          'FROM STL_SYNC_RULE a,   '||
                          'STL_OFFER_T b,     '||
                          'STL_RATE_T c,     '||
                          'STL_RULE_ITEM_T d,    '||
                          'stludr.sync_onepower_' ||inMonth|| ' e,   '||
                          'Stl_tariff_rate_t f '||
                          'WHERE a.POSPECNUMBER = b.OFFER_CODE   '||
                          'AND a.SOSPECNUMBER = b.PRODUCT_CODE   '||
                          'AND a.ORDERTYPE = b.ORDER_MODE    '||
                          'AND b.RULE_ID = c.RULE_ID     '||
                          'AND b.RULE_ID = d.RULE_ID    '||
                          'AND a.FEETYPE = d.CHARGE_ITEM   '||
                          'and a.ordertype = e.ordermode    '||
                          'and a.customernumber = e.customernumber   '||
                          'and a.pospecnumber = e.pospecnumber   '||
                          'and a.sospecnumber = e.sospecnumber    '||
                          'and a.poid = e.poid    '||
                          'and a.soid = e.soid    '||
                          'and a.feetype = e.feetype  '||
                          'AND a.POSPECNUMBER in (''50051'')   '||
                          'AND a.sospecnumber in (''2023999400085449'')   '||
                          'AND a.SETTLEMEASURE in(''2'',''3'')   '||
                          'and c.rate_type = 2   AND c.RATE_ID = f.RATE_ID AND c.rate_type = f.tariff_type  '||
                          'AND b.DATA_SOURCE = 1 ';

SELECT 'iv_Sql='||  @iv_Sql;
PREPARE STMT FROM @iv_Sql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

END;
select 25;



INSERT INTO stl_tariff_parameter_t
SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
       a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
       e.RATE_ID rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value,
       a.SETTLEMEASURE tariff_type, decode(a.PRIORITY, NULL,
                                           row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH
             ORDER BY a.SETTLERATE) - 1,'',row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH ORDER BY a.SETTLERATE) - 1, a.PRIORITY) calc_priority,
       decode(a.SETTLEMEASURE, '1', round(a.SETTLERATE / a.SUMSETTLERATE, 27), '2',
              a.SETTLEFEE) rate_value, decode(a.ORDERTYPE, '1', decode(floor(a.RULESTARTMONTH / 201501),
                                                                       0, to_date('201501', 'yyyymm'), decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))),
                                              decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))) eff_date,
       decode(floor(a.ruleendmonth / to_char(b.exp_date, 'YYYYMM')), 0, add_months(to_date(a.RULEENDMONTH, 'yyyymm'), 1) - 1, b.exp_date) exp_date, 0 dest_source, 1 route_flag
FROM STL_SYNC_RULE a,
     STL_OFFER_T b,
     STL_RATE_T c,
     STL_RULE_ITEM_T d,
     STL_TARIFF_RATE_T e
WHERE a.POSPECNUMBER = b.OFFER_CODE
  AND a.SOSPECNUMBER = b.PRODUCT_CODE
  AND a.ORDERTYPE = b.ORDER_MODE
  AND b.RULE_ID = c.RULE_ID
  AND b.RULE_ID = d.RULE_ID
  AND a.FEETYPE = d.CHARGE_ITEM
  AND a.SETTLEMEASURE = e.TARIFF_TYPE
  AND c.RATE_ID = e.RATE_ID
  AND a.POSPECNUMBER IN ('50120')
  and (a.sospecnumber not in (select product_code from stl_config_db2f where type_nm = 'LP' and column_nm = 'product_code')
    or a.sospecnumber is null)
  AND a.SETTLEMEASURE = 1
  AND b.DATA_SOURCE = 1;

select 26;

END;

-- 按产品（高精度定位），2406费项
		 set @iv_Sql :=  'INSERT INTO STL_TARIFF_PARAMETER_T ' ||
      'SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code, ' ||
             'a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id, ' ||
             'e.RATE_ID rate_id, a.feetype charge_item, a.COOPERATIVEPROV object_value, ' ||
             'a.settlemeasure tariff_type, 0 calc_priority, ' ||
             'round(a.SETTLEFEE * f.amount / decode(a.sospecnumber, ''2022999400074101'', 100, 1)) rate_value , ' ||
             'to_date(a.RULESTARTMONTH, ''yyyymm'') eff_date, add_months(to_date( ' ||
             'a.RULEENDMONTH, ''yyyymm''), 1) - 1 exp_date, 98 dest_source, 1 route_flag ' ||
        'FROM STL_SYNC_RULE a, ' ||
             'STL_OFFER_T b, ' ||
             'STL_RATE_T c, ' ||
             'STL_RULE_ITEM_T d, ' ||
             'STL_TARIFF_RATE_T e, ' ||
             'stludr.SYNC_BL_RULE_' || inMonth || ' f ' ||
       'WHERE a.POSPECNUMBER = b.OFFER_CODE ' ||
         'AND a.sospecnumber = b.PRODUCT_CODE ' ||
         'AND a.ORDERTYPE = b.ORDER_MODE ' ||
         'AND b.RULE_ID = c.RULE_ID ' ||
         'AND b.RULE_ID = d.RULE_ID ' ||
         'AND a.FEETYPE = d.CHARGE_ITEM ' ||
         'AND e.TARIFF_TYPE = 2 ' ||
         'AND c.RATE_ID = e.RATE_ID ' ||
         'AND a.POSPECNUMBER = ''50091'' ' ||
         'AND a.feetype = ''2406'' ' ||
         'AND a.SETTLEMEASURE = ''2'' ' ||
         'AND b.DATA_SOURCE = 1 ' ||
         'and a.soid = f.soid ' ||
         'and f.pospecnumber = ''50091'' ' ||
         'and f.feetype = ''2406''';  
   
        SELECT 'iv_Sql='||  @iv_Sql;
        PREPARE STMT FROM @iv_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;   
				
				-- 按产品（高精度定位），其它费项
				
     set @iv_Sql :=  'INSERT INTO STL_TARIFF_PARAMETER_T ' ||
      'SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code, ' ||
             'a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id, ' ||
             'e.RATE_ID rate_id, a.feetype charge_item, a.COOPERATIVEPROV object_value, ' ||
             'a.settlemeasure tariff_type, 0 calc_priority, ' ||
             'a.SETTLEFEE rate_value, to_date(a.RULESTARTMONTH, ''yyyymm'') eff_date, add_months(to_date( ' ||
             'a.RULEENDMONTH, ''yyyymm''), 1) - 1 exp_date, 98 dest_source, 1 route_flag ' ||
        'FROM STL_SYNC_RULE a, ' ||
             'STL_OFFER_T b, ' ||
             'STL_RATE_T c, ' ||
             'STL_RULE_ITEM_T d, ' ||
             'STL_TARIFF_RATE_T e ' ||
       'WHERE a.POSPECNUMBER = b.OFFER_CODE ' ||
         'AND a.sospecnumber = b.PRODUCT_CODE ' ||
         'AND a.ORDERTYPE = b.ORDER_MODE ' ||
         'AND b.RULE_ID = c.RULE_ID ' ||
         'AND b.RULE_ID = d.RULE_ID ' ||
         'AND a.FEETYPE = d.CHARGE_ITEM ' ||
         'AND e.TARIFF_TYPE = 2 ' ||
         'AND c.RATE_ID = e.RATE_ID ' ||
         'AND a.POSPECNUMBER = ''50091'' ' ||
         'AND a.feetype <> ''2406'' ' ||
         'AND a.SETTLEMEASURE = ''2'' ' ||
         'AND b.DATA_SOURCE = 1';  
   
        SELECT 'iv_Sql='||  @iv_Sql;
        PREPARE STMT FROM @iv_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        select 28;
        UPDATE STL_SYNC_RULE r SET r.SETTLEFEE = (SELECT totalfee FROM (SELECT a.soid,a.fee_type,a.multi * u.UNIT_PRICE  AS totalFee FROM (SELECT
               a.*,
               b.attr_name,
               b.attr_value service_enum,
               c.plan_type service_name,
               c.fee_type
           FROM
               (
                   SELECT
                       ATTR_GROUP_NUM,soid,
                       MAX(cnt) * MAX(yearCnt) AS multi
                   FROM
                       (
                           SELECT
                               ATTR_GROUP_NUM,
                               soid,
                               CASE
                                   WHEN attr_id = '6000602004' THEN attr_value
                                   ELSE NULL
                                   END AS cnt,
                               CASE
                                   WHEN attr_id = '6000602005' THEN attr_value
                                   ELSE NULL
                                   END AS yearCnt
                           FROM
                               STL_SYNC_ATTR
                           WHERE
                               POSPECNUMBER = '60006'
                             AND SOSPECNUMBER = '2024999480005517'
                       )
                   GROUP BY
                       ATTR_GROUP_NUM,soid) a ,
               STL_SYNC_ATTR b,
               stludr.RVL_ZXWS_CONFIG c
           WHERE
               a.ATTR_GROUP_NUM = b.ATTR_GROUP_NUM
             AND b.attr_value = c.plan_id
             and a.soid=b.soid
             AND b.attr_id = '6000602011'
             AND c.offer_code = '60006'
             AND c.PRODUCT_CODE = '2024999480005517') a
              LEFT JOIN  STL_CONFIG_UNIT u ON a.fee_type = u.CHARGE_ITEM
            AND  u.OFFER_CODE ='60006' AND u.PRODUCT_CODE = '2024999480005517') a  WHERE a.soid=r.soid AND a.fee_type=r.FEETYPE
        ) WHERE r.POSPECNUMBER ='60006' AND SOSPECNUMBER ='2024999480005517';

        select 29;
        INSERT INTO STL_TARIFF_PARAMETER_T
        SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
               a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
               2 rate_id, a.FEETYPE charge_item, 'ZW' object_value,
               2 tariff_type, decode(a.PRIORITY, NULL,
                                     row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH
                                         ORDER BY a.SETTLERATE) - 1, a.PRIORITY) calc_priority,
               a.SETTLEFEE rate_value, decode(a.ORDERTYPE, '1', decode(floor(a.RULESTARTMONTH / 201501),
                                                                       0, to_date('201501', 'yyyymm'), decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))),
                                              decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))) eff_date,
               decode(floor(a.ruleendmonth / to_char(b.exp_date, 'YYYYMM')), 0, add_months(to_date(a.RULEENDMONTH, 'yyyymm'), 1) - 1, b.exp_date) exp_date, 5 dest_source, 1 route_flag
        FROM STL_SYNC_RULE a,
             STL_OFFER_T b,
             STL_RATE_T c,
             STL_RULE_ITEM_T d
        WHERE a.POSPECNUMBER = b.OFFER_CODE
          AND a.SOSPECNUMBER = b.PRODUCT_CODE
          AND a.ORDERTYPE = b.ORDER_MODE
          AND b.RULE_ID = c.RULE_ID
          AND b.RULE_ID = d.RULE_ID
          AND a.FEETYPE = d.CHARGE_ITEM
          AND a.POSPECNUMBER = '60006'
          AND a.SOSPECNUMBER ='2024999480005517'
          AND a.SETTLEMEASURE = 3
          and c.rate_type = 2
          AND b.DATA_SOURCE = 3;

COMMIT;


select 30;
SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || outReturn;

END;

END//

DELIMITER ;
