/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：流量统付规则计算-repart规则计算
**/
use stlusers;
DROP PROCEDURE IF EXISTS stlusers.P_SETTLE_RULE_PROC_REPART_GPRSDOM;

DELIMITER ;;
CREATE or replace DEFINER="stlusers"@"10.%" PROCEDURE `stlusers`.P_SETTLE_RULE_PROC_REPART_GPRSDOM(
        inMonth in VARCHAR2,
        inBatch IN VARCHAR2,
        flag_version IN VARCHAR2,
        reserve1 IN VARCHAR2,
        reserve2 IN VARCHAR2,
        proc_out OUT VARCHAR2,
        outSysError OUT VARCHAR2(1000),
        outReturn OUT NUMBER(4),
        outBL OUT VARCHAR2,
        outAR OUT VARCHAR2)
AS
    iv_Gprs_Main VARCHAR2(10);
    iv_Gprs_Supp VARCHAR2(10);
    iv_Gprs_Supp1 varchar2(10);

    P_ERRCODE   VARCHAR2(16);
    P_ERRMSG    VARCHAR2(1024);

    v_proc_name   VARCHAR2(36) := 'P_SETTLE_RULE_PROC_REPART_GPRSDOM';
    day_start int;
    day_end int;
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
GET DIAGNOSTICS CONDITION 1 P_ERRCODE = RETURNED_SQLSTATE, P_ERRMSG = MESSAGE_TEXT;
outSysError := substr(P_ERRMSG, 1, 1000);
        outReturn  := -1;
ROLLBACK;


select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg ;
call LOG_PROCEDURES(outSysError, v_proc_name);
SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = outSysError;

END;

BEGIN

         /*优化内容： call P_SETTLE_RULE_PROC_REPART_PARAMETER_WL2('202307','2','1','','','',@p1,@p2,@p3,@p4);
          1、delete STL_REPART_PARAMETER_T 修改为truncate partition
          2、取消STL_GPRSDOM_RULE的插入排序
        alter table stludr.UR_GPRSDOM_202307_T add index idx_provcode(provcode);
          */
        outSysError := 'OK';
        outReturn := 0;

select concat('1_', now());

--开始时间
SET @P_TIME_GPRSDOM := SYSDATE;
call LOG_PROCEDURES('开始执行', v_proc_name);

select dictvalue into iv_Gprs_Main from stludr.stl_conf_dict where item = 'gprsdom_main';
select dictvalue into iv_Gprs_Supp from stludr.stl_conf_dict where item = 'gprsdom_supp';
select dictvalue into iv_Gprs_Supp1 from stludr.stl_conf_dict where item = 'gprsdom_supp_1';


outReturn := 0;
outSysError := 'OK';

 --流量统付（企业流量池）
TRUNCATE TABLE STL_GPRSDOM_RULE;

      --注意此处与最初存储过程业务不符，按这个逻辑计算ratio总是100，开发注意确认。
           --临时表使用前清空。
call LOG_PROCEDURES('分区处理UR_GPRSDOM大表数据', v_proc_name);
truncate table stl_gprsdom_rule_tmp;
--临时表stl_gprsdom_rule_tmp用于存储按省份统计的数据
-- 分区处理UR_GPRSDOM大表数据
for i in 0 .. 10 loop
    day_start := 3*i+1;
    day_end := 3*i+3;
    
    -- 简化动态SQL构建
    set @vSql := 'insert into stl_gprsdom_rule_tmp '||
                       'SELECT null,ACCT_MONTH,EC_CODE,OFFER_CODE,PRODUCT_CODE,OFFER_ORDER_ID,PRODUCT_ORDER_ID, PROVCODE, sum(DATA_VALUE) prov_amount '||
                       'FROM stludr.UR_GPRSDOM_' || inMonth || '_T '||
                       'WHERE partition_id_day between ' || day_start || ' and ' || day_end || ' '||
                       'GROUP BY  ACCT_MONTH,EC_CODE,OFFER_CODE,PRODUCT_CODE,OFFER_ORDER_ID,PRODUCT_ORDER_ID, PROVCODE';
    select concat('day_start:',day_start,'-day_end:',day_end);
    PREPARE STMT FROM @vSql;
    EXECUTE STMT;
    DEALLOCATE PREPARE STMT;
end loop;
call LOG_PROCEDURES('统计UR_GPRSDOM_yyyymm话单到stl_gprsdom_rule_tmp', v_proc_name);

-- 分批插入完成后，进行最终汇总（参考NEWRCS处理方式）
CREATE TEMPORARY TABLE stl_gprsdom_rule_final AS 
SELECT ACCT_MONTH, EC_CODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID, PROVCODE, sum(prov_amount) prov_amount 
FROM stl_gprsdom_rule_tmp 
GROUP BY ACCT_MONTH, EC_CODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID, PROVCODE;

-- 清空原临时表并插入汇总后的数据
TRUNCATE TABLE stl_gprsdom_rule_tmp;
INSERT INTO stl_gprsdom_rule_tmp (ACCT_MONTH, EC_CODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID, PROVCODE, prov_amount) 
SELECT * FROM stl_gprsdom_rule_final;

-- 删除临时汇总表
DROP TEMPORARY TABLE stl_gprsdom_rule_final;

--使用临时表stl_gprsdom_rule_tmp 的数据汇总全部的数量并计算各省占全部的比例。
call LOG_PROCEDURES('使用临时表stl_gprsdom_rule_tmp 的数据汇总全部的数量并计算各省占全部的比例', v_proc_name);

INSERT INTO STL_GPRSDOM_RULE 
(ORGMONTH, CUSTOMERNUMBER, POSPECNUMBER, SOSPECNUMBER, 
 POID, SOID, MEMPROV_CD, AMOUNT, TOTAL_AMOUNT, RATIO) 
SELECT a.ACCT_MONTH, 
       a.EC_CODE, 
       a.OFFER_CODE, 
       a.PRODUCT_CODE, 
       a.OFFER_ORDER_ID, 
       a.PRODUCT_ORDER_ID, 
       a.PROVCODE, 
       a.prov_amount, 
       b.total_amount, 
       round(a.prov_amount / b.total_amount * 100, 7) ratio 
FROM (SELECT ACCT_MONTH, 
             EC_CODE, 
             OFFER_CODE, 
             PRODUCT_CODE, 
             OFFER_ORDER_ID, 
             PRODUCT_ORDER_ID, 
             PROVCODE, 
             prov_amount 
      FROM  stl_gprsdom_rule_tmp) a, 
     (SELECT ACCT_MONTH, 
             EC_CODE, 
             OFFER_CODE, 
             PRODUCT_CODE, 
             OFFER_ORDER_ID, 
             PRODUCT_ORDER_ID, 
             sum(prov_amount) total_amount 
      FROM stl_gprsdom_rule_tmp
      GROUP BY ACCT_MONTH, EC_CODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID) b 
WHERE a.ACCT_MONTH = b.ACCT_MONTH 
  AND a.OFFER_ORDER_ID = b.OFFER_ORDER_ID 
  AND a.PRODUCT_ORDER_ID = b.PRODUCT_ORDER_ID 
  AND b.total_amount <> 0;

select concat('2_', now());

call LOG_PROCEDURES('STL_GPRSDOM_RULE 给成员省97%', v_proc_name);
select concat('3_', now());
--结给成员归属省97%
INSERT INTO STL_REPART_PARAMETER_T
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*, ft.FEETYPE, 0 dest_source, 1 route_flag
FROM
    (SELECT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
            a.POID poid_inst_id, a.SOID svc_inst_id, d.PROD_ORDER_MODE order_mode, b.RULE_ID rule_id,
            c.RATE_ID rate_id, row_number() over(PARTITION BY a.SOID ORDER BY to_number(a.RATIO))
                  calc_priority, a.MEMPROV_CD object_value, e.tariff_type tariff_type,
            round(a.RATIO * decode(d.prod_order_mode,'1',iv_Gprs_Supp1,'3',iv_Gprs_Supp) / 100,9) rate_value
         /*a.RATIO * iv_Gprs_Supp / 100 rate_value*/, to_date(a.ORGMONTH, 'yyyymm') eff_date, add_months(to_date(
                                                                                                                 a.ORGMONTH, 'yyyymm'), 1) - 1 exp_date, a.ORGMONTH acct_month
     FROM STL_GPRSDOM_RULE a,
          STL_OFFER_T b,
          STL_RATE_T c,
          STL_SERV_BIZ_CODE d,
          STL_REPART_RATE_T e
     WHERE a.POSPECNUMBER = b.OFFER_CODE
       AND b.PRODUCT_CODE = -1
       AND d.PROD_ORDER_MODE = b.ORDER_MODE
       AND b.RULE_ID = c.RULE_ID
       AND inMonth BETWEEN to_char(d.EFFECTIVE_DATE, 'yyyymm')
         AND to_char(d.EXPIRY_DATE, 'yyyymm')
       AND a.SOID = d.ORDER_ID
       AND a.SOID IN (SELECT PRODUCTID FROM STL_PRODUCT_RATERPLAN pc WHERE
         pc.RATERPLAN IN ('1', '2', '3', '5'))
       AND b.DATA_SOURCE = 1
       AND c.RATE_ID = e.RATE_ID
       AND a.ORGMONTH = inMonth) data,
    (SELECT '02' feetype FROM DUAL UNION SELECT '16' FROM DUAL
     UNION SELECT '18' FROM DUAL UNION SELECT '1006' FROM DUAL
     UNION SELECT '52' feetype FROM DUAL UNION SELECT '66' FROM DUAL
     UNION SELECT '68' FROM DUAL UNION SELECT '5006' FROM DUAL) ft;
select concat('4_', now());

call LOG_PROCEDURES('STL_GPRSDOM_RULE 给主办省3%', v_proc_name);
--结给主办省3%  object_value  null-> object_value 2021.1.4
INSERT INTO STL_REPART_PARAMETER_T
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*, ft.FEETYPE, 0 dest_source, 1 route_flag
FROM
    (SELECT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
            a.POID poid_inst_id, a.SOID svc_inst_id, d.PROD_ORDER_MODE order_mode, b.RULE_ID rule_id,
            c.RATE_ID rate_id, 0 calc_priority, '${EC_PROV_CODE_07}' object_value, e.tariff_type tariff_type,
            iv_Gprs_Main rate_value, to_date(a.ORGMONTH, 'yyyymm') eff_date, add_months(to_date(
                                                                                                a.ORGMONTH, 'yyyymm'), 1) - 1 exp_date, a.ORGMONTH acct_month
     FROM (SELECT DISTINCT ORGMONTH, CUSTOMERNUMBER, POSPECNUMBER, SOSPECNUMBER, POID, SOID FROM STL_GPRSDOM_RULE) a,
          STL_OFFER_T b,
          STL_RATE_T c,
          STL_SERV_BIZ_CODE d,
          STL_REPART_RATE_T e
     WHERE a.POSPECNUMBER = b.OFFER_CODE
       AND b.PRODUCT_CODE = -1
       AND d.PROD_ORDER_MODE = '3' and d.prod_order_mode = b.ORDER_MODE
       AND b.RULE_ID = c.RULE_ID
       AND inMonth BETWEEN to_char(d.EFFECTIVE_DATE, 'yyyymm')
         AND to_char(d.EXPIRY_DATE, 'yyyymm')
       AND a.SOID = d.ORDER_ID
       AND a.SOID IN (SELECT PRODUCTID FROM STL_PRODUCT_RATERPLAN pc WHERE
         pc.RATERPLAN IN ('1', '2', '3', '5'))
       AND b.DATA_SOURCE = 1
       AND c.RATE_ID = e.RATE_ID
       AND a.ORGMONTH = inMonth) data,
    (SELECT '02' feetype FROM DUAL UNION SELECT '16' FROM DUAL
     UNION SELECT '18' FROM DUAL UNION SELECT '1006' FROM DUAL
     UNION
     SELECT '52' feetype FROM DUAL UNION SELECT '66' FROM DUAL
     UNION SELECT '68' FROM DUAL UNION SELECT '5006' FROM DUAL) ft;


select concat('5_', now());
call LOG_PROCEDURES('5.流量统付（个人流量包-产品)处理-1', v_proc_name);
--流量统付（个人流量包-产品）
TRUNCATE TABLE STL_GPRSDOM_PER_RULE;


            -- 由于表名包含变量，保留动态SQL但简化构建方式
            set @vSql := CONCAT('INSERT INTO STL_GPRSDOM_PER_RULE ',
                           '(ORGMONTH, CUSTOMERNUMBER, POSPECNUMBER, SOSPECNUMBER, POID, SOID, ',
                           'MEMPROV_CD, FEE, TOTAL_FEE, RATIO) ',
                           'SELECT a.orgmonth, a.customernumber, a.pospecnumber, a.sospecnumber, a.poid, a.soid, ',
                           'a.memprov_cd, a.orgfee, b.total_fee, ',
                           'round(a.orgfee / decode(b.total_fee, 0, 1, b.total_fee) * 100, 7) ratio ',
                           'FROM ',
                          '(SELECT ORGMONTH, CUSTOMERNUMBER, POSPECNUMBER, SOSPECNUMBER, POID, SOID, ',
                                 'PROV_CD memprov_cd, sum(AMOUNT) orgfee ',
                            'FROM stludr.SYNC_BL_RULE_', inMonth, ' ',
                           'WHERE STATUS = ''0'' ',
                             'AND SOSPECNUMBER IN (SELECT PRODUCT_CODE FROM STL_BUSINESS_TYPE WHERE BIZ_TYPE = ''GPRS'') ',
                             'AND DATASOURCE = ''BL'' AND ORGMONTH = ', inMonth, ' ',
                           'GROUP BY ORGMONTH, CUSTOMERNUMBER, POSPECNUMBER, SOSPECNUMBER, POID, SOID, PROV_CD) a, ',
                          '(SELECT ORGMONTH, CUSTOMERNUMBER, SOSPECNUMBER, POID, SOID, sum(AMOUNT) total_fee ',
                            'FROM stludr.SYNC_BL_RULE_', inMonth, ' ',
                           'WHERE STATUS = ''0'' ',
                             'AND SOSPECNUMBER IN (SELECT PRODUCT_CODE FROM STL_BUSINESS_TYPE WHERE BIZ_TYPE = ''GPRS'') ',
                             'AND DATASOURCE = ''BL'' AND ORGMONTH = ', inMonth, ' ',
                           'GROUP BY ORGMONTH, CUSTOMERNUMBER, SOSPECNUMBER, POID, SOID) b ',
                     'WHERE a.poid = b.poid AND a.soid = b.soid ',
                     'ORDER BY orgmonth, soid');
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
            outSysError := 'OK';
select concat('6_', now());
call LOG_PROCEDURES('6.流量统付（个人流量包-产品)处理-2', v_proc_name);

--给成员归属省97%
INSERT INTO STL_REPART_PARAMETER_T
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*, ft.FEETYPE, 0 dest_source, 1 route_flag
FROM
    (SELECT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
            a.POID poid_inst_id, a.SOID svc_inst_id, d.PROD_ORDER_MODE order_mode, b.RULE_ID rule_id,
            c.RATE_ID rate_id, row_number() over(PARTITION BY a.SOID ORDER BY to_number(a.RATIO))
                 calc_priority, a.MEMPROV_CD object_value, e.Tariff_Type tariff_type,
            round(a.RATIO * decode(d.prod_order_mode,'1',iv_Gprs_Supp1,'3',iv_Gprs_Supp) / 100, 9) rate_value
         /*a.RATIO * iv_Gprs_Supp / 100 rate_value*/, to_date(a.ORGMONTH, 'yyyymm') eff_date, add_months(to_date(
                                                                                                                 a.ORGMONTH, 'yyyymm'), 1) - 1 exp_date, a.ORGMONTH acct_month
     FROM STL_GPRSDOM_PER_RULE a,
          STL_OFFER_T b,
          STL_RATE_T c,
          STL_SERV_BIZ_CODE d,
          STL_REPART_RATE_T e
     WHERE a.POSPECNUMBER = b.OFFER_CODE
       AND b.PRODUCT_CODE = -1
       AND d.PROD_ORDER_MODE = b.ORDER_MODE
       AND b.RULE_ID = c.RULE_ID
       AND a.SOID = d.ORDER_ID
       AND inMonth BETWEEN to_char(d.EFFECTIVE_DATE, 'yyyymm')
         AND to_char(d.EXPIRY_DATE, 'yyyymm')
       AND a.SOID IN (SELECT PRODUCTID FROM STL_PRODUCT_RATERPLAN pc WHERE
         pc.RATERPLAN IN ('4'))
       AND b.DATA_SOURCE = 1
       AND c.RATE_ID = e.RATE_ID
       AND a.ORGMONTH = inMonth) data,
    (SELECT '02' feetype FROM DUAL
     UNION SELECT '1006' FROM DUAL
     UNION SELECT '1035' FROM DUAL
     UNION
     SELECT '52' FROM DUAL
     UNION SELECT '5006' FROM DUAL
     UNION SELECT '5035' FROM DUAL) ft;
select concat('7_', now());
call LOG_PROCEDURES('7.流量统付（个人流量包-产品)处理-3', v_proc_name);

-- 给主办省3%   null->'${EC_PROV_CODE_07}'
INSERT INTO STL_REPART_PARAMETER_T
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*, ft.FEETYPE, 0 dest_source, 1 route_flag
FROM
    (SELECT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
            a.POID poid_inst_id, a.SOID svc_inst_id, d.PROD_ORDER_MODE order_mode, b.RULE_ID rule_id,
            c.RATE_ID rate_id, 0 calc_priority, '${EC_PROV_CODE_07}' object_value, e.tariff_type tariff_type,
            iv_Gprs_Main rate_value, to_date(a.ORGMONTH, 'yyyymm') eff_date, add_months(to_date(
                                                                                                a.ORGMONTH, 'yyyymm'), 1) - 1 exp_date, a.ORGMONTH acct_month
     FROM (SELECT DISTINCT ORGMONTH, CUSTOMERNUMBER, POSPECNUMBER, SOSPECNUMBER, POID, SOID FROM STL_GPRSDOM_PER_RULE) a,
          STL_OFFER_T b,
          STL_RATE_T c,
          STL_SERV_BIZ_CODE d,
          STL_REPART_RATE_T e
     WHERE a.POSPECNUMBER = b.OFFER_CODE
       AND b.PRODUCT_CODE = -1
       AND d.PROD_ORDER_MODE = '3' and d.prod_order_mode = b.ORDER_MODE
       AND b.RULE_ID = c.RULE_ID
       AND a.SOID = d.ORDER_ID
       AND inMonth BETWEEN to_char(d.EFFECTIVE_DATE, 'yyyymm')
         AND to_char(d.EXPIRY_DATE, 'yyyymm')
       AND a.SOID IN (SELECT PRODUCTID FROM STL_PRODUCT_RATERPLAN pc WHERE
         pc.RATERPLAN IN ('4'))
       AND b.DATA_SOURCE = 1
       AND c.RATE_ID = e.RATE_ID
       AND a.ORGMONTH = inMonth) data,
    (SELECT '02' feetype FROM DUAL
     UNION SELECT '1006' FROM DUAL
     UNION SELECT '1035' FROM DUAL
     UNION
     SELECT '52' FROM DUAL
     UNION SELECT '5006' FROM DUAL
     UNION SELECT '5035' FROM DUAL) ft;
select concat('8_', now());
call LOG_PROCEDURES('8.流量统付（个人流量包-产品)处理-4', v_proc_name);

-- 流量统付（个人流量包-成员） null ->'${MEM_PROV_15}'
--结给成员的97%
INSERT INTO STL_REPART_PARAMETER_T
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*, ft.FEETYPE, 0 dest_source, 1 route_flag
FROM
    (SELECT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
            a.POID poid_inst_id, a.SOID svc_inst_id, d.PROD_ORDER_MODE order_mode, b.RULE_ID rule_id,
            c.RATE_ID rate_id, 1 calc_priority,
            '${MEM_PROV_15}' object_value, e.tariff_type tariff_type,
            decode(d.prod_order_mode,'1',iv_Gprs_Supp1,'3',iv_Gprs_Supp) rate_value, to_date(a.ORGMONTH, 'yyyymm') eff_date, add_months(to_date(
                                                                                                                                                a.ORGMONTH, 'yyyymm'), 1) - 1 exp_date, a.ORGMONTH acct_month
     FROM (SELECT DISTINCT POSPECNUMBER, SOSPECNUMBER, POID, SOID, ORGMONTH
           FROM STL_GPRSDOM_PER_RULE) a,
          STL_OFFER_T b,
          STL_RATE_T c,
          STL_SERV_BIZ_CODE d,
          STL_REPART_RATE_T e
     WHERE a.POSPECNUMBER = b.OFFER_CODE
       AND b.PRODUCT_CODE = -1
       AND d.PROD_ORDER_MODE = b.ORDER_MODE
       AND b.RULE_ID = c.RULE_ID
       AND a.SOID = d.ORDER_ID
       AND inMonth BETWEEN to_char(d.EFFECTIVE_DATE, 'yyyymm')
         AND to_char(d.EXPIRY_DATE, 'yyyymm')
       AND a.SOID IN (SELECT PRODUCTID FROM STL_PRODUCT_RATERPLAN pc WHERE
         pc.RATERPLAN IN ('4'))
       AND b.DATA_SOURCE = 1
       AND c.RATE_ID = e.RATE_ID
       AND a.ORGMONTH = inMonth) data,
    (SELECT '04' feetype FROM DUAL
     UNION SELECT '18' FROM DUAL
     UNION
     SELECT '54' FROM DUAL
     UNION SELECT '68' FROM DUAL) ft;
select concat('9_', now());
call LOG_PROCEDURES('9.流量统付（个人流量包-产品)处理-5', v_proc_name);

-- 结给主办省的3% NULL -> ${EC_PROV_CODE_07}
INSERT INTO STL_REPART_PARAMETER_T
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*, ft.FEETYPE, 0 dest_source, 1 route_flag
FROM
    (SELECT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
            a.POID poid_inst_id, a.SOID svc_inst_id, d.PROD_ORDER_MODE order_mode, b.RULE_ID rule_id,
            c.RATE_ID rate_id, 0 calc_priority,
            '${EC_PROV_CODE_07}' object_value, e.tariff_type tariff_type,
            iv_Gprs_Main rate_value, to_date(a.ORGMONTH, 'yyyymm') eff_date, add_months(to_date(
                                                                                                a.ORGMONTH, 'yyyymm'), 1) - 1 exp_date, a.ORGMONTH acct_month
     FROM (SELECT DISTINCT POSPECNUMBER, SOSPECNUMBER, POID, SOID, ORGMONTH
           FROM STL_GPRSDOM_PER_RULE) a,
          STL_OFFER_T b,
          STL_RATE_T c,
          STL_SERV_BIZ_CODE d,
          STL_REPART_RATE_T e
     WHERE a.POSPECNUMBER = b.OFFER_CODE
       AND b.PRODUCT_CODE = -1
       AND d.PROD_ORDER_MODE = '3' and d.prod_order_mode = b.ORDER_MODE
       AND b.RULE_ID = c.RULE_ID
       AND a.SOID = d.ORDER_ID
       AND inMonth BETWEEN to_char(d.EFFECTIVE_DATE, 'yyyymm')
         AND to_char(d.EXPIRY_DATE, 'yyyymm')
       AND a.SOID IN (SELECT PRODUCTID FROM STL_PRODUCT_RATERPLAN pc WHERE
         pc.RATERPLAN IN ('4'))
       AND b.DATA_SOURCE = 1
       AND c.RATE_ID = e.RATE_ID
       AND a.ORGMONTH = inMonth) data,
    (SELECT '04' feetype FROM DUAL
     UNION SELECT '18' FROM DUAL
     UNION
     SELECT '54' FROM DUAL
     UNION SELECT '68' FROM DUAL) ft;

select concat('10_', now());

COMMIT;

SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
call LOG_PROCEDURES('completed successfully. 执行完成总耗时秒='||TIMESTAMPDIFF(SECOND, @P_TIME_GPRSDOM, NOW()), v_proc_name);

END;

END;;
DELIMITER ;
