PROCEDURE TARIFF_PARAMETER
  (
    inMonth          IN   VARCHAR2,
    outSysError      OUT  VARCHAR2,
    outReturn        OUT  NUMBER
  )
  IS
    iv_Sql VARCHAR2(2000);
    iv_Seq NUMBER(12);
    iv_Poid VARCHAR2(20);
    iv_Soid VARCHAR2(20);
    iv_Feetype VARCHAR2(4);
    iv_RuleStartMonth CHAR(6);
    iv_RuleEndMonth CHAR(6);
    dyn_Select VARCHAR2(1024);
    iv_test varchar2(5000);

  BEGIN
    outSysError := '';
    outReturn := 0;

    LOG('Beginning to insert table STL_TARIFF_PARAMETER_T.', 'TARIFF');

    --清空原表
    iv_Sql := 'TRUNCATE TABLE STL_TARIFF_PARAMETER_T';
    EXECUTE IMMEDIATE iv_Sql;
    COMMIT;

    LOG('Common products(all services) begins.', 'TARIFF');
    BEGIN
      --按商品（不包含全网IDC-1010403），按比例
      /*INSERT INTO STL_TARIFF_PARAMETER_T
      SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
             a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
             e.RATE_ID rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value,
             a.SETTLEMEASURE tariff_type, decode(a.PRIORITY, NULL,
             row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH
             ORDER BY a.SETTLERATE) - 1, a.PRIORITY) calc_priority,
             decode(a.SETTLEMEASURE, '1', round(a.SETTLERATE / a.SUMSETTLERATE, 27), '2',
             a.SETTLEFEE) rate_value, decode(a.ORDERTYPE, '1', decode(floor(a.RULESTARTMONTH / 201501),
             0, to_date('201501', 'yyyymm'), to_date(a.RULESTARTMONTH, 'yyyymm')),
             to_date(a.RULESTARTMONTH, 'yyyymm')) eff_date, add_months(to_date(
             a.RULEENDMONTH, 'yyyymm'), 1) - 1 exp_date,
             decode(a.sospecnumber, '50001', 1, 0) dest_source, 1 route_flag
        FROM STL_SYNC_RULE a,
             STL_OFFER_T b,
             STL_RATE_T c,
             STL_RULE_ITEM_T d,
             STL_TARIFF_RATE_T e
       WHERE a.POSPECNUMBER = b.OFFER_CODE
         AND b.PRODUCT_CODE = -1
         AND a.ORDERTYPE = b.ORDER_MODE
         AND b.RULE_ID = c.RULE_ID
         AND b.RULE_ID = d.RULE_ID
         AND a.FEETYPE = d.CHARGE_ITEM
         AND a.SETTLEMEASURE = e.TARIFF_TYPE
         AND c.RATE_ID = e.RATE_ID
         AND a.POSPECNUMBER NOT IN ('1010403', '1010402')
         AND a.SETTLEMEASURE = 1
         AND b.DATA_SOURCE = 1;*/

      INSERT INTO STL_TARIFF_PARAMETER_T
      SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
             a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
             e.RATE_ID rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value,
             a.SETTLEMEASURE tariff_type, decode(a.PRIORITY, NULL,
             row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH
             ORDER BY a.SETTLERATE) - 1, a.PRIORITY) calc_priority,
             decode(a.SETTLEMEASURE, '1', round(a.SETTLERATE / a.SUMSETTLERATE, 27), '2',
             a.SETTLEFEE) rate_value, decode(a.ORDERTYPE, '1', decode(floor(a.RULESTARTMONTH / 201501),
             0, to_date('201501', 'yyyymm'), decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))),
             decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))) eff_date,
             decode(floor(a.ruleendmonth / to_char(b.exp_date, 'YYYYMM')), 0, add_months(to_date(a.RULEENDMONTH, 'yyyymm'), 1) - 1, b.exp_date) exp_date,
             decode(a.sospecnumber, '50001', 1, 0) dest_source, 1 route_flag
        FROM STL_SYNC_RULE a,
             STL_OFFER_T b,
             STL_RATE_T c,
             STL_RULE_ITEM_T d,
             STL_TARIFF_RATE_T e
       WHERE a.POSPECNUMBER = b.OFFER_CODE
         AND b.PRODUCT_CODE = -1
         AND a.ORDERTYPE = b.ORDER_MODE
         AND b.RULE_ID = c.RULE_ID
         AND b.RULE_ID = d.RULE_ID
         AND a.FEETYPE = d.CHARGE_ITEM
         AND a.SETTLEMEASURE = e.TARIFF_TYPE
         AND c.RATE_ID = e.RATE_ID
         AND a.POSPECNUMBER NOT IN ('1010403', '1010402', '50021')
         AND a.SETTLEMEASURE = 1
         AND b.DATA_SOURCE = 1;
      LOG('Common products(all services) ends.', 'TARIFF');

      LOG('Common products(specific services) begins.', 'TARIFF');
      --按产品（不包含全网IDC-1010403，移动云SaaS产品），按比例
      /*INSERT INTO STL_TARIFF_PARAMETER_T
      SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
             a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
             e.RATE_ID rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value,
             a.SETTLEMEASURE tariff_type, decode(a.PRIORITY, NULL,
             row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH
             ORDER BY a.SETTLERATE) - 1, a.PRIORITY) calc_priority,
             decode(a.SETTLEMEASURE, '1', round(a.SETTLERATE / a.SUMSETTLERATE, 27), '2',
             a.SETTLEFEE) rate_value, decode(a.ORDERTYPE, '1', decode(floor(a.RULESTARTMONTH / 201501),
             0, to_date('201501', 'yyyymm'), to_date(a.RULESTARTMONTH, 'yyyymm')),
             to_date(a.RULESTARTMONTH, 'yyyymm')) eff_date, add_months(to_date(
             a.RULEENDMONTH, 'yyyymm'), 1) - 1 exp_date, 0 dest_source, 1 route_flag
        FROM STL_SYNC_RULE a,
             STL_OFFER_T b,
             STL_RATE_T c,
             STL_RULE_ITEM_T d,
             STL_TARIFF_RATE_T e
       WHERE a.POSPECNUMBER = b.OFFER_CODE
         AND a.SOSPECNUMBER = b.PRODUCT_CODE
         AND a.ORDERTYPE = b.ORDER_MODE
         AND b.RULE_ID = c.RULE_ID
         AND b.RULE_ID = d.RULE_ID
         AND a.FEETYPE = d.CHARGE_ITEM
         AND a.SETTLEMEASURE = e.TARIFF_TYPE
         AND c.RATE_ID = e.RATE_ID
         AND a.POSPECNUMBER NOT IN ('1010403', '1010800')
         and (a.sospecnumber not in (select product_code from stl_config_db2f where type_nm = 'LP')
              or a.sospecnumber is null)
         AND a.SETTLEMEASURE = 1
         AND b.DATA_SOURCE = 1;*/

      INSERT INTO STL_TARIFF_PARAMETER_T
      SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
             a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
             e.RATE_ID rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value,
             a.SETTLEMEASURE tariff_type, decode(a.PRIORITY, NULL,
             row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH
             ORDER BY a.SETTLERATE) - 1, a.PRIORITY) calc_priority,
             decode(a.SETTLEMEASURE, '1', round(a.SETTLERATE / a.SUMSETTLERATE, 27), '2',
             a.SETTLEFEE) rate_value, decode(a.ORDERTYPE, '1', decode(floor(a.RULESTARTMONTH / 201501),
             0, to_date('201501', 'yyyymm'), decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))),
             decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))) eff_date,
             decode(floor(a.ruleendmonth / to_char(b.exp_date, 'YYYYMM')), 0, add_months(to_date(a.RULEENDMONTH, 'yyyymm'), 1) - 1, b.exp_date) exp_date, 0 dest_source, 1 route_flag
        FROM STL_SYNC_RULE a,
             STL_OFFER_T b,
             STL_RATE_T c,
             STL_RULE_ITEM_T d,
             STL_TARIFF_RATE_T e
       WHERE a.POSPECNUMBER = b.OFFER_CODE
         AND a.SOSPECNUMBER = b.PRODUCT_CODE
         AND a.ORDERTYPE = b.ORDER_MODE
         AND b.RULE_ID = c.RULE_ID
         AND b.RULE_ID = d.RULE_ID
         AND a.FEETYPE = d.CHARGE_ITEM
         AND a.SETTLEMEASURE = e.TARIFF_TYPE
         AND c.RATE_ID = e.RATE_ID
         AND a.POSPECNUMBER NOT IN ('1010403', '1010800', '50021','50120')
         and (a.sospecnumber not in (select product_code from stl_config_db2f where type_nm = 'LP' and column_nm = 'product_code')
              or a.sospecnumber is NULL)
         and a.SOSPECNUMBER !='2023999400085449'
         AND a.SETTLEMEASURE = 1
         AND b.DATA_SOURCE = 1;
      LOG('Common products(specific services) ends.', 'TARIFF');

     --xsl 2023/12/20 新增idc算力 charge_item不能为-1
           INSERT INTO STL_TARIFF_PARAMETER_T
      SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
             a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
             e.RATE_ID rate_id, a.FEETYPE  charge_item, a.COOPERATIVEPROV object_value,
             a.SETTLEMEASURE tariff_type, decode(a.PRIORITY, NULL,
             row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH
             ORDER BY a.SETTLERATE) - 1, a.PRIORITY) calc_priority,
             decode(a.SETTLEMEASURE, '1', round(a.SETTLERATE / a.SUMSETTLERATE, 27), '2',
             a.SETTLEFEE) rate_value, decode(a.ORDERTYPE, '1', decode(floor(a.RULESTARTMONTH / 201501),
             0, to_date('201501', 'yyyymm'), decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))),
             decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))) eff_date,
             decode(floor(a.ruleendmonth / to_char(b.exp_date, 'YYYYMM')), 0, add_months(to_date(a.RULEENDMONTH, 'yyyymm'), 1) - 1, b.exp_date) exp_date, 0 dest_source, 1 route_flag
        FROM STL_SYNC_RULE a,
             STL_OFFER_T b,
             STL_RATE_T c,
             STL_RULE_ITEM_T d,
             STL_TARIFF_RATE_T e
       WHERE a.POSPECNUMBER = b.OFFER_CODE
         AND a.SOSPECNUMBER = b.PRODUCT_CODE
         AND a.ORDERTYPE = b.ORDER_MODE
         AND b.RULE_ID = c.RULE_ID
         AND b.RULE_ID = d.RULE_ID
         AND a.FEETYPE = d.CHARGE_ITEM
         AND a.SETTLEMEASURE = e.TARIFF_TYPE
         AND c.RATE_ID = e.RATE_ID
         AND a.POSPECNUMBER ='50051'
         AND a.SOSPECNUMBER ='2023999400085449'
         AND a.SETTLEMEASURE = 1
         AND b.DATA_SOURCE = 1;

         INSERT INTO STL_TARIFF_PARAMETER_T
              SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
             a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
             e.RATE_ID rate_id, a.FEETYPE  charge_item, a.COOPERATIVEPROV object_value,
             a.SETTLEMEASURE tariff_type, decode(a.PRIORITY, NULL,
             row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH
             ORDER BY a.SETTLERATE) - 1, a.PRIORITY) calc_priority,
             decode(a.SETTLEMEASURE, '1', round(a.SETTLERATE / a.SUMSETTLERATE, 27), '2',
             a.SETTLEFEE) rate_value, decode(a.ORDERTYPE, '1', decode(floor(a.RULESTARTMONTH / 201501),
             0, to_date('201501', 'yyyymm'), decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))),
             decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))) eff_date,
             decode(floor(a.ruleendmonth / to_char(b.exp_date, 'YYYYMM')), 0, add_months(to_date(a.RULEENDMONTH, 'yyyymm'), 1) - 1, b.exp_date) exp_date, 0 dest_source, 1 route_flag
        FROM STL_SYNC_RULE a,
             STL_OFFER_T b,
             STL_RATE_T c,
             STL_RULE_ITEM_T d,
             STL_TARIFF_RATE_T e
       WHERE a.POSPECNUMBER = b.OFFER_CODE
         AND a.SOSPECNUMBER = b.PRODUCT_CODE
         AND a.ORDERTYPE = b.ORDER_MODE
         AND b.RULE_ID = c.RULE_ID
         AND b.RULE_ID = d.RULE_ID
         AND a.FEETYPE = d.CHARGE_ITEM
         AND a.SETTLEMEASURE = e.TARIFF_TYPE
         AND c.RATE_ID = e.RATE_ID
         AND c.rate_type = e.tariff_type
         AND a.POSPECNUMBER ='50051'
         AND a.SOSPECNUMBER ='2023999400085449'
         AND a.SETTLEMEASURE = 2 AND a.FEETYPE NOT IN ('1850','2164','2165')
         AND b.DATA_SOURCE = 1;

      LOG('Common products(specific services) ends.', 'TARIFF');


      --插入实收历史数据（2015年前）
      LOG('Common old rules begins.', 'TARIFF');
      INSERT INTO STL_TARIFF_PARAMETER_T
      SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, t.*
        FROM STL_TARIFF_PARAMETER_T_HIS t;
      LOG('Common old rules ends.', 'TARIFF');

      LOG('Common products, line-fixed fee begins.', 'TARIFF');
      --按商品（不包含全网IDC-1010403、智慧店铺-50075、智慧园区-50076、高精度定位-50091），固定值
      INSERT INTO STL_TARIFF_PARAMETER_T
      SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
             a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
             e.RATE_ID rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value,
             decode(a.SETTLEMEASURE, '3', '2', a.settlemeasure) tariff_type, 0 calc_priority,
             decode(a.SETTLEMEASURE, '1', round(a.SETTLERATE / a.SUMSETTLERATE, 27),
             a.SETTLEFEE) rate_value, to_date(a.RULESTARTMONTH, 'yyyymm') eff_date, add_months(to_date(
             a.RULEENDMONTH, 'yyyymm'), 1) - 1 exp_date, 0 dest_source, 1 route_flag
        FROM STL_SYNC_RULE a,
             STL_OFFER_T b,
             STL_RATE_T c,
             STL_RULE_ITEM_T d,
             STL_TARIFF_RATE_T e
       WHERE a.POSPECNUMBER = b.OFFER_CODE
         AND b.PRODUCT_CODE = -1
         AND a.ORDERTYPE = b.ORDER_MODE
         AND b.RULE_ID = c.RULE_ID
         AND b.RULE_ID = d.RULE_ID
         AND a.FEETYPE = d.CHARGE_ITEM
         AND decode(a.SETTLEMEASURE, '3', '2', a.settlemeasure) = e.TARIFF_TYPE
         AND c.RATE_ID = e.RATE_ID
         AND a.POSPECNUMBER NOT IN ('1010403', '50075','50076', '50091')
         AND a.SETTLEMEASURE in (2, 3)
         AND a.SETTLEFEE > 0
         AND b.DATA_SOURCE = 1;

      INSERT INTO STL_TARIFF_PARAMETER_T
      SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
             a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
             e.RATE_ID rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value,
             decode(a.SETTLEMEASURE, '3', '2', a.settlemeasure) tariff_type, 1 calc_priority,
             decode(a.SETTLEMEASURE, '1', to_char(round(a.SETTLERATE / a.SUMSETTLERATE, 27)),
             '$') rate_value, to_date(a.RULESTARTMONTH, 'yyyymm') eff_date, add_months(to_date(
             a.RULEENDMONTH, 'yyyymm'), 1) - 1 exp_date, 0 dest_source, 1 route_flag
        FROM STL_SYNC_RULE a,
             STL_OFFER_T b,
             STL_RATE_T c,
             STL_RULE_ITEM_T d,
             STL_TARIFF_RATE_T e
       WHERE a.POSPECNUMBER = b.OFFER_CODE
         AND b.PRODUCT_CODE = -1
         AND a.ORDERTYPE = b.ORDER_MODE
         AND b.RULE_ID = c.RULE_ID
         AND b.RULE_ID = d.RULE_ID
         AND a.FEETYPE = d.CHARGE_ITEM
         AND decode(a.SETTLEMEASURE, '3', '2', a.settlemeasure) = e.TARIFF_TYPE
         AND c.RATE_ID = e.RATE_ID
         AND a.POSPECNUMBER NOT IN ('1010403', '50075','50076', '50091')
         AND a.SETTLEMEASURE in (2, 3)
         AND a.SETTLEFEE <= 0
         AND b.DATA_SOURCE = 1;
      LOG('Common products, line-fixed fee ends.', 'TARIFF');

      --按产品（不包含全网IDC-1010403、智慧店铺-50075、智慧园区-50076、 OnePower工业互联网-50097）、中移急救、
           --  OneHealth平台、Onecity、高精度定位-50091  固定值
      INSERT INTO STL_TARIFF_PARAMETER_T
      SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
             a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
             e.RATE_ID rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value,
             decode(a.SETTLEMEASURE, '3', '2', a.settlemeasure) tariff_type, 0 calc_priority,
             decode(a.SETTLEMEASURE, '1', round(a.SETTLERATE / a.SUMSETTLERATE, 27),
             a.SETTLEFEE) rate_value, to_date(a.RULESTARTMONTH, 'yyyymm') eff_date, add_months(to_date(
             a.RULEENDMONTH, 'yyyymm'), 1) - 1 exp_date, 0 dest_source, 1 route_flag
        FROM STL_SYNC_RULE a,
             STL_OFFER_T b,
             STL_RATE_T c,
             STL_RULE_ITEM_T d,
             STL_TARIFF_RATE_T e
       WHERE a.POSPECNUMBER = b.OFFER_CODE
         AND a.sospecnumber = b.PRODUCT_CODE
         AND a.ORDERTYPE = b.ORDER_MODE
         AND b.RULE_ID = c.RULE_ID
         AND b.RULE_ID = d.RULE_ID
         AND a.FEETYPE = d.CHARGE_ITEM
         AND decode(a.SETTLEMEASURE, '3', '2', a.settlemeasure) = e.TARIFF_TYPE
         AND c.RATE_ID = e.RATE_ID
         AND a.POSPECNUMBER NOT IN ('1010403', '50075','50076','50097','50008','50074','50104','50068','50091')
         AND a.sospecnumber !='2023999400085449'
         AND a.SETTLEMEASURE in (2, 3)
         AND a.SETTLEFEE >= 0
         AND b.DATA_SOURCE = 1;

      INSERT INTO STL_TARIFF_PARAMETER_T
      SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
             a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
             e.RATE_ID rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value,
             decode(a.SETTLEMEASURE, '3', '2', a.settlemeasure) tariff_type, 1 calc_priority,
             decode(a.SETTLEMEASURE, '1', to_char(round(a.SETTLERATE / a.SUMSETTLERATE, 27)),
             '$') rate_value, to_date(a.RULESTARTMONTH, 'yyyymm') eff_date, add_months(to_date(
             a.RULEENDMONTH, 'yyyymm'), 1) - 1 exp_date, 0 dest_source, 1 route_flag
        FROM STL_SYNC_RULE a,
             STL_OFFER_T b,
             STL_RATE_T c,
             STL_RULE_ITEM_T d,
             STL_TARIFF_RATE_T e
       WHERE a.POSPECNUMBER = b.OFFER_CODE
         AND a.sospecnumber = b.PRODUCT_CODE
         AND a.ORDERTYPE = b.ORDER_MODE
         AND b.RULE_ID = c.RULE_ID
         AND b.RULE_ID = d.RULE_ID
         AND a.FEETYPE = d.CHARGE_ITEM
         AND decode(a.SETTLEMEASURE, '3', '2', a.settlemeasure) = e.TARIFF_TYPE
         AND c.RATE_ID = e.RATE_ID
         AND a.POSPECNUMBER NOT IN ('1010403', '50075','50076','50097','50008','50074','50104','50068','50091')
         AND a.SETTLEMEASURE in (2, 3)
         AND a.SETTLEFEE < 0
         AND b.DATA_SOURCE = 1;
      LOG('Common products, line-fixed fee ends.', 'TARIFF');
      COMMIT;

    EXCEPTION
      WHEN OTHERS THEN
        outSysError := 'Exception occurred when inserting ordinary rules into Tariff Rule Table.
                    ' || ' ErrorCode: ' || SQLCODE || ':' || SQLERRM;
        outReturn := -1;
        LOG(outSysError, 'TARIFF');
        ROLLBACK;
    END;

    --插入全网IDC业务数据
    BEGIN
      LOG('IDC(mode 1) begins.', 'TARIFF');
      --受理模式1
      INSERT INTO STL_TARIFF_PARAMETER_T
      SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
             a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
             c.RATE_ID rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value,
             a.SETTLEMEASURE tariff_type, decode(a.PRIORITY, NULL,
             row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH
             ORDER BY a.SETTLERATE) - 1, a.PRIORITY) calc_priority, '1' rate_value,
             to_date(a.RULESTARTMONTH, 'yyyymm') eff_date, add_months(to_date(
             a.RULEENDMONTH, 'yyyymm'), 1) - 1 exp_date,
             decode(a.COOPERATIVEPROV, '000', '1', '0') dest_source,
             decode(a.COOPERATIVEPROV, '000', '0', '1') route_flag
        FROM STL_SYNC_RULE a,
             STL_OFFER_T b,
             STL_RATE_T c,
             STL_RULE_ITEM_T d,
             STL_TARIFF_RATE_T e
       WHERE a.POSPECNUMBER = b.OFFER_CODE
         AND b.PRODUCT_CODE = -1
         AND a.ORDERTYPE = b.ORDER_MODE
         AND b.RULE_ID = c.RULE_ID
         AND b.RULE_ID = d.RULE_ID
         AND a.FEETYPE = d.CHARGE_ITEM
         AND c.RATE_ID = e.RATE_ID
         AND e.TARIFF_TYPE = 1
         AND a.POSPECNUMBER IN ('1010403')
         AND a.SETTLERATE <> 5
         AND a.ORDERTYPE = '1'
         AND b.DATA_SOURCE = 1;

      LOG('IDC(mode 1) ends.', 'TARIFF');

      LOG('IDC(mode 3) begins.', 'TARIFF');
      --受理模式3
      INSERT INTO STL_TARIFF_PARAMETER_T
      SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
             a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
             c.RATE_ID rate_id, '-1' charge_item,
             a.COOPERATIVEPROV object_value, a.SETTLEMEASURE tariff_type, decode(a.PRIORITY, NULL,
             row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH
             ORDER BY a.SETTLERATE) - 1, a.PRIORITY) calc_priority,
             round(a.SETTLERATE / a.SUMSETTLERATE, 27)
             rate_value, to_date(a.RULESTARTMONTH, 'yyyymm') eff_date, add_months(to_date(
             a.RULEENDMONTH, 'yyyymm'), 1) - 1 exp_date, 0 dest_source, 1 route_flag
        FROM STL_SYNC_RULE a,
             STL_OFFER_T b,
             STL_RATE_T c,
             STL_RULE_ITEM_T d,
             STL_TARIFF_RATE_T e
       WHERE a.POSPECNUMBER = b.OFFER_CODE
         AND b.PRODUCT_CODE = -1
         AND a.ORDERTYPE = b.ORDER_MODE
         AND b.RULE_ID = c.RULE_ID
         AND b.RULE_ID = d.RULE_ID
         AND a.FEETYPE = d.CHARGE_ITEM
         AND e.RATE_ID = c.RATE_ID
         AND e.TARIFF_TYPE = 1
         AND a.POSPECNUMBER IN ('1010403')
         AND a.ORDERTYPE = '3'
         AND b.DATA_SOURCE = 1;
       LOG('IDC(mode 3) ends.', 'TARIFF');
       COMMIT;

    EXCEPTION
      WHEN OTHERS THEN
        outSysError := 'Exception occurred when inserting IDC rules into Tariff Rule Table.
                       ' || ' ErrorCode: ' || SQLCODE || ':' || SQLERRM;
        outReturn := -1;
        LOG(outSysError, 'TARIFF');
        ROLLBACK;
    END;

    --插入IDC业务数据
    BEGIN
      LOG('General IDC begins.', 'TARIFF');
      INSERT INTO STL_TARIFF_PARAMETER_T
      SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
             a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
             e.RATE_ID rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value,
             a.SETTLEMEASURE tariff_type, decode(a.PRIORITY, NULL,
             row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH
             ORDER BY a.SETTLERATE) - 1, a.PRIORITY) calc_priority,
             decode(a.SETTLEMEASURE, '1', round(a.SETTLERATE / a.SUMSETTLERATE, 27), '2',
             a.SETTLEFEE) rate_value, decode(a.ORDERTYPE, '1', decode(floor(a.RULESTARTMONTH / 201501),
             0, to_date('201501', 'yyyymm'), to_date(a.RULESTARTMONTH, 'yyyymm')),
             to_date(a.RULESTARTMONTH, 'yyyymm')) eff_date, add_months(to_date(
             a.RULEENDMONTH, 'yyyymm'), 1) - 1 exp_date, decode(a.ORDERTYPE, '1', decode(a.COOPERATIVEPROV, '000', 1, 0), 0) dest_source,
             decode(a.ORDERTYPE, '1', decode(a.COOPERATIVEPROV, '000', 0, 1), 1) route_flag
        FROM STL_SYNC_RULE a,
             stlusers.STL_OFFER_T b,
             stlusers.STL_RATE_T c,
             stlusers.STL_RULE_ITEM_T d,
             stlusers.STL_TARIFF_RATE_T e
       WHERE a.POSPECNUMBER = b.OFFER_CODE
         AND a.SOSPECNUMBER = b.PRODUCT_CODE
         AND a.ORDERTYPE = b.ORDER_MODE
         AND b.RULE_ID = c.RULE_ID
         AND b.RULE_ID = d.RULE_ID
         AND a.FEETYPE = d.CHARGE_ITEM
         AND a.SETTLEMEASURE = e.TARIFF_TYPE
         AND c.RATE_ID = e.RATE_ID
         AND a.POSPECNUMBER in ('1010800')
         AND a.SETTLEMEASURE = 1
         AND b.DATA_SOURCE = 1
         AND a.SETTLERATE <> 0;

      LOG('General IDC ends.', 'TARIFF');
      COMMIT;

    EXCEPTION
      WHEN OTHERS THEN
        outSysError := 'Exception occurred when inserting General IDC rules into Tariff Rule Table.
                       ' || ' ErrorCode: ' || SQLCODE || ':' || SQLERRM;
        outReturn := -1;
        LOG(outSysError, 'TARIFF');
        ROLLBACK;
    END;

    --插入移动云SaaS类产品结算规则
    BEGIN
      LOG('M Cloud begins.', 'TARIFF');
      INSERT INTO STL_TARIFF_PARAMETER_T
      SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
             a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
             e.RATE_ID rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value,
             a.SETTLEMEASURE tariff_type, decode(a.PRIORITY, NULL,
             row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH
             ORDER BY a.SETTLERATE) - 1, a.PRIORITY) calc_priority,
             decode(a.SETTLEMEASURE, '1', round(a.SETTLERATE / a.SUMSETTLERATE, 27), '2',
             a.SETTLEFEE) rate_value, decode(a.ORDERTYPE, '1', decode(floor(a.RULESTARTMONTH / 201501),
             0, to_date('201501', 'yyyymm'), to_date(a.RULESTARTMONTH, 'yyyymm')),
             to_date(a.RULESTARTMONTH, 'yyyymm')) eff_date, add_months(to_date(
             a.RULEENDMONTH, 'yyyymm'), 1) - 1 exp_date, decode(a.attrname, '主办省', 98, 4) dest_source,
             0 route_flag
        FROM STL_SYNC_RULE a,
             stlusers.STL_OFFER_T b,
             stlusers.STL_RATE_T c,
             stlusers.STL_RULE_ITEM_T d,
             stlusers.STL_TARIFF_RATE_T e
       WHERE a.POSPECNUMBER = b.OFFER_CODE
         AND a.SOSPECNUMBER = b.PRODUCT_CODE
         AND a.ORDERTYPE = b.ORDER_MODE
         AND b.RULE_ID = c.RULE_ID
         AND b.RULE_ID = d.RULE_ID
         AND a.FEETYPE = d.CHARGE_ITEM
         AND a.SETTLEMEASURE = e.TARIFF_TYPE
         AND c.RATE_ID = e.RATE_ID
         AND a.POSPECNUMBER in ('1010402')
         and (a.sospecnumber in (select product_code from stl_config_db2f where type_nm = 'LP' and column_nm = 'product_code'))
         AND a.SETTLEMEASURE = 1
         AND b.DATA_SOURCE = 3
         AND a.SETTLERATE <> 0;

      LOG('M Cloud ends.', 'TARIFF');
      COMMIT;

    EXCEPTION
      WHEN OTHERS THEN
        outSysError := 'Exception occurred when inserting M Cloud rules into Tariff Rule Table.
                       ' || ' ErrorCode: ' || SQLCODE || ':' || SQLERRM;
        outReturn := -1;
        LOG(outSysError, 'TARIFF');
        ROLLBACK;
    END;

    --插入智慧店铺结算规则
    BEGIN
      LOG('Smart Shop begins.', 'TARIFF');
      INSERT INTO STL_TARIFF_PARAMETER_T
      SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
             a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
             2 rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value,
             2 tariff_type, decode(a.PRIORITY, NULL,
             row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH
             ORDER BY a.SETTLERATE) - 1, a.PRIORITY) calc_priority,
             a.SETTLEFEE rate_value, decode(a.ORDERTYPE, '1', decode(floor(a.RULESTARTMONTH / 201501),
             0, to_date('201501', 'yyyymm'), decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))),
             decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))) eff_date,
             decode(floor(a.ruleendmonth / to_char(b.exp_date, 'YYYYMM')), 0, add_months(to_date(a.RULEENDMONTH, 'yyyymm'), 1) - 1, b.exp_date) exp_date, 98 dest_source, 1 route_flag
        FROM STL_SYNC_RULE a,
             STL_OFFER_T b,
             STL_RATE_T c,
             STL_RULE_ITEM_T d
       WHERE a.POSPECNUMBER = b.OFFER_CODE
         AND a.SOSPECNUMBER = b.PRODUCT_CODE
         AND a.ORDERTYPE = b.ORDER_MODE
         AND b.RULE_ID = c.RULE_ID
         AND b.RULE_ID = d.RULE_ID
         AND a.FEETYPE = d.CHARGE_ITEM
         AND a.POSPECNUMBER = '50075'
         AND a.SETTLEMEASURE = 3
         and c.rate_type = 2
         AND b.DATA_SOURCE = 1;
      LOG('Smart Shop ends.', 'TARIFF');
      COMMIT;

    EXCEPTION
      WHEN OTHERS THEN
        outSysError := 'Exception occurred when inserting Smart Shop rules into Tariff Rule Table.
                       ' || ' ErrorCode: ' || SQLCODE || ':' || SQLERRM;
        outReturn := -1;
        LOG(outSysError, 'TARIFF');
        ROLLBACK;
    END;

/*    --插入智慧园区结算规则
    BEGIN
      LOG('Smart Park begins.', 'TARIFF');
      INSERT INTO STL_TARIFF_PARAMETER_T
      SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
             a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
             2 rate_id, a.feetype charge_item, a.COOPERATIVEPROV object_value,
             2 tariff_type, decode(a.PRIORITY, NULL,
             row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH
             ORDER BY a.SETTLERATE) - 1, a.PRIORITY) calc_priority,
             a.SETTLEFEE rate_value, decode(a.ORDERTYPE, '1', decode(floor(a.RULESTARTMONTH / 201501),
             0, to_date('201501', 'yyyymm'), decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))),
             decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))) eff_date,
             decode(floor(a.ruleendmonth / to_char(b.exp_date, 'YYYYMM')), 0, add_months(to_date(a.RULEENDMONTH, 'yyyymm'), 1) - 1, b.exp_date) exp_date, 98 dest_source, 1 route_flag
        FROM STL_SYNC_RULE a,
             STL_OFFER_T b,
             STL_RATE_T c,
             STL_RULE_ITEM_T d
       WHERE a.POSPECNUMBER = b.OFFER_CODE
         AND a.SOSPECNUMBER = b.PRODUCT_CODE
         AND a.ORDERTYPE = b.ORDER_MODE
         AND b.RULE_ID = c.RULE_ID
         AND b.RULE_ID = d.RULE_ID
         AND a.FEETYPE = d.CHARGE_ITEM
         AND a.POSPECNUMBER = '50076'
         AND a.SETTLEMEASURE in('2','3')
         and c.rate_type = 2
         AND b.DATA_SOURCE = 1;
      LOG('Smart Park ends.', 'TARIFF');
      COMMIT;

    EXCEPTION
      WHEN OTHERS THEN
        outSysError := 'Exception occurred when inserting Smart Park rules into Tariff Rule Table.
                       ' || ' ErrorCode: ' || SQLCODE || ':' || SQLERRM;
        outReturn := -1;
        LOG(outSysError, 'TARIFF');
        ROLLBACK;
    END;*/


     --OnePower工业互联网平台结算规则、移动千里眼、OneHealth平台、智慧园区、OneCity
     --排除OnePower业务所有云端产品
    BEGIN
      LOG('OnePower begins.', 'TARIFF');
      INSERT INTO STL_TARIFF_PARAMETER_T
      SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
             a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
             2 rate_id,  a.feetype charge_item, a.COOPERATIVEPROV object_value,
             2 tariff_type, decode(a.PRIORITY, NULL,
             row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH
             ORDER BY a.SETTLERATE) - 1, a.PRIORITY) calc_priority,
             a.SETTLEFEE rate_value, decode(a.ORDERTYPE, '1', decode(floor(a.RULESTARTMONTH / 201501),
             0, to_date('201501', 'yyyymm'), decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))),
             decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))) eff_date,
             decode(floor(a.ruleendmonth / to_char(b.exp_date, 'YYYYMM')), 0, add_months(to_date(a.RULEENDMONTH, 'yyyymm'), 1) - 1, b.exp_date) exp_date, 98 dest_source, 1 route_flag
        FROM STL_SYNC_RULE a,
             STL_OFFER_T b,
             STL_RATE_T c,
             STL_RULE_ITEM_T d
       WHERE a.POSPECNUMBER = b.OFFER_CODE
         AND a.SOSPECNUMBER = b.PRODUCT_CODE
         AND a.ORDERTYPE = b.ORDER_MODE
         AND b.RULE_ID = c.RULE_ID
         AND b.RULE_ID = d.RULE_ID
         AND a.FEETYPE = d.CHARGE_ITEM
         AND a.POSPECNUMBER in ('50097','50008','50104','50076','50068')
        AND a.feetype not in(select charge_item from stl_onepower_yunduan_config@udr_link)
         AND a.SETTLEMEASURE in('2','3')
         and c.rate_type = 2
         AND b.DATA_SOURCE = 1;
      LOG('OnePower ends.', 'TARIFF');
      COMMIT;

    EXCEPTION
      WHEN OTHERS THEN
        outSysError := 'Exception occurred when inserting OnePower rules into Tariff Rule Table.
                       ' || ' ErrorCode: ' || SQLCODE || ':' || SQLERRM;
        outReturn := -1;
        LOG(outSysError, 'TARIFF');
        ROLLBACK;
    END;


    --OnePower云端产品按天结算
     BEGIN
      LOG('OnePower begins.', 'TARIFF');
      iv_test :=  'INSERT INTO STL_TARIFF_PARAMETER_T  '||
      'SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,  '||
             'a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,  '||
             '2 rate_id,  a.feetype charge_item, a.COOPERATIVEPROV object_value,   '||
             '2 tariff_type, decode(a.PRIORITY, NULL,   '||
             'row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH  '||
             'ORDER BY a.SETTLERATE) - 1, a.PRIORITY) calc_priority,   '||
             'round(a.SETTLEFEE * (e.eff_days/to_number(to_char(last_day(to_date('''|| inMonth || ''',''yyyymm'')),''dd'')))) rate_value, decode(a.ORDERTYPE, ''1'', decode(floor(a.RULESTARTMONTH / 201501),  '||
             '0, to_date(''201501'', ''yyyymm''), decode(floor(a.rulestartmonth / to_char(b.eff_date, ''YYYYMM'')), 0, b.eff_date, to_date(a.rulestartmonth, ''YYYYMM''))),  '||
             'decode(floor(a.rulestartmonth / to_char(b.eff_date, ''YYYYMM'')), 0, b.eff_date, to_date(a.rulestartmonth, ''YYYYMM''))) eff_date,   '||
             'decode(floor(a.ruleendmonth / to_char(b.exp_date, ''YYYYMM'')), 0, add_months(to_date(a.RULEENDMONTH, ''yyyymm''), 1) - 1, b.exp_date) exp_date, 98 dest_source, 1 route_flag '||
        'FROM STL_SYNC_RULE a,   '||
             'STL_OFFER_T b,     '||
             'STL_RATE_T c,     '||
             'STL_RULE_ITEM_T d,    '||
             'sync_onepower_' ||inMonth|| ' @udr_link e,   '||
             'stl_onepower_yunduan_config@udr_link   f '||
       'WHERE a.POSPECNUMBER = b.OFFER_CODE   '||
         'AND a.SOSPECNUMBER = b.PRODUCT_CODE   '||
         'AND a.ORDERTYPE = b.ORDER_MODE    '||
         'AND b.RULE_ID = c.RULE_ID     '||
         'AND b.RULE_ID = d.RULE_ID    '||
         'AND a.FEETYPE = d.CHARGE_ITEM   '||
         'and a.ordertype = e.ordermode    '||
         'and a.customernumber = e.customernumber   '||
         'and a.pospecnumber = e.pospecnumber   '||
         'and a.sospecnumber = e.sospecnumber    '||
         'and a.poid = e.poid    '||
         'and a.soid = e.soid    '||
         'and a.feetype = e.feetype  '||
         'and a.POSPECNUMBER = f.offer_code  '||
         'and a.SOSPECNUMBER = f.product_code   '||
         'and a.feetype = f.charge_item   '||
         'AND a.POSPECNUMBER in (''50097'')   '||
         'AND a.SETTLEMEASURE in(''2'',''3'')   '||
         'and c.rate_type = 2   '||
         'AND b.DATA_SOURCE = 1 ';

     execute immediate iv_test;

        EXCEPTION
      WHEN OTHERS THEN
        outSysError := 'Exception occurred when inserting OnePower rules into Tariff Rule Table.
                       ' || ' ErrorCode: ' || SQLCODE || ':' || SQLERRM;
        outReturn := -1;
        LOG(outSysError, 'TARIFF');
        ROLLBACK;
    END;

    BEGIN
      LOG('idc begins.', 'TARIFF');
      iv_test :=  'INSERT INTO STL_TARIFF_PARAMETER_T  '||
      'SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,  '||
             'a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,  '||
             '2 rate_id,  a.feetype charge_item, a.COOPERATIVEPROV object_value,   '||
             '2 tariff_type, decode(a.PRIORITY, NULL,   '||
             'row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH  '||
             'ORDER BY a.SETTLERATE) - 1, a.PRIORITY) calc_priority,   '||
             'round(a.SETTLEFEE * (e.eff_days/to_number(to_char(last_day(to_date('''|| inMonth || ''',''yyyymm'')),''dd'')))) rate_value, decode(a.ORDERTYPE, ''1'', decode(floor(a.RULESTARTMONTH / 201501),  '||
             '0, to_date(''201501'', ''yyyymm''), decode(floor(a.rulestartmonth / to_char(b.eff_date, ''YYYYMM'')), 0, b.eff_date, to_date(a.rulestartmonth, ''YYYYMM''))),  '||
             'decode(floor(a.rulestartmonth / to_char(b.eff_date, ''YYYYMM'')), 0, b.eff_date, to_date(a.rulestartmonth, ''YYYYMM''))) eff_date,   '||
             'decode(floor(a.ruleendmonth / to_char(b.exp_date, ''YYYYMM'')), 0, add_months(to_date(a.RULEENDMONTH, ''yyyymm''), 1) - 1, b.exp_date) exp_date, 0 dest_source, 1 route_flag '||
        'FROM STL_SYNC_RULE a,   '||
             'STL_OFFER_T b,     '||
             'STL_RATE_T c,     '||
             'STL_RULE_ITEM_T d,    '||
             'sync_onepower_' ||inMonth|| ' @udr_link e,   '||
             'Stl_tariff_rate_t f '||
       'WHERE a.POSPECNUMBER = b.OFFER_CODE   '||
         'AND a.SOSPECNUMBER = b.PRODUCT_CODE   '||
         'AND a.ORDERTYPE = b.ORDER_MODE    '||
         'AND b.RULE_ID = c.RULE_ID     '||
         'AND b.RULE_ID = d.RULE_ID    '||
         'AND a.FEETYPE = d.CHARGE_ITEM   '||
         'and a.ordertype = e.ordermode    '||
         'and a.customernumber = e.customernumber   '||
         'and a.pospecnumber = e.pospecnumber   '||
         'and a.sospecnumber = e.sospecnumber    '||
         'and a.poid = e.poid    '||
         'and a.soid = e.soid    '||
         'and a.feetype = e.feetype  '||
         'AND a.POSPECNUMBER in (''50051'')   '||
         'AND a.sospecnumber in (''2023999400085449'')   '||
         'AND a.SETTLEMEASURE in(''2'',''3'')   '||
         'and c.rate_type = 2   AND c.RATE_ID = f.RATE_ID AND c.rate_type = f.tariff_type  '||
         'AND b.DATA_SOURCE = 1 ';

     execute immediate iv_test;

    EXCEPTION
      WHEN OTHERS THEN
        outSysError := 'Exception occurred when inserting idc rules into Tariff Rule Table.
                       ' || ' ErrorCode: ' || SQLCODE || ':' || SQLERRM;
        outReturn := -1;
        LOG(outSysError, 'TARIFF');
        ROLLBACK;
    END;


    --和医疗  叠加包操作
    BEGIN
      LOG('HYL begins.', 'TARIFF');
      INSERT INTO STL_TARIFF_PARAMETER_T
    select SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, offer_code, product_code, poid_inst_id, svc_inst_id, order_mode, rule_id,
       rate_id, charge_item, object_value, tariff_type, calc_priority, rate_value, eff_date, exp_date, dest_source, route_flag
    from
   (SELECT '', a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
             a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
             2 rate_id,  a.feetype charge_item, a.COOPERATIVEPROV object_value,
             2 tariff_type, decode(a.PRIORITY, NULL,
             row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH
             ORDER BY a.SETTLERATE) - 1, a.PRIORITY) calc_priority,
             fee rate_value, decode(a.ORDERTYPE, '1', decode(floor(a.RULESTARTMONTH / 201501),
             0, to_date('201501', 'yyyymm'), decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))),
             decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))) eff_date,
             decode(floor(a.ruleendmonth / to_char(b.exp_date, 'YYYYMM')), 0, add_months(to_date(a.RULEENDMONTH, 'yyyymm'), 1) - 1, b.exp_date) exp_date, 98 dest_source, 1 route_flag
        FROM (select sum(settlefee) fee,customernumber, customername, pospecnumber, pospecname,  sospecnumber, sospecname, poid, soid, settlemeasure, feetype, settlerate,
                   settleitem, cooperativeprov, rulestartmonth, ruleendmonth, ordertype, outprov, priority,
                  sumsettlerate, attrname, settle_unit from STL_SYNC_RULE  where sospecnumber='5007401' and  feetype not in ('1990','1991','2000')
               group by customernumber, customername, pospecnumber, pospecname,  sospecnumber, sospecname, poid, soid, settlemeasure, feetype, settlerate,
                   settleitem, cooperativeprov, rulestartmonth, ruleendmonth, ordertype, outprov, priority,
                  sumsettlerate, attrname, settle_unit) a,
             STL_OFFER_T b,
             STL_RATE_T c,
             STL_RULE_ITEM_T d
       WHERE a.POSPECNUMBER = b.OFFER_CODE
         AND a.SOSPECNUMBER = b.PRODUCT_CODE
         AND a.ORDERTYPE = b.ORDER_MODE
         AND b.RULE_ID = c.RULE_ID
         AND b.RULE_ID = d.RULE_ID
         AND a.FEETYPE = d.CHARGE_ITEM
         AND a.POSPECNUMBER = '50074'
         AND a.SETTLEMEASURE in('2')
         and c.rate_type = 2
         AND b.DATA_SOURCE = 1
         group  by a.POSPECNUMBER, a.SOSPECNUMBER, a.POID, a.SOID, a.ORDERTYPE, b.RULE_ID, a.feetype, a.COOPERATIVEPROV,
         a.PRIORITY, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH,a.SETTLERATE, b.eff_date, b.exp_date, fee);
       LOG('HYL ends.', 'TARIFF');
        COMMIT;

    EXCEPTION
      WHEN OTHERS THEN
        outSysError := 'Exception occurred when inserting HYL rules into Tariff Rule Table.
                       ' || ' ErrorCode: ' || SQLCODE || ':' || SQLERRM;
        outReturn := -1;
        LOG(outSysError, 'TARIFF');
        ROLLBACK;
    END;


    --插入渠道代理商结算规则
    BEGIN
      LOG('SA begins.', 'TARIFF');
      INSERT INTO STL_TARIFF_PARAMETER_T
      SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, data.* FROM
      (SELECT DISTINCT e.PRODUCT_CODE offer_code, e.SERVICE_CODE product_code,
             a.POID poid_inst_id, a.SOID svc_inst_id, '1' order_mode, b.RULE_ID rule_id,
             c.RATE_ID rate_id, '-1' charge_item, a.SAID object_value, '1' tariff_type,
             '0' calc_priority, f.RATE * a.SARATE * 0.8 / 100 rate_value,
             to_date(a.RULESTARTMONTH, 'yyyymm') eff_date, add_months(to_date(a.RULEENDMONTH,
             'yyyymm'), 1) - 1 exp_date, 0 dest_source, 1 route_flag
        FROM STL_SA_RULE a,
             STL_OFFER_T b,
             STL_RATE_T c,
             STL_SERV_BIZ_CODE e,
             STL_SATYPE f
       WHERE e.PRODUCT_CODE = b.OFFER_CODE
         AND b.PRODUCT_CODE = -1
         AND b.RULE_ID = c.RULE_ID
         AND b.DATA_SOURCE = 7
         AND a.POID = e.PROD_ORDER_ID
         AND a.SOID = e.ORDER_ID
         AND to_char(e.EFFECTIVE_DATE, 'yyyymm') <= a.RULEENDMONTH
         AND to_char(e.EXPIRY_DATE, 'yyyymm') >= a.RULESTARTMONTH
         AND a.SATYPE = f.SATYPE) data;

      INSERT INTO STL_TARIFF_PARAMETER_T
      SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, data.* FROM
      (SELECT DISTINCT e.PRODUCT_CODE offer_code, e.SERVICE_CODE product_code,
             a.POID poid_inst_id, a.SOID svc_inst_id, '1' order_mode, b.RULE_ID rule_id,
             c.RATE_ID rate_id, '-1' charge_item, a.SAID object_value, '1' tariff_type,
             '1' calc_priority, f.RATE * a.SARATE * 0.2 / 100 rate_value,
             to_date(a.RULESTARTMONTH, 'yyyymm') eff_date, add_months(to_date(a.RULEENDMONTH,
             'yyyymm'), 1) - 1 exp_date, 5 dest_source, 1 route_flag
        FROM STL_SA_RULE a,
             STL_OFFER_T b,
             STL_RATE_T c,
             STL_SERV_BIZ_CODE e,
             STL_SATYPE f
       WHERE e.PRODUCT_CODE = b.OFFER_CODE
         AND b.PRODUCT_CODE = -1
         AND b.RULE_ID = c.RULE_ID
         AND b.DATA_SOURCE = 7
         AND a.POID = e.PROD_ORDER_ID
         AND a.SOID = e.ORDER_ID
         AND to_char(e.EFFECTIVE_DATE, 'yyyymm') <= a.RULEENDMONTH
         AND to_char(e.EXPIRY_DATE, 'yyyymm') >= a.RULESTARTMONTH
         AND a.SATYPE = f.SATYPE) data;
      LOG('SA ends.', 'TARIFF');
      COMMIT;

    EXCEPTION
      WHEN OTHERS THEN
        outSysError := 'Exception occurred when inserting SA rules into Tariff Rule Table.
                       ' || ' ErrorCode: ' || SQLCODE || ':' || SQLERRM;
        outReturn := -1;
        LOG(outSysError, 'TARIFF');
        ROLLBACK;
    END;

    --插入CP结算规则
    BEGIN
      LOG('CP begins.', 'TARIFF');
      --按商品
      INSERT INTO STL_TARIFF_PARAMETER_T
      SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
             a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
             c.RATE_ID rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value,
             a.SETTLEMEASURE tariff_type, decode(a.PRIORITY, NULL,
             row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH
             ORDER BY a.SETTLERATE) - 1, a.PRIORITY) calc_priority,
             decode(a.SETTLEMEASURE, '1', round(a.SETTLERATE / a.SUMSETTLERATE, 27), '2',
             a.SETTLEFEE) rate_value, to_date(a.RULESTARTMONTH, 'yyyymm') eff_date, add_months(to_date(
             a.RULEENDMONTH, 'yyyymm'), 1) - 1 exp_date, decode(a.attrname, '基地省', 7, 0) dest_source,
             1 route_flag
        FROM STL_SYNC_RULE a,
             STL_OFFER_T b,
             STL_RATE_T c,
             STL_RULE_ITEM_T d
       WHERE a.POSPECNUMBER = b.OFFER_CODE
         AND b.PRODUCT_CODE = -1
         AND a.ORDERTYPE = b.ORDER_MODE
         AND b.RULE_ID = c.RULE_ID
         AND b.RULE_ID = d.RULE_ID
         AND a.FEETYPE = d.CHARGE_ITEM
         AND b.DATA_SOURCE = 5;

      --按产品
      INSERT INTO STL_TARIFF_PARAMETER_T
      SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
             a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
             c.RATE_ID rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value,
             a.SETTLEMEASURE tariff_type, decode(a.PRIORITY, NULL,
             row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH
             ORDER BY a.SETTLERATE) - 1, a.PRIORITY) calc_priority,
             decode(a.SETTLEMEASURE, '1', round(a.SETTLERATE / a.SUMSETTLERATE, 27), '2',
             a.SETTLEFEE) rate_value, to_date(a.RULESTARTMONTH, 'yyyymm') eff_date, add_months(to_date(
             a.RULEENDMONTH, 'yyyymm'), 1) - 1 exp_date, decode(a.attrname, '基地省', 7, 0) dest_source,
             1 route_flag
        FROM STL_SYNC_RULE a,
             STL_OFFER_T b,
             STL_RATE_T c,
             STL_RULE_ITEM_T d
       WHERE a.POSPECNUMBER = b.OFFER_CODE
         AND a.SOSPECNUMBER = b.PRODUCT_CODE
         AND a.ORDERTYPE = b.ORDER_MODE
         AND b.RULE_ID = c.RULE_ID
         AND b.RULE_ID = d.RULE_ID
         AND a.FEETYPE = d.CHARGE_ITEM
         AND b.DATA_SOURCE = 5;
      LOG('CP ends.', 'TARIFF');
      COMMIT;

      /*LOG('-----------Settlemeasure = 2 begins.-----------');
      --所有固定值的，再插入一个$
      INSERT INTO STL_TARIFF_PARAMETER_T
      SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.OFFER_CODE, a.PRODUCT_CODE, a.PROD_INST_ID,
             a.SVC_INST_ID, a.ORDER_MODE, a.RULE_ID, a.RATE_ID, a.CHARGE_ITEM, a.OBJECT_VALUE,
             a.TARIFF_TYPE, a.CALC_PRIORITY, '$' rate_value, a.EFF_DATE, a.EXP_DATE, a.DEST_SOURCE
        FROM STL_TARIFF_PARAMETER_T a
       WHERE a.TARIFF_TYPE = 2;
      LOG('-----------Settlemeasure = 2 ends.-----------');
      COMMIT;*/

    EXCEPTION
      WHEN OTHERS THEN
        outSysError := 'Exception occurred when inserting CP rules into Tariff Rule Table.
                       ' || ' ErrorCode: ' || SQLCODE || ':' || SQLERRM;
        outReturn := -1;
        LOG(outSysError, 'TARIFF');
        ROLLBACK;
    END;


    --插入智能路由功能费和停机保号费
    BEGIN
      LOG('IR95 begins.', 'TARIFF');

      --按产品属性填写的比例计算
      INSERT INTO STL_TARIFF_PARAMETER_T
      SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
             a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
             e.RATE_ID rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value,
             a.SETTLEMEASURE tariff_type, decode(a.PRIORITY, NULL,
             row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH
             ORDER BY a.SETTLERATE) - 1, a.PRIORITY) calc_priority,
             decode(a.SETTLEMEASURE, '1', round(a.SETTLERATE / a.SUMSETTLERATE, 27),
             a.SETTLEFEE) rate_value, /*decode(a.ORDERTYPE, '1', decode(floor(a.RULESTARTMONTH / 201501),
             0, to_date('201501', 'yyyymm'), *//*decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))),*/
             decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))/*)*/ eff_date,
             decode(floor(a.ruleendmonth / to_char(b.exp_date, 'YYYYMM')), 0, add_months(to_date(a.RULEENDMONTH, 'yyyymm'), 1) - 1, b.exp_date) exp_date, 0 dest_source, 1 route_flag
        FROM STL_SYNC_RULE a,
             STL_OFFER_T b,
             STL_RATE_T c,
             STL_RULE_ITEM_T d,
             STL_TARIFF_RATE_T e
       WHERE a.POSPECNUMBER = b.OFFER_CODE
         AND a.SOSPECNUMBER = b.PRODUCT_CODE
         AND a.ORDERTYPE = b.ORDER_MODE
         AND b.RULE_ID = c.RULE_ID
         AND b.RULE_ID = d.RULE_ID
         AND a.FEETYPE = d.CHARGE_ITEM
         AND a.SETTLEMEASURE = e.TARIFF_TYPE
         AND c.RATE_ID = e.RATE_ID
         AND a.POSPECNUMBER = '50021'
         and a.sospecnumber = '5002101'
         and a.feetype = '02'
         AND b.DATA_SOURCE = 1;

      --按产品属性填写的接入省平均分配
      execute immediate 'truncate table stl_ir95_rule';

      insert into stl_ir95_rule(order_mode, pospecnumber, sospecnumber, poid, soid, in_prov, rulestartmonth, ruleendmonth)
      select ordertype, pospecnumber, sospecnumber, poid, soid, cooperativeprov, rulestartmonth, ruleendmonth
        from stl_sync_rule
       where pospecnumber = '50021';

      update stl_ir95_rule a
         set prov_count = (select count(*) from stl_ir95_rule b where a.soid = b.soid);

      update stl_ir95_rule
         set settle_rate = round(1 / prov_count, 7);

      --停机保号费
      insert into stl_tariff_parameter_t
        SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.sospecnumber product_code,
             a.POID poid_inst_id, a.SOID svc_inst_id, a.order_mode order_mode, b.RULE_ID rule_id,
             e.RATE_ID rate_id, '50' charge_item, a.in_prov object_value,
             1 tariff_type, 0 calc_priority,
             a.settle_rate rate_value, /*decode(a.ORDERTYPE, '1', decode(floor(a.RULESTARTMONTH / 201501),
             0, to_date('201501', 'yyyymm'), decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))),*/
             decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))/*)*/ eff_date,
             decode(floor(a.ruleendmonth / to_char(b.exp_date, 'YYYYMM')), 0, add_months(to_date(a.RULEENDMONTH, 'yyyymm'), 1) - 1, b.exp_date) exp_date, 0 dest_source, 1 route_flag
        FROM STL_IR95_RULE a,
             STL_OFFER_T b,
             STL_RATE_T c,
             STL_RULE_ITEM_T d,
             STL_TARIFF_RATE_T e
       WHERE a.POSPECNUMBER = b.OFFER_CODE
         and a.sospecnumber = b.product_code
         AND a.order_mode = b.ORDER_MODE
         AND b.RULE_ID = c.RULE_ID
         AND b.RULE_ID = d.RULE_ID
         and d.charge_item = '50'
         AND e.TARIFF_TYPE = 1
         and e.match_mode = 2
         and a.order_mode = 1
         AND c.RATE_ID = e.RATE_ID
         AND a.POSPECNUMBER = '50021'
         and a.sospecnumber = '5002101'
         AND b.DATA_SOURCE = 1;

       --功能费
       insert into stl_tariff_parameter_t
         SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.sospecnumber product_code,
             a.POID poid_inst_id, a.SOID svc_inst_id, a.order_mode order_mode, b.RULE_ID rule_id,
             e.RATE_ID rate_id, '02' charge_item, a.in_prov object_value,
             1 tariff_type, 0 calc_priority,
             a.settle_rate rate_value, /*decode(a.ORDERTYPE, '1', decode(floor(a.RULESTARTMONTH / 201501),
             0, to_date('201501', 'yyyymm'), decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))),*/
             decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))/*)*/ eff_date,
             decode(floor(a.ruleendmonth / to_char(b.exp_date, 'YYYYMM')), 0, add_months(to_date(a.RULEENDMONTH, 'yyyymm'), 1) - 1, b.exp_date) exp_date, 0 dest_source, 1 route_flag
        FROM STL_IR95_RULE a,
             STL_OFFER_T b,
             STL_RATE_T c,
             STL_RULE_ITEM_T d,
             STL_TARIFF_RATE_T e
       WHERE a.POSPECNUMBER = b.OFFER_CODE
         and a.sospecnumber = b.product_code
         AND a.order_mode = b.ORDER_MODE
         AND b.RULE_ID = c.RULE_ID
         AND b.RULE_ID = d.RULE_ID
         and d.charge_item = '02'
         AND e.TARIFF_TYPE = 1
         and e.match_mode = 2
         and a.order_mode = 1
         AND c.RATE_ID = e.RATE_ID
         AND a.POSPECNUMBER = '50021'
         and a.sospecnumber <> '5002101'
         AND b.DATA_SOURCE = 1;

      COMMIT;
      LOG('IR95 ends.', 'TARIFF');

    EXCEPTION
      WHEN OTHERS THEN
        outSysError := 'Exception occurred when inserting CP rules into Tariff Rule Table.
                       ' || ' ErrorCode: ' || SQLCODE || ':' || SQLERRM;
        outReturn := -1;
        LOG(outSysError, 'TARIFF');
        ROLLBACK;
    END;

    BEGIN
      LOG('SRV6 VPN begins.', 'TARIFF');
      --按产品（srv6 VPN），按比例
      INSERT INTO STL_TARIFF_PARAMETER_T
      SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
             a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id,
             e.RATE_ID rate_id, '-1' charge_item, a.COOPERATIVEPROV object_value,
             a.SETTLEMEASURE tariff_type, decode(a.PRIORITY, NULL,
             row_number() over(PARTITION BY a.POID, a.SOID, a.FEETYPE, a.RULESTARTMONTH, a.RULEENDMONTH
             ORDER BY a.SETTLERATE) - 1, a.PRIORITY) calc_priority,
             decode(a.SETTLEMEASURE, '1', round(a.SETTLERATE / a.SUMSETTLERATE, 27), '2',
             a.SETTLEFEE) rate_value, decode(a.ORDERTYPE, '1', decode(floor(a.RULESTARTMONTH / 201501),
             0, to_date('201501', 'yyyymm'), decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))),
             decode(floor(a.rulestartmonth / to_char(b.eff_date, 'YYYYMM')), 0, b.eff_date, to_date(a.rulestartmonth, 'YYYYMM'))) eff_date,
             decode(floor(a.ruleendmonth / to_char(b.exp_date, 'YYYYMM')), 0, add_months(to_date(a.RULEENDMONTH, 'yyyymm'), 1) - 1, b.exp_date) exp_date, 0 dest_source, 1 route_flag
        FROM STL_SYNC_RULE a,
             STL_OFFER_T b,
             STL_RATE_T c,
             STL_RULE_ITEM_T d,
             STL_TARIFF_RATE_T e
       WHERE a.POSPECNUMBER = b.OFFER_CODE
         AND a.SOSPECNUMBER = b.PRODUCT_CODE
         AND a.ORDERTYPE = b.ORDER_MODE
         AND b.RULE_ID = c.RULE_ID
         AND b.RULE_ID = d.RULE_ID
         AND a.FEETYPE = d.CHARGE_ITEM
         AND a.SETTLEMEASURE = e.TARIFF_TYPE
         AND c.RATE_ID = e.RATE_ID
         AND a.POSPECNUMBER IN ('50120')
         and (a.sospecnumber not in (select product_code from stl_config_db2f where type_nm = 'LP' and column_nm = 'product_code')
              or a.sospecnumber is null)
         AND a.SETTLEMEASURE = 1
         AND b.DATA_SOURCE = 1;
      LOG('SRV6 VPN ends.', 'TARIFF');
      COMMIT;

    EXCEPTION
      WHEN OTHERS THEN
        outSysError := 'Exception occurred when inserting SRV6 VPN rules into Tariff Rule Table.
                       ' || ' ErrorCode: ' || SQLCODE || ':' || SQLERRM;
        outReturn := -1;
        LOG(outSysError, 'TARIFF');
        ROLLBACK;
    END;
    
    BEGIN
      LOG('GJDWJK begins.', 'TARIFF');
      --按产品（高精度定位），2406费项
      iv_Sql := 'INSERT INTO STL_TARIFF_PARAMETER_T ' ||
      'SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code, ' ||
             'a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id, ' ||
             'e.RATE_ID rate_id, a.feetype charge_item, a.COOPERATIVEPROV object_value, ' ||
             'a.settlemeasure tariff_type, 0 calc_priority, ' ||
             'round(a.SETTLEFEE * f.amount / decode(a.sospecnumber, ''2022999400074101'', 100, 1)) rate_value , ' ||
             'to_date(a.RULESTARTMONTH, ''yyyymm'') eff_date, add_months(to_date( ' ||
             'a.RULEENDMONTH, ''yyyymm''), 1) - 1 exp_date, 98 dest_source, 1 route_flag ' ||
        'FROM STL_SYNC_RULE a, ' ||
             'STL_OFFER_T b, ' ||
             'STL_RATE_T c, ' ||
             'STL_RULE_ITEM_T d, ' ||
             'STL_TARIFF_RATE_T e, ' ||
             'stludr.SYNC_BL_RULE_' || inMonth || '@udr_link f ' ||
       'WHERE a.POSPECNUMBER = b.OFFER_CODE ' ||
         'AND a.sospecnumber = b.PRODUCT_CODE ' ||
         'AND a.ORDERTYPE = b.ORDER_MODE ' ||
         'AND b.RULE_ID = c.RULE_ID ' ||
         'AND b.RULE_ID = d.RULE_ID ' ||
         'AND a.FEETYPE = d.CHARGE_ITEM ' ||
         'AND e.TARIFF_TYPE = 2 ' ||
         'AND c.RATE_ID = e.RATE_ID ' ||
         'AND a.POSPECNUMBER = ''50091'' ' ||
         'AND a.feetype = ''2406'' ' ||
         'AND a.SETTLEMEASURE = ''2'' ' ||
         'AND b.DATA_SOURCE = 1 ' ||
         'and a.soid = f.soid ' ||
         'and f.pospecnumber = ''50091'' ' ||
         'and f.feetype = ''2406''';
       
       execute immediate iv_Sql;
       
      --按产品（高精度定位），其它费项
      iv_Sql := 'INSERT INTO STL_TARIFF_PARAMETER_T ' ||
      'SELECT SEQ_TARIFF_PARAMETER_ID.NEXTVAL id, a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code, ' ||
             'a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERTYPE order_mode, b.RULE_ID rule_id, ' ||
             'e.RATE_ID rate_id, a.feetype charge_item, a.COOPERATIVEPROV object_value, ' ||
             'a.settlemeasure tariff_type, 0 calc_priority, ' ||
             'a.SETTLEFEE rate_value, to_date(a.RULESTARTMONTH, ''yyyymm'') eff_date, add_months(to_date( ' ||
             'a.RULEENDMONTH, ''yyyymm''), 1) - 1 exp_date, 98 dest_source, 1 route_flag ' ||
        'FROM STL_SYNC_RULE a, ' ||
             'STL_OFFER_T b, ' ||
             'STL_RATE_T c, ' ||
             'STL_RULE_ITEM_T d, ' ||
             'STL_TARIFF_RATE_T e ' ||
       'WHERE a.POSPECNUMBER = b.OFFER_CODE ' ||
         'AND a.sospecnumber = b.PRODUCT_CODE ' ||
         'AND a.ORDERTYPE = b.ORDER_MODE ' ||
         'AND b.RULE_ID = c.RULE_ID ' ||
         'AND b.RULE_ID = d.RULE_ID ' ||
         'AND a.FEETYPE = d.CHARGE_ITEM ' ||
         'AND e.TARIFF_TYPE = 2 ' ||
         'AND c.RATE_ID = e.RATE_ID ' ||
         'AND a.POSPECNUMBER = ''50091'' ' ||
         'AND a.feetype <> ''2406'' ' ||
         'AND a.SETTLEMEASURE = ''2'' ' ||
         'AND b.DATA_SOURCE = 1';
         
      execute immediate iv_Sql;
      LOG('GJDWJK ends.', 'TARIFF');
      COMMIT;

    EXCEPTION
      WHEN OTHERS THEN
        outSysError := 'Exception occurred when inserting GJDWJK rules into Tariff Rule Table.
                       ' || ' ErrorCode: ' || SQLCODE || ':' || SQLERRM;
        outReturn := -1;
        LOG(outSysError, 'TARIFF');
        ROLLBACK;
    END;


  EXCEPTION
    WHEN OTHERS THEN
      LOG(SQLCODE || ':' || SQLERRM, 'TARIFF');
  END;