/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：规则计算-为repart规则表增加分区
**/
DROP PROCEDURE IF EXISTS stlusers.P_SETTLE_RULE_PROC_PARTITION_BUILD;
DELIMITER ;;
CREATE DEFINER="stlusers"@"10.%" PROCEDURE "P_SETTLE_RULE_PROC_PARTITION_BUILD"(
    inMonth          IN   VARCHAR2
)
AS
    part_count    NUMBER(4);
    part_month    varchar2(6);
    v_proc_name   VARCHAR2(36) := 'P_SETTLE_RULE_PROC_PARTITION_BUILD';
BEGIN

    part_month := to_char(add_months(to_date(inMonth, 'yyyymm'), 1), 'yyyymm');


    -- SELECT COUNT(1) INTO part_count FROM USER_TAB_PARTITIONS
    -- WHERE TABLE_NAME = 'STL_REPART_PARAMETER_T'
    -- AND PARTITION_NAME = 'P_' || part_month;

    SELECT
		  count(1) INTO part_count
    FROM
      INFORMATION_SCHEMA.PARTITIONS
    WHERE
      TABLE_SCHEMA = 'stlusers'
      AND TABLE_NAME = 'STL_REPART_PARAMETER_T'
      AND PARTITION_NAME = 'P_' || part_month;


    IF part_count = 0
    THEN
      BEGIN
          -- 清空原表
          set @vSql := 'ALTER TABLE STL_REPART_PARAMETER_T ' ||
              'ADD PARTITION ( partition P_' || part_month || ' VALUES LESS THAN (''' || part_month || ''') ENGINE = GreatDB)';
          SELECT @vSql;
          PREPARE STMT FROM @vSql;
          EXECUTE STMT;
          DEALLOCATE PREPARE STMT;
      END;

      SELECT  v_proc_name || ' Partition P_' || part_month || ' is built successfully.' as info;
    ELSE
      SELECT  v_proc_name || ' Partition P_' || part_month || ' cannot be built because it exists already.' as info;
    END IF;

    COMMIT;
END ;;
DELIMITER ;