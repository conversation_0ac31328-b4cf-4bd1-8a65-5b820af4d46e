/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：规则计算-repart规则计算
**/
use stlusers;
DROP PROCEDURE IF EXISTS stlusers.P_SETTLE_RULE_PROC_REPART_PARAMETER;

DELIMITER ;;
CREATE or replace DEFINER="stlusers"@"10.%" PROCEDURE `stlusers`.P_SETTLE_RULE_PROC_REPART_PARAMETER(
        inMonth in VARCHAR2,
        inBatch IN VARCHAR2,
        flag_version IN VARCHAR2,
        reserve1 IN VARCHAR2,
        reserve2 IN VARCHAR2,
        proc_out OUT VARCHAR2,
        outSysError OUT VARCHAR2(1000),
        outReturn OUT NUMBER(4),
        outBL OUT VARCHAR2,
        outAR OUT VARCHAR2)
AS
    vSql VARCHAR2(10240);


    ivNextMonth varchar2(6);
    P_ERRCODE   VARCHAR2(16);
    P_ERRMSG    VARCHAR2(1024);

    v_proc_name   VARCHAR2(36) := 'P_SETTLE_RULE_PROC_REPART_PARAMETER';
    day_start int;
    day_end int;
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
GET DIAGNOSTICS CONDITION 1 P_ERRCODE = RETURNED_SQLSTATE, P_ERRMSG = MESSAGE_TEXT;
outSysError := substr(P_ERRMSG, 1, 1000);
outReturn  := -1;
ROLLBACK;


select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg ;
call LOG_PROCEDURES(outSysError, v_proc_name);
SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = outSysError;

END;

BEGIN

         /*优化内容： call P_SETTLE_RULE_PROC_REPART_PARAMETER_WL2('202307','2','1','','','',@p1,@p2,@p3,@p4);
          1、delete STL_REPART_PARAMETER_T 修改为truncate partition
          2、取消STL_GPRSDOM_RULE的插入排序
          alter table stlusers.STL_CDN_HW_RULE modify RATIO varchar(64);
          alter table stludr.sync_interface_bl_202307 add index idx_pospecnumber(pospecnumber)
          alter table stludr.cust_prod_info add index idx_pc_sc_am(product_code, service_code, acct_month);
          alter table stlusers.stl_national_rate add index idx_am_bt(acct_month, biz_type);
          alter table stludr.ur_cdn_202307_t add index idx_1(rate_back_id,sett_prov,ACCU_VOLUME,ACCU_DURATION);
          alter table stl_cdn_rule modify PRODUCT_CODE varchar(64);
          alter table stludr.ur_cdn_202307_t add key idx_accu_volume(accu_volume);
        alter table stludr.ur_cdn_202307_t add key idx_accu_duration(accu_duration);
        alter table stludr.ur_cdn_202307_t add index idx_rbi_sgn_dt (rate_back_id,sub_group_num,dup_time);
        alter table stludr.UR_GPRSDOM_202307_T add index idx_provcode(provcode);
          */

        outSysError := 'OK';
        outReturn := 0;
        ivNextMonth := to_char(add_months(to_date(inMonth, 'yyyymm'), 1), 'yyyymm');

call LOG_PROCEDURES('call P_SETTLE_RULE_PROC_PARTITION_BUILD(inMonth)', v_proc_name);
call P_SETTLE_RULE_PROC_PARTITION_BUILD(inMonth);

call LOG_PROCEDURES('清理当前账期数据：'||inMonth, v_proc_name);
set @vSql := 'ALTER TABLE STL_REPART_PARAMETER_T TRUNCATE PARTITION P_'|| ivNextMonth;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
select concat('1_', now());


--开始时间
SET @P_TIME := SYSDATE;
call LOG_PROCEDURES('开始执行', v_proc_name);

call LOG_PROCEDURES('行业WLAN处理', v_proc_name);
--行业WLAN
TRUNCATE TABLE STL_WLHY_RULE;



            set @vSql := 'INSERT INTO STL_WLHY_RULE ' ||
                 '(ORGMONTH, ORDER_MODE, CUSTOMERNUMBER, POSPECNUMBER, SOSPECNUMBER, ' ||
                 'POID, SOID, PROV_CD, AMOUNT, TOTAL_AMOUNT, RATIO) ' ||
                 'SELECT a.ACCT_MONTH, ' ||
                        'a.ORDER_MODE, ' ||
                        'a.EC_CODE, ' ||
                        'a.OFFER_CODE, ' ||
                        'a.PRODUCT_CODE, ' ||
                        'a.OFFER_ORDER_ID, ' ||
                        'a.PRODUCT_ORDER_ID, ' ||
                        'a.SETT_PROV, ' ||
                        'a.prov_amount, ' ||
                        'b.total_amount, ' ||
                        'round(a.prov_amount / b.total_amount * 100, 7) ratio ' ||
                   'FROM (SELECT ACCT_MONTH, ' ||
                                'ORDER_MODE, ' ||
                                'EC_CODE, ' ||
                                'OFFER_CODE, ' ||
                                'PRODUCT_CODE, ' ||
                                'OFFER_ORDER_ID, ' ||
                                'PRODUCT_ORDER_ID, ' ||
                                'SETT_PROV, ' ||
                                'sum(ACCU_DURATION) prov_amount ' ||
                           'FROM stludr.UR_WLHY_' || inMonth || '_T ' ||
                          'GROUP BY ACCT_MONTH, ORDER_MODE, EC_CODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID, SETT_PROV) a, ' ||
                        '(SELECT ACCT_MONTH, ' ||
                                'ORDER_MODE, ' ||
                                'EC_CODE, ' ||
                                'OFFER_CODE, ' ||
                                'PRODUCT_CODE, ' ||
                                'OFFER_ORDER_ID, ' ||
                                'PRODUCT_ORDER_ID, ' ||
                                'sum(ACCU_DURATION) total_amount ' ||
                           'FROM stludr.UR_WLHY_' || inMonth || '_T ' ||
                          'GROUP BY ACCT_MONTH, ORDER_MODE, EC_CODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID) b ' ||
                  'WHERE a.ACCT_MONTH = b.ACCT_MONTH ' ||
                    'AND a.OFFER_ORDER_ID = b.OFFER_ORDER_ID ' ||
                    'AND a.PRODUCT_ORDER_ID = b.PRODUCT_ORDER_ID ' ||
                    'AND b.total_amount <> 0 ' ||
                  'ORDER BY ACCT_MONTH, PRODUCT_ORDER_ID';
select concat('2_', now());
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

--共享版
INSERT INTO STL_REPART_PARAMETER_T
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*, -1 FEETYPE, 0 dest_source, 1 route_flag
FROM
    (SELECT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
            a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDER_MODE, b.RULE_ID rule_id,
            c.RATE_ID rate_id, row_number() over(PARTITION BY a.SOID ORDER BY to_number(a.RATIO)) - 1
                 calc_priority, a.PROV_CD object_value, '1' tariff_type,
            a.RATIO / 100 rate_value, to_date(a.ORGMONTH, 'yyyymm') eff_date, add_months(to_date(
                                                                                                 a.ORGMONTH, 'yyyymm'), 1) - 1 exp_date, a.ORGMONTH acct_month
     FROM STL_WLHY_RULE a,
          STL_OFFER_T b,
          STL_RATE_T c
     WHERE a.POSPECNUMBER = b.OFFER_CODE
       AND a.POSPECNUMBER in ('50009')
       AND b.PRODUCT_CODE = -1
       AND b.RULE_ID = c.RULE_ID
       AND b.DATA_SOURCE = 1
       AND a.ORGMONTH = inMonth) data;

select concat('3_', now());
call LOG_PROCEDURES('SIM盾处理', v_proc_name);
--SIM盾
TRUNCATE TABLE STL_SIM_RULE;


            set @vSql := 'INSERT INTO STL_SIM_RULE ' ||
                 '(ORGMONTH, ORDER_MODE, CUSTOMERNUMBER, POSPECNUMBER, SOSPECNUMBER, ' ||
                 'POID, SOID, PROV_CD, AMOUNT, TOTAL_AMOUNT, RATIO) ' ||
                 'SELECT a.ACCT_MONTH, ' ||
                        'a.ORDER_MODE, ' ||
                        'a.EC_CODE, ' ||
                        'a.OFFER_CODE, ' ||
                        'a.PRODUCT_CODE, ' ||
                        'a.OFFER_ORDER_ID, ' ||
                        'a.PRODUCT_ORDER_ID, ' ||
                        'a.SETT_PROV, ' ||
                        'a.prov_amount, ' ||
                        'b.total_amount, ' ||
                        'round(a.prov_amount / b.total_amount * 100 * 0.6, 7) ratio ' ||
                   'FROM (SELECT ACCT_MONTH, ' ||
                                'PROD_ORDER_MODE ORDER_MODE, ' ||
                                'EC_CODE, ' ||
                                'OFFER_CODE, ' ||
                                'PRODUCT_CODE, ' ||
                                'OFFER_ORDER_ID, ' ||
                                'PRODUCT_ORDER_ID, ' ||
                                'MEM_PROVCODE SETT_PROV, ' ||
                                'COUNT(*) prov_amount ' ||
                           'FROM stludr.UR_SIM_' || inMonth || '_T ' ||
                          'GROUP BY ACCT_MONTH, PROD_ORDER_MODE, EC_CODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID, MEM_PROVCODE) a, ' ||
                        '(SELECT ACCT_MONTH, ' ||
                                'PROD_ORDER_MODE ORDER_MODE, ' ||
                                'EC_CODE, ' ||
                                'OFFER_CODE, ' ||
                                'PRODUCT_CODE, ' ||
                                'OFFER_ORDER_ID, ' ||
                                'PRODUCT_ORDER_ID, ' ||
                                'COUNT(*) total_amount ' ||
                           'FROM stludr.UR_SIM_' || inMonth || '_T ' ||
                          'GROUP BY ACCT_MONTH, PROD_ORDER_MODE, EC_CODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID) b ' ||
                  'WHERE a.ACCT_MONTH = b.ACCT_MONTH ' ||
                    'AND a.OFFER_ORDER_ID = b.OFFER_ORDER_ID ' ||
                    'AND a.PRODUCT_ORDER_ID = b.PRODUCT_ORDER_ID ' ||
                    'AND b.total_amount <> 0 ' ||
                  'ORDER BY ACCT_MONTH, PRODUCT_ORDER_ID';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
select concat('4_', now());

insert into stl_sim_rule (ORGMONTH, ORDER_MODE, CUSTOMERNUMBER, POSPECNUMBER, SOSPECNUMBER,POID, SOID, PROV_CD, AMOUNT, TOTAL_AMOUNT, RATIO)
select orgmonth, order_mode, customernumber, pospecnumber, poid, sospecnumber, soid,
       decode(order_mode, 1, '000', 3,'${EC_PROV_CODE_07}'), null, null, 20
from stl_sim_rule
where orgmonth = inMonth
group by orgmonth, order_mode, customernumber, pospecnumber, poid, sospecnumber, soid
union all
select orgmonth, order_mode, customernumber, pospecnumber, poid, sospecnumber, soid,
       '020', null, null, 20
from stl_sim_rule
where orgmonth = inMonth
group by orgmonth, order_mode, customernumber, pospecnumber, poid, sospecnumber, soid;
select concat('5_', now());

INSERT INTO STL_REPART_PARAMETER_T
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*, -1 FEETYPE, 0 dest_source, 1 route_flag
FROM
    (SELECT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
            a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDER_MODE, b.RULE_ID rule_id,
            c.RATE_ID rate_id, row_number() over(PARTITION BY a.SOID ORDER BY to_number(a.RATIO)) - 1
                    calc_priority, a.PROV_CD object_value, '1' tariff_type,
            a.RATIO / 100 rate_value, to_date(a.ORGMONTH, 'yyyymm') eff_date, add_months(to_date(
                                                                                                 a.ORGMONTH, 'yyyymm'), 1) - 1 exp_date, a.ORGMONTH acct_month
     FROM STL_SIM_RULE a,
          STL_OFFER_T b,
          STL_RATE_T c
     WHERE a.POSPECNUMBER = b.OFFER_CODE
       AND a.POSPECNUMBER in ('50015')
       AND b.PRODUCT_CODE = -1
       AND b.RULE_ID = c.RULE_ID
       AND b.DATA_SOURCE = 1
       AND a.ORGMONTH = inMonth
       and a.order_mode = b.order_mode) data;

select concat('6_', now());
call LOG_PROCEDURES('企业互联网电视（基础产品）处理', v_proc_name);
--企业互联网电视（基础产品）
INSERT INTO STL_REPART_PARAMETER_T
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, '4040109' offer_code, '4010901' product_code,
       a.POID poid_inst_id, a.SOID svc_inst_id, d.PROD_ORDER_MODE order_mode, b.RULE_ID rule_id,
       c.RATE_ID rate_id, 0 calc_priority, a.ORDERPROV object_value, '1' tariff_type,
       '1' rate_value, to_date(a.ORGMONTH, 'yyyymm') eff_date, add_months(to_date(
                                                                                  a.ORGMONTH, 'yyyymm'), 1) - 1 exp_date, a.ORGMONTH acct_month, '-1' charge_item, 0 dest_source,
       1 route_flag
FROM STL_INTER_ENT_TV a,
     STL_OFFER_T b,
     STL_RATE_T c,
     STL_SERV_BIZ_CODE d
WHERE b.OFFER_CODE = '4040109'
  AND b.PRODUCT_CODE = '4010901'
  AND d.PROD_ORDER_MODE = b.ORDER_MODE
  AND b.RULE_ID = c.RULE_ID
  AND a.SOID = d.ORDER_ID
  AND inMonth BETWEEN to_char(d.EFFECTIVE_DATE, 'yyyymm')
    AND to_char(d.EXPIRY_DATE, 'yyyymm')
  AND b.DATA_SOURCE = 5
  AND a.ORGMONTH = inMonth;

select concat('7_', now());
call LOG_PROCEDURES('云MAS，异网大于本网且异网结算方家数为2的', v_proc_name);
----云MAS
----异网大于本网且异网结算方家数为2的
--受理模式
set @vSql := 'INSERT INTO STLUSERS.STL_REPART_PARAMETER_T ' ||
                         'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.* FROM ' ||
                         '(SELECT a.pospecnumber, a.sospecnumber, ' ||
                               'a.offer_order_id poid_inst_id, a.product_order_id svc_inst_id, a.ordermode, b.RULE_ID rule_id, ' ||
                               'c.RATE_ID rate_id, row_number() over(PARTITION BY a.offer_order_id ' ||
                               'ORDER BY a.RATE) calc_priority, a.oth_sp object_value, ' ||
                               'e.tariff_type tariff_type, decode(row_number() over(PARTITION BY a.offer_order_id ' ||
                               'ORDER BY a.RATE), 1, a.rate, ''$'') rate_value, ' ||
                               'to_date(a.orgmonth, ''yyyymm'') eff_date, add_months(to_date( ' ||
                               'a.orgmonth, ''yyyymm''), 1) - 1 exp_date, a.orgmonth, ''-1'' charge_item, ' ||
                               'decode(a.charge_code, ''1040'', 97, ''3870'', 96) dest_source, decode(a.charge_code, ''1040'', 1, ''3870'', 0)  route_flag ' ||
                          'FROM stludr.cmas_swap_02 a, ' ||
                               'STL_OFFER_T b, ' ||
                               'STL_RATE_T c, ' ||
                               'STL_REPART_RATE_T e ' ||
                         'WHERE a.pospecnumber = b.OFFER_CODE ' ||
                           'and b.order_mode = ''1'' ' ||
                           'AND a.product_order_id IS NULL ' ||
                           'AND b.RULE_ID = c.RULE_ID ' ||
                           'AND c.RATE_ID = e.RATE_ID ' ||
                           'AND e.MATCH_MODE = 1 ' ||
                           'and e.tariff_type = 1 ' ||
                           'AND b.DATA_SOURCE = 1 ' ||
                           'AND c.RATE_TYPE = 3 ' ||
                           'and a.ordermode = b.order_mode ' ||
                           'AND ''' || inMonth || ''' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') ' ||
                           'and ''' || inMonth || ''' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') ' ||
                           'AND a.orgmonth = ''' || inMonth || ''' ' ||
                           'AND (a.pospecnumber IN (SELECT DISTINCT OFFER_CODE FROM STL_BUSINESS_TYPE ' ||
                                                 'WHERE BIZ_TYPE IN (''CMAS1'')) or a.sospecnumber = ''110151'') ' ||
                          'UNION ALL ' ||
                       'SELECT DISTINCT a.pospecnumber, a.sospecnumber, ' ||
                               'a.offer_order_id poid_inst_id, a.product_order_id svc_inst_id, a.ORDERMODE, b.RULE_ID rule_id, ' ||
                               'c.RATE_ID rate_id, row_number() over(PARTITION BY a.offer_order_id ' ||
                               'ORDER BY a.RATE) calc_priority, a.oth_sp object_value, ' ||
                               'e.tariff_type tariff_type, decode(row_number() over(PARTITION BY a.offer_order_id, a.product_order_id ' ||
                               'ORDER BY a.RATE), 1, a.rate, ''$'') rate_value, ' ||
                               'to_date(a.orgmonth, ''yyyymm'') eff_date, add_months(to_date( ' ||
                               'a.orgmonth, ''yyyymm''), 1) - 1 exp_date, a.orgmonth, ''-1'' charge_item, ' ||
                               'decode(a.charge_code, ''1040'', 97, ''3870'', 96) dest_source, decode(a.charge_code, ''1040'', 1, ''3870'', 0)  route_flag ' ||
                          'FROM stludr.cmas_swap_02 a, ' ||
                               'STL_OFFER_T b, ' ||
                               'STL_RATE_T c, ' ||
                               'STL_REPART_RATE_T e ' ||
                         'WHERE a.pospecnumber = b.OFFER_CODE ' ||
                           'and b.order_mode = ''1'' ' ||
                           'AND (a.sospecnumber = b.PRODUCT_CODE OR a.sospecnumber IS NULL) ' ||
                           'AND a.product_order_id IS NOT NULL ' ||
                           'AND b.RULE_ID = c.RULE_ID ' ||
                           'AND c.RATE_ID = e.RATE_ID ' ||
                           'AND e.MATCH_MODE = 2 ' ||
                           'and e.tariff_type = 1 ' ||
                           'AND b.DATA_SOURCE = 1 ' ||
                           'AND c.RATE_TYPE = 3 ' ||
                           'and a.ordermode = b.order_mode ' ||
                           'AND ''' || inMonth || ''' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') ' ||
                           'and ''' || inMonth || ''' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') ' ||
                           'AND a.orgmonth = ''' || inMonth || ''' ' ||
                           'AND (a.pospecnumber IN (SELECT DISTINCT OFFER_CODE FROM STL_BUSINESS_TYPE ' ||
                                   'WHERE BIZ_TYPE IN (''CMAS1'')) or a.sospecnumber = ''110151'')) data';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
select concat('8_', now());
call LOG_PROCEDURES('云MAS,异网不大于本网或异网大于本网但异网结算方家数为1的', v_proc_name);


         --异网不大于本网或异网大于本网但异网结算方家数为1的
         --受理模式1
                 set @vSql := 'INSERT INTO STLUSERS.STL_REPART_PARAMETER_T ' ||
                              'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.* FROM ' ||
                              '(SELECT DISTINCT a.pospecnumber, a.sospecnumber, ' ||
                                    'a.poid poid_inst_id, a.soid svc_inst_id, a.ordermode, b.RULE_ID rule_id, ' ||
                                    'c.RATE_ID rate_id, 0 calc_priority, a.prov_cd object_value, ' ||
                                    'e.tariff_type tariff_type, sum(a.amount) rate_value, ' ||
                                    'to_date(a.orgmonth, ''yyyymm'') eff_date, add_months(to_date( ' ||
                                    'a.orgmonth, ''yyyymm''), 1) - 1 exp_date, a.orgmonth, ''-1'' charge_item, ' ||
                                    'decode(a.feetype, ''1040'', 97, ''3870'', 96) dest_source, decode(a.feetype, ''1040'', 1, ''3870'', 0)  route_flag ' ||
                               'FROM stludr.SYNC_BL_RULE_' || inMonth || ' a, ' ||
                                    'STL_OFFER_T b, ' ||
                                    'STL_RATE_T c, ' ||
                                    'STL_REPART_RATE_T e ' ||
                              'WHERE a.pospecnumber = b.OFFER_CODE ' ||
                                'and b.order_mode = ''1'' ' ||
                                'AND a.soid IS NULL ' ||
                                'AND b.RULE_ID = c.RULE_ID ' ||
                                'AND c.RATE_ID = e.RATE_ID ' ||
                                'AND e.MATCH_MODE = 1 ' ||
                                'and e.tariff_type = 2 ' ||
                                'AND b.DATA_SOURCE = 1 ' ||
                                'AND c.RATE_TYPE = 3 ' ||
                                'and a.ordermode = b.order_mode ' ||
                                'and a.status = ''0'' ' ||
                                'AND ' || inMonth || ' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') ' ||
                                'and ' || inMonth || ' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') ' ||
                                'AND a.orgmonth = ' || inMonth || ' ' ||
                                'AND (a.pospecnumber IN (SELECT DISTINCT OFFER_CODE FROM STL_BUSINESS_TYPE ' ||
                                                      'WHERE BIZ_TYPE IN (''CMAS1'')) or a.sospecnumber = ''110151'') ' ||
                              'GROUP BY a.pospecnumber, a.sospecnumber, a.poid, a.soid, a.ORDERMODE, b.RULE_ID, ' ||
                                    'c.RATE_ID, e.tariff_type, a.orgmonth, a.feetype, a.prov_cd ' ||
                               'UNION ALL ' ||
                            'SELECT DISTINCT a.pospecnumber, a.sospecnumber, ' ||
                                    'a.poid poid_inst_id, a.soid svc_inst_id, a.ORDERMODE, b.RULE_ID rule_id, ' ||
                                    'c.RATE_ID rate_id, 0 calc_priority, a.prov_cd object_value, ' ||
                                    'e.tariff_type tariff_type, sum(a.amount) rate_value, ' ||
                                    'to_date(a.orgmonth, ''yyyymm'') eff_date, add_months(to_date( ' ||
                                    'a.orgmonth, ''yyyymm''), 1) - 1 exp_date, a.orgmonth, ''-1'' charge_item, ' ||
                                    'decode(a.feetype, ''1040'', 97, ''3870'', 96) dest_source, decode(a.feetype, ''1040'', 1, ''3870'', 0) route_flag ' ||
                               'FROM stludr.SYNC_BL_RULE_' || inMonth || ' a, ' ||
                                    'STL_OFFER_T b, ' ||
                                    'STL_RATE_T c, ' ||
                                    'STL_REPART_RATE_T e ' ||
                              'WHERE a.pospecnumber = b.OFFER_CODE ' ||
                                'and b.order_mode = ''1'' ' ||
                                'AND (a.sospecnumber = b.PRODUCT_CODE OR a.sospecnumber IS NULL) ' ||
                                'AND a.soid IS NOT NULL ' ||
                                'AND b.RULE_ID = c.RULE_ID ' ||
                                'AND c.RATE_ID = e.RATE_ID ' ||
                                'AND e.MATCH_MODE = 2 ' ||
                                'and e.tariff_type = 2 ' ||
                                'AND b.DATA_SOURCE = 1 ' ||
                                'AND c.RATE_TYPE = 3 ' ||
                                'and a.ordermode = b.order_mode ' ||
                                'and a.status = ''0'' ' ||
                                'AND ' || inMonth || ' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') ' ||
                                'and ' || inMonth || ' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') ' ||
                                'AND a.orgmonth = ' || inMonth || ' ' ||
                                'AND (a.pospecnumber IN (SELECT DISTINCT OFFER_CODE FROM STL_BUSINESS_TYPE ' ||
                                                      'WHERE BIZ_TYPE IN (''CMAS1'')) or a.sospecnumber = ''110151'') ' ||
                              'GROUP BY a.pospecnumber, a.sospecnumber, a.poid, a.soid, a.ORDERMODE, b.RULE_ID, ' ||
                                    'c.RATE_ID, e.tariff_type, a.orgmonth, a.feetype, a.prov_cd) data';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
select concat('9_', now());



            -- 本网 null->${EC_PROV_CODE_07}
            set @vSql := 'INSERT INTO STL_REPART_PARAMETER_T ' ||
                         'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, t.* FROM ' ||
                         '(SELECT DISTINCT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,  ' ||
                                'a.POID poid_inst_id, a.SOID svc_inst_id, a.ordermode, b.RULE_ID rule_id, ' ||
                                'c.RATE_ID rate_id, 1 calc_priority, ''${EC_PROV_CODE_07}'' object_value,  ' ||
                                'e.tariff_type tariff_type,  ''$'' rate_value,  ' ||
                                'to_date(a.ORGMONTH, ''yyyymm'') eff_date, add_months(to_date(  ' ||
                                'a.ORGMONTH, ''yyyymm''), 1) - 1 exp_date, a.ORGMONTH, ''-1'' charge_item, decode(a.ordermode, ''1'', 0, ''3'', 98) dest_source, ' ||
                                '0 route_flag ' ||
                           'FROM stludr.SYNC_INTERFACE_BL_' || inMonth || ' a,  ' ||
                                'STL_OFFER_T b,  ' ||
                                'STL_RATE_T c,  ' ||
                                'STL_REPART_RATE_T e  ' ||
                         'WHERE a.POSPECNUMBER = b.OFFER_CODE  ' ||
                            'AND A.SOID IS NULL  ' ||
                            'and a.poid not in (select distinct offer_order_id from stludr.cmas_swap_02) ' ||
                            'AND b.RULE_ID = c.RULE_ID  ' ||
                            'AND c.RATE_ID = e.RATE_ID  ' ||
                            'AND e.MATCH_MODE = 1 ' ||
                            'and e.tariff_type = 2 ' ||
                            'AND c.RATE_TYPE = 3 ' ||
							'and a.ordermode = ''1'' ' ||
                            'and a.ordermode = b.order_mode ' ||
                            'AND ' || inMonth || ' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') ' ||
                            'and ' || inMonth || ' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') ' ||
                            'AND (a.POSPECNUMBER IN (SELECT DISTINCT OFFER_CODE FROM stlusers.STL_BUSINESS_TYPE ' ||
                                                         'WHERE BIZ_TYPE IN (''CMAS1''))  or a.sospecnumber = ''110151'') ' ||
                            'AND decode(a.DATASOURCE, ''BL'', ''1'', ''AR'', ''2'') = b.DATA_SOURCE  ' ||
                            'AND a.ORGMONTH = ' || inMonth || ' ' ||
                            'UNION ALL ' ||
                         'SELECT DISTINCT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,  ' ||
                                'a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERMODE, b.RULE_ID rule_id, ' ||
                                'c.RATE_ID rate_id, 1 calc_priority, ''${EC_PROV_CODE_07}'' object_value,  ' ||
                                'e.tariff_type tariff_type,  ''$'' rate_value,  ' ||
                                'to_date(a.ORGMONTH, ''yyyymm'') eff_date, add_months(to_date(  ' ||
                                'a.ORGMONTH, ''yyyymm''), 1) - 1 exp_date, a.ORGMONTH, ''-1'' charge_item, decode(a.ordermode, ''1'', 0, ''3'', 98) dest_source, ' ||
                                '0 route_flag ' ||
                           'FROM stludr.SYNC_INTERFACE_BL_' || inMonth || ' a,  ' ||
                                'STL_OFFER_T b,  ' ||
                                'STL_RATE_T c,  ' ||
                                'STL_REPART_RATE_T e  ' ||
                         'WHERE a.POSPECNUMBER = b.OFFER_CODE  ' ||
                            'AND a.SOSPECNUMBER = b.PRODUCT_CODE ' ||
                            'AND a.SOID IS NOT NULL ' ||
                            'and a.soid not in (select distinct product_order_id from stludr.cmas_swap_02 where product_order_id is not null) ' ||
                            'AND b.RULE_ID = c.RULE_ID  ' ||
                            'AND c.RATE_ID = e.RATE_ID  ' ||
                            'AND e.MATCH_MODE = 2 ' ||
                            'and e.tariff_type = 2 ' ||
                            'AND c.RATE_TYPE = 3 ' ||
							'and a.ordermode = ''1'' ' ||
                            'and a.ordermode = b.order_mode ' ||
                            'AND ' || inMonth || ' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') ' ||
                            'and ' || inMonth || ' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') ' ||
                            'AND (a.POSPECNUMBER IN (SELECT DISTINCT OFFER_CODE FROM stlusers.STL_BUSINESS_TYPE ' ||
                                                         'WHERE BIZ_TYPE IN (''CMAS1''))  or a.sospecnumber = ''110151'') ' ||
                            'AND decode(a.DATASOURCE, ''BL'', ''1'', ''AR'', ''2'') = b.DATA_SOURCE  ' ||
                            'AND a.ORGMONTH = ' || inMonth || ') t';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
select concat('10_', now());


call LOG_PROCEDURES('SD-WAN组网业务政企收入还原', v_proc_name);

----SD-WAN组网业务政企收入还原
--国际公司 【冗余】
set @vSql := 'INSERT INTO STLUSERS.STL_REPART_PARAMETER_T ' ||
                         'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.* FROM   '||
                         '(SELECT DISTINCT a.pospecnumber, a.sospecnumber,   '||
                         'a.poid poid_inst_id, a.soid svc_inst_id, a.ORDERMODE, b.RULE_ID rule_id, '||
                         'c.RATE_ID rate_id, 0 calc_priority, ''030'' object_value, '||
                         'e.tariff_type tariff_type, a.notaxfee rate_value, '||
                         'to_date(a.orgmonth, ''yyyymm'') eff_date, add_months(to_date(   '||
                         'a.orgmonth, ''yyyymm''), 1) - 1 exp_date, a.orgmonth, a.feetype charge_item, '||
                         '99 dest_source, 1 route_flag '||
                         'FROM stludr.SYNC_INTERFACE_SDWAN_' || inMonth || ' a,  '||
                         'STL_OFFER_T b, '||
                         'STL_RATE_T c,  '||
                         'STL_REPART_RATE_T e  '||
                         'WHERE a.pospecnumber = b.OFFER_CODE '||
                         'and b.order_mode = ''1''  '||
                         'AND (a.sospecnumber = b.PRODUCT_CODE OR a.sospecnumber IS NULL) '||
                         'AND a.soid IS NOT NULL   '||
                         'AND b.RULE_ID = c.RULE_ID   '||
                         'AND c.RATE_ID = e.RATE_ID   '||
                         'AND e.MATCH_MODE = 2   '||
                         'AND b.DATA_SOURCE = 1   '||
                         'AND c.RATE_TYPE = 3  '||
                         'and a.ordermode = b.order_mode  '||
                         'and a.feetype not in(''3682'',''3683'',''3684'',''3685'',''3865'' ) '||
                         'AND ' || inMonth || ' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') '||
                         'and ' || inMonth || ' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') '||
                         'AND a.orgmonth = ' || inMonth || '  '||
                         'GROUP BY a.pospecnumber, a.sospecnumber, a.poid, a.soid, a.ORDERMODE, b.RULE_ID, '||
                         ' c.RATE_ID, e.tariff_type, a.orgmonth, a.notaxfee, c.in_object_id, a.feetype) data ';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
select concat('11_', now());


            -- 出账金额-国际公司金额=EC所结金额  NULL->${EC_PROV_CODE_07}
            set @vSql := 'INSERT INTO STLUSERS.STL_REPART_PARAMETER_T ' ||
                         'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, t.* FROM   '||
                         '(SELECT DISTINCT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,  '||
                         'a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERMODE, b.RULE_ID rule_id,  '||
                         'c.RATE_ID rate_id, 1 calc_priority, ''${EC_PROV_CODE_07}'' object_value,  '||
                         'e.tariff_type tariff_type,  ''$'' rate_value,  '||
                         'to_date(a.ORGMONTH, ''yyyymm'') eff_date, add_months(to_date(   '||
                         'a.ORGMONTH, ''yyyymm''), 1) - 1 exp_date, a.ORGMONTH, a.feetype charge_item, decode(a.ordermode, ''1'', 0, ''3'', 98) dest_source, '||
                         '0 route_flag '||
                         'FROM stludr.SYNC_INTERFACE_BL_' || inMonth || ' a,  '||
                         'STL_OFFER_T b,  '||
                         'STL_RATE_T c,   '||
                         'STL_REPART_RATE_T e  '||
                         'WHERE a.POSPECNUMBER = b.OFFER_CODE  '||
                         'AND a.SOSPECNUMBER = b.PRODUCT_CODE '||
                         'AND a.SOID IS NOT NULL   '||
                         'AND b.RULE_ID = c.RULE_ID   '||
                         'AND c.RATE_ID = e.RATE_ID   '||
                         'AND e.MATCH_MODE = 2  '||
                         'and e.tariff_type = 2   '||
                         'AND c.RATE_TYPE = 3   '||
                         'and a.ordermode = b.order_mode '||
                         'and a.feetype in(''02'',''08'',''2163'') '||
                         'AND ' || inMonth || ' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') '||
                         'and ' || inMonth || ' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') '||
                         'AND decode(a.DATASOURCE, ''BL'', ''1'', ''AR'', ''2'') = b.DATA_SOURCE '||
                         'AND a.ORGMONTH = ' || inMonth || ') t ';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
select concat('12_', now());


           -- 成员视频彩铃    省间结算金额  NULL ${MEM_PROV_15}
           set @vSql := 'INSERT INTO STLUSERS.STL_REPART_PARAMETER_T ' ||
             'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.* FROM  '||
            '(SELECT DISTINCT a.pospecnumber, a.sospecnumber,    '||
                 'a.poid poid_inst_id, a.soid svc_inst_id, a.ORDERMODE, b.RULE_ID rule_id,  '||
                 'c.RATE_ID rate_id, 0 calc_priority, ''${MEM_PROV_15}'' object_value,   '||
                 'e.tariff_type tariff_type, a.notaxfee rate_value,   '||
                 'to_date(a.orgmonth, ''yyyymm'') eff_date, add_months(to_date(    '||
                 'a.orgmonth, ''yyyymm''), 1) - 1 exp_date, a.orgmonth, a.feetype charge_item,  '||
                '99 dest_source, 1 route_flag  '||
            'FROM stludr.SYNC_INTERFACE_SDWAN_' || inMonth || ' a,  '||
                 'STL_OFFER_T b,   '||
                 'STL_RATE_T c,   '||
                 'STL_REPART_RATE_T e  '||
             'WHERE a.pospecnumber = b.OFFER_CODE   '||
             'and b.order_mode = ''1''  '||
             'AND (a.sospecnumber = b.PRODUCT_CODE OR a.sospecnumber IS NULL)   '||
             'AND a.soid IS NOT NULL   '||
             'AND b.RULE_ID = c.RULE_ID    '||
             'AND c.RATE_ID = e.RATE_ID    '||
             'AND e.MATCH_MODE = 2     '||
             'AND b.DATA_SOURCE = 1    '||
             'AND c.RATE_TYPE = 3   '||
             'and a.ordermode = b.order_mode   '||
             'and a.feetype in(''3682'',''3683'',''3684'',''3685'',''3865'')   '||
             'AND ' || inMonth || ' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') '||
             'and ' || inMonth || ' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'')  '||
             'AND a.orgmonth = ' || inMonth || '  '||
             'GROUP BY a.pospecnumber, a.sospecnumber, a.poid, a.soid, a.ORDERMODE, b.RULE_ID,  '||
                'c.RATE_ID, e.tariff_type, a.orgmonth, a.notaxfee, c.in_object_id, a.feetype) data  ';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
select concat('13_', now());


           -- 出账金额-省间结算金额=EC所结金额  NULL-> ${EC_PROV_CODE_07}
           set @vSql := 'INSERT INTO STLUSERS.STL_REPART_PARAMETER_T ' ||
            'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, t.* FROM   '||
                '(SELECT DISTINCT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,  '||
                'a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERMODE, b.RULE_ID rule_id,  '||
                 'c.RATE_ID rate_id, 1 calc_priority, ''${EC_PROV_CODE_07}'' object_value,   '||
                 'e.tariff_type tariff_type,  ''$'' rate_value, '||
                 'to_date(a.ORGMONTH, ''yyyymm'') eff_date, add_months(to_date(  '||
                 'a.ORGMONTH, ''yyyymm''), 1) - 1 exp_date, a.ORGMONTH, a.feetype charge_item, decode(a.ordermode, ''1'', 0, ''3'', 98) dest_source,  '||
                 '0 route_flag  '||
            'FROM stludr.SYNC_INTERFACE_BL_' || inMonth || ' a,  '||
                 'STL_OFFER_T b, '||
                 'STL_RATE_T c,   '||
                 'STL_REPART_RATE_T e '||
            'WHERE a.POSPECNUMBER = b.OFFER_CODE  '||
             'AND a.SOSPECNUMBER = b.PRODUCT_CODE   '||
             'AND a.SOID IS NOT NULL    '||
             'AND b.RULE_ID = c.RULE_ID   '||
             'AND c.RATE_ID = e.RATE_ID   '||
             'AND e.MATCH_MODE = 2   '||
             'and e.tariff_type = 2    '||
             'AND c.RATE_TYPE = 3   '||
             'and a.ordermode = b.order_mode  '||
             'and a.feetype in(''3682'',''3683'',''3684'',''3685'',''3865'')   '||
             'AND ' || inMonth || ' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'')  '||
             'and ' || inMonth || ' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'')   '||
             'AND decode(a.DATASOURCE, ''BL'', ''1'', ''AR'', ''2'') = b.DATA_SOURCE   '||
             'AND a.ORGMONTH = ' || inMonth || ') t ';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

select concat('14_', now());

call LOG_PROCEDURES('OneVillage分段计算单价', v_proc_name);

---- OneVillage  分段计算单价
set @vSql := 'merge into stludr.sync_interface_amount_' || inMonth || '  f '||
                          'using (select poid, soid, feetype,   '||
                          '      (case                     '||
                          '       when price >= 119250 then  '||
                          '      notaxfee * 0.8     '||
                          '    else                '||
                          '     95400 * amount    '||
                          '  end) settle_fee      '||
                          '  from (select /*+ hash_join(a,b)*/a.poid,      '||
                          '         a.soid,      '||
                          '         a.feetype,   '||
                          '        round(a.notaxfee / b.amount) price,  '||
                          '       a.notaxfee,    '||
                          '       b.amount        '||
                          '  from stludr.sync_interface_bl_' || inMonth || '  a,   '||
                          '      stludr.sync_interface_amount_' || inMonth || '  b   '||
                          ' where a.sospecnumber = ''2022999400055822'' '||
                          '   and a.poid = b.poid   '||
                          '  and a.soid = b.soid   '||
                          '  and a.feetype = b.feetype)) t  '||
                          'on (f.poid = t.poid and f.soid = t.soid and f.feetype = t.feetype)  '||
                          'when matched then  '||
                          'update set f.notaxfee = t.settle_fee ';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

select concat('15_', now());

--OneVillage 规则计算  null-> ${EC_PROV_CODE_07}
set @vSql := 'INSERT INTO STLUSERS.STL_REPART_PARAMETER_T    '||
            'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, t.* FROM  '||
            '  (SELECT /*+ hash_join(a,b,c,e)*/DISTINCT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,  '||
            'a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERMODE, b.RULE_ID rule_id,  '||
            'c.RATE_ID rate_id, 1 calc_priority, ''${EC_PROV_CODE_07}'' object_value, '||
            'e.tariff_type tariff_type,  a.notaxfee rate_value,  '||
            'to_date(a.ORGMONTH, ''yyyymm'') eff_date, add_months(to_date(  '||
            'a.ORGMONTH, ''yyyymm''), 1) - 1 exp_date, a.ORGMONTH,  ''-1'' charge_item, 98 dest_source, '||
            '1 route_flag   '||
            'FROM stludr.SYNC_INTERFACE_AMOUNT_' || inMonth || ' a, '||
            ' STL_OFFER_T b,  '||
            ' STL_RATE_T c,  '||
            ' STL_REPART_RATE_T e  '||
            ' WHERE a.POSPECNUMBER = b.OFFER_CODE  '||
            'AND a.SOSPECNUMBER = b.PRODUCT_CODE  '||
            'AND a.SOID IS NOT NULL   '||
            'AND b.RULE_ID = c.RULE_ID  '||
            'AND c.RATE_ID = e.RATE_ID  '||
            'AND e.MATCH_MODE = 2 '||
            'and e.tariff_type = 2  '||
            'AND c.RATE_TYPE = 3   '||
            'and a.ordermode = b.order_mode '||
            'and a.sospecnumber=''2022999400055822'' '||
            'AND ' || inMonth || ' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') '||
            'and ' || inMonth || ' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') '||
            'AND decode(a.DATASOURCE, ''BL'', ''1'', ''AR'', ''2'') = b.DATA_SOURCE  '||
            'AND a.ORGMONTH = ' || inMonth || ') t ';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

select concat('16_', now());
call LOG_PROCEDURES('插入SIP音视频规则', v_proc_name);
--SIP音视频
--固话通信费部分
--出账费项为普通通信费
set @vSql := 'INSERT INTO STLUSERS.STL_REPART_PARAMETER_T ' ||
           'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.* FROM ' ||
           '(SELECT /*+ hash_join(a,b,c,d,e)*/DISTINCT a.pospecnumber, a.sospecnumber, ' ||
                 'a.poid poid_inst_id, a.soid svc_inst_id, a.ordermode, b.RULE_ID rule_id, ' ||
                 'c.RATE_ID rate_id, 0 calc_priority, a.prov_cd object_value, ' ||
                 'e.tariff_type tariff_type, round(sum(a.amount) / 10 * 0.3) rate_value, ' ||
                 'to_date(a.orgmonth, ''yyyymm'') eff_date, add_months(to_date( ' ||
                 'a.orgmonth, ''yyyymm''), 1) - 1 exp_date, a.orgmonth, d.charge_item charge_item, ' ||
                 '0 dest_source, 1 route_flag ' ||
            'FROM stludr.SYNC_BL_RULE_' || inMonth || ' a, ' ||
                 'STL_OFFER_T b, ' ||
                 'STL_RATE_T c, ' ||
                 'stl_rule_item_t d, ' ||
                 'STL_REPART_RATE_T e ' ||
           'WHERE a.pospecnumber = b.OFFER_CODE ' ||
             'AND b.RULE_ID = c.RULE_ID ' ||
             'AND c.RATE_ID = e.RATE_ID ' ||
             'AND e.MATCH_MODE = 2 ' ||
             'AND b.DATA_SOURCE = 1 ' ||
             'AND c.RATE_TYPE = 3 ' ||
             'and d.charge_item = a.feetype and d.rule_id = b.rule_id ' ||
             'and a.ordermode = b.order_mode ' ||
             'AND ' || inMonth || ' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') ' ||
             'and ' || inMonth || ' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') ' ||
             'and ' || inMonth || ' BETWEEN to_char(d.EFF_DATE, ''yyyymm'') AND to_char(d.EXP_DATE, ''yyyymm'') ' ||
             'AND a.orgmonth = ' || inMonth || ' ' ||
             'AND a.pospecnumber = ''50016'' and a.sospecnumber = ''5001613'' ' ||
           'GROUP BY a.pospecnumber, a.sospecnumber, a.poid, a.soid, a.ORDERMODE, a.prov_cd, b.RULE_ID, ' ||
                 'c.RATE_ID, e.tariff_type, a.orgmonth, d.charge_item) data';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

select concat('17_', now());

--出账费项为低消通信费
set @vSql := 'INSERT INTO STLUSERS.STL_REPART_PARAMETER_T ' ||
           'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.* FROM ' ||
           '(SELECT /*+ hash_join(a,b,c,d,e)*/DISTINCT a.pospecnumber, a.sospecnumber, ' ||
                 'a.poid poid_inst_id, a.soid svc_inst_id, a.ordermode, b.RULE_ID rule_id, ' ||
                 'c.RATE_ID rate_id, 0 calc_priority, a.prov_cd object_value, ' ||
                 'e.tariff_type tariff_type, round(sum(a.amount) / 10 * 0.3) rate_value, ' ||
                 'to_date(a.orgmonth, ''yyyymm'') eff_date, add_months(to_date( ' ||
                 'a.orgmonth, ''yyyymm''), 1) - 1 exp_date, a.orgmonth, ' ||
                 'decode(d.charge_item, ''1597'', ''1599'', ''1598'', ''1599'', ''5597'', ''5599'', ''5598'', ''5599'') charge_item, ' ||
                 '0 dest_source, 1 route_flag ' ||
            'FROM stludr.SYNC_BL_RULE_' || inMonth || ' a, ' ||
                 'STL_OFFER_T b, ' ||
                 'STL_RATE_T c, ' ||
                 'stl_rule_item_t d, ' ||
                 'STL_REPART_RATE_T e ' ||
           'WHERE a.pospecnumber = b.OFFER_CODE ' ||
             'AND b.RULE_ID = c.RULE_ID ' ||
             'AND c.RATE_ID = e.RATE_ID ' ||
             'AND e.MATCH_MODE = 2 ' ||
             'AND b.DATA_SOURCE = 1 ' ||
             'AND c.RATE_TYPE = 3 ' ||
             'and d.charge_item = a.feetype and d.rule_id = b.rule_id ' ||
             'and a.ordermode = b.order_mode ' ||
             'and d.charge_item in(''1597'',''1598'',''5597'',''5598'')  '||
             'AND ' || inMonth || ' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') ' ||
             'and ' || inMonth || ' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') ' ||
             'and ' || inMonth || ' BETWEEN to_char(d.EFF_DATE, ''yyyymm'') AND to_char(d.EXP_DATE, ''yyyymm'') ' ||
             'AND a.orgmonth = ' || inMonth || ' ' ||
             'AND a.pospecnumber = ''50016'' and a.sospecnumber = ''5001613'' ' ||
           'GROUP BY a.pospecnumber, a.sospecnumber, a.poid, a.soid, a.ORDERMODE, a.prov_cd, b.RULE_ID, ' ||
                 'c.RATE_ID, e.tariff_type, a.orgmonth, d.charge_item) data';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

select concat('18_', now());

-- 其它部分（特服通信费+低消通信费与普通通信费的差额） NULL->${EC_PROV_CODE_07}
set @vSql := 'INSERT INTO STL_REPART_PARAMETER_T ' ||
          'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, t.* FROM ' ||
          '(SELECT /*+ hash_join(a,b,c,d,e)*/DISTINCT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,  ' ||
                 'a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERMODE, b.RULE_ID rule_id, ' ||
                 'c.RATE_ID rate_id, 1 calc_priority, ''${EC_PROV_CODE_07}'' object_value,  ' ||
                 'e.tariff_type tariff_type,  ''$'' rate_value,  ' ||
                 'to_date(a.ORGMONTH, ''yyyymm'') eff_date, add_months(to_date(  ' ||
                 'a.ORGMONTH, ''yyyymm''), 1) - 1 exp_date, a.ORGMONTH, d.charge_item charge_item, 0 dest_source, ' ||
                 '1 route_flag ' ||
            'FROM stludr.SYNC_INTERFACE_BL_' || inMonth || ' a,  ' ||
                 'STL_OFFER_T b,  ' ||
                 'STL_RATE_T c,  ' ||
                 'stl_rule_item_t d, ' ||
                 'STL_REPART_RATE_T e  ' ||
          'WHERE a.POSPECNUMBER = b.OFFER_CODE  ' ||
             'AND b.RULE_ID = c.RULE_ID  ' ||
             'AND c.RATE_ID = e.RATE_ID  ' ||
             'AND e.MATCH_MODE = 2 ' ||
             'AND c.RATE_TYPE = 3 ' ||
             'and d.charge_item = a.feetype and d.rule_id = b.rule_id ' ||
             'and a.ordermode = b.order_mode ' ||
             'AND ' || inMonth || ' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') ' ||
             'AND ' || inMonth || ' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') ' ||
             'AND ' || inMonth || ' BETWEEN to_char(d.EFF_DATE, ''yyyymm'') AND to_char(d.EXP_DATE, ''yyyymm'') ' ||
             'AND a.pospecnumber = ''50016'' and a.sospecnumber = ''5001613'' ' ||
             'AND decode(a.DATASOURCE, ''BL'', ''1'', ''AR'', ''2'') = b.DATA_SOURCE  ' ||
             'AND a.ORGMONTH = ' || inMonth || ') t';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

select concat('19_', now());

call LOG_PROCEDURES('插入双跨结算规则', v_proc_name);

--插入双跨结算规则
--话单部分
set @vSql := 'INSERT INTO STL_REPART_PARAMETER_T ' ||
           'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.* FROM ' ||
           '(SELECT distinct a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code, ' ||
                 'a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERMODE order_mode, b.RULE_ID rule_id, ' ||
                 'c.RATE_ID rate_id, 0 calc_priority, ''${EC_PROV_CODE_07}'' object_value, ' ||
                 '''2'' tariff_type,  sum(a.AMOUNT) rate_value, ' ||
                 'to_date(a.ORGMONTH, ''yyyymm'') eff_date, add_months(to_date( ' ||
                 'a.ORGMONTH, ''yyyymm''), 1) - 1 exp_date, a.ORGMONTH, ''-1'' charge_item,  ' ||
                 '99 dest_source, 0 route_flag ' ||
            'FROM stludr.SYNC_BL_SETTLE_' || inMonth || ' a, ' ||
                 'stlusers.STL_OFFER_T b, ' ||
                 'stlusers.STL_RATE_T c ' ||
           'WHERE a.POSPECNUMBER = b.OFFER_CODE ' ||
             'AND b.PRODUCT_CODE = -1 ' ||
             'and a.ordermode = b.order_mode ' ||
			 'and a.ordermode = ''3'' ' ||
             'AND b.RULE_ID = c.RULE_ID ' ||
             'AND c.RATE_TYPE = 3 ' ||
             'AND a.POSPECNUMBER = ''0102001'' ' ||
             'AND a.FEETYPE = ''1080'' ' ||
             'AND a.SOSPECNUMBER IS NULL ' ||
             'AND decode(a.DATASOURCE, ''BL'', ''1'', ''AR'', ''2'') = b.DATA_SOURCE ' ||
             'AND a.ORGMONTH = ' || inMonth || ' ' ||
             'and to_date(' || inMonth || ', ''yyyymm'') between b.eff_date and b.exp_date ' ||
             'and to_date(' || inMonth || ', ''yyyymm'') between c.eff_date and c.exp_date ' ||
             'GROUP BY a.POSPECNUMBER, a.SOSPECNUMBER, a.POID, a.SOID, a.ORDERMODE, a.ORGMONTH, ' ||
                 'b.RULE_ID, c.RATE_ID) data';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

select concat('20_', now());

--（帐单-话单）部分
set @vSql := 'INSERT INTO STL_REPART_PARAMETER_T ' ||
           'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, t.* FROM ' ||
           '(SELECT DISTINCT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code, ' ||
                 'a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERMODE order_mode, b.RULE_ID rule_id, ' ||
                 'c.RATE_ID rate_id, 1 calc_priority, ''${EC_PROV_CODE_07}'' object_value, ' ||
                 '''2'' tariff_type,  ''$'' rate_value, ' ||
                 'to_date(a.ORGMONTH, ''yyyymm'') eff_date, add_months(to_date( ' ||
                 'a.ORGMONTH, ''yyyymm''), 1) - 1 exp_date, a.ORGMONTH, ''-1'' charge_item, 98 dest_source, ' ||
                 '1 route_flag ' ||
            'FROM stludr.SYNC_INTERFACE_BL_' || inMonth || ' a, ' ||
                 'stlusers.STL_OFFER_T b, ' ||
                 'stlusers.STL_RATE_T c ' ||
            'WHERE a.POSPECNUMBER = b.OFFER_CODE ' ||
             'AND b.PRODUCT_CODE = -1 ' ||
             'and a.ordermode = b.order_mode ' ||
			 'and a.ordermode = ''3'' ' ||
             'AND b.RULE_ID = c.RULE_ID ' ||
             'AND c.RATE_TYPE = 3 ' ||
             'AND a.POSPECNUMBER = ''0102001'' ' ||
             'AND a.SOID IS NULL ' ||
             'AND decode(a.DATASOURCE, ''BL'', ''1'', ''AR'', ''2'') = b.DATA_SOURCE ' ||
             'and to_date(' || inMonth || ', ''yyyymm'') between b.eff_date and b.exp_date ' ||
             'and to_date(' || inMonth || ', ''yyyymm'') between c.eff_date and c.exp_date ' ||
             'AND a.ORGMONTH = ' || inMonth || ') t';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

select concat('21_', now());
call LOG_PROCEDURES('插入固话云视讯结算规则', v_proc_name);

--插入固话云视讯结算规则
--话单部分
set @vSql := 'INSERT INTO STL_REPART_PARAMETER_T ' ||
           'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.* FROM ' ||
           '(SELECT distinct a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code, ' ||
                 'a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERMODE order_mode, b.RULE_ID rule_id, ' ||
                 'c.RATE_ID rate_id, 0 calc_priority, ''${EC_PROV_CODE_07}'' object_value, ' || --这个地方写EC归属省是因为取不到成员归属省且反正是冗余数据
                 '''2'' tariff_type,  sum(a.AMOUNT) rate_value, ' ||
                 'to_date(a.ORGMONTH, ''yyyymm'') eff_date, add_months(to_date( ' ||
                 'a.ORGMONTH, ''yyyymm''), 1) - 1 exp_date, a.ORGMONTH, ''-1'' charge_item,  ' ||
                 '99 dest_source, 0 route_flag ' ||
            'FROM stludr.SYNC_BL_SETTLE_' || inMonth || ' a, ' ||
                 'stlusers.STL_OFFER_T b, ' ||
                 'stlusers.STL_RATE_T c ' ||
           'WHERE a.POSPECNUMBER = b.OFFER_CODE ' ||
             'AND b.PRODUCT_CODE = a.sospecnumber ' ||
             'AND b.RULE_ID = c.RULE_ID ' ||
             'AND c.RATE_TYPE = 3 ' ||
             'AND a.SOSPECNUMBER = ''5001702'' ' ||
             'AND a.FEETYPE = ''1080'' ' ||
             'AND decode(a.DATASOURCE, ''BL'', ''1'', ''AR'', ''2'') = b.DATA_SOURCE ' ||
             'AND a.ORGMONTH = ' || inMonth || ' ' ||
             /*'and to_date(' || inMonth || ', ''yyyymm'') between b.eff_date and b.exp_date ' ||
             'and to_date(' || inMonth || ', ''yyyymm'') between c.eff_date and c.exp_date ' ||*/
             'GROUP BY a.POSPECNUMBER, a.SOSPECNUMBER, a.POID, a.SOID, a.ORDERMODE, a.ORGMONTH, ' ||
                 'b.RULE_ID, c.RATE_ID) data';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

select concat('22_', now());

--（帐单-话单）部分
set @vSql := 'INSERT INTO STL_REPART_PARAMETER_T ' ||
                         'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, t.* FROM ' ||
                         '(SELECT DISTINCT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code, ' ||
                               'a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERMODE order_mode, b.RULE_ID rule_id, ' ||
                               'c.RATE_ID rate_id, 1 calc_priority, ''${EC_PROV_CODE_07}'' object_value, ' ||
                               '''2'' tariff_type,  ''$'' rate_value, ' ||
                               'to_date(a.ORGMONTH, ''yyyymm'') eff_date, add_months(to_date( ' ||
                               'a.ORGMONTH, ''yyyymm''), 1) - 1 exp_date, a.ORGMONTH, ''-1'' charge_item, 0 dest_source, ' ||
                               '1 route_flag ' ||
                          'FROM stludr.SYNC_INTERFACE_BL_' || inMonth || ' a, ' ||
                               'stlusers.STL_OFFER_T b, ' ||
                               'stlusers.STL_RATE_T c ' ||
                               /*'stlusers.stl_rule_item_t d ' ||*/
                          'WHERE a.POSPECNUMBER = b.OFFER_CODE ' ||
                           'AND b.PRODUCT_CODE = a.sospecnumber ' ||
                           'AND b.RULE_ID = c.RULE_ID ' ||
                           'AND c.RATE_TYPE = 3 ' ||
                           'AND a.SOSPECNUMBER = ''5001702'' ' ||
                           'AND decode(a.DATASOURCE, ''BL'', ''1'', ''AR'', ''2'') = b.DATA_SOURCE ' ||
                           /*'and d.rule_id = c.rule_id ' ||
                           'and d.charge_item = a.feetype ' ||*/
                           /*'and to_date(' || inMonth || ', ''yyyymm'') between b.eff_date and b.exp_date ' ||
                           'and to_date(' || inMonth || ', ''yyyymm'') between c.eff_date and c.exp_date ' ||
                           'and to_date(' || inMonth || ', ''yyyymm'') between d.eff_date and d.exp_date ' ||*/
                           'AND a.ORGMONTH = ' || inMonth || ') t';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

select concat('23_', now());

call LOG_PROCEDURES('插入WLAN统付结算规则', v_proc_name);
--插入WLAN统付结算规则
set @vSql := 'INSERT INTO STL_REPART_PARAMETER_T ' ||
                         'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, datax.* FROM ( '||
                         'SELECT  a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code, ' ||
                              'a.POID prod_inst_id, a.SOID svc_inst_id, a.ORDERMODE order_mode, c.RULE_ID, d.RATE_ID, row_number() ' ||
                              'over(PARTITION BY a.SOID, a.feetype ORDER BY to_number(a.AMOUNT)) - 1 calc_priority, a.PROV_CD object_value, ' ||
                              '''1'' tariff_type, round(a.AMOUNT / decode(b.TOTAL, 0, 1, b.TOTAL), 15) rate_value, ' ||
                              'to_date(a.ORGMONTH, ''yyyymm'') eff_date, add_months(to_date(a.ORGMONTH, ''yyyymm''), 1) - 1 exp_date, ' ||
                              'a.ORGMONTH acct_month, a.feetype charge_item, 0 dest_source, 1 route_flag ' ||
                         'FROM stludr.SYNC_BL_RULE_' || inMonth || ' a, ' ||
                             '(SELECT POID, SOID, feetype, SUM(AMOUNT) total ' ||
                               'FROM stludr.SYNC_BL_RULE_' || inMonth || ' ' ||
                              'WHERE PROV_CD IS NOT NULL ' ||
                                'AND SOSPECNUMBER IN (SELECT PRODUCT_CODE FROM STL_BUSINESS_TYPE ' ||
                                                      'WHERE BIZ_TYPE = ''GPRSW'') ' ||
                              'GROUP BY POID, SOID, feetype) b, ' ||
                              'STL_OFFER_T c, ' ||
                              'STL_RATE_T d ' ||
                         'WHERE a.POID = b.POID ' ||
                          'AND a.SOID = b.SOID ' ||
                          'and a.feetype = b.feetype ' ||
                          'AND a.PROV_CD IS NOT NULL ' ||
                          'AND a.POSPECNUMBER = c.OFFER_CODE ' ||
                          'AND c.PRODUCT_CODE = -1 ' ||
                          'AND a.ORDERMODE = c.ORDER_MODE ' ||
                          'AND c.RULE_ID = d.RULE_ID ' ||
                          'AND ' || inMonth || ' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') ' ||
                              'AND to_char(c.EXP_DATE, ''yyyymm'') ' ||
                          'AND c.DATA_SOURCE = 1' ||
                          ') datax';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

select concat('24_', now());

--插入能力开放结算规则
call LOG_PROCEDURES('插入能力开放结算规则', v_proc_name);

TRUNCATE TABLE STL_NLKF_RULE;


            set @vSql := 'INSERT INTO STL_NLKF_RULE ' ||
                           '(ACCT_MONTH, ORDER_MODE, EC_CODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID, FEETYPE, SETT_PROV, ' ||
                           'PROV_AMOUNT, TOTAL_AMOUNT, RATIO, DEST_SOURCE, ROUTE_FLAG) ' ||
                         'SELECT a.orgmonth, ' ||
                           'a.ordermode,  ' ||
                           'a.customernumber, ' ||
                           'a.pospecnumber, ' ||
                           'a.sospecnumber, ' ||
                           'a.poid, ' ||
                           'a.soid, ' ||
                           'a.feetype, ' ||
                           'a.prov_cd, ' ||
                           'a.orgfee, ' ||
                           'b.total_fee, ' ||
                           'decode(a.sospecnumber,''5001606'',round(a.orgfee / decode(b.total_fee, 0, 1, b.total_fee) * 100 * 0.3, 7),  '||
                           ' ''5001602'',round(a.orgfee / decode(b.total_fee, 0, 1, b.total_fee) * 100 * 0.3, 7),   '||
                           ' ''5001603'',round(a.orgfee / decode(b.total_fee, 0, 1, b.total_fee) * 100 * 0.3, 7),   '||
                           ' ''5001604'',round(a.orgfee / decode(b.total_fee, 0, 1, b.total_fee) * 100 * 0.3, 7),   '||
               ' ''20009'',round(a.orgfee / decode(b.total_fee, 0, 1, b.total_fee) * 100 * 0.3, 7),   '||
                           'round(a.orgfee / decode(b.total_fee, 0, 1, b.total_fee) * 100 * 0.7, 7))  ratio, ' ||
                           '''0'' dest_source, ' ||
                           '''1'' route_flag ' ||
                      'FROM ' ||
                          '(SELECT ORGMONTH, ' ||
                                 'ORDERMODE, ' ||
                                 'CUSTOMERNUMBER, ' ||
                                 'POSPECNUMBER, ' ||
                                 'SOSPECNUMBER, ' ||
                                 'POID, ' ||
                                 'SOID, ' ||
                                 'FEETYPE, ' ||
                                 'PROV_CD, ' ||
                                 'sum(AMOUNT) orgfee ' ||
                            'FROM stludr.SYNC_BL_RULE_' || inMonth || ' ' ||
                           'WHERE STATUS = ''0'' ' ||
                             'AND (POSPECNUMBER in (''50016'',''0102001'') AND SOSPECNUMBER <> ''5001613'' ) or  (POSPECNUMBER =''50121'' AND SOSPECNUMBER = ''5001612'' ) '||
                             'AND DATASOURCE = ''BL'' ' ||
                             'AND ORGMONTH = ' || inMonth || ' ' ||
                           'GROUP BY ORGMONTH, ORDERMODE, CUSTOMERNUMBER, POSPECNUMBER, SOSPECNUMBER, POID, SOID, FEETYPE, PROV_CD) a, ' ||
                          '(SELECT ORGMONTH, ' ||
                                 'ORDERMODE, ' ||
                                 'CUSTOMERNUMBER, ' ||
                                 'SOSPECNUMBER, ' ||
                                 'POID, ' ||
                                 'SOID, ' ||
                                 'FEETYPE, ' ||
                                 'sum(AMOUNT) total_fee ' ||
                            'FROM stludr.SYNC_BL_RULE_' || inMonth || ' ' ||
                           'WHERE STATUS = ''0'' ' ||
                             'AND (POSPECNUMBER in (''50016'',''0102001'') AND SOSPECNUMBER <> ''5001613'' ) or  (POSPECNUMBER =''50121'' AND SOSPECNUMBER = ''5001612'' ) '||
                             'AND DATASOURCE = ''BL'' ' ||
                             'AND ORGMONTH = ' || inMonth || ' ' ||
                           'GROUP BY ORGMONTH, ORDERMODE, CUSTOMERNUMBER, SOSPECNUMBER, POID, SOID, FEETYPE) b ' ||
                     'WHERE a.poid = b.poid ' ||
                       'AND a.soid = b.soid ' ||
                       'and a.feetype = b.feetype';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
select concat('25_', now());


INSERT INTO STL_NLKF_RULE
SELECT null,ACCT_MONTH, EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID,PRODUCT_ORDER_ID, '${EC_PROV_CODE_07}' SETT_PROV, FEETYPE,
       NULL, NULL, decode(product_code,'20009',70,'5001606',70,'5001602',70,'5001603',70,'5001604',70,30), '0', decode(product_code, '5001606', '0', '1')
FROM STL_NLKF_RULE
WHERE ACCT_MONTH = inMonth
GROUP BY ACCT_MONTH, EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID,PRODUCT_ORDER_ID, FEETYPE;

select concat('26_', now());
INSERT INTO STL_REPART_PARAMETER_T(ID,OFFER_CODE,PRODUCT_CODE,PROD_INST_ID,SVC_INST_ID,
                                   ORDER_MODE,RULE_ID,RATE_ID,CALC_PRIORITY,
                                   OBJECT_VALUE,TARIFF_TYPE,RATE_VALUE,EFF_DATE,
                                   EXP_DATE,ACCT_MONTH,DEST_SOURCE,CHARGE_ITEM,ROUTE_FLAG)
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*
FROM
    (SELECT a.OFFER_CODE offer_code, a.PRODUCT_CODE product_code,
            a.OFFER_ORDER_ID poid_inst_id, a.PRODUCT_ORDER_ID svc_inst_id, a.ORDER_MODE order_mode, b.RULE_ID rule_id,
            c.RATE_ID rate_id, row_number() over(PARTITION BY a.PRODUCT_ORDER_ID, a.feetype ORDER BY to_number(a.RATIO)) - 1
                 calc_priority, a.SETT_PROV object_value, '1' tariff_type,
            a.RATIO / 100 rate_value, to_date(a.ACCT_MONTH, 'yyyymm') eff_date, add_months(to_date(
                                                                                                   a.ACCT_MONTH, 'yyyymm'), 1) - 1 exp_date, a.ACCT_MONTH acct_month,a.dest_source, d.charge_item, a.route_flag
     FROM STL_NLKF_RULE a,
          STL_OFFER_T b,
          STL_RATE_T c,
          stl_rule_item_t d
     WHERE a.OFFER_CODE = b.OFFER_CODE
       AND a.PRODUCT_CODE = b.PRODUCT_CODE
       AND a.ORDER_MODE = b.ORDER_MODE
       AND b.RULE_ID = c.RULE_ID
       and c.rule_id = d.rule_id
       and c.rate_type = 3
       and a.feetype = d.charge_item
       AND b.DATA_SOURCE = 1
       AND a.ratio <> 0
       AND a.ACCT_MONTH = inMonth
       and to_date(inMonth, 'YYYYMM') between b.eff_date and b.exp_date
       and to_date(inMonth, 'YYYYMM') between c.eff_date and c.exp_date
       and to_date(inMonth, 'YYYYMM') between d.eff_date and d.exp_date) data;


select concat('27_', now());

--插入企业和多号全网产品规则
call LOG_PROCEDURES('插入企业和多号全网产品规则', v_proc_name);

set @vSql := 'INSERT INTO STL_REPART_PARAMETER_T ' ||
                 'select SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.* from ' ||
                 '(select a.pospecnumber offer_code, ' ||
                 'a.sospecnumber product_code, poid poid_inst_id, soid svc_inst_id, a.ordermode order_mode, ' ||
                 'b.rule_id, c.rate_id, decode(a.remark, ''M'', 1, 0) calc_priority, a.prov_cd object_value, ''2'' tariff_type, ' ||
                 'decode(a.remark, ''M'', ''$'', a.amount) rate_value, to_date(a.orgmonth, ''yyyymm'') eff_date, add_months(to_date( ' ||
                 'a.orgmonth, ''yyyymm''), 1) - 1 exp_date, a.orgmonth acct_month, a.feetype, 0 dest_source, 1 route_flag ' ||
            'from stludr.sync_bl_rule_' || inMonth || ' a, ' ||
                 'stl_offer_t b, ' ||
                 'stl_rate_t c, ' ||
                 'stl_rule_item_t d ' ||
           'where a.pospecnumber = b.OFFER_CODE ' ||
             'and a.sospecnumber = b.product_code ' ||
             'and a.pospecnumber = ''1101010'' ' ||
             'and a.sospecnumber = ''9101002'' ' ||
             'and b.data_source = 1 ' ||
             'AND a.ORDERMODE = b.ORDER_MODE ' ||
             'AND b.RULE_ID = c.RULE_ID ' ||
             'and b.rule_id = d.rule_id ' ||
             'and d.charge_item = a.feetype ' ||
             'and a.orgmonth = ' || inMonth || ' ' ||
             'order by order_mode, svc_inst_id, feetype, object_value) data';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


select concat('28_', now());
--插入EBOSS云网融合产品规则
call LOG_PROCEDURES('插入EBOSS云网融合产品规则', v_proc_name);

set @vSql := 'INSERT INTO STL_REPART_PARAMETER_T ' ||
                 'select SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.* from ' ||
                 '(select a.product_spec_number offer_code, ' ||
                 '''-1'' product_code, a.product_id poid_inst_id, '''' svc_inst_id, a.order_mode order_mode, ' ||
                 'b.rule_id, c.rate_id, row_number() over(PARTITION BY a.product_id, a.prod_charge_code ORDER BY to_number(a.settle_rate)) calc_priority, a.settle_in_prov object_value, ''1'' tariff_type, ' ||
                 'round(to_number(a.settle_rate / 100), 7)  rate_value, to_date(a.bill_month, ''yyyymm'') eff_date, add_months(to_date( ' ||
                 'a.bill_month, ''yyyymm''), 1) - 1 exp_date, a.bill_month acct_month, a.prod_charge_code feetype, 0 dest_source, 1 route_flag ' ||
            'from stl_sync_eboss a, ' ||
                 'stl_offer_t b, ' ||
                 'stl_rate_t c, ' ||
                 'stl_rule_item_t d ' ||
           'where a.product_spec_number = b.OFFER_CODE ' ||
             'and a.status = ''0'' '  ||
             'and b.product_code = ''-1'' ' ||
             'and a.product_spec_number in (''9200374'', ''9200371'') ' ||
             'and b.data_source = 1 ' ||
             'AND a.ORDER_MODE = b.ORDER_MODE ' ||
             'AND b.RULE_ID = c.RULE_ID ' ||
             'and b.rule_id = d.rule_id ' ||
             'and a.bill_month = ' || inMonth || ' ' ||
             'order by order_mode, poid_inst_id, feetype, object_value) data';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


select concat('29_', now());
--插入智能路由规则
call LOG_PROCEDURES('插入智能路由规则', v_proc_name);

set @vSql := 'INSERT INTO STLUSERS.STL_REPART_PARAMETER_T ' ||
          'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.* FROM ' ||
           '(SELECT DISTINCT a.pospecnumber, a.sospecnumber, ' ||
                 'a.poid poid_inst_id, a.soid svc_inst_id, a.ORDERMODE, b.RULE_ID rule_id, ' ||
                 'c.RATE_ID rate_id, 0 calc_priority, a.prov_cd object_value, ' ||
                 'e.tariff_type tariff_type, a.amount rate_value, ' ||
                 'to_date(a.orgmonth, ''yyyymm'') eff_date, add_months(to_date( ' ||
                 'a.orgmonth, ''yyyymm''), 1) - 1 exp_date, a.orgmonth, a.feetype charge_item, ' ||
                 '0 dest_source, 0 route_flag ' ||
            'FROM stludr.SYNC_BL_SETTLE_' || inMonth || ' a, ' ||
                 'STL_OFFER_T b, ' ||
                 'STL_RATE_T c, ' ||
                 'stl_rule_item_t d, ' ||
                 'STL_REPART_RATE_T e ' ||
           'WHERE a.pospecnumber = b.OFFER_CODE ' ||
             'AND (a.sospecnumber = b.PRODUCT_CODE OR a.sospecnumber IS NULL) ' ||
             'AND a.soid IS NOT NULL ' ||
             'and a.ordermode = b.order_mode ' ||
             'and b.rule_id = d.rule_id ' ||
             'and a.feetype = d.charge_item ' ||
             'AND b.RULE_ID = c.RULE_ID ' ||
             'AND c.RATE_ID = e.RATE_ID ' ||
             'AND e.MATCH_MODE = 2 ' ||
             'AND b.DATA_SOURCE = 1 ' ||
             'AND c.RATE_TYPE = 3 ' ||
             'and a.ordermode = b.order_mode ' ||
             'AND ' || inMonth || ' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') ' ||
             'and ' || inMonth || ' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') ' ||
             'AND a.orgmonth = ' || inMonth || ' ' ||
             'AND a.pospecnumber = ''50021'' ' ||
           'GROUP BY a.pospecnumber, a.sospecnumber, a.poid, a.soid, a.ORDERMODE, b.RULE_ID, ' ||
                 'c.RATE_ID, e.tariff_type, a.orgmonth, a.prov_cd, a.feetype, a.amount) data';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
select concat('30_', now());
call LOG_PROCEDURES('插入梧桐风控收入还原规则', v_proc_name);

        --插入梧桐风控收入还原规则

            set @vSql := 'INSERT INTO STLUSERS.STL_REPART_PARAMETER_T ' ||
          'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.* FROM  ' ||
          '(select distinct a.pospecnumber, a.sospecnumber, a.poid, a.soid, a.ordermode, b.rule_id, c.rate_id, 0 calc_priority, a.prov_cd object_values, ' ||
                 'd.tariff_type, sum(a.amount) rate_value, to_date(a.orgmonth, ''yyyymm'') eff_date, add_months(to_date(a.orgmonth, ''yyyymm''), 1) - 1 exp_date, ' ||
                 'a.orgmonth, ''5'' || a.feetype charge_item, ''99'' dest_source, 1 route_flag ' ||
            'from stludr.sync_bl_settle_' || inMonth || ' a,  ' ||
                 'stl_offer_t b, ' ||
                 'stl_rate_t c, ' ||
                 'stl_repart_rate_t d ' ||
           'where pospecnumber = ''50024'' and sospecnumber = ''2021999400052091'' and feetype like ''1%'' ' ||
             'and b.data_source = 1 and c.rate_type = 3 and d.match_mode = 2 ' ||
             'and a.pospecnumber = b.offer_code and a.sospecnumber = b.product_code and a.ordermode = b.order_mode ' ||
             'and b.rule_id = c.rule_id ' ||
             'and c.rate_id = d.rate_id ' ||
             'and ''' || inMonth || ''' between to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') ' ||
             'and ''' || inMonth || ''' between to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') ' ||
           'group by a.pospecnumber, a.sospecnumber, a.poid, a.soid, a.ordermode, b.rule_id, c.rate_id, a.prov_cd, ' ||
                    'd.tariff_type, a.orgmonth, a.feetype) data';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
select concat('31_', now());


          set @vSql := 'INSERT INTO STLUSERS.STL_REPART_PARAMETER_T ' ||
          'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.* FROM  ' ||
          '(select distinct a.pospecnumber, a.sospecnumber, a.poid, a.soid, a.ordermode, b.rule_id, c.rate_id, 0 calc_priority, ''${EC_PROV_CODE_07}'' object_values, ' ||
                 'd.tariff_type, ''$'' rate_value, to_date(a.orgmonth, ''yyyymm'') eff_date, add_months(to_date(a.orgmonth, ''yyyymm''), 1) - 1 exp_date, ' ||
                 'a.orgmonth, a.feetype charge_item, ''0'' dest_source, 1 route_flag ' ||
            'from stludr.sync_interface_bl_' || inMonth || ' a,  ' ||
                 'stl_offer_t b, ' ||
                 'stl_rate_t c, ' ||
                 'stl_repart_rate_t d ' ||
           'where pospecnumber = ''50024'' and sospecnumber = ''2021999400052091'' ' ||
             'and b.data_source = 1 and c.rate_type = 3 and d.match_mode = 2 ' ||
             'and a.pospecnumber = b.offer_code and a.sospecnumber = b.product_code and a.ordermode = b.order_mode ' ||
             'and b.rule_id = c.rule_id ' ||
             'and c.rate_id = d.rate_id ' ||
             'and ''' || inMonth || ''' between to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') ' ||
             'and ''' || inMonth || ''' between to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') ' ||
           'group by a.pospecnumber, a.sospecnumber, a.poid, a.soid, a.ordermode, b.rule_id, c.rate_id, ' ||
                    'd.tariff_type, a.orgmonth, a.feetype) data';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


COMMIT;
call LOG_PROCEDURES('call P_SETTLE_RULE_PROC_REPART_GPRSDOM', v_proc_name);
call P_SETTLE_RULE_PROC_REPART_GPRSDOM(inMonth,inBatch,flag_version,reserve1,reserve2,proc_out, outSysError, outReturn,outBL,outAR);

call LOG_PROCEDURES('call P_SETTLE_RULE_PROC_REPART_CDN_MAIN', v_proc_name);
call P_SETTLE_RULE_PROC_REPART_CDN_MAIN(inMonth,inBatch,flag_version,reserve1,reserve2,proc_out, outSysError, outReturn,outBL,outAR);

SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
call LOG_PROCEDURES('completed successfully. 执行完成总耗时秒='||TIMESTAMPDIFF(SECOND, @P_TIME, NOW()), v_proc_name);

END;

END;;
DELIMITER ;
