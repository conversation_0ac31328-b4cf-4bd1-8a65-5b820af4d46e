/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：入口DB2F-db2f任务生成
**/
DROP PROCEDURE IF EXISTS stlusers.P_SETTLE_DB2F_sql_Insert;
DELIMITER //
CREATE DEFINER="stlusers"@"10.%" PROCEDURE "P_SETTLE_DB2F_sql_Insert"(
    inMonth          IN   VARCHAR2,
    batch            IN   VARCHAR2,
    file_Count       IN   NUMBER,
    outSysError      OUT  VARCHAR2,
    outReturn        OUT  NUMBER

  )
-- RETURN VARCHAR2
  AS
    v_proc_name   VARCHAR2(30) := 'P_SETTLE_DB2F_sql_Insert';
    sql_Text         VARCHAR2(2000);
    i NUMBER;
  BEGIN
    outSysError := 'OK';
    outReturn := 0;


    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        outSysError := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || outReturn || '|'  || @p1 || '|'|| '1|DB2F入口文件数据写入报错。|  ' || outSysError ) AS error_msg ;
    END;


    BEGIN
      i := 1;
      WHILE i <= file_Count LOOP
        BEGIN
            set @sql_Text := 'INSERT INTO stlusers.FILE_DB2F_TASK ' ||
                            '(TASK_ID, TASK_TYPE, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, ' ||
                            'DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, PROCESS_DATE) ' ||
                            'SELECT stlusers.SEQ_DB2F_ID.nextval, ' ||
                                  'a.TASK_TYPE, ' ||
                                  'a.TASK_NAME, ' ||
                                  'a.DB_NAME, ' ||
                                  'a.DB_USER, ' ||
                                  'a.BODY_SQL_TEXT, ' ||
                                  'a.OUTGOING_DIR, ' ||
                                  'a.FORMAT_ID, ' ||
                                  ''''', ' ||
                                  ''''', ' ||
                                  'a.FILE_LEN, ' ||
                                  'a.DESCRIPTION, ' ||
                                  '''N'', ' ||
                                  inMonth || ', ' ||
                                  '''' || sysdate || ''' ' ||
                              'FROM stlusers.STL_CONFIG_DB2F_INSERT a ' ||
                              'WHERE a.CONF_ID = ' || to_char(batch * 100 + i);

            set i = i + 1;
            SELECT @sql_Text;
            PREPARE STMT FROM @sql_Text;
            EXECUTE STMT;
            DEALLOCATE PREPARE STMT;
        END;
      END LOOP;
        outReturn := 0;
        outSysError := 'DB2F入口文件数据已写入file_db2f_task表。';
    END;

    SELECT '0|DB2F入口文件数据已写入file_db2f_task表。 |' || 'procedure ' || v_proc_name  as info;
  END //
DELIMITER ;