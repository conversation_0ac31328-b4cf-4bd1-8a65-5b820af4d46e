/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：结算前数据准备-数据源调整-计费与结算使用量接口表金额计算
**/
DELIMITER ;;
CREATE OR REPLACE DEFINER="stludr"@"10.%" PROCEDURE stludr."STL_SETTLEFEE_CALCULATE"(
    inMonth          IN   VARCHAR2,
    inBatch          IN   VARCHAR2,
    outSysError      OUT  VARCHAR2,
    outReturn        OUT  NUMBER
)
AS
    v_proc_name VARCHAR2(30) := 'STL_SETTLEFEE_CALCULATE';
    vSql varchar2(9999);

BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
outSysError := substr(@p2, 1, 1000);
    outReturn  := -1;
ROLLBACK;
select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg ;
END;

BEGIN

        outSysError := '';
        outReturn := 0;

    if ( length(inMonth) < 6 )  then
SELECT 'inMonth length less than 6. inMonth=[' || inMonth || ']' FROM dual;
outReturn := -1;
ROLLBACK;
RETURN;
end if;

SELECT 'inMonth=' ||  inMonth;
SET @P_TIME := SYSDATE;

SET @vSql := 'update /*+ index(a idx_com1)*/ sync_interface_amount_' || inMonth || ' a ' ||
                     ' inner join stl_amount_rule b '||
                     ' on b.order_mode = a.ordermode ' ||
                     ' and b.offer_code = a.pospecnumber ' ||
                     ' and b.product_code = a.sospecnumber '||
                     ' and b.rateplan_id = a.cdr_chargecode ' ||
                     ' and b.charge_code = a.feetype ' ||
                     ' and b.match_type= ''OPRF'' ' ||
                     ' and ''' || inMonth || ''' between b.start_month and b.end_month and a.pospecnumber !=''60010'' ' ||
                     ' set a.notaxfee = round(a.amount * b.unit_price) ';


SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_01', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));


SET @vSql := 'update /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ sync_interface_amount_' || inMonth || ' a ' ||
                     ' inner join stl_amount_rule b '||
                     'on b.order_mode = a.ordermode ' ||
                     'and b.offer_code = a.pospecnumber ' ||
                     'and b.product_code = a.sospecnumber '||
                     'and b.charge_code = a.feetype ' ||
                     'and b.match_type= ''OPF'' ' ||
                     'and ''' || inMonth || ''' between b.start_month and b.end_month ' ||
                     'set a.notaxfee = round(a.amount * b.unit_price) ';


SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_02', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));


--和教育-智慧校园叠加包资费

SET @vSql := 'update /*+ AUTOCOMMIT_DURING_DML() */ sync_interface_amount_' || inMonth || ' a '||
             'join (select b.ordertype, b.pospecnumber, b.sospecnumber, b.poid, b.soid, b.feetype, sum(settlefee) settlefee '||
                 'from stlusers.stl_sync_rule b '||
                 'where b.pospecnumber = ''50049'' '||
                  'and b.sospecnumber = ''5004927'' '||
                  'and b.feetype in (''2280'', ''2281'', ''2282'', ''2283'', ''2284'') '||
                  'and ''' || inMonth || ''' between b.rulestartmonth and  b.ruleendmonth '||
                 'group by b.ordertype,b.pospecnumber,b.sospecnumber, b.poid,b.soid,b.feetype) b '||
                 'on b.ordertype = a.ordermode and b.pospecnumber = a.pospecnumber and b.sospecnumber = a.sospecnumber and b.poid = a.poid and b.soid = a.soid and b.feetype = a.feetype '||
             'set a.notaxfee = round(a.amount * b.settlefee) '||
             'where a.pospecnumber = ''50049'' '||
             ' and a.sospecnumber = ''5004927'' '||
             ' and a.feetype in (''2280'', ''2281'', ''2282'', ''2283'', ''2284'')';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_03', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

--成员视频彩铃-服务资费

SET @vSql := 'update /*+ AUTOCOMMIT_DURING_DML() */ sync_interface_amount_' || inMonth || ' a '||
                     'join (select b.ordertype, b.pospecnumber, b.sospecnumber, b.poid, b.soid, b.feetype, sum(settlefee) settlefee '||
                             'from stlusers.stl_sync_rule b '||
                             'where b.pospecnumber = ''*********'' '||
                             'and b.sospecnumber = ''910401'' '||
                             'and b.feetype =''1656'' '||
                             'and ''' || inMonth || ''' between b.rulestartmonth and  b.ruleendmonth '||
                             'group by b.ordertype,b.pospecnumber,b.sospecnumber, b.poid,b.soid,b.feetype) b '||
                             'on b.ordertype = a.ordermode and b.pospecnumber = a.pospecnumber and b.sospecnumber = a.sospecnumber and b.poid = a.poid and b.soid = a.soid and b.feetype = a.feetype '||
                         'set a.notaxfee = round(a.amount * b.settlefee) '||
                     'where a.pospecnumber = ''*********'' '||
                         'and a.sospecnumber = ''910401'' '||
                         ' and a.feetype =''1656'' ';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_04', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

--能开-工作号  成员数 >= 100000

SET @vSql := 'update /*+ AUTOCOMMIT_DURING_DML() */ sync_interface_amount_' || inMonth || ' a '||
                     'join  stl_amount_rule b  on b.offer_code = a.pospecnumber '||
                     'and b.product_code = a.sospecnumber '||
                     'and b.price_tag = ''G_E_100000'' '||
                     'and b.match_type = ''OP'' '||
                     'and ''' || inMonth || ''' between b.start_month and b.end_month '||
                     'set a.notaxfee = round(a.amount * b.unit_price) '||
                     'where a.amount >= 100000 ';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_05', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));


SET @vSql := 'update /*+ AUTOCOMMIT_DURING_DML() */ sync_interface_amount_' || inMonth || ' a '||
                     'join  stl_amount_rule b  on b.offer_code = a.pospecnumber '||
                     'and b.product_code = a.sospecnumber '||
                     'and b.price_tag = ''L_100000'' '||
                     'and b.match_type = ''OP'' '||
                     'and ''' || inMonth || ''' between b.start_month and b.end_month '||
                     'set a.notaxfee = round(a.amount * b.unit_price) '||
                     'where a.amount < 100000 ';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_06', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- 云游戏实例按天结算,最终结算金额=unit_price*N*D/days, XiongXiaogang_20230904
SET @vSql := 'delete  stludr.sync_his_amount_onegame where ORGMONTH=' || inMonth || ' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_07', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));


SET @vSql := 'update /*+ AUTOCOMMIT_DURING_DML() */ sync_interface_amount_' || inMonth || ' a ' ||
                     'set a.notaxfee = round(a.amount * (select b.unit_price from stl_amount_rule b ' ||
                                           'where b.order_mode = a.ordermode ' ||
                                           'and b.offer_code = a.pospecnumber ' ||
                                           'and b.product_code = a.sospecnumber '||
                                           'and b.rateplan_id = a.cdr_chargecode ' ||
                                           'and b.charge_code = a.feetype ' ||
                                           'and b.match_type= ''OPRF'' ' ||
                                           'and ''' || inMonth || ''' between b.start_month and b.end_month) * ' ||
                                           '(select NVL(c.eff_days, 0) from stludr.sync_onepower_' || inMonth || ' c ' ||
                                           'where c.ordermode = a.ordermode ' ||
                                           'and c.pospecnumber = a.pospecnumber ' ||
                                           'and c.sospecnumber = a.sospecnumber '||
                                           'and c.poid = a.poid ' ||
                                           'and c.soid = a.soid ' ||
                                           'and c.feetype = a.feetype ' ||
                                           'and c.rateplan_id = a.cdr_chargecode   '||
                                           'and ''' || inMonth || ''' between c.orgmonth and c.orgmonth) / ' ||
                                           '(SELECT EXTRACT(DAY FROM LAST_DAY(TO_DATE(''' || inMonth || ''', ''yyyymm''))) AS days_in_month FROM DUAL)) ' ||
                'where a.pospecnumber IN (''50121'',''50107'') ' ||
                'and a.sospecnumber in( ''2023999400018530'',''2025999480002334'',''2025999480002335'',''2025999480002341'',''2025999480002342'') ' ||
                'and a.feetype in (''3334'', ''3335'',''4782'',''4791'',''4792'',''4795'',''4797'',''4798'') and a.cdr_chargecode not in(''700877'',''700884'',''700885'') ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_08', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- 按天计费的套餐 结算金额=unit_price*N*D
SET @vSql := 'update /*+ AUTOCOMMIT_DURING_DML() */ sync_interface_amount_' || inMonth || ' a ' ||
             'set a.notaxfee = round(a.amount * (select b.unit_price from stl_amount_rule b ' ||
             'where b.order_mode = a.ordermode ' ||
             'and b.offer_code = a.pospecnumber ' ||
             'and b.product_code = a.sospecnumber '||
             'and b.rateplan_id = a.cdr_chargecode ' ||
             'and b.charge_code = a.feetype ' ||
             'and b.match_type= ''OPRF'' ' ||
             'and ''' || inMonth || ''' between b.start_month and b.end_month) * ' ||
             '(select NVL(c.eff_days, 0) from stludr.sync_onepower_' || inMonth || ' c ' ||
             'where c.ordermode = a.ordermode ' ||
             'and c.pospecnumber = a.pospecnumber ' ||
             'and c.sospecnumber = a.sospecnumber '||
             'and c.poid = a.poid ' ||
             'and c.soid = a.soid ' ||
             'and c.feetype = a.feetype ' ||
             'and c.rateplan_id = a.cdr_chargecode   '||
             'and ''' || inMonth || ''' between c.orgmonth and c.orgmonth))  ' ||
             'where a.pospecnumber =''50107'' ' ||
             'and a.sospecnumber =''2025999480002335'' ' ||
             'and a.feetype =''4791'' and a.cdr_chargecode in(''700884'',''700885'') ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_08_1', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- 处理历史数据信息
SET @vSql :=  'INSERT INTO stludr.SYNC_HIS_AMOUNT_ONEGAME SELECT a.ORGBID,a.ORDERMODE,a.DATASOURCE,a.ORGMONTH,a.CUSTOMERNUMBER,a.DN,a.POSPECNUMBER,a.SOSPECNUMBER,a.POID,a.SOID,a.FEETYPE,a.ORGFEE,SYNCTIME,a.STATUS,a.ERRMESG,a.PROVCD,a.REMARK,a.TAXFEE,a.TAXRATE,a.NOTAXFEE,a.AMOUNT,a.CDR_CHARGECODE,a.CDR_CHARGENAME ' ||
      'from sync_interface_amount_' || inMonth || ' a  WHERE  NOT EXISTS ( ' ||
	  'SELECT 1 FROM SYNC_ONEGAME_' || inMonth || ' f ' ||
	  'WHERE f.soid = a.soid ' ||
      'AND f.CHAGE_CODE = a.feetype ' ||
      'AND f.RATEPLAN_ID = a.cdr_chargecode ' ||
	  'and f.END_MONTH = ''1'' )  ' ||
	  'and a.POSPECNUMBER=''50107'' and a.sospecnumber = ''2023999400018530'' and a.feetype in (''3334'', ''3335'') and  a.cdr_chargecode  in(''4345'', ''4346'', ''4347'', ''4348'', ''4349'', ''4350'', ''4351'', ''4352'', ''4353'', ''4354'', ''4355'', ''4356'', ''4357'', ''4358'', ''4359'', ''4360'')';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_09', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- 处理历史数据信息
SET @vSql :=  'INSERT INTO stludr.SYNC_HIS_AMOUNT_ONEGAME SELECT a.ORGBID,a.ORDERMODE,a.DATASOURCE,a.ORGMONTH,a.CUSTOMERNUMBER,a.DN,a.POSPECNUMBER,a.SOSPECNUMBER,a.POID,a.SOID,a.FEETYPE,a.ORGFEE,SYNCTIME,a.STATUS,a.ERRMESG,a.PROVCD,a.REMARK,a.TAXFEE,a.TAXRATE,a.NOTAXFEE,a.AMOUNT,a.CDR_CHARGECODE,a.CDR_CHARGENAME ' ||
              'from sync_interface_amount_' || inMonth || ' a  WHERE EXISTS ( ' ||
              'SELECT 1 FROM SYNC_ONEGAME_' || inMonth || ' f ' ||
              'WHERE f.soid = a.soid ' ||
              'AND f.CHAGE_CODE = a.feetype ' ||
              'AND f.RATEPLAN_ID = a.cdr_chargecode ' ||
              'and f.END_MONTH = ''0'' )  ' ||
              'and a.POSPECNUMBER=''50107'' and a.sospecnumber in (''2025999480002334'',''2025999480002342'',''2025999480002341'',''2025999480002335'') and a.feetype in (''4782'',''4798'',''4795'',''4792'') ' ||
              'and  a.cdr_chargecode  in(''700857'', ''700858'', ''700859'', ''700860'', ''700861'', ''700862'', ''700863'', ''700869'', ''700870'', ''700871'', ''700872'', ''700873'', ''700874'', ''700875'' ' ||
              ' ,''700895'',''700896'',''700890'',''700891'',''700886'',''700887'')';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_10', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- 云游戏实例按天结算,末月结算金额=订单包月结算总金额(省专结算价*使用量N*50107010009购买时长(月)+(续订时长*省专结算价* 使用量N))-前序月份已结算金额(SYNC_HIS_AMOUNT_ONEGAME))
SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
    'set a.notaxfee = round( ( a.amount * (select b.unit_price from stl_amount_rule b ' ||     -- 结算单价
                                         'where b.order_mode = a.ordermode  ' ||
                                           'and b.offer_code = a.pospecnumber ' ||
                                           'and b.product_code = a.sospecnumber ' ||
                                           'and b.rateplan_id = a.cdr_chargecode ' ||
                                           'and b.charge_code = a.feetype  ' ||
                                           'and b.match_type= ''OPRF''  ' ||
                                           'and ''' || inMonth || ''' between b.start_month and b.end_month) *  ' ||

										  '(select NVL(c.PURCHASE_TIME, 0) from stludr.SYNC_ONEGAME_' || inMonth || ' c ' ||
											'where c.SOID = a.soid  ' ||
										   'and c.END_MONTH = ''1'' ' ||
										   ' and c.CHAGE_CODE = a.feetype ' ||
										   'and c.rateplan_id = a.cdr_chargecode   ' ||
                                           ')  + ' ||

										 '(select b.unit_price from stl_amount_rule b  ' ||
                                           'where b.order_mode = a.ordermode   ' ||
                                           'and b.offer_code = a.pospecnumber  ' ||
                                           'and b.product_code = a.sospecnumber  ' ||
                                           'and b.rateplan_id = a.cdr_chargecode  ' ||
                                           'and b.charge_code = a.feetype  ' ||
                                           'and b.match_type= ''OPRF''  ' ||
                                           'and ''' || inMonth || ''' between b.start_month and b.end_month) * ' ||

										   '(select NVL(c.RENEWAL_TIME, 0) from stludr.SYNC_ONEGAME_' || inMonth || ' c  ' ||
											'where c.SOID = a.soid ' ||
										   'and c.END_MONTH = ''1''  ' ||
										    'and c.CHAGE_CODE = a.feetype ' ||
										   'and c.rateplan_id = a.cdr_chargecode) * a.amount  ' ||
									') -  NVL((SELECT sum(T.notaxfee) from stludr.SYNC_HIS_AMOUNT_ONEGAME t WHERE t.ordermode=a.ordermode AND  t.poid = a.poid and t.soid = a.soid AND t.feetype = a.feetype AND  t.cdr_chargecode=a.cdr_chargecode ),0)) ' ||
								'where EXISTS (  ' ||
									  'SELECT 1  ' ||
									  'FROM SYNC_ONEGAME_' || inMonth || ' f ' ||
									  'WHERE f.soid = a.soid ' ||
								      'AND  f.CHAGE_CODE = a.feetype ' ||
								      'AND f.RATEPLAN_ID = a.cdr_chargecode ' ||
									  'and f.END_MONTH = ''1'') ' ||
                                     'and a.pospecnumber =''50107'' and ( ' ||
                                     '(a.sospecnumber = ''2023999400018530'' and a.feetype in (''3334'', ''3335''))  ' ||
                                     ' or ' ||
                                     '(a.sospecnumber in (''2025999480002334'',''2025999480002342'',''2025999480002341'',''2025999480002335'') and a.feetype in (''4782'',''4798'',''4795'',''4792'') and  ' ||
                                     'a.cdr_chargecode  in(''700857'', ''700858'', ''700859'', ''700860'', ''700861'', ''700862'', ''700863'', ''700869'', ''700870'', ''700871'', ''700872'', ''700873'', ''700874'', ''700875'', ' ||
                                     ' ''700895'',''700896'',''700890'',''700891'',''700886'',''700887'')) ) ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_10', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- BIL-ZQ-202410-22-量子密话产品支撑需求计费结算,销售价>=10元/成员/月，则以10元/成员/月结算；销售价<10元/成员/月，则以实际销售价结算；
SET @vSql := 'update /*+ AUTOCOMMIT_DURING_DML() */ sync_interface_amount_' || inMonth || ' a '||
             'join  stl_amount_rule b  '||
             'on b.order_mode = a.ordermode ' ||
             'and b.offer_code = a.pospecnumber ' ||
             'and b.product_code = a.sospecnumber '||
             'and b.rateplan_id = a.cdr_chargecode ' ||
             'and b.charge_code = a.feetype ' ||
             'and b.match_type= ''OPRF'' ' ||
             'and ''' || inMonth || ''' between b.start_month and b.end_month '||
             'set a.notaxfee = round(a.amount * b.unit_price) '||
             'where a.pospecnumber= ''60010'' AND a.sospecnumber=''2024999480010058'' and CAST(IFNULL(a.notaxfee, 0) AS SIGNED) >= 1000 ';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_11', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

COMMIT;

outSysError := 'OK';
outReturn := 0;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_99', '执行完成总耗时秒='||TIMESTAMPDIFF(SECOND, @P_TIME, NOW()));
SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || to_number(outReturn);
END;

END ;;
DELIMITER ;