/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：结算前数据准备-数据源调整-费项映射
**/
use stludr;
DELIMITER ;;
CREATE OR REPLACE DEFINER="stludr"@"10.%" PROCEDURE stludr.STL_CHARGECODE_MAPPING(
    inMonth          IN   VARCHAR2,
    inBatch          IN   VARCHAR2,
    outSysError      OUT  VARCHAR2,
    outReturn        OUT  NUMBER
)
AS

    v_proc_name       VARCHAR2(30) := 'STL_CHARGECODE_MAPPING';
    vSql      varchar2(9999);


BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
outSysError := substr(@p2, 1, 1000);
            outReturn  := -1;
ROLLBACK;

--  日志写表
select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg ;
END;

BEGIN
    outSysError := '';
    outReturn := 0;

    if ( length(inMonth) < 6 )  then
SELECT 'inMonth length less than 6. inMonth=[' || inMonth || ']' FROM dual;
outReturn := -1;
ROLLBACK;
RETURN;
end if;

SELECT 'inMonth=' ||  inMonth;
SET @P_TIME := SYSDATE;

    SET @vSql := 'truncate table int_interface_bl';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

SET @vSql := 'truncate table int_interface_ar';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_00', '数据清理完成开始执行开始时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));
SET @vSql := 'insert into int_interface_bl ' ||
            'select null, a.orgbid, a.ordermode, a.datasource, a.orgmonth, a.customernumber, a.dn, a.pospecnumber, a.sospecnumber, a.poid, a.soid, b.mapped_charge_code feetype, a.orgfee, a.synctime, ''0'' status, errmesg, a.provcd, a.remark, a.taxfee, a.taxrate, a.notaxfee, a.cdr_chargecode, a.cdr_chargename, a.status_orig, a.accountid, a.partid, a.list_price, a.adj_type, a.adjmonth ' ||
              'from sync_interface_bl_' || inMonth || ' a, stl_chargecode_mapping b ' ||
             'where a.ordermode = b.order_mode and a.pospecnumber = b.offer_code and a.sospecnumber = b.product_code and a.feetype = b.orig_charge_code and a.orgmonth between b.start_month and b.end_month and b.direction in (''1'', ''3'') and b.match_type = ''OP'' and a.status = ''4'' ' ||
              'union all ' ||
             'select null, a.orgbid, a.ordermode, a.datasource, a.orgmonth, a.customernumber, a.dn, a.pospecnumber, a.sospecnumber, a.poid, a.soid, b.mapped_charge_code feetype, a.orgfee, a.synctime, ''0'' status, errmesg, a.provcd, a.remark, a.taxfee, a.taxrate, a.notaxfee, a.cdr_chargecode, a.cdr_chargename, a.status_orig, a.accountid, a.partid, a.list_price, a.adj_type, a.adjmonth ' ||
              'from sync_interface_bl_' || inMonth || ' a, stl_chargecode_mapping b ' ||
             'where a.ordermode = b.order_mode and a.pospecnumber = b.offer_code and a.feetype = b.orig_charge_code and a.orgmonth between b.start_month and b.end_month and b.direction in (''1'', ''3'') and b.match_type = ''O'' and a.status = ''4'' ' ||
              'union all ' ||
             'select null, a.orgbid, a.ordermode, a.datasource, a.orgmonth, a.customernumber, a.dn, a.pospecnumber, a.sospecnumber, a.poid, a.soid, b.mapped_charge_code feetype, a.orgfee, a.synctime, ''0'' status, errmesg, a.provcd, a.remark, a.taxfee, a.taxrate, a.notaxfee, a.cdr_chargecode, a.cdr_chargename, a.status_orig, a.accountid, a.partid, a.list_price, a.adj_type, a.adjmonth ' ||
              'from sync_interface_bl_' || inMonth || ' a, stl_chargecode_mapping b ' ||
             'where a.ordermode = b.order_mode and a.pospecnumber = b.offer_code and a.sospecnumber = b.product_code and a.cdr_chargecode = b.rateplan_id and a.feetype = b.orig_charge_code and a.orgmonth between b.start_month and b.end_month and b.direction in (''1'', ''3'') and b.match_type = ''OPR'' and a.status in (''4'', ''5'')   '||
             'union all ' ||
            'select null, a.orgbid, a.ordermode, a.datasource, a.orgmonth, a.customernumber, a.dn, a.pospecnumber, a.sospecnumber, a.poid, a.soid, b.mapped_charge_code feetype, a.orgfee, a.synctime, ''0'' status, errmesg, a.provcd, a.remark, a.taxfee, a.taxrate, a.notaxfee, a.cdr_chargecode, a.cdr_chargename, null, a.accountid, a.partid, a.list_price, a.adj_type, a.adjmonth ' ||
              'from sync_interface_sdwan_' || inMonth || ' a, stl_chargecode_mapping b ' ||
             'where a.ordermode = b.order_mode and a.pospecnumber = b.offer_code and a.sospecnumber = b.product_code and a.feetype = b.orig_charge_code and a.orgmonth between b.start_month and b.end_month and b.direction in (''1'', ''3'') and b.match_type = ''OP'' and a.status = ''4'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_01', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));


SET @vSql := 'insert into int_interface_ar ' ||
            'select null, a.orgbid, a.ordermode, a.datasource, a.orgmonth, a.customernumber, a.dn, a.pospecnumber, a.sospecnumber, a.poid, a.soid, b.mapped_charge_code feetype, a.orgfee, a.synctime, ''0'' status, errmesg, a.provcd, a.paymonth, a.remark, a.taxfee, a.taxrate, a.notaxfee, a.cdr_chargecode, a.cdr_chargename, a.accountid, a.adj_type, a.adjmonth ' ||
              'from sync_interface_ar_' || inMonth || ' a, stl_chargecode_mapping b ' ||
             'where a.ordermode = b.order_mode and a.pospecnumber = b.offer_code and a.sospecnumber = b.product_code and a.feetype = b.orig_charge_code and a.orgmonth between b.start_month and b.end_month and b.direction in (''1'', ''3'') and b.match_type = ''OP'' and a.status = ''4'' ' ||
             'union all ' ||
            'select null, a.orgbid, a.ordermode, a.datasource, a.orgmonth, a.customernumber, a.dn, a.pospecnumber, a.sospecnumber, a.poid, a.soid, b.mapped_charge_code feetype, a.orgfee, a.synctime, ''0'' status, errmesg, a.provcd, a.paymonth, a.remark, a.taxfee, a.taxrate, a.notaxfee, a.cdr_chargecode, a.cdr_chargename, a.accountid, a.adj_type, a.adjmonth ' ||
              'from sync_interface_ar_' || inMonth || ' a, stl_chargecode_mapping b ' ||
             'where a.ordermode = b.order_mode and a.pospecnumber = b.offer_code and a.feetype = b.orig_charge_code and a.orgmonth between b.start_month and b.end_month and b.direction in (''1'', ''3'') and b.match_type = ''O'' and a.status = ''4''';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_02', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));


SET @vSql := 'insert /*+ AUTOCOMMIT_DURING_DML() */  into int_interface_bl ' ||
            'select null, a.orgbid, a.ordermode, a.datasource, a.orgmonth, a.customernumber, a.dn, a.pospecnumber, a.sospecnumber, a.poid, a.soid, b.mapped_charge_code feetype, a.orgfee, a.synctime, ''0'' status, errmesg, a.provcd, a.remark, a.taxfee, a.taxrate, a.notaxfee, a.cdr_chargecode, a.cdr_chargename, a.status_orig, a.accountid, a.partid, 0 list_price, null, null ' ||
              'from sync_interface_amount_' || inMonth || ' a, stl_chargecode_mapping b ' ||
             'where a.ordermode = b.order_mode and a.pospecnumber = b.offer_code and a.sospecnumber = b.product_code and a.feetype = b.orig_charge_code and a.orgmonth between b.start_month and b.end_month and b.direction in (''1'', ''3'') and b.match_type = ''OP'' and a.status = ''4'' ' ||
             'union all ' ||
            'select null, a.orgbid, a.ordermode, a.datasource, a.orgmonth, a.customernumber, a.dn, a.pospecnumber, a.sospecnumber, a.poid, a.soid, b.mapped_charge_code feetype, a.orgfee, a.synctime, ''0'' status, errmesg, a.provcd, a.remark, a.taxfee, a.taxrate, a.notaxfee, a.cdr_chargecode, a.cdr_chargename, a.status_orig, a.accountid, a.partid, 0 list_price, null, null ' ||
              'from sync_interface_amount_' || inMonth || ' a, stl_chargecode_mapping b ' ||
             'where a.ordermode = b.order_mode and a.pospecnumber = b.offer_code and a.feetype = b.orig_charge_code and a.orgmonth between b.start_month and b.end_month and b.direction in (''1'', ''3'') and b.match_type = ''O'' and a.status = ''4'' ' ||
            'union all ' ||
            'select null, a.orgbid, a.ordermode, a.datasource, a.orgmonth, a.customernumber, a.dn, a.pospecnumber, a.sospecnumber, a.poid, a.soid, b.mapped_charge_code feetype, a.orgfee, a.synctime, ''0'' status, errmesg, a.provcd, a.remark, a.taxfee, a.taxrate, a.notaxfee, a.cdr_chargecode, a.cdr_chargename, a.status_orig, a.accountid, a.partid, 0 list_price, null, null ' ||
              'from sync_interface_amount_' || inMonth || ' a, stl_chargecode_mapping b ' ||
             'where a.ordermode = b.order_mode and a.pospecnumber = b.offer_code and a.sospecnumber = b.product_code and a.cdr_chargecode = b.rateplan_id and a.feetype = b.orig_charge_code and a.orgmonth between b.start_month and b.end_month and b.direction in (''1'', ''3'') and b.match_type = ''OPR'' and (b.payment_type = ''G'' or b.payment_type is null) and a.status = ''4'' ' ||
            'union all ' ||
            'select null, a.orgbid, a.ordermode, a.datasource, a.orgmonth, a.customernumber, a.dn, a.pospecnumber, a.sospecnumber, a.poid, a.soid, b.mapped_charge_code feetype, a.orgfee, a.synctime, ''0'' status, errmesg, a.provcd, a.remark, a.taxfee, a.taxrate, a.notaxfee, a.cdr_chargecode, a.cdr_chargename, a.status_orig, a.accountid, a.partid, 0 list_price, null, null ' ||
              'from sync_interface_amount_' || inMonth || ' a, stl_chargecode_mapping b ' ||
             'where a.ordermode = b.order_mode and a.pospecnumber = b.offer_code and a.sospecnumber = b.product_code and a.cdr_chargecode = b.rateplan_id and a.feetype = b.orig_charge_code and a.orgmonth between b.start_month and b.end_month and b.direction in (''1'', ''3'') and b.match_type = ''OPR'' and b.payment_type = ''T'' and a.status = ''5'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_03', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- 高精度定位bl接口表状态还原为0
SET @vSql := 'update sync_interface_bl_' || inMonth || ' set status = ''0'' ' ||
                         'where pospecnumber = ''50091'' and status = ''4''';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_04', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- 高精度定位 厘米级RTK-账号池产品bl接口表排除
SET @vSql := 'update sync_interface_bl_' || inMonth || ' set status = ''1'' ' ||
                     'where pospecnumber = ''50091'' and sospecnumber = ''2022999400021738''';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_05', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- OneGame-智能产品类bl接口表状态还原为0
SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
             'set a.status = 0 ' ||
             'where a.pospecnumber =''50107'' ' ||
             'and sospecnumber = ''2025999480002341'' '||
             'and a.status = ''4'' and (a.FEETYPE=''4796'' OR a.FEETYPE=''8796'') ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_06', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- OneGame-智能产品类ar接口表状态还原为0
SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
             'set a.status = 0 ' ||
             'where a.pospecnumber =''50107'' ' ||
             'and sospecnumber = ''2025999480002341'' '||
             'and a.status = ''4'' and (a.FEETYPE=''4796'' OR a.FEETYPE=''8796'') ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_06_1', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- OneGame-数智人云渲染bl接口表状态还原为0
SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
             'set a.status = 0 ' ||
             'where a.pospecnumber =''50107'' ' ||
             'and sospecnumber = ''2025999480002343'' '||
             'and a.status = ''4'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_07', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- OneGame-数智人云渲染ar接口表状态还原为0
SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
             'set a.status = 0 ' ||
             'where a.pospecnumber =''50107'' ' ||
             'and sospecnumber = ''2025999480002343'' '||
             'and a.status = ''4'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_07_1', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

COMMIT;

outSysError := 'OK';
    outReturn := 0;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_99', '执行完成总耗时秒='||TIMESTAMPDIFF(SECOND, @P_TIME, NOW()));

SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || to_number(outReturn);
END;
END;;
DELIMITER ;
