/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：结算前数据准备-数据源调整-接口表税率及状态更新
 1. 税率为0的，更新为6
 2. 应收接口表的计费账期更新为出账月
 3. 移动云测试客户，状态更新为1
 4. 政企内部客户，状态更新为2
 5. 标准化产品订购，状态更新为3
 6. MPLS-VPN业务，改为按含税金额结算（税率更新为0）
 7. 和医疗业务，状态更新为6，金额修改为CRM归档金额
 8. 能力开放特服号、 能力开放短信通知类受理模式3、工作号、5G阅信、热线彩印、名片彩印、中间号、移动办公个付业务，状态更新为4
 9. 联想权益、和易报统付，状态更新为5 +移动办公智慧流程基础服务
 10. 和教育、梧桐风控受理模式3收入还原部分，状态更新为7
**/
use stludr;
DELIMITER ;;
CREATE OR REPLACE DEFINER="stludr"@"10.%" PROCEDURE stludr."STL_UPD_TAXRATE"(
    inMonth          IN   VARCHAR2,
    inBatch          IN   VARCHAR2,
    outSysError      OUT  VARCHAR2,
    outReturn        OUT  NUMBER
)
AS

    v_proc_name       VARCHAR2(30) := 'STL_UPD_TAXRATE';
    vSql varchar2(9999);
BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
outSysError := substr(@p2, 1, 1000);
            outReturn  := -1;
ROLLBACK;

--  日志写表
select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg ;
END;

BEGIN

    outSysError := '';
    outReturn := 0;

    if ( length(inMonth) < 6 )  then
SELECT 'inMonth length less than 6. inMonth=[' || inMonth || ']' FROM dual;
outReturn := -1;
ROLLBACK;
RETURN;
end if;

SELECT 'inMonth=' ||  inMonth;
SET @P_TIME := SYSDATE;

----更新税率
SET @vSql := 'UPDATE SYNC_INTERFACE_BL_' || inMonth || ' a ' ||
             'SET a.TAXRATE = ''6'', ' ||
                 'a.NOTAXFEE = a.ORGFEE, ' ||
                 'a.TAXFEE = 0 ' ||
           'WHERE a.TAXRATE IS NULL';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_01', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

SET @vSql := 'UPDATE SYNC_INTERFACE_AR_' || inMonth || ' a ' ||
             'SET a.TAXRATE = ''6'', ' ||
                 'a.NOTAXFEE = a.ORGFEE, ' ||
                 'a.TAXFEE = 0 ' ||
           'WHERE a.TAXRATE IS NULL';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_02', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

COMMIT;

----更新应收接口表中往月账期
SET @vSql := 'UPDATE SYNC_INTERFACE_BL_' || inMonth || ' a ' ||
             'SET a.ORGMONTH = ' || inMonth || ' ' ||
           'WHERE a.ORGMONTH != ''' || inMonth || ''' ';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_03', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

COMMIT;

----移动云测试客户排除（所有应收和11月以后的实收）
SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
             'set a.status = ''1'' ' ||
           'where a.pospecnumber = ''1010402'' ' ||
             'and a.poid in (select poid from stlusers.stl_csmp_poid where settlemonth = ''' || inMonth || ''')';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_04', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));


SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
             'set a.status = ''1'' ' ||
           'where a.pospecnumber = ''1010402'' ' ||
             'and a.poid in (select poid from stlusers.stl_csmp_poid where settlemonth = ''201810'') ' ||
             'and a.orgmonth <= ''201810''';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_05', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
             'set a.status = ''1'' ' ||
           'where a.pospecnumber = ''1010402'' ' ||
             'and a.poid in (select poid from stlusers.stl_csmp_poid b where b.settlemonth = a.orgmonth) ' ||
             'and a.orgmonth > ''201810''';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_06', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

commit;

----政企内部客户排除
SET @vSql := 'update sync_interface_bl_' || inMonth || ' t ' ||
         'set t.status = 2 ' ||
        'where t.status = 0 and exists (select 1 from zq_customer_config z ' ||
             'where z.customer_code = t.customernumber and z.type = ''C'')';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_07', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

SET @vSql := 'update sync_interface_ar_' || inMonth || ' t ' ||
         'set t.status = 2 ' ||
        'where t.status = 0 and exists (select 1 from zq_customer_config z ' ||
             'where z.customer_code = t.customernumber and z.type = ''C'')';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_08', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

SET @vSql := 'update sync_bl_settle_' || inMonth || ' t ' ||
         'set t.status = 2 ' ||
        'where t.status = 0 and exists (select 1 from zq_customer_config z ' ||
             'where z.customer_code = t.customernumber and z.type = ''C'')';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_09', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

SET @vSql := 'update stlusers.interface_sub_member_' || inMonth || '_t t ' ||
         'set t.status = 2 ' ||
        'where t.status = 0 and exists (select 1 from zq_customer_config z ' ||
             'where z.customer_code = t.customer_code and z.type = ''C'')';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_10', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

----标准化产品订购数据排除
SET @vSql := 'update sync_interface_bl_' || inMonth || ' t ' ||
         'set t.status = 3 ' ||
        'where t.status = 0 and exists (select 1 from zq_customer_config z ' ||
             'where z.customer_code = t.customernumber and z.offer_code = t.pospecnumber and z.type = ''CO'')';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_11', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

SET @vSql := 'update sync_interface_ar_' || inMonth || ' t ' ||
         'set t.status = 3 ' ||
        'where t.status = 0 and exists (select 1 from zq_customer_config z ' ||
             'where z.customer_code = t.customernumber and z.offer_code = t.pospecnumber and z.type = ''CO'')';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_12', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

SET @vSql := 'update sync_bl_settle_' || inMonth || ' t ' ||
         'set t.status = 3 ' ||
        'where t.status = 0 and exists (select 1 from zq_customer_config z ' ||
             'where z.customer_code = t.customernumber and z.offer_code = t.pospecnumber and z.type = ''CO'')';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_13', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

SET @vSql := 'update stlusers.interface_sub_member_' || inMonth || '_t t ' ||
         'set t.status = 3 ' ||
        'where t.status = 0 and exists (select 1 from zq_customer_config z ' ||
             'where z.customer_code = t.customer_code and z.offer_code = t.product_code and z.type = ''CO'')';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_14', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

commit;


/* --更新 srv6 bl接口表数据状态
SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
         'set a.status = 4 ' ||
       'where a.pospecnumber = ''50120'' ' ||
         'and a.sospecnumber = ''2023999400073791'' '||
         'and a.feetype in(''3967'',''1665'',''1424'',''1663'',''7967'',''5665'',''5424'',''5663'') '||
         'and a.ordermode=''3'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_15', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

--更新 srv6 ar接口表数据状态
SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
        'set a.status = 4 ' ||
       'where a.pospecnumber = ''50120'' ' ||
         'and a.sospecnumber = ''2023999400073791'' '||
         'and a.feetype in(''3967'',''1665'',''1424'',''1663'',''7967'',''5665'',''5424'',''5663'') '||
         'and a.ordermode=''3'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_16', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));
commit; */

---- 云MAS受理模式3、5接口表数据排除
SET @vSql := 'update sync_interface_bl_' || inMonth || ' t ' ||
         'set t.status = 1 ' ||
        'where t.status = 0 and ordermode <> ''1'' and (pospecnumber between ''010101012'' and ''010101017'' or pospecnumber = ''60000'' or sospecnumber = ''110151'')';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_17', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

SET @vSql := 'update sync_interface_ar_' || inMonth || ' t ' ||
         'set t.status = 1 ' ||
        'where t.status = 0 and ordermode <> ''1'' and (pospecnumber between ''010101012'' and ''010101017'' or pospecnumber = ''60000'' or sospecnumber = ''110151'')';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_18', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));
commit;
-- onePark  2024999480000942, 2024999480000943, 2024999480000944
SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
                      'set a.status = 4 ' ||
                    'where a.sospecnumber in (2024999480000942, 2024999480000943, 2024999480000944) and a.status = ''0''';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_19', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));
commit;

--onePark 受理模式3 接口表数据排除
SET @vSql := 'update sync_interface_bl_' || inMonth || ' t ' ||
                          'set t.status = 1 ' ||
                          'where t.status = 0 and ordermode <> ''1'' and sospecnumber in( ''2024999480000942'',''2024999480000943'',''2024999480000944'' ) ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_20', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- 智能路由语音受理模式3通信费从应收接口表排除
SET @vSql := 'update sync_interface_bl_' || inMonth || ' t ' ||
            'set t.status = 1 ' ||
            'where t.status = ''0'' and t.ordermode = ''3'' and sospecnumber in (''5002101'',''5002105'') and feetype = ''01''';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_21', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- 数智协同
SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
                     'set a.status = 4 ' ||
                     'where a.sospecnumber in (''2024999480001535'', ''2024999480001490'', ''2024999480001491'',''2024999480002026'',''2024999480002149'') and a.status = ''0'' ';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_22', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

SET @vSql :='update sync_interface_bl_' || inMonth || ' t ' ||
                    'set t.status = 1 ' ||
                    'where t.status = 0 and ordermode <> ''1'' and sospecnumber in(''2024999480001535'', ''2024999480001490'', ''2024999480001491'',''2024999480002026'',''2024999480002149'') ';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_23', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

--oneSoc 接口表数据排除
SET @vSql :=  'update sync_interface_bl_' || inMonth || ' t ' ||
                'set t.status = 1 ' ||
                'where t.status = 0 and sospecnumber =''2024999480001612'' and t.ordermode=''3'' ';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_24', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- OneSOC一体化安全运营中心
SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
         'set a.status = 4 ' ||
        'where a.pospecnumber =''60004''' ||
        'and sospecnumber = ''2024999480001612'' '||
         'and a.status = ''0'' and a.ordermode=''1'' ';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_73', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));
commit;


----MPLS-VPN商品、SRV6商品-境内发起境外CE产品境外费项 将含税金额改为不含税金额
SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
           'set a.notaxfee = a.notaxfee / (1 + a.taxrate / 100), ' ||
               'a.orgfee = a.orgfee / (1 + a.taxrate / 100), ' ||
               'a.taxrate = 0 ' ||
         'where a.pospecnumber in (''01011309'', ''50042'', ''50120'') ' ||
           'and a.sospecnumber in (''111253'', ''5004203'', ''2023999400073791'') ' ||
           'and a.feetype in (''1425'', ''5425'', ''1428'', ''5428'', ''1429'', ''5429'', ''1664'', ''5664'', ''1666'', ''5666'', ''3968'', ''7968'')';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_25', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
           'set a.notaxfee = a.notaxfee / (1 + a.taxrate / 100), ' ||
               'a.orgfee = a.orgfee / (1 + a.taxrate / 100), ' ||
               'a.taxrate = 0 ' ||
         'where a.pospecnumber in (''01011309'', ''50042'', ''50120'') ' ||
           'and a.sospecnumber in (''111253'', ''5004203'', ''2023999400073791'') ' ||
           'and a.feetype in (''1425'', ''5425'', ''1428'', ''5428'', ''1429'', ''5429'', ''1664'', ''5664'', ''1666'', ''5666'', ''3968'', ''7968'')';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_26', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

commit;


----和医疗原始费用修改为CRM归档金额
SET @vSql := 'update stludr.sync_interface_bl_' || inMonth || ' a ' ||
                         'set status = ''6'' ' ||
                         'where pospecnumber = ''50074'' ' ||
                         'and feetype in(''1990'',''5990'',''1991'',''5991'',''2000'',''6000'')' ||
                           'and not exists (select * from stlusers.stl_sync_rule b ' ||
                               'where b.pospecnumber = ''50074'' ' ||
                                 'and a.orgmonth between b.rulestartmonth and b.ruleendmonth ' ||
                                 'and a.poid = b.poid and a.soid = b.soid and get_normal_feetype(a.feetype) = b.feetype)';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_27', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

SET @vSql := 'update stludr.sync_interface_ar_' || inMonth || ' a ' ||
                          'set status = ''6'' ' ||
                         'where pospecnumber = ''50074'' ' ||
                         'and feetype in(''1990'',''5990'',''1991'',''5991'',''2000'',''6000'')' ||
                           'and not exists (select * from stlusers.stl_sync_rule b ' ||
                               'where b.pospecnumber = ''50074'' ' ||
                                 'and a.orgmonth between b.rulestartmonth and b.ruleendmonth ' ||
                                 'and a.poid = b.poid and a.soid = b.soid and get_normal_feetype(a.feetype) = b.feetype)';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_28', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

SET @vSql := 'update stludr.sync_interface_bl_' || inMonth || ' a ' ||
                           'set notaxfee = (select b.settlefee from stlusers.stl_sync_rule b ' ||
                               'where b.pospecnumber = ''50074'' ' ||
                                 'and a.orgmonth between b.rulestartmonth and b.ruleendmonth ' ||
                                 'and a.poid = b.poid and a.soid = b.soid and get_normal_feetype(a.feetype) = b.feetype) ' ||
                         'where pospecnumber = ''50074'' and status = ''0'' and feetype in(''1990'',''5990'',''1991'',''5991'',''2000'',''6000'')';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_29', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

SET @vSql := 'update stludr.sync_interface_ar_' || inMonth || ' a ' ||
                           'set notaxfee = (select b.settlefee from stlusers.stl_sync_rule b ' ||
                               'where b.pospecnumber = ''50074'' ' ||
                                 'and a.orgmonth between b.rulestartmonth and b.ruleendmonth ' ||
                                 'and a.poid = b.poid and a.soid = b.soid and get_normal_feetype(a.feetype) = b.feetype) ' ||
                         'where pospecnumber = ''50074'' and status = ''0'' and feetype in(''1990'',''5990'',''1991'',''5991'',''2000'',''6000'')';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_30', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

commit;


----能力开放特服号
SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
           'set a.status = 4 ' ||
         'where a.pospecnumber = ''50016'' ' ||
           'and a.sospecnumber <> ''5001606'' ' ||
           'and a.dn like ''12539%'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_31', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
           'set a.status = 4 ' ||
         'where a.pospecnumber = ''50016'' ' ||
           'and a.sospecnumber <> ''5001606'' ' ||
           'and a.dn like ''12539%'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_32', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

----受理模式3短信通知类不进行出账费用结算
SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
           'set a.status = 4 ' ||
         'where a.pospecnumber = ''50016'' ' ||
           'and a.sospecnumber between ''5001608'' and ''5001611'' ' ||
           'and a.ordermode = ''3'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_33', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
           'set a.status = 4 ' ||
         'where a.pospecnumber = ''50016'' ' ||
           'and a.sospecnumber between ''5001608'' and ''5001611'' ' ||
           'and a.ordermode = ''3'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_34', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

--更新工作号冗余费项的状态
SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
          'set a.status = 7 ' ||
        'where a.pospecnumber = ''50016'' ' ||
          'and a.sospecnumber = ''2022999400004820'' '||
          'and (a.ordermode= ''3'' or (a.ordermode = ''1'' and a.feetype not in (''2318'',''2326'',''6318'',''6326''))) ' ||
          'and a.status=0 ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_35', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

--更新 工作号 bl接口表数据状态
SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
          'set a.status = 4 ' ||
        'where a.pospecnumber = ''50016'' ' ||
          'and a.sospecnumber = ''2022999400004820'' '||
          'and a.feetype in(''2318'',''2326'',''6318'',''6326'') '||
          'and a.ordermode=''1'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_36', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

--更新 工作号  ar接口表数据状态
SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
          'set a.status = 4 ' ||
        'where a.pospecnumber = ''50016'' ' ||
          'and a.sospecnumber = ''2022999400004820'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_37', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

--云游戏QOS
--产品5001612 -》原商品50016 换了新商品  50121
SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
                     'set a.status = 4 ' ||
                     'where a.pospecnumber = ''50121'' ' ||
                     'and a.sospecnumber = ''5001612'' ' ||
                     'and a.dn like ''12539%'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_38', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
                     'set a.status = 4 ' ||
                     'where a.pospecnumber = ''50121'' ' ||
                     'and a.sospecnumber = ''5001612'' ' ||
                     'and a.dn like ''12539%'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_39', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
                     'set a.status = 4 ' ||
                     'where a.pospecnumber = ''50121'' ' ||
                     'and a.sospecnumber = ''2023999400018530'' ' ;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_40', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
                     'set a.status = 4 ' ||
                     'where a.pospecnumber = ''50121'' ' ||
                     'and a.sospecnumber = ''2023999400018530'' ' ;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_41', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

--5G-阅信   更新bl接口表数据
SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
          'set a.status = 4 ' ||
        'where a.pospecnumber = ''50034'' ' ||
          'and a.sospecnumber = ''5003401'' ' ||
         'and a.feetype not in(''3908'',''7908'') ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_42', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- OnePark行业增值   更新bl接口表数据，受理模式1 政企收入还原数据
SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
        'set a.status = 5 ' ||
        'where a.pospecnumber = ''50076'' ' ||
        'and a.sospecnumber = ''2023999400048574'' ' ||
        'and a.cdr_chargecode = ''G'' ' ||
        'and a.ordermode=''1'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_43', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

--5G-阅信   更新ar接口表数据
SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
          'set a.status = 4 ' ||
        'where a.pospecnumber = ''50034'' ' ||
          'and a.sospecnumber = ''5003401''  '||
         'and a.feetype not in(''3908'',''7908'') ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_44', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

--和盾安全产品   更新bl接口表数据
SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
          'set a.status = 4 ' ||
        'where a.pospecnumber = ''50086'' ' ||
          'and a.sospecnumber in (''2022999400016609'',''2022999400016610'',''2023999400059470'',''2023999400059469'') ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_45', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));


--和盾安全产品   更新ar接口表数据
SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
          'set a.status = 4 ' ||
        'where a.pospecnumber = ''50086'' ' ||
          'and a.sospecnumber in (''2022999400016609'',''2022999400016610'',''2023999400059470'',''2023999400059469'') ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_46', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

----中移凌云
SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
          'set a.status = 4 ' ||
        'where a.pospecnumber = ''50087'' ' ||
          'and a.sospecnumber = ''2022999400021419'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_47', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));


--和盾安全融合产品   更新bl接口表数据
SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
          'set a.status = 4 ' ||
        'where a.pospecnumber = ''50088'' ' ||
          'and a.sospecnumber = ''2022999400016609'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_48', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));


--和盾安全融合产品   更新ar接口表数据
SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
          'set a.status = 4 ' ||
        'where a.pospecnumber = ''50088'' ' ||
          'and a.sospecnumber = ''2022999400016609'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_49', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));


--热线彩印   更新bl接口表数据
SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
          'set a.status = 4 ' ||
        'where a.pospecnumber = ''50022'' ' ||
          'and a.sospecnumber = ''5002202'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_50', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));


--热线彩印   更新ar接口表数据
SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
          'set a.status = 4 ' ||
        'where a.pospecnumber = ''50022'' ' ||
          'and a.sospecnumber = ''5002202'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_51', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

--阅信(增值能力)、onepower互联网工业、跨省专线卫士
SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
         'set a.status = 4 ' ||
         'where a.pospecnumber in(''50020'',''50118'') or (a.pospecnumber =''50097'' and a.sospecnumber!=''2025999480001608'') ' ||
         'and a.status = ''0'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_52', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));


--阅信(增值能力)、onepower互联网工业、移动办公DICT集成服务包
SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
         'set a.status = 4 ' ||
         'where (a.pospecnumber =''50020'' or a.sospecnumber=''2023999400036743'' or (a.pospecnumber =''50097'' and a.sospecnumber!=''2025999480001608'') ) ' ||
         'and a.status = ''0'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_53', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- onepower-AI移企查产品,无省间
SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
             'set a.status = 1 ' ||
             'where  a.sospecnumber=''2025999480001608'' ' ||
             'and a.status = ''0'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_52', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));



-- onepower-AI移企查产品,无省间
SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
             'set a.status = 1 ' ||
             'where a.sospecnumber=''2025999480001608'' ' ||
             'and a.status = ''0'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_53', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

--更新星火党建应收接口表数据状态
SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
         'set a.status = 4 ' ||
         'where a.pospecnumber = ''50096'' ' ||
           'and a.sospecnumber = ''2022999400077568'' ' ||
           'and a.feetype in (''3305'', ''3306'', ''3307'')';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_54', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

--更新星火党建实收接口表数据状态
SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
         'set a.status = 4 ' ||
         'where a.pospecnumber = ''50096'' ' ||
           'and a.sospecnumber = ''2022999400077568'' ' ||
           'and a.feetype in (''3305'', ''3306'', ''3307'')';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_55', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));


--名片彩印   更新amount接口表个付数据
SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
          'set a.status = 4 ' ||
        'where a.pospecnumber = ''50022'' ' ||
          'and a.sospecnumber = ''5002201'' '||
          'and a.cdr_chargename=''2'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_56', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));


--名片彩印   更新amount接口表统付数据
SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
          'set a.status = 5 ' ||
        'where a.pospecnumber = ''50022'' ' ||
          'and a.sospecnumber = ''5002201'' '||
          'and a.status = ''0'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_57', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

--能开-中间号
SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
           'set a.status = 4 ' ||
         'where a.pospecnumber = ''50016'' ' ||
           'and a.sospecnumber = ''5001606'' '||
           'and a.status = ''0'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_58', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));


--移动办公-快通知、移动办公-三公产品
SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
           'set a.status = 4 ' ||
         'where a.pospecnumber = ''50038'' ' ||
           'and a.sospecnumber in(''2022999400045330'',''2023999400007532'',''2023999400007535'',''2023999400007533'') '||
           'and a.status = ''0'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_59', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

--OneTrip基础产品、 OneGame-游戏云直播、 OneCity产品、e企收银产品
SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
           'set a.status = 4 ' ||
             'where a.pospecnumber in(''50078'',''50100'',''50107'',''50068'',''50099'')' ||
             'and sospecnumber not in( ''2023999400052876'',''2025999480002343'') '||
           'and a.status = ''0'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_60', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- OneGame-智能产品类
SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
             'set a.status = 1 ' ||
             'where a.pospecnumber =''50107'' ' ||
             'and sospecnumber = ''2025999480002341'' and (a.FEETYPE=''4796'' OR a.FEETYPE=''8796'') '||
             'and a.status = ''4'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_60_1', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- OneGame-智能产品类
SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
             'set a.status = 4 ' ||
             'where a.pospecnumber =''50107'' ' ||
             'and sospecnumber = ''2025999480002341'' '||
             'and a.status = ''0'' and (a.FEETYPE=''4796'' OR a.FEETYPE=''8796'') ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_60_2', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- OneGame-智能产品类
SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
             'set a.status = 4 ' ||
             'where a.pospecnumber =''50107'' ' ||
             'and sospecnumber = ''2025999480002341'' '||
             'and a.status = ''0'' and (a.FEETYPE=''4796'' OR a.FEETYPE=''8796'') ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_60_3', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- OneGame-数智人云渲染-tariff规则，不取amount表数据
SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
             'set a.status = 1 ' ||
             'where a.pospecnumber =''50107'' ' ||
             'and sospecnumber = ''2025999480002343'' '||
             'and a.status = ''0'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_60_4', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- OneGame-数智人云渲染
SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
             'set a.status = 4 ' ||
             'where a.pospecnumber =''50107'' ' ||
             'and sospecnumber =''2025999480002343'' '||
             'and a.status = ''0'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_60_5', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));


-- OneGame-数智人云渲染
SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
             'set a.status = 4 ' ||
             'where a.pospecnumber =''50107'' ' ||
             'and sospecnumber =''2025999480002343'' '||
             'and a.status = ''0'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_60_6', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

--云游戏实例
SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
                   'set a.status = 4 ' ||
                 'where a.pospecnumber =''50121'''||
                 'and sospecnumber = ''2023999400018530'' '||
                   'and a.status = ''0'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_61', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));
/*
 --OneCity产品
execute immediate 'update sync_interface_amount_' || inMonth || ' a ' ||
   'set a.status = 4 ' ||
 'where a.pospecnumber  = ''50068''  ' ||
   'and a.status = ''0'' ';


  --e企收银产品
execute immediate 'update sync_interface_amount_' || inMonth || ' a ' ||
   'set a.status = 4 ' ||
 'where a.pospecnumber  = ''50099''  ' ||
   'and a.status = ''0'' '; */

--sd-wan组网业务、e企组网、千里眼标准产品增值服务、OneNET标准产品增值服务包、移动办公DICT集成服务包
--行车卫士标准产品
SET @vSql := 'update sync_interface_sdwan_' || inMonth || ' a ' ||
           'set a.status = 4 ' ||
         'where (a.pospecnumber in(''50077'',''50110'',''50117'',''50119'')   ' ||
         'or a.sospecnumber=''2022999400072067''  '||
         'or a.sospecnumber=''2023999400010056''   '||
         'or a.sospecnumber=''2023999400048574''    '||
        ' or a.sospecnumber=''2023999400036743'')   '||
           'and a.status = ''0'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_62', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));


--企业视频彩铃产品 【融合50036产品】
SET @vSql := 'update /*+ AUTOCOMMIT_DURING_DML() */ sync_interface_amount_' || inMonth || ' a ' ||
           'set a.status = 4 ' ||
         'where a.pospecnumber in (''010113001'',''50036'')   ' ||
           'and a.sospecnumber = ''910401''   '||
           'and a.feetype <> ''3681''  '||
           'and a.status = ''0'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_63', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- OneVillage 商品
SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
           'set a.status = 4 ' ||
         'where a.pospecnumber  = ''50106''  ' ||
           'and a.status = ''0'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_64', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));


----移动办公个付（含联想权益）
SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
           'set a.status = 4 ' ||
         'where a.pospecnumber = ''50038'' ' ||
           'and a.cdr_chargename in (''2'', ''3'') ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_65', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));


-- 企业学培个付
SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
           'set a.status = 4 ' ||
         'where a.pospecnumber = ''60017'' ' ||
           'and a.cdr_chargename in (''2'', ''3'') ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_65_1', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));



----联想权益、和易报统付  移动办公智慧流程基础服务  移动办公数字人
SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
           'set a.status = 5 ' ||
           'where a.pospecnumber = ''50038'' and (a.sospecnumber between ''5003810'' and ''5003822'' or a.sospecnumber in (''2024999480000641'',''2021999400048964'',''2022999400013561'',''2022999400013562'',''2023999400041622'',''2023999400041623'',''2023999400092024'',''2024999480007396'')) and a.status = ''0'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_66', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

----和教育
--去除bl表受理模式3收入还原部分
SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
                'set a.status = 7 ' ||
              'where a.pospecnumber = ''50049'' and a.ordermode = ''3'' ' ||
                'and (a.sospecnumber in (''5004901'', ''5004902'', ''5004903'', ''5004909'', ''5004915'', ''5004929'', ''5004935'', ' ||
                    '''5004936'', ''5004938'', ''5004948'', ''5004951'', ''5004952'', ''5004953'', ''5004965'', ''5004966'', ''5004970'', ' ||
                    '''5004971'', ''5004927'',''2022999400066446'',''2022999400066447'',''2022999400066448'',''2022999400066449'',''2024999480007066'') or (a.sospecnumber = ''5004964'' and a.cdr_chargecode = ''2737''))' ;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_67', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
                'set a.status = 4 ' ||
              'where a.pospecnumber = ''50049'' and a.status = ''0''';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_68', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
                'set a.status = 4 ' ||
              'where a.pospecnumber = ''50049'' ' ||
                'and (a.sospecnumber = ''5004957'' and a.cdr_chargecode = ''2724'' or a.sospecnumber = ''5004964'' and a.cdr_chargecode = ''2738'') ' ||
                'and a.feetype = ''02'' and a.status = ''0''';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_69', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

----梧桐风控受理模式3
--去除bl表受理模式3收入还原部分
SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
                'set a.status = 7 ' ||
              'where a.pospecnumber = ''50024'' and a.sospecnumber = ''2021999400052091'' and a.ordermode = ''3'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_70', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

--和对讲 bl接口表数据状态
SET @vSql :='update sync_interface_bl_' || inMonth || ' a ' ||
                    'set a.status = 4 ' ||
                    'WHERE EXISTS ( ' ||
                    'SELECT 1 FROM stl_mnp_record_poc b ' ||
                    'where a.pospecnumber = ''50025'' ' ||
                    'and a.sospecnumber = ''5002501'' ' ||
                    'and a.ordermode in(''1'',''3'') ' ||
                    'AND a.DN =b.MEMBER_CODE ' ||
                    'AND b.OTH_NETWORK_FLAG=''1'' ' ||
                    'AND a.soid = b.prod_order_id ' ||
                    'AND b.settlemonth = ' || inMonth || ' ' ||
                    'AND b.partid = substr(''' || inMonth || ''', 5, 2)) ' ||
                    'AND a.cdr_chargecode != ''4739'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_71', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

--更新 和对讲  ar接口表数据状态
SET @vSql :='update sync_interface_ar_' || inMonth || ' a ' ||
                    'set a.status = 4 ' ||
                    'WHERE EXISTS ( ' ||
                    'SELECT 1 FROM stl_mnp_record_poc b ' ||
                    'where a.pospecnumber = ''50025'' ' ||
                    'and a.sospecnumber = ''5002501'' ' ||
                    'and a.ordermode in(''1'',''3'') ' ||
                    'AND a.DN =b.MEMBER_CODE ' ||
                    'AND b.OTH_NETWORK_FLAG=''1'' ' ||
                    'AND a.soid = b.prod_order_id ' ||
                    'AND b.settlemonth = ' || inMonth || ' ' ||
                    'AND b.partid = substr(''' || inMonth || ''', 5, 2)) ' ||
                    'AND a.cdr_chargecode != ''4739'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_72', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

--和对讲 bl接口表数据状态
SET @vSql :='update sync_interface_bl_' || inMonth || ' a ' ||
                    'set a.status = 1 ' ||
                    'where a.pospecnumber = ''50025'' ' ||
                    'and a.sospecnumber = ''5002501'' ' ||
                    'and a.ordermode in(''3'') ' ||
                    'and a.cdr_chargecode = ''4739'' ';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_73', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

--和对讲 ar接口表数据状态
SET @vSql :='update sync_interface_ar_' || inMonth || ' a ' ||
                    'set a.status = 1 ' ||
                    'where a.pospecnumber = ''50025'' ' ||
                    'and a.sospecnumber = ''5002501'' ' ||
                    'and a.ordermode in(''3'') ' ||
                    'and  a.cdr_chargecode = ''4739'' ';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_74', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

--和对讲 bl接口表数据状态
SET @vSql :='update sync_interface_bl_' || inMonth || ' a ' ||
                    'set a.status = 4 ' ||
                    'where a.pospecnumber = ''50025'' ' ||
                    'and a.sospecnumber = ''5002501'' ' ||
                    'and a.ordermode in(''1'') ' ||
                    'and a.cdr_chargecode = ''4739'' ';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_75', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

--和对讲 ar接口表数据状态
SET @vSql :='update sync_interface_ar_' || inMonth || ' a ' ||
                    'set a.status = 4 ' ||
                    'where a.pospecnumber = ''50025'' ' ||
                    'and a.sospecnumber = ''5002501'' ' ||
                    'and a.ordermode in(''1'') ' ||
                    'and  a.cdr_chargecode = ''4739'' ';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_76', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

--和对讲 amount接口表数据状态 受理模式3的4103费项的异网号码不结算
SET @vSql :='update sync_interface_amount_' || inMonth || ' a ' ||
                    'set a.status = 1 ' ||
                    'WHERE EXISTS ( ' ||
                    'SELECT 1 FROM stl_mnp_record_poc b ' ||
                    'where a.pospecnumber = ''50025'' ' ||
                    'and a.sospecnumber = ''5002501'' ' ||
                    'and a.ordermode in(''3'') ' ||
                    'AND a.DN =b.MEMBER_CODE ' ||
                    'AND b.OTH_NETWORK_FLAG=''1'' ' ||
                    'AND a.soid = b.prod_order_id ' ||
                    'AND b.settlemonth = ' || inMonth || ' ' ||
                    'AND b.partid = substr(''' || inMonth || ''', 5, 2)) and a.feetype=''4103'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_77', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- 高精度定位受理模式1 bl结构表状态修改
SET @vSql :='update sync_interface_bl_' || inMonth || ' a ' ||
                    'set a.status = 4 ' ||
                    'where a.pospecnumber = ''50091'' ' ||
                    'and a.ordermode = ''1''';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_78', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- oneTraffic
SET @vSql :='update sync_interface_sdwan_' || inMonth || ' a ' ||
            'set a.status = 4 ' ||
            'where a.pospecnumber =''50109'' and a.sospecnumber = ''2023999400007369''  ' ||
            'and a.status = ''0'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_78', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- BIL-ZQ-202407-19-5G融媒结算规则需求
    SET @vSql :='update sync_interface_amount_' || inMonth || ' a ' ||
                'set a.status = 4 ' ||
                'where a.pospecnumber =''50126'' and a.sospecnumber in( ''2024999480001225'',''2024999480002677'',''2024999480002678'',''2024999480002680'',''2024999480002679'' ) ' ||
                'and a.status = ''0'' ';
    SELECT @vSql;
    PREPARE STMT FROM @vSql;
    EXECUTE STMT;
    DEALLOCATE PREPARE STMT;
    call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_79', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- 量子密话个付
SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
             'set a.status = 4 ' ||
             'where a.pospecnumber = ''60010'' and a.sospecnumber = ''2024999480010058'' ' ||
             'and a.cdr_chargename in (''2'', ''3'') ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_89', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- 量子密话统付
SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
             'set a.status = 5 ' ||
             'where a.pospecnumber = ''60010'' and a.sospecnumber = ''2024999480010058'' and a.status = ''0'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_90', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));
-- BIL-ZQ-202504-03-专线延伸服务ITS业务结算需求-结算。AI语音助手（120600）、AI语音助手（120200）、AI语音助手（120101）、餐旅助手
SET @vSql :='update sync_interface_amount_' || inMonth || ' a ' ||
            'set a.status = 4 ' ||
            'where a.pospecnumber =''60013'' and a.sospecnumber in( ''2024999480011493'',''2025999480001961'',''2025999480001960'',''2024999480011455'') ' ||
            'and a.status = ''0'' ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_91', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- 和商务TV
SET @vSql :='update sync_interface_amount_' || inMonth || ' a ' ||
            'set a.status = 4 ' ||
            'where a.pospecnumber = ''50028'' ' ||
            'and a.status = ''0''';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_92', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- 咪咕人车家
SET @vSql :='update sync_interface_amount_' || inMonth || ' a ' ||
            'set a.status = 4 ' ||
            'where a.pospecnumber = ''60005'' and a.sospecnumber=''2024999480004464'' ' ||
            'and a.status = ''0''';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_93', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- e企组网-云无线
SET @vSql :='update sync_interface_amount_' || inMonth || ' a ' ||
            'set a.status = 4 ' ||
            'where a.pospecnumber = ''50110'' and a.sospecnumber=''2024999480004078'' ' ||
            'and a.status = ''0''';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_94', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

----调用用量接口结算价格计算存储过程
call STL_SETTLEFEE_CALCULATE(inMonth, inBatch,@outSysError, @outReturn);
IF ( @outReturn != 0) THEN
SELECT 'call STL_SETTLEFEE_CALCULATE error, return_code=' || @outReturn FROM dual;
ROLLBACK;
RETURN;
END IF;
commit;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_80', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

----调用费项映射存储过程
call STL_CHARGECODE_MAPPING(inMonth,inBatch, @outSysError, @outReturn);
IF ( @outReturn != 0) THEN
SELECT 'call STL_CHARGECODE_MAPPING error, return_code=' || @outReturn FROM dual;
ROLLBACK;
RETURN;
END IF;
commit;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_81', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- CDN 95峰计算
call PROC_CDN_RATE(inMonth, @outReturn, @outSysError);
IF ( @outReturn != 0) THEN
SELECT 'call PROC_CDN_RATE error, return_code=' || @outReturn FROM dual;
ROLLBACK;
RETURN;
END IF;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_82', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- CDN CDN_VAS计算
call PROC_CDN_VAS(inMonth, @outReturn, @outSysError);
IF ( @outReturn != 0) THEN
SELECT 'call PROC_CDN_VAS error, return_code=' || @outReturn FROM dual;
ROLLBACK;
RETURN;
END IF;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_83', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- PROC_CDN_RATE_RIF计算
call PROC_CDN_RATE_RIF(inMonth, @nReturn, @outSysError);
IF ( @nReturn != 0) THEN
SELECT 'call PROC_CDN_RATE_RIF error, return_code=' || @nReturn FROM dual;
ROLLBACK;
RETURN;
END IF;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_84', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- CDN全站加速计算
call PROC_CDNAPPEND_RATE(inMonth, @nReturn, @outSysError);
IF ( @nReturn != 0) THEN
SELECT 'call PROC_CDNAPPEND_RATE error, return_code=' || @nReturn FROM dual;
ROLLBACK;
RETURN;
END IF;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_85', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- 海外CDN 数据源结算
call PROC_HWCDN_RATE(inMonth, @nReturn, @outSysError);
IF ( @nReturn != 0) THEN
SELECT 'call PROC_HWCDN_RATE error, return_code=' || @nReturn FROM dual;
ROLLBACK;
RETURN;
END IF;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_86', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

SET @vSql := 'TRUNCATE TABLE stludr.stl_cb_mode';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_87', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

insert into stl_cb_mode
select null,a.bal_month, a.ecid, a.product_code, b.order_mode, ''
from (select distinct bal_month, ecid, t.product_code
      from stludr.sync_cb_interface t
      where t.bal_month = inMonth
        and t.product_code in ('010101001', '010105003')) a
         left join (select distinct ec_code,
                                    product_code,
                                    decode(prod_order_mode, 1, '1', '3/5') order_mode
                    from stlusers.stl_serv_biz_code
                    where inMonth between to_char(effective_date, 'yyyymm') and
                        to_char(expiry_date, 'yyyymm')
                      and product_code in ('010101001', '010105003')
                      and ec_code in (select distinct ecid
                                      from stludr.sync_cb_interface
                                      where bal_month = inMonth)) b on a.ecid =
                                                                       b.ec_code
    and a.product_code =b.product_code;

COMMIT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_88', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

outSysError := 'OK';
outReturn := 0;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_99', '执行完成总耗时秒='||TIMESTAMPDIFF(SECOND, @P_TIME, NOW()));
SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || to_number(outReturn);
END;
END;;
DELIMITER ;
