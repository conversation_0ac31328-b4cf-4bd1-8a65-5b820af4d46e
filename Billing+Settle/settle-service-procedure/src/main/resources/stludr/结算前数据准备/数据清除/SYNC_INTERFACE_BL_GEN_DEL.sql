/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：计费同步结算前 清理接口表数据
**/
DELIMITER  //
CREATE or REPLACE DEFINER="stludr"@"10.%" PROCEDURE "SYNC_INTERFACE_BL_GEN_DEL"(
                                        szMonth        IN  VARCHAR2,
                                        szSysErr        OUT VARCHAR2,
                                        nReturn         OUT VARCHAR2
)
AS


    sz_break               varchar2(10);
    szSql                  VARCHAR2(5000);
    vSql                   VARCHAR2(5000);
    nRawCnt        NUMBER;
    v_err_msg  VARCHAR2(512);
    v_proc_name         VARCHAR2(50) := 'SYNC_INTERFACE_BL_GEN_DEL';

BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
szSysErr := substr(@p2, 1, 1000);
        szSysErr := 'break:'||sz_break||'error:'||szSysErr;
        nReturn  := -1;
ROLL<PERSON>CK;


select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
END;

BEGIN


        sz_break := '201';

        set @vSql := 'truncate table Sync_Interface_BL_5';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := 'truncate table sync_interface_amount_5';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


set @vSql := 'insert into Sync_Interface_BL_5 select * from  Sync_Interface_BL_'||substr(szMonth, 1, 6)|| ' where remark=5';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


set @vSql := 'insert into sync_interface_amount_5 select * from  SYNC_INTERFACE_AMOUNT_'||substr(szMonth, 1, 6)|| ' where remark=5';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := 'truncate table SYNC_INTERFACE_AMOUNT_'||substr(szMonth, 1, 6)|| '';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := 'truncate table Sync_Interface_BL_'||substr(szMonth, 1, 6)|| '';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := 'insert  into  Sync_Interface_BL_'||substr(szMonth, 1, 6)|| '  select * from   Sync_Interface_BL_5';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := 'insert  into  SYNC_INTERFACE_AMOUNT_'||substr(szMonth, 1, 6)|| '  select * from  sync_interface_amount_5';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := 'DELETE FROM   SYNC_INTERFACE_SDWAN_'||substr(szMonth, 1, 6)|| ' WHERE remark=2';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


set @vSql := 'DELETE FROM   SYNC_ONEPOWER_'||substr(szMonth, 1, 6)|| '';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


set @vSql := 'DELETE FROM   SYNC_ONEGAME_'||substr(szMonth, 1, 6)|| '';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := 'DELETE FROM   BIZ_MSG_MMM_STL WHERE acct_month='||substr(szMonth, 1, 6)|| '';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


szSysErr := 'OK';
        nReturn := 0;

SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;

END;
END//
delimiter ;