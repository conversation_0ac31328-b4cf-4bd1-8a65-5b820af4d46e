/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：出账流程-恢复结算前状态（日志/任务/查重表清除）
  1. 清理结果表数据
  2. 清理临时接口表数据
**/
DROP PROCEDURE IF EXISTS stludr.`SETTLE_RESTORE`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "SETTLE_RESTORE"(
	inMonth in VARCHAR2,
	inBatch in NUMBER,
	outSysError out VARCHAR2,
	outReturn out NUMBER)
AS

	vSql      		varchar2(10240);
	v_proc_name  	VARCHAR2(30) := 'STLUDR.SETTLE_RESTORE';
	P_ERRCODE VARCHAR2(1024);
	P_ERRMSG VARCHAR2(2048);

BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
GET DIAGNOSTICS CONDITION 1
    P_ERRCODE = RETURNED_SQLSTATE, P_ERRMSG = MESSAGE_TEXT;
outSysError := substr(P_ERRMSG, 1, 2048);
        outReturn  := -1;
ROLLBACK;
select ('exception: ' || outReturn || '|'  || P_ERRCODE || '|' || outSysError ) AS error_msg ;
call STLUDR.STL_ERROR_LOG(inMonth,P_ERRCODE,outSysError,'',v_proc_name,substr(@vSql,1,2048));

END;

BEGIN
       outSysError := '';
       outReturn := 0;

       set @vSql := 'delete from STLUDR.RVL_INFO_LOG where RPT_FILENAME = ''' || v_proc_name || '''';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;

if (inBatch not in (0, 1, 2)) then
         outReturn := 1;
         outSysError := '批次错误。现仅支持：预出账-0；一批-1；二批-2。';
call STL_INFO_LOG(inMonth, '', v_proc_name, outSysError);
else
			if (inBatch = 0 or inBatch = 1) then
		set @vSql := 'TRUNCATE TABLE ur_recv_' || inMonth || '_t';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
set @vSql := 'TRUNCATE TABLE ur_cpr_' || inMonth || '_t';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
set @vSql := 'TRUNCATE TABLE ur_eboss_' || inMonth || '_t';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
set @vSql := 'TRUNCATE TABLE err_bill_' || inMonth;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
set @vSql := 'TRUNCATE TABLE err_cpr_' || inMonth;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
set @vSql := 'TRUNCATE TABLE err_eboss_' || inMonth;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
set @vSql := 'TRUNCATE TABLE int_interface_bl';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
set @vSql := 'TRUNCATE TABLE int_interface_ar';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
commit;

if (inBatch = 0) then
	outSysError := '预出账应收结果表已清空。';
else if (inBatch = 1) then
	outSysError := '一批应收结果表已清空。';
end if;
end if;
call STL_INFO_LOG(inMonth, '', v_proc_name, outSysError);

else
					set @vSql := 'delete from ur_recv_' || inMonth || '_t where phase = 2';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
set @vSql := 'delete from ur_paid_' || inMonth || '_t where phase = 2';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
set @vSql := 'delete from ur_eboss_' || inMonth || '_t where phase = 2';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
set @vSql := 'delete from ur_cpr_' || inMonth || '_t where phase = 2';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
set @vSql := 'delete from ur_cpp_' || inMonth || '_t where phase = 2';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
set @vSql := 'delete from ur_agent_' || inMonth || '_t where phase = 2';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
set @vSql := 'delete from ur_ext_' || inMonth || '_t where phase = 2';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
set @vSql := 'delete from ur_esp_' || inMonth || '_t where phase = 2';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
set @vSql := 'truncate table int_interface_bl';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
set @vSql := 'truncate table int_interface_ar';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

commit;

outReturn := 0;
			outSysError := '二批结果表数据已删除。';
call STL_INFO_LOG(inMonth, '', v_proc_name, outSysError);

end if;
end if;

SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
call STL_INFO_LOG(inMonth, '', v_proc_name, 'procedure ' || v_proc_name || ' completed successfully. outReturn = success');

END;
END ;;
DELIMITER ;
