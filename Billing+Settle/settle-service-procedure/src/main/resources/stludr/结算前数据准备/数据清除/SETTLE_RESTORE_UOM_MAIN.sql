/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：结算前数据准备-数据清除-恢复结算前状态
**/
DROP PROCEDURE IF EXISTS stludr.`SETTLE_RESTORE_UOM_MAIN`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "SETTLE_RESTORE_UOM_MAIN"(inMonth in varchar2,
                                        inBatch IN VARCHAR2,
                                        flag_version IN VARCHAR2,
                                        reserve1 IN VARCHAR2,
                                        reserve2 IN VARCHAR2,
                                        proc_out OUT VARCHAR2,
                                        outSysError OUT VARCHAR2(1000),
                                        outReturn OUT NUMBER(4),
                                        outBL OUT VARCHAR2,
                                        outAR OUT VARCHAR2  )
AS



    v_proc_name  VARCHAR2(50) := 'SETTLE_RESTORE_UOM_MAIN';
BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
outSysError := substr(@p2, 1, 1000);
        outReturn  := -1;
        outBL := -1;
        outAR := -1;
ROLLBACK;
select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg ;
call STLUDR.STL_ERROR_LOG(inMonth,outReturn,@p1,'',v_proc_name,outSysError);
END;
BEGIN
       set @vSql := 'delete from STLUDR.RVL_INFO_LOG where RPT_FILENAME = ''' || v_proc_name || '''';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;

call STLUDR.SETTLE_RESTORE(inMonth, inBatch,outSysError, outReturn);
IF outReturn != 0 THEN
        ROLLBACK;
SELECT ('exception: ' || outReturn || '|' || outSysError ) AS error_msg;
RETURN;
END IF;

call STLUSERS.SETTLE_RESTORE(inMonth, inBatch,outSysError, outReturn);
IF outReturn != 0 THEN
        ROLLBACK;
SELECT ('exception: ' || outReturn || '|' || outSysError ) AS error_msg;
RETURN;
END IF;
COMMIT;
proc_out :='0';
      outSysError := 'OK';
      outReturn := 0;
      outBL := 0;
      outAR := 0;

SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
call STL_INFO_LOG(inMonth, '', v_proc_name, outSysError);
END;
END ;;
DELIMITER ;