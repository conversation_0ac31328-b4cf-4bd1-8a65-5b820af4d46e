CREATE TABLE IF NOT EXISTS stludr.temp_maapmma_45053  (
  `ec_code_prov` varchar(10) null DEFAULT NULL,
  `manufacturer_code` varchar(32) null DEFAULT NULL,
  `location` varchar(10) null DEFAULT NULL,
  `charge_out` decimal(32, 0) null DEFAULT NULL,
  `total` varchar(18) null DEFAULT NULL
)  ENGINE=GreatDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin ;

-- 清理临时表
truncate table stludr.temp_maapmma_45053;
-- 第一步：创建临时表存储主表和customer_45053_temp的关联结果
insert into stludr.temp_maapmma_45053
SELECT a.ec_code_prov,
       a.manufacturer_code,
       cust.LOCATION,
       sum(decode(a.rate_back_id, '1', 54, a.bill_charge)) as charge_out,
       count(a.cerca_mark) as total
FROM stludr.UR_MAAPMMA_202502_T a
JOIN stludr.customer_45053_temp cust ON a.EC_CODE = cust.EC_CODE
WHERE a.offer_code = '50034'
  AND a.product_code = '5003401'
  AND a.cerca_mark = '1'
GROUP BY a.ec_code_prov, a.manufacturer_code, cust.LOCATION;

-- 第二步：插入最终结果
INSERT INTO stludr.udm_data_for_45053
SELECT *
FROM (SELECT t.ec_code_prov                ec_code,
             f.terminal_name,
             t.charge_out,
             t.total                       sum_num,
             b.evaluationscore             evaluation_score,
             c.incentivecoefficient        incentive_coefficient,
             t.LOCATION                    location
      FROM stludr.temp_maapmma_45053 t
               JOIN stludr.MAAPMMA_VENDOR_CONF f ON t.manufacturer_code = f.terminal_brand
               JOIN (SELECT x.terminalvendor, x.evaluationscore
                    FROM stlusers.MAAP_TERMINAL_INFO x,
                         (SELECT max(createdate) createdate,
                                 terminalvendor
                          FROM stlusers.MAAP_TERMINAL_INFO
                          WHERE '202502' BETWEEN startdate AND enddate
                            AND (incentivecoefficient IS NULL OR incentivecoefficient = '')
                          GROUP BY terminalvendor) y
                    WHERE x.createdate = y.createdate
                      AND x.terminalvendor = y.terminalvendor
                      AND (incentivecoefficient IS NULL OR incentivecoefficient = '')
                      AND '202502' BETWEEN x.startdate AND x.enddate) b ON f.terminal_vendor = b.terminalvendor
               JOIN (SELECT x.terminalvendor,
                           x.incentivecoefficient
                    FROM stlusers.MAAP_TERMINAL_INFO x,
                         (SELECT max(createdate) createdate,
                                 terminalvendor
                          FROM stlusers.MAAP_TERMINAL_INFO
                          WHERE '202502' BETWEEN startdate AND enddate
                            AND (evaluationscore IS NULL OR evaluationscore = '')
                          GROUP BY terminalvendor) y
                    WHERE x.createdate = y.createdate
                      AND x.terminalvendor = y.terminalvendor
                      AND (x.evaluationscore IS NULL OR x.evaluationscore = '')
                      AND '202502' BETWEEN x.startdate AND x.enddate) c ON f.terminal_vendor = c.terminalvendor);