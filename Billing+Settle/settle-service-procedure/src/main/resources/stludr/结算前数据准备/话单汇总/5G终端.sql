INSERT /*+ AUTOCOMMIT_DURING_DML() */ INTO SYNC_BL_SETTLE_MID 
SELECT /*+ hash_join()*/ NULL, ticket_id stream_id, 
       'BL' datasource, 
       t.prod_order_mode ordermode, 
       t.acct_month orgmonth, 
       t.ec_code customernumber, 
       t.offer_code pospecnumber, 
       t.product_code sospecnumber, 
       t.offer_order_id poid, 
       t.product_order_id soid, 
       decode(c.charge_item_ref, '1587', '157', c.charge_item_ref) feetype, 
       '' dn, 
       t.fee amount, 
       '2' amount_type, 
       to_char(SYSDATE, 'yyyymmddhh24miss') synctime, 
       '0' status, 
       '' errmesg, 
       t.terminalvendor prov_cd, 
       null accountid, 
       '2' remark 
FROM (
    SELECT /*+ hash_join()*/ MAX(a.TICKET_ID) ticket_id, 
           a.ACCT_MONTH, 
           a.EC_CODE, 
           a.PROD_ORDER_MODE, 
           a.OFFER_CODE, 
           a.PRODUCT_CODE, 
           a.OFFER_ORDER_ID, 
           a.PRODUCT_ORDER_ID, 
           round(decode(a.rate_back_id, '1', nvl(f.cdr_rate, 1), a.bill_charge) * 10 * count(a.cerca_mark) * 0.3 * d.incentivecoefficient * c.evaluationscore / 10) fee, 
           a.charge_code, 
           d.terminalvendor 
    FROM stludr.UR_MAAPMMA_#inMonth#_T a, 
         (SELECT x.terminalvendor, x.evaluationscore 
          FROM stlusers.MAAP_TERMINAL_INFO x, 
               (SELECT max(createdate) createdate, terminalvendor 
                FROM stlusers.MAAP_TERMINAL_INFO 
                WHERE '#inMonth#' BETWEEN startdate AND enddate 
                  AND (incentivecoefficient IS NULL OR incentivecoefficient = '') 
                GROUP BY terminalvendor) y 
          WHERE x.createdate = y.createdate 
            AND x.terminalvendor = y.terminalvendor 
            AND (x.incentivecoefficient IS NULL OR x.incentivecoefficient = '') 
            AND '#inMonth#' BETWEEN x.startdate AND x.enddate) c, 
         (SELECT x.terminalvendor, x.incentivecoefficient 
          FROM stlusers.MAAP_TERMINAL_INFO x, 
               (SELECT max(createdate) createdate, terminalvendor 
                FROM stlusers.MAAP_TERMINAL_INFO 
                WHERE '#inMonth#' BETWEEN startdate AND enddate 
                  AND (evaluationscore IS NULL OR evaluationscore = '') 
                GROUP BY terminalvendor) y 
          WHERE x.createdate = y.createdate 
            AND x.terminalvendor = y.terminalvendor 
            AND (x.evaluationscore IS NULL OR x.evaluationscore = '') 
            AND '#inMonth#' BETWEEN x.startdate AND x.enddate) d, 
         stludr.MAAPMMA_VENDOR_CONF e, 
         (SELECT * 
          FROM stludr.BIZ_MSG_MMM_STL 
          WHERE acct_month = '#inMonth#' 
            AND CHARGE_ITEM = 'Multimedia_Fee' 
            AND bill_Type = '1') f 
    WHERE a.offer_code = '50034' 
      AND a.product_code = '5003401' 
      AND a.cerca_mark = '1' 
      AND a.manufacturer_code = e.terminal_brand 
      AND e.terminal_vendor = c.terminalvendor 
      AND e.terminal_vendor = d.terminalvendor 
      AND a.product_order_id = f.subscriber_id(+) 
    GROUP BY ACCT_MONTH, 
             EC_CODE, 
             a.PROD_ORDER_MODE, 
             OFFER_CODE, 
             a.PRODUCT_CODE, 
             OFFER_ORDER_ID, 
             PRODUCT_ORDER_ID, 
             rate_back_id, 
             f.cdr_rate, 
             bill_charge, 
             charge_code, 
             cerca_mark, 
             d.incentivecoefficient, 
             c.evaluationscore, 
             d.terminalvendor
) t, 
stlusers.STL_CHARGE_ITEM_DEF c 
WHERE t.charge_code = c.charge_item;
