/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：结算前数据准备-特定业务专属报表-云MAS各异网服务商家数和用量占比计算
 1.流量统付（个人流量包）部分
 2.云MAS部分
**/
use stludr;
DROP PROCEDURE IF EXISTS stludr.`STL_SYNC_BL_SETTLE_MAS`;
DELIMITER ;;
CREATE OR REPLACE DEFINER="stludr"@"10.%" PROCEDURE stludr.`STL_SYNC_BL_SETTLE_MAS`(
    inMonth          IN   VARCHAR2,
    inBatch          IN   VARCHAR2,
    flag_version     IN   VARCHAR2,
    reserve1         IN   VARCHAR2,
    reserve2         IN   VARCHAR2,
    proc_out         OUT  VARCHAR2,
    outSysError      OUT  VARCHAR2,
    outReturn        OUT  NUMBER,
    outBL            OUT  VARCHAR2,
    outAR            OUT  VARCHAR2
)
AS

    v_proc_name       VARCHAR2(30) := 'STL_SYNC_BL_SETTLE_MAS';
    iv_Sql_Insert    VARCHAR2(3072);
    iv_Sql_Insert1   VARCHAR2(1024);
    iv_Sql_Insert2   VARCHAR2(2048);
    iv_Sql_Insert3   VARCHAR2(2048);
    iv_Sql_Update    VARCHAR2(1024);
    P_ERRCODE   VARCHAR2(32);
    P_ERRMSG    VARCHAR2(2048);

cursor maapmma_1587 is select subscriber_id, mmm_count,bill_type,cdr_rate from BIZ_MSG_MMM_STL where acct_month = inMonth and SERVICE_CODE ='5003401' and CHARGE_ITEM='Multimedia_Fee' order by SUBSCRIBER_ID ,bill_Type,cdr_rate;

inSoid           number(15);
inCount          integer;
billType         integer;
cdrRate           integer;
BEGIN

    outSysError := '';
    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
GET DIAGNOSTICS CONDITION 1 P_ERRCODE = RETURNED_SQLSTATE, P_ERRMSG = MESSAGE_TEXT;
outSysError := substr(P_ERRMSG, 1, 1000);
	    	outReturn  := -1;
ROLLBACK;
select ('exception: ' || outReturn || '|' || '|' || outSysError ) AS error_msg ;
call STLUDR.STL_ERROR_LOG(inMonth,P_ERRCODE,outSysError,'',v_proc_name,'');
SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = outSysError;
END;

BEGIN
    if ( length(inMonth) < 6 )  then
SELECT 'inMonth length less than 6. inMonth=[' || inMonth || ']' FROM dual;
outReturn := -1;
ROLLBACK;
RETURN;
end if;

SELECT 'inMonth=' ||  inMonth;
SET @P_TIME := SYSDATE;


--清空当月话单表
SET @vSql := 'truncate table SYNC_BL_SETTLE_' || inMonth;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

SET @vSql := 'truncate table SYNC_BL_RULE_' || inMonth;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

SET @vSql := 'delete from STLUDR.CHECK_CDR_LOG where P_NAME='''|| v_proc_name ||''' and SETTLEMONTH= '|| inMonth;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

SET @vSql := 'delete from stludr.stl_5g_terminal_source where acct_month= '|| inMonth;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

SET @vSql := 'delete from stludr.maapnma_terminal_prod_total where acct_month= '|| inMonth;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

SET @vSql := 'delete from stludr.maapnma_terminal_prod_fee_rule where acct_month= '|| inMonth;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

SET @vSql := 'delete from stludr.maapnma_terminal_prod_fee_rule_swap';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;



call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_00', '数据清理完成开始执行开始时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));
--流量统付（个人流量包）部分
--step 1 统计SYNC_INTERFACE_BL_<month>的数据
SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_RULE_SWAP ' ||
               'SELECT null,''BL'' datasource, ' ||
                       'ordermode, ' ||
                       'orgmonth, ' ||
                       'customernumber, ' ||
                       'pospecnumber, ' ||
                       'sospecnumber, ' ||
                       'poid, ' ||
                       'soid, ' ||
                       'dn, ' ||
                       'sum(amount) amount, ' ||
                       '''0'' amount_type, ' ||
                       'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime, ' ||
                       '''0'' status, ' ||
                       'NULL errmesg, ' ||
                       'remark, ' ||
                       'NULL feetype ' ||
                  'FROM (SELECT a.ORDERMODE, ' ||
                               'a.ORGMONTH, ' ||
                               'a.CUSTOMERNUMBER, ' ||
                               'a.POSPECNUMBER, ' ||
                               'a.SOSPECNUMBER, ' ||
                               'a.POID, ' ||
                               'a.SOID, ' ||
                               'a.NOTAXFEE amount, ' ||
                               'a.REMARK, substr(a.DN, 1, 7) DN ' ||
                          'FROM SYNC_INTERFACE_BL_' || inMonth || ' a ' ||
                         'WHERE a.DN IS NOT NULL ' ||
                           'AND a.SOSPECNUMBER IN (SELECT PRODUCT_CODE FROM stlusers.STL_BUSINESS_TYPE ' ||
                                                   'WHERE BIZ_TYPE = ''GPRS'') ' ||
                           'AND a.STATUS = ''0'' ' ||
                           'AND a.FEETYPE IN (''18'', ''04'')) ' ||
                 'GROUP BY ordermode, orgmonth, customernumber, pospecnumber, sospecnumber, ' ||
                          'poid, soid, remark, dn';
SELECT @iv_Sql_Insert;
PREPARE STMT FROM @iv_Sql_Insert;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_01', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

--step 2 生成最终表的数据
SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_RULE_' || inMonth || ' ' ||
               'SELECT null,''BL'' datasource, ' ||
                       'ordermode, ' ||
                       'orgmonth, ' ||
                       'customernumber, ' ||
                       'pospecnumber, ' ||
                       'sospecnumber, ' ||
                       'poid, ' ||
                       'soid, ' ||
                       'NULL dn, ' ||
                       'amount, ' ||
                       '''0'' amount_type, ' ||
                       'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime, ' ||
                       '''0'' status, ' ||
                       'NULL errmesg, ' ||
                       'prov_cd, ' ||
                       'remark, ' ||
                       'NULL feetype ' ||
                  'FROM ( SELECT a.ORDERMODE, ' ||
                               'a.ORGMONTH, ' ||
                               'a.CUSTOMERNUMBER, ' ||
                               'a.POSPECNUMBER, ' ||
                               'a.SOSPECNUMBER, ' ||
                               'a.POID, ' ||
                               'a.SOID, ' ||
                               'a.amount, ' ||
                               'd.PROV_CD, ' ||
                               'a.REMARK ' ||
                          'FROM SYNC_BL_RULE_SWAP a, ' ||
                               'stlusers.STL_IMSI_LD_CD c, ' ||
                               'stlusers.STL_DOM_LD_AREA_CD_PROV d ' ||
                         'WHERE a.DN IS NOT NULL ' ||
                           'AND c.LD_AREA_CD = d.LD_AREA_CD ' ||
                           'AND C.MSISDN_AREA_ID = a.DN ' ||
                           'AND to_date(' || inMonth || ', ''YYYYMM'') BETWEEN trunc(c.EFF_TM, ''MM'') AND c.EXP_TM )';
SELECT @iv_Sql_Insert;
PREPARE STMT FROM @iv_Sql_Insert;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_02', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));


--云MAS部分

----更新 OTH_SP字段的空值
SET @iv_Sql_Insert := 'truncate table cmas_swap_01';
SELECT @iv_Sql_Insert;
PREPARE STMT FROM @iv_Sql_Insert;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

SET @iv_Sql_Insert := 'truncate table cmas_swap_02';
SELECT @iv_Sql_Insert;
PREPARE STMT FROM @iv_Sql_Insert;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


--先对ur_cmas_202311_t group by，再做offer_mode和product_code进行过滤。因为分组字段中包含这两列，所以先分组再过滤是等价的。
truncate table cmas_mid1;

--修改round(SUM)组合的使用，升级导致xplan不支持round函数下推，对此处进行拆分，插入时cmas_mid1，cmas_mid2中间表只是用sum()计算，
--修改中间表cmas_mid1，cmas_mid2中 fee字段类型，由decial(12,0) 改为 decimal(16,4)

SET @iv_Sql_Insert := 'INSERT INTO cmas_mid1 ' ||
             'SELECT null, a.ORDER_MODE, ' ||
                     'a.ACCT_MONTH, ' ||
                     'a.EC_CODE,  ' ||
                     'a.OFFER_CODE,  ' ||
                     'a.PRODUCT_CODE,  ' ||
                     'a.OFFER_ORDER_ID,  ' ||
                     'a.PRODUCT_ORDER_ID,  ' ||
                     'SUM(a.CHARGE2 / 100) fee, ' ||
                     'nvl(oth_sp_orig, ''00'') oth_sp,  ' ||
                     'a.ec_code_prov, ' ||
                     'a.charge_code1 ' ||
                'FROM UR_CMAS_' || inMonth || '_T a  ' ||
                'WHERE a.oth_sp_orig <> ''03'' ' ||
                  'and a.order_mode = ''1'' ' ||
               'GROUP BY ACCT_MONTH, EC_CODE, EC_CODE_PROV, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, ' ||
                     'OFFER_ORDER_ID, PRODUCT_ORDER_ID, OTH_SP_ORIG, charge_code1 ' ;
SELECT @iv_Sql_Insert;
PREPARE STMT FROM @iv_Sql_Insert;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_03', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));
-- 万里bug兜底ur话单入库时decimal字段入成0，正确应该为null 不用20240530，这里cmas_mid1->SYNC_BL_RULE_->cmas_swap_01->cmas_swap_02表，导致最终P_SETTLE_RULE_PROC_REPART_PARAMETER中计算云MAS关联cmas_swap_02表计算少了规则（stlusers.STL_REPART_PARAMETER_T表010101012业务）
update cmas_mid1 set PRODUCT_ORDER_ID=null where PRODUCT_ORDER_ID=0;
commit;


SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_RULE_' || inMonth || ' ' ||
             'SELECT null,''BL'' datasource, ' ||
                     'order_mode ordermode,  ' ||
                     'acct_month orgmonth, ' ||
                     'ec_code customernumber, ' ||
                     'offer_code pospecnumber,  ' ||
                     'product_code sospecnumber, ' ||
                     'offer_order_id poid, ' ||
                     'product_order_id soid, ' ||
                     'NULL dn, ' ||
                     'fee amount,  ' ||
                     '''1'' amount_type, ' ||
                     'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime, ' ||
                     '''0'' status, ' ||
                     'NULL errmesg,  ' ||
                     'decode(oth_sp, ''00'', ''ZQ'', ''01'', ''ZW'', ''02'', ''ZYJC'') prov_cd, ' ||
                     '''2'' remark, ' ||
                     'decode(charge_code1, ''Comm_Fee_3870'', ''3870'', ''1040'') feetype ' ||
                'FROM (SELECT a.ORDER_MODE, ' ||
                     'a.ACCT_MONTH, ' ||
                     'a.EC_CODE,  ' ||
                     'a.OFFER_CODE,  ' ||
                     'a.PRODUCT_CODE,  ' ||
                     'a.OFFER_ORDER_ID,  ' ||
                     'a.PRODUCT_ORDER_ID,  ' ||
                     'round(a.fee,0) fee, ' ||
                     'a.oth_sp,  ' ||
                     'a.ec_code_prov, ' ||
                     'a.charge_code1 ' ||
                'FROM  cmas_mid1 a  ' ||
                'WHERE (a.OFFER_CODE IN (SELECT DISTINCT OFFER_CODE FROM stlusers.STL_BUSINESS_TYPE  ' ||
                                        'WHERE BIZ_TYPE in (''CMAS1'', ''CMAS5'')) or a.product_code = ''110151'')  ' ||
                ' ) GROUP BY order_mode, acct_month, ec_code, ec_code_prov, offer_code, product_code, offer_order_id, product_order_id, ' ||
                      'fee, oth_sp, charge_code1';

SELECT @iv_Sql_Insert;
PREPARE STMT FROM @iv_Sql_Insert;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_04', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));



--先对ur_cmas_202311_t group by，再做offer_mode和product_code进行过滤。因为分组字段中包含这两列，所以先分组再过滤是等价的。
truncate table cmas_mid2;

SET @iv_Sql_Insert := 'INSERT INTO cmas_mid2  ' ||
                         'SELECT MAX(a.TICKET_ID) ticket_id,  '||
                                 'a.ACCT_MONTH, '||
                                 'a.EC_CODE,    '||
                                 'a.ORDER_MODE,  '||
                                 'a.OFFER_CODE,   '||
                                 'a.PRODUCT_CODE, '||
                                 'a.OFFER_ORDER_ID,  '||
                                 'a.PRODUCT_ORDER_ID,  '||
                                 'SUM(a.CHARGE2 / 100) fee,  '||
                                 'nvl(oth_sp_orig, ''00'') oth_sp,  ' ||
                                 'a.charge_code1 '||
                     'FROM UR_CMAS_' || inMonth || '_T a  '||
                       'WHERE  a.oth_sp_orig <> ''03'' ' ||
                  'and a.order_mode in (''3'', ''5'') ' ||
               'GROUP BY ACCT_MONTH, EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, ' ||
                     'OFFER_ORDER_ID, PRODUCT_ORDER_ID, OTH_SP_ORIG, charge_code1 ';

SELECT @iv_Sql_Insert;
PREPARE STMT FROM @iv_Sql_Insert;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_05', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

-- 万里bug兜底ur话单入库时decimal字段入成0，正确应该为null 不用20240530 （为防止万一有影响这里一起修正数据）
update cmas_mid2 set PRODUCT_ORDER_ID=null where PRODUCT_ORDER_ID=0;
commit;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_06', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));


SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_SETTLE_' || inMonth || ' ' ||
                         'SELECT null,ticket_id stream_id,  '||
                         '''BL'' datasource,  '||
                         't.order_mode,  '||
                         't.acct_month orgmonth,   '||
                         't.ec_code customernumber,   '||
                         't.offer_code pospecnumber,    '||
                         't.product_code sospecnumber,  '||
                         't.offer_order_id poid,    '||
                         't.product_order_id soid,  '||
                         'decode(charge_code1, ''Comm_Fee_3870'', ''3870'', ''1040'') feetype,  '||
                         'NULL dn,   '||
                         't.fee amount,   '||
                         '''2'' amount_type,   '||
                         'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime,   '||
                         '''0'' status, '||
                         'NULL errmesg,   '||
                         'decode(oth_sp, ''00'', ''ZQ'', ''01'', ''ZW'', ''02'', ''ZYJC'') prov_cd,    '||
                         'NULL accountid,    '||
                         '''2'' remark     '||
                    'FROM (SELECT a.ticket_id,  '||
                                 'a.ACCT_MONTH, '||
                                 'a.EC_CODE,    '||
                                 'a.ORDER_MODE,  '||
                                 'a.OFFER_CODE,   '||
                                 'a.PRODUCT_CODE, '||
                                 'a.OFFER_ORDER_ID,  '||
                                 'a.PRODUCT_ORDER_ID,  '||
                                 'round(a.fee,0) fee,  '||
                                 'a.oth_sp,  ' ||
                                 'a.charge_code1 '||
                     'FROM cmas_mid2 a  '||
                       'WHERE (a.OFFER_CODE IN (SELECT DISTINCT OFFER_CODE FROM stlusers.STL_BUSINESS_TYPE  ' ||
                                        'WHERE BIZ_TYPE in (''CMAS1'', ''CMAS5'')) or a.product_code = ''110151'')) t ';

SELECT @iv_Sql_Insert;
PREPARE STMT FROM @iv_Sql_Insert;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_07', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));



----话单汇总数据关联计费出账数据，写入中间表
SET @iv_Sql_Insert := 'insert into cmas_swap_01(offer_order_id, product_order_id, notaxfee, half_notaxfee, oth_sp, oth_fee, ' ||
                       ' pospecnumber, sospecnumber, ordermode, orgmonth, charge_code) ' ||
      'select a.poid, a.soid, nvl(b.notaxfee, 0) notaxfee, nvl(floor(b.notaxfee / 2), 0) half_notaxfee, a.prov_cd oth_sp, a.amount, ' ||
             'a.pospecnumber, a.sospecnumber, a.ordermode, a.orgmonth, a.feetype ' ||
        'from (select t.poid, t.soid, sum(t.amount) amount, t.prov_cd, t.pospecnumber, t.sospecnumber, t.ordermode, t.orgmonth, t.feetype ' ||
                'from sync_bl_rule_' || inMonth || ' t ' ||
               'where (t.pospecnumber in (select distinct offer_code ' ||
                                           'from stlusers.stl_business_type ' ||
                                          'where biz_type = ''CMAS1'') ' ||
                  'or t.sospecnumber = ''110151'') ' ||
                 'and t.ordermode = ''1'' ' ||
                 'and t.soid is null ' ||
               'group by t.orgmonth, t.ordermode, t.pospecnumber, t.sospecnumber, t.poid, t.soid, t.prov_cd, t.feetype) a ' ||
        'left join (select t.poid, t.soid, sum(t.notaxfee) notaxfee ' ||
                     'from sync_interface_bl_' || inMonth || ' t ' ||
                    'where t.ordermode = ''1'' ' ||
                      'and t.soid is null ' ||
                      'and (t.pospecnumber in (select distinct offer_code ' ||
                                               'from stlusers.stl_business_type ' ||
                                              'where biz_type = ''CMAS1'') ' ||
                       'or t.sospecnumber = ''110151'') ' ||
                    'group by t.poid, t.soid) b ' ||
          'on a.poid = b.poid and a.soid is null and b.soid is null ' ||
       'union all ' ||
      'select a.poid, a.soid, b.notaxfee, floor(b.notaxfee / 2) half_notaxfee, a.prov_cd oth_sp, a.amount, ' ||
             'a.pospecnumber, a.sospecnumber, a.ordermode, a.orgmonth, a.feetype ' ||
        'from (select t.poid, t.soid, sum(t.amount) amount, t.prov_cd, t.pospecnumber, t.sospecnumber, t.ordermode, t.orgmonth, t.feetype ' ||
                'from sync_bl_rule_' || inMonth || ' t ' ||
               'where (t.pospecnumber in (select distinct offer_code ' ||
                                           'from stlusers.stl_business_type ' ||
                                          'where biz_type = ''CMAS1'') ' ||
                  'or t.sospecnumber = ''110151'') ' ||
                 'and t.ordermode = ''1'' ' ||
                 'and t.soid is not null ' ||
               'group by t.orgmonth, t.ordermode, t.pospecnumber, t.sospecnumber, t.poid, t.soid, t.prov_cd, t.feetype) a ' ||
        'left join (select t.poid, t.soid, sum(t.notaxfee) notaxfee ' ||
                     'from sync_interface_bl_' || inMonth || ' t ' ||
                    'where t.ordermode = ''1'' ' ||
                      'and t.soid is not null ' ||
                      'and (t.pospecnumber in (select distinct offer_code ' ||
                                               'from stlusers.stl_business_type ' ||
                                              'where biz_type = ''CMAS1'') ' ||
                       'or t.sospecnumber = ''110151'') ' ||
                    'group by t.poid, t.soid) b ' ||
          'on a.poid = b.poid and a.soid = b.soid';

SELECT @iv_Sql_Insert;
PREPARE STMT FROM @iv_Sql_Insert;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_08', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

----计算异网服务商数量和异网结算总金额
merge into cmas_swap_01 a
    using (select offer_order_id, product_order_id, sum(oth_fee) oth_total_fee, count(*) sp_count
           from cmas_swap_01 t
           where product_order_id is null
           group by offer_order_id, product_order_id) b
    on (a.offer_order_id = b.offer_order_id)
    when matched then
        update set a.sp_count = b.sp_count, a.oth_total_fee = b.oth_total_fee;
commit;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_09', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

merge into cmas_swap_01 a
    using (select offer_order_id, product_order_id, sum(oth_fee) oth_total_fee, count(*) sp_count
           from cmas_swap_01 t
           where product_order_id is not null
           group by offer_order_id, product_order_id) b
    on (a.offer_order_id = b.offer_order_id and a.product_order_id = b.product_order_id)
    when matched then
        update set a.sp_count = b.sp_count, a.oth_total_fee = b.oth_total_fee;
commit;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_10', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

----标异网服务商数为2且异网大于出账费用的
update cmas_swap_01
set flag = 1
where sp_count >= 2
  and oth_total_fee > notaxfee;
commit;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_11', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

----符合要求的话单汇总数据写入中间表2
insert into CMAS_SWAP_02(offer_order_id, product_order_id, notaxfee, half_notaxfee, oth_sp, oth_fee, sp_count, oth_total_fee,
                         pospecnumber, sospecnumber, ordermode, orgmonth, rate, charge_code)
select offer_order_id, product_order_id, notaxfee, half_notaxfee, oth_sp, oth_fee, sp_count, oth_total_fee,
       pospecnumber, sospecnumber, ordermode, orgmonth, round(oth_fee / oth_total_fee, 27) rate, charge_code
from CMAS_SWAP_01
where flag = '1';

commit;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_12', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

----更新sync_bl_rule_YYYYMM，存在于cmas_swap_02中的数据，状态更新为2
SET @iv_Sql_Update := 'merge into sync_bl_rule_' || inMonth || ' a ' ||
          'using (select distinct offer_order_id ' ||
                  'from cmas_swap_02 t ' ||
                 'where product_order_id is null) b ' ||
            'on (a.poid = b.offer_order_id) ' ||
          'when matched then ' ||
          'update set a.status = ''2'' ' ||
          'where a.soid is null';

SELECT @iv_Sql_Update;
PREPARE STMT FROM @iv_Sql_Update;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_13', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

set @iv_Sql_Update := 'merge into sync_bl_rule_' || inMonth || ' a ' ||
      'using (select distinct offer_order_id, product_order_id ' ||
              'from cmas_swap_02 t ' ||
             'where product_order_id is not null) b ' ||
        'on (a.poid = b.offer_order_id and a.soid = b.product_order_id) ' ||
      'when matched then ' ||
      'update set a.status = ''2'' ' ||
      'where a.soid is not null';

SELECT @iv_Sql_Update;
PREPARE STMT FROM @iv_Sql_Update;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_14', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

commit;

--终端分成-叠加包更新
begin
open maapmma_1587;
loop
fetch maapmma_1587 into inSoid, inCount,billType,cdrRate;
            exit when maapmma_1587%NOTFOUND;

            IF (billType = 1) THEN
            -- 随机取订购中的部分话单归入叠加包，rate_back_id更新为1，万里优化ticket_id做索引，跟晓菲确认过ticket_id是唯一的，

            execute immediate 'update /*+ AUTOCOMMIT_DURING_DML() */ ur_maapmma_' || inMonth || '_t as t ' ||
                              'set t.rate_back_id =  case  WHEN ' || cdrRate || '  = 50 THEN  ''2'' else  ''1'' end where t.product_order_id = ' || inSoid || '  and  t.rate_back_id is null order by t.dup_time limit ' || inCount ;
ELSE
               execute immediate 'update /*+ AUTOCOMMIT_DURING_DML() */ ur_maapmma_' || inMonth || '_t as t ' ||
                                  'set t.bill_charge = 0,t.charge=0 where t.product_order_id = ' || inSoid || ' and   t.rate_back_id is null limit ' || inCount;
END IF;
end loop;
close maapmma_1587;
commit;
end;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_15', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));


--终端分成-互联网公司
--step 0 临时表数据清理
SET @iv_Sql_Insert := 'truncate table sync_bl_rule_mma_01';
SELECT @iv_Sql_Insert;
PREPARE STMT FROM @iv_Sql_Insert;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

--step 1 临时表数据插入
SET @iv_Sql_Insert := 'insert into sync_bl_rule_mma_01 ' ||
                ' SELECT MAX(a.TICKET_ID) ticket_id, a.ACCT_MONTH, a.EC_CODE,  a.PROD_ORDER_MODE, a.OFFER_CODE,  a.PRODUCT_CODE,  a.OFFER_ORDER_ID,  a.PRODUCT_ORDER_ID, ' ||
                ' round(SUM(a.CHARGE/10)) fee, a.CHARGE_CODE,null  ' ||
                ' FROM UR_MAAPMMA_' || inMonth || '_T a  where a.offer_code = ''50034'' and a.product_code =''5003401''  ' ||
                ' GROUP BY ACCT_MONTH, EC_CODE, PROD_ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID,charge_code  ';
SELECT @iv_Sql_Insert;
PREPARE STMT FROM @iv_Sql_Insert;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_15_1', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

SET @iv_Sql_Insert := 'INSERT /*+ AUTOCOMMIT_DURING_DML() */ INTO SYNC_BL_SETTLE_MID ' ||
            'SELECT NULL,ticket_id stream_id,  '||
           '''BL'' datasource,    '||
           't.prod_order_mode ordermode,   '||
           't.acct_month orgmonth,    '||
           't.ec_code customernumber,    '||
           't.offer_code pospecnumber,    '||
           't.product_code sospecnumber,    '||
           't.offer_order_id poid,   '||
           't.product_order_id soid,   '||
           'decode(c.charge_item_ref,''1587'',''587'',c.charge_item_ref) feetype,   '||
           'NULL dn,   '||
           't.fee amount,    '||
           '''2'' amount_type,   '||
           'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime,   '||
           '''0'' status,  '||
           'NULL errmesg,    '||
           'NULL prov_cd,    '||
           'NULL accountid,   '||
           '''2'' remark      '||
            'FROM sync_bl_rule_mma_01 t, stlusers.STL_CHARGE_ITEM_DEF c WHERE t.charge_code = c.charge_item ';
SELECT @iv_Sql_Insert;
PREPARE STMT FROM @iv_Sql_Insert;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_15_2', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));


insert into stludr.stl_5g_terminal_source(acct_month, terminalvendor, evaluationscore, incentivecoefficient, terminal_brand, terminal_name)
SELECT inMonth,b.TERMINALVENDOR,b.EVALUATIONSCORE,b.INCENTIVECOEFFICIENT,a.TERMINAL_BRAND,a.TERMINAL_NAME from stludr.MAAPMMA_VENDOR_CONF a JOIN (
    SELECT
        TERMINALVENDOR,
        MAX(EVALUATIONSCORE) as EVALUATIONSCORE,
        MAX(INCENTIVECOEFFICIENT) as INCENTIVECOEFFICIENT
    FROM ( -- 获取最新的评估系数
             SELECT
                 TERMINALVENDOR,
                 EVALUATIONSCORE,
                 NULL as INCENTIVECOEFFICIENT
             FROM (
                      SELECT
                          TERMINALVENDOR,
                          EVALUATIONSCORE,
                          ROW_NUMBER() OVER(PARTITION BY TERMINALVENDOR ORDER BY CREATEDATE DESC) as rn
                      FROM stlusers.maap_terminal_info
                      WHERE (INCENTIVECOEFFICIENT IS NULL OR INCENTIVECOEFFICIENT = '')
                        AND EVALUATIONSCORE IS NOT NULL
                        AND EVALUATIONSCORE != ''
          AND inMonth BETWEEN STARTDATE AND ENDDATE
                  ) eval_ranked WHERE rn = 1
             UNION ALL
             -- 获取最新的激励系数
             SELECT
                 TERMINALVENDOR,
                 NULL as EVALUATIONSCORE,
                 INCENTIVECOEFFICIENT
             FROM (
                      SELECT
                          TERMINALVENDOR,
                          INCENTIVECOEFFICIENT,
                          ROW_NUMBER() OVER(PARTITION BY TERMINALVENDOR ORDER BY CREATEDATE DESC) as rn
                      FROM stlusers.maap_terminal_info
                      WHERE (EVALUATIONSCORE IS NULL OR EVALUATIONSCORE = '')
                        AND INCENTIVECOEFFICIENT IS NOT NULL
                        AND INCENTIVECOEFFICIENT != ''
          AND inMonth BETWEEN STARTDATE AND ENDDATE
                  ) incent_ranked WHERE rn = 1
         ) combined
    GROUP BY TERMINALVENDOR
    ORDER BY TERMINALVENDOR ) b on b.TERMINALVENDOR = a.terminal_vendor;

call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_15_3', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));
COMMIT;

SET @iv_Sql_Insert = CONCAT('
    INSERT INTO stludr.maapnma_terminal_prod_total
    (acct_month, ticket_id, ec_code, ec_prov, order_mode, offer_code, product_code,
     offer_order_id, product_order_id, rate_back_id, bill_charge, charge_code,
     manufacturer_code, IS_HEAD, total_count)
    SELECT /*+ PARALLEL(a, 8) */
        ''', inMonth, ''',
        MAX(a.TICKET_ID) AS ticket_id,
        a.EC_CODE,
        a.ec_code_prov,
        a.PROD_ORDER_MODE,
        a.OFFER_CODE,
        a.PRODUCT_CODE,
        a.OFFER_ORDER_ID,
        a.PRODUCT_ORDER_ID,
        a.rate_back_id,
        a.bill_charge,
        a.charge_code,
        a.manufacturer_code,
        a.IS_HEAD,
        COUNT(*) total_count
    FROM
        stludr.UR_MAAPMMA_', inMonth, '_T a
    WHERE
        a.offer_code = ''50034''
        AND a.product_code = ''5003401''
        AND a.cerca_mark = ''1''
    GROUP BY
        a.EC_CODE,
        a.ec_code_prov,
        a.PROD_ORDER_MODE,
        a.OFFER_CODE,
        a.PRODUCT_CODE,
        a.OFFER_ORDER_ID,
        a.PRODUCT_ORDER_ID,
        a.rate_back_id,
        a.bill_charge,
        a.charge_code,
        a.manufacturer_code,
        a.IS_HEAD,
        a.cerca_mark');

SELECT @iv_Sql_Insert;
PREPARE STMT FROM @iv_Sql_Insert;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_15_4', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));
COMMIT;

update stludr.maapnma_terminal_prod_total a
    inner join stlusers.STL_CHARGE_ITEM_DEF b on a.charge_code = b.charge_item
    set a.charge_item = decode(b.charge_item_ref, '1587', '157', b.charge_item_ref)
where a.acct_month = inMonth;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_15_5', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));
COMMIT;


update stludr.maapnma_terminal_prod_total a
    inner join stludr.stl_5g_terminal_source b on a.manufacturer_code = b.TERMINAL_BRAND
    -- rate_back_id为1的，视为叠加包内话单，单价更新为54厘
    set a.settle_fee = round(decode(a.rate_back_id, '1', 54, a.bill_charge) * 10 * total_count * 0.3 * b.incentivecoefficient * b.evaluationscore / 10),
        a.bg_fee= round(decode(a.rate_back_id, '1', 54, a.bill_charge)* total_count),
        a.terminalvendor = b.TERMINALVENDOR
where a.acct_month = inMonth and b.acct_month=inMonth;

call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_15_6', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));
COMMIT;


merge into stludr.maapnma_terminal_prod_total a
    using (
        --评估系数为0的
        select
            evaluationscore,
            incentivecoefficient,
            terminalvendor
        from stludr.stl_5g_terminal_source
        where acct_month = inMonth and terminalvendor in ('1', '4', '5') and evaluationscore = '0'
        group by evaluationscore,
                 incentivecoefficient,
                 terminalvendor
    ) b
    on (a.terminalvendor = b.terminalvendor)
    when matched then
        update set a.settle_fee = 0 ,a.total_count = 0,a.bg_fee=0
    where a.acct_month = inMonth and a.IS_HEAD in ('1') and a.rate_back_id='2' ;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_15_7', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));
COMMIT;

merge into stludr.maapnma_terminal_prod_total a
    using (
        select
            evaluationscore,
            incentivecoefficient,
            terminalvendor
        from stludr.stl_5g_terminal_source
        where acct_month = inMonth and terminalvendor in ('1', '4', '5') and evaluationscore != '0'
        group by evaluationscore,
            incentivecoefficient,
            terminalvendor
    ) c
    on (a.terminalvendor = c.terminalvendor)
    when matched then
        update set a.settle_fee = round(50 * 10 * a.total_count * 0.3 * c.incentivecoefficient * c.evaluationscore / 10),
            a.bg_fee= round(50 * a.total_count)
    where a.acct_month = inMonth and a.IS_HEAD in ('1') and a.rate_back_id='2';
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_15_8', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));
COMMIT;

-- 取出终端类型为6的金额，分别俩个规则计算
INSERT INTO stludr.maapnma_terminal_prod_fee_rule
(id,acct_month,product_order_id,total_count,terminalvendor,evaluationscore,incentivecoefficient,rule_1_fee,rule_2_fee,final_fee)
SELECT DISTINCT
    a.id,
    a.acct_month,
    a.product_order_id,
    a.total_count,
    a.terminalvendor,
    b.EVALUATIONSCORE,
    b.INCENTIVECOEFFICIENT,
    a.settle_fee as rule_1_fee,
    CASE WHEN a.settle_fee = 0 and a.rate_back_id is null THEN 0 ELSE round(a.total_count * 20) END AS rule_2_fee,
    null
FROM
    stludr.maapnma_terminal_prod_total a
        JOIN stludr.stl_5g_terminal_source b ON lower( a.manufacturer_code ) = lower( b.terminal_brand )
WHERE
    b.TERMINALVENDOR in ('2','6','8','15')
  AND a.ACCT_MONTH = inMonth
  AND b.ACCT_MONTH = inMonth;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_15_9', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));
COMMIT;


insert into stludr.maapnma_terminal_prod_fee_rule_swap
(product_order_id,terminalvendor,fee1_sum,fee2_sum,fee_rule)
select
    product_order_id,
    terminalvendor,
    sum(rule_1_fee) as fee1_sum,
    sum(rule_2_fee) as fee2_sum,
    case
        when sum(rule_1_fee) > sum(rule_2_fee) then 1
        else 2
        end as fee_rule
from stludr.maapnma_terminal_prod_fee_rule
where acct_month = inMonth
group by product_order_id,terminalvendor;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_15_10', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

COMMIT;

update stludr.maapnma_terminal_prod_fee_rule a
    inner join stludr.maapnma_terminal_prod_fee_rule_swap b on a.product_order_id = b.product_order_id and a.terminalvendor = b.terminalvendor
    set a.final_fee = case
        when b.fee_rule = 1 then a.rule_1_fee
        else a.rule_2_fee
end
where a.acct_month = inMonth;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_15_11', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

COMMIT;

-- 取金额大的
update stludr.maapnma_terminal_prod_total a
    inner join stludr.maapnma_terminal_prod_fee_rule b on a.product_order_id = b.product_order_id and a.id=b.id
    set a.settle_fee = b.final_fee,
        a.bg_fee = CASE
        WHEN b.final_fee = b.rule_2_fee THEN b.final_fee
        ELSE a.bg_fee
END
where a.acct_month = inMonth;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_15_12', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));
COMMIT;

INSERT /*+ AUTOCOMMIT_DURING_DML() */ INTO SYNC_BL_SETTLE_MID
SELECT NULL, ticket_id stream_id,
       'BL' datasource,
       t.order_mode ordermode,
       t.acct_month orgmonth,
       t.ec_code customernumber,
       t.offer_code pospecnumber,
       t.product_code sospecnumber,
       t.offer_order_id poid,
       t.product_order_id soid,
       t.charge_item feetype,
       '' dn,
       t.settle_fee amount,
       '2' amount_type,
       to_char(SYSDATE, 'yyyymmddhh24miss') synctime,
       '0' status,
       '' errmesg,
       t.terminalvendor prov_cd,
       null accountid,
       '2' remark
FROM maapnma_terminal_prod_total t
WHERE acct_month = inMonth;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_15_13', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));
COMMIT;

outSysError := 'OK';
outReturn := 0;

SET @vSql := 'INSERT INTO STLUDR.CHECK_CDR_LOG(SETTLEMONTH, P_NAME, MODUL, TOTAL) ' ||
                 'SELECT ' || QUOTE(inMonth) || ', ' || QUOTE(v_proc_name) || ', ' ||
                  CONCAT('''SYNC_BL_RULE_', inMonth, '表入库条数：''') || ', COUNT(*) ' ||
                 'FROM SYNC_BL_RULE_' || inMonth;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
commit;

SET @vSql := 'INSERT INTO STLUDR.CHECK_CDR_LOG(SETTLEMONTH, P_NAME, MODUL, TOTAL) ' ||
                 'SELECT ' || QUOTE(inMonth) || ', ' || QUOTE(v_proc_name) || ', ' ||
                  CONCAT('''SYNC_BL_SETTLE_', inMonth, '表入库条数：''') || ', COUNT(*) ' ||
                 'FROM SYNC_BL_SETTLE_' || inMonth;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
commit;

call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_99', '执行完成总耗时秒='||TIMESTAMPDIFF(SECOND, @P_TIME, NOW()));

SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
END;
END ;;
DELIMITER ;
