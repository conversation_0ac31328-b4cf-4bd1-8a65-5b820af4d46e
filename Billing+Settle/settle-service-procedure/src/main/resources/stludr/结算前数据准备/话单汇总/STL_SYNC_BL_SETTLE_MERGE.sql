/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：结算前数据准备-话单汇总-话单汇总中间表数据写入话单汇总表
 1.将 STL_SYNC_BL_SETTLE_MAS和STL_SYNC_BL_SETTLE_OTHER 两个存储过程的结果进行合并处理，即将当月话单中间表导入到当月话单汇总表进行合并处理
**/
use stludr;
DROP PROCEDURE IF EXISTS stludr.`STL_SYNC_BL_SETTLE_MERGE`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE stludr.`STL_SYNC_BL_SETTLE_MERGE`(
    inMonth          IN   VARCHAR2,
    inBatch          IN   VARCHAR2,
    flag_version     IN   VARCHAR2,
    reserve1         IN   VARCHAR2,
    reserve2         IN   VARCHAR2,
    proc_out         OUT  VARCHAR2,
    outSysError      OUT  VARCHAR2,
    outReturn        OUT  NUMBER,
    outBL            OUT  VARCHAR2,
    outAR            OUT  VARCHAR2
)
AS

    v_proc_name       VARCHAR2(30) := 'STL_SYNC_BL_SETTLE_MERGE';
    iv_Sql_Insert    VARCHAR2(3072);
    P_ERRCODE   VARCHAR2(32);
    P_ERRMSG    VARCHAR2(2048);


BEGIN

    outSysError := '';
    outReturn := 0;

	DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
GET DIAGNOSTICS CONDITION 1 P_ERRCODE = RETURNED_SQLSTATE, P_ERRMSG = MESSAGE_TEXT;
outSysError := substr(P_ERRMSG, 1, 1000);
	    	outReturn  := -1;
ROLLBACK;
select ('exception: ' || outReturn || '|' || '|' || outSysError ) AS error_msg ;
call STLUDR.STL_ERROR_LOG(inMonth,P_ERRCODE,outSysError,'',v_proc_name,'');
SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = outSysError;
END;

BEGIN
        if ( length(inMonth) < 6 )  then
SELECT 'inMonth length less than 6. inMonth=[' || inMonth || ']' FROM dual;
outReturn := -1;
ROLLBACK;
RETURN;
end if;

SELECT 'inMonth=' ||  inMonth FROM dual;
SET @P_TIME := SYSDATE;

SET @vSql := 'delete from STLUDR.CHECK_CDR_LOG where P_NAME='''|| v_proc_name ||''' and SETTLEMONTH= '|| inMonth;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

		--将当月话单中间表   导入到当月话单表        SYNC_BL_SETTLE_
		SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_SETTLE_' || inMonth || ' ' ||
		                '(STREAM_ID,DATASOURCE,ORDERMODE,ORGMONTH,CUSTOMERNUMBER,POSPECNUMBER,SOSPECNUMBER,POID,SOID,FEETYPE,DN,AMOUNT,AMOUNT_TYPE,SYNCTIME,STATUS,ERRMESG,PROV_CD,ACCOUNTID,REMARK) ' ||
                       'SELECT a.STREAM_ID,a.DATASOURCE,a.ORDERMODE,a.ORGMONTH,a.CUSTOMERNUMBER,a.POSPECNUMBER,a.SOSPECNUMBER, '||
		               'a.POID, a.SOID,a.FEETYPE,a.DN,a.AMOUNT,a.AMOUNT_TYPE,a.SYNCTIME,a.STATUS,a.ERRMESG,a.PROV_CD,a.ACCOUNTID,a.REMARK ' ||
		               'FROM  SYNC_BL_SETTLE_MID a';
SELECT @iv_Sql_Insert;
PREPARE STMT FROM @iv_Sql_Insert;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

--将当月话单中间表   导入到当月话单表        SYNC_BL_RULE_
SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_RULE_' || inMonth || ' ' ||
                       '(DATASOURCE,ORDERMODE,ORGMONTH,CUSTOMERNUMBER,POSPECNUMBER,SOSPECNUMBER,POID,SOID,DN,AMOUNT,AMOUNT_TYPE,SYNCTIME,STATUS,ERRMESG,PROV_CD,REMARK,FEETYPE) ' ||
                       'SELECT a.DATASOURCE ,a.ORDERMODE,a.ORGMONTH,a.CUSTOMERNUMBER,a.POSPECNUMBER,a.SOSPECNUMBER,a.POID,a.SOID,' ||
                       'a.DN,a.AMOUNT,a.AMOUNT_TYPE,a.SYNCTIME,a.STATUS,a.ERRMESG,a.PROV_CD,a.REMARK,a.FEETYPE ' ||
                       'FROM  SYNC_BL_RULE_MID a';
SELECT @iv_Sql_Insert;
PREPARE STMT FROM @iv_Sql_Insert;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

commit;

outSysError := 'OK';
        outReturn := 0;

		SET @vSql := 'INSERT INTO STLUDR.CHECK_CDR_LOG(SETTLEMONTH, P_NAME, MODUL, TOTAL) ' ||
		                 'SELECT ' || QUOTE(inMonth) || ', ' || QUOTE(v_proc_name) || ', ' ||
		                  CONCAT('''SYNC_BL_RULE_', inMonth, '表汇总入库条数：''') || ', COUNT(*) ' ||
		                 'FROM SYNC_BL_RULE_' || inMonth;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
commit;

SET @vSql := 'INSERT INTO STLUDR.CHECK_CDR_LOG(SETTLEMONTH, P_NAME, MODUL, TOTAL) ' ||
		                 'SELECT ' || QUOTE(inMonth) || ', ' || QUOTE(v_proc_name) || ', ' ||
		                  CONCAT('''SYNC_BL_SETTLE_', inMonth, '表汇总入库条数：''') || ', COUNT(*) ' ||
		                 'FROM SYNC_BL_SETTLE_' || inMonth;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
commit;

SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_99', '执行完成总耗时秒='||TIMESTAMPDIFF(SECOND, @P_TIME, NOW()));
END;
END ;;
DELIMITER ;