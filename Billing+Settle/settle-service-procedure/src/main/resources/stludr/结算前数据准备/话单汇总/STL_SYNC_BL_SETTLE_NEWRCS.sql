/*
* 阅信(增值能力)
*【20241225】
*万里优化，将STL_SYNC_BL_SETTLE_MAS 处理ur_newrcs_yyyymm 的逻辑单独迁移到一个存储里面
*将步骤17~19独立出来形成一个新的存储过程，处理ur_newrcs的数据，与原存储过程并行调用。
*新的存储过程调整：
*   1、结算只有3个shard，所以建议分批搞，每次3个分片。31天分成11个批次。循环处理
*   2、acct_month,OFFER_CODE,PRODUCT_CODE明确的字段，直接使用常量替代，不放到分组group by里了。group by的字段越多，耗时越多
*/
use stludr;
DELIMITER ;;
CREATE OR REPLACE DEFINER="stludr"@"10.%" PROCEDURE `stludr`.STL_SYNC_BL_SETTLE_NEWRCS(
    inMonth          IN   VARCHAR2,
    inBatch          IN   VARCHAR2,
    flag_version     IN   VARCHAR2,
    reserve1         IN   VARCHAR2,
    reserve2         IN   VARCHAR2,
    proc_out         OUT  VARCHAR2,
    outSysError      OUT  VARCHAR2,
    outReturn        OUT  NUMBER,
    outBL            OUT  VARCHAR2,
    outAR            OUT  VARCHAR2
)
as
   v_proc_name       VARCHAR2(30) := 'STL_SYNC_BL_SETTLE_NEWRCS';
   P_ERRCODE   VARCHAR2(32);
   P_ERRMSG    VARCHAR2(2048);
cursor newcrs_4210 is select subscriber_id, mmm_count from BIZ_MSG_MMM_STL where acct_month = inMonth and SERVICE_CODE ='5002001';

inSoid           number(15);
inCount          integer;
day_start int;
day_end int;


begin
    SELECT 'inMonth=' ||  inMonth;
    SET @P_TIME := SYSDATE;
    outSysError := '';
    outReturn := 0;


    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 P_ERRCODE = RETURNED_SQLSTATE, P_ERRMSG = MESSAGE_TEXT;
        outSysError := substr(P_ERRMSG, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        -- 异常信息输出
        select ('=== EXCEPTION CAUGHT ===' ) AS error_header ;
        select ('SQLSTATE: ' || P_ERRCODE) AS sql_state ;
        select ('ERROR_MESSAGE: ' || P_ERRMSG) AS error_message ;
        select ('PROC_NAME: ' || v_proc_name) AS proc_name ;
        select ('exception: ' || outReturn || '|' || P_ERRCODE || '|' || outSysError ) AS error_msg ;

        -- 异常日志记录
        call STLUDR.STL_ERROR_LOG(inMonth,P_ERRCODE,outSysError,'',v_proc_name,'');
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = outSysError;
    END;
begin
    begin
        open newcrs_4210;
        loop
        fetch newcrs_4210 into inSoid, inCount;
                            exit when newcrs_4210%NOTFOUND;

        -- 5g阅信增值服务-叠加包更新
        execute immediate 'update /*+ AUTOCOMMIT_DURING_DML() */ ur_newrcs_' || inMonth || '_t as t ' ||

                          'set t.rate_back_id = ''1'' ,t.charge=''24'' ,t.charge_code=''Comm_Fee_4210'' where t.product_order_id = ' || inSoid  || ' limit ' || inCount ;

        end loop;
        close newcrs_4210;
    end;
select 'update ur_newrcs_' || inMonth || '_t';
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_1', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

----阅信(增值能力)
--step 0 临时表数据清理
SET @iv_Sql_Insert := 'truncate table sync_bl_rule_swap_05';
SELECT @iv_Sql_Insert;
PREPARE STMT FROM @iv_Sql_Insert;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_2', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

--step 1 临时表数据插入
for i in 0 .. 10 loop
      day_start:=3*i+1;
      day_end:=3*i+3;

      SET @iv_Sql_Insert := 'insert into sync_bl_rule_swap_05 ' ||
                            ' SELECT MAX(a.TICKET_ID) ticket_id, '||inMonth ||'  ACCT_MONTH, a.EC_CODE,  a.PROD_ORDER_MODE, ''50020'' OFFER_CODE,  ''5002001'' PRODUCT_CODE,  a.OFFER_ORDER_ID,  a.PRODUCT_ORDER_ID, ' ||
                            ' SUM(a.CHARGE/10) fee, a.CHARGE_CODE,null  ' ||
                            ' FROM UR_NEWRCS_' || inMonth || '_T a  where a.offer_code = ''50020'' and a.product_code =''5002001''  ' ||
                            ' and partition_id_day between '|| day_start ||' and '||day_end ||
                            ' GROUP BY  EC_CODE, PROD_ORDER_MODE,  OFFER_ORDER_ID, PRODUCT_ORDER_ID,charge_code  ';
SELECT @iv_Sql_Insert;
PREPARE STMT FROM @iv_Sql_Insert;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
end loop;
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_3', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

SET @iv_Sql_Insert := 'INSERT /*+ AUTOCOMMIT_DURING_DML() */ INTO SYNC_BL_SETTLE_MID ' ||
                          'SELECT null, ticket_id stream_id, '||
                          '''BL'' datasource,  '||
                          't.prod_order_mode ordermode, '||
                          't.acct_month orgmonth,    '||
                          't.ec_code customernumber,  '||
                          't.offer_code pospecnumber,   '||
                          't.product_code sospecnumber,    '||
                          't.offer_order_id poid,        '||
                          't.product_order_id soid,   '||
                          'c.charge_item_ref feetype,     '||
                          'NULL dn,      '||
                          't.fee amount,    '||
                          '''2'' amount_type,  '||
                          'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime,   '||
                          '''0'' status, '||
                          'NULL errmesg,   '||
                          'NULL prov_cd,   '||
                          'NULL accountid,   '||
                          '''2'' remark      '||
                          'FROM (select MAX(TICKET_ID) ticket_id,
                                 acct_month,
                                 ec_code,
                                 PROD_ORDER_MODE,
                                 OFFER_CODE,
                                 PRODUCT_CODE,
                                 OFFER_ORDER_ID,
                                 PRODUCT_ORDER_ID,
                                 round(sum(fee)) fee,
                                 charge_code
                                from sync_bl_rule_swap_05
                                 group by  ACCT_MONTH,
                                  EC_CODE,
                             PROD_ORDER_MODE,
                            OFFER_CODE,
                            PRODUCT_CODE,
                            OFFER_ORDER_ID,
                            PRODUCT_ORDER_ID,
                             charge_code) t, stlusers.STL_CHARGE_ITEM_DEF c WHERE t.charge_code = c.charge_item';
SELECT @iv_Sql_Insert;
PREPARE STMT FROM @iv_Sql_Insert;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outSysError := 'OK';
outReturn := 0;
SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;

call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_99', '执行完成总耗时秒='||TIMESTAMPDIFF(SECOND, @P_TIME, NOW()));
end;
END ;;
DELIMITER ;