**************关基计费库-boss_uom

SELECT max(cast(nodeid as SIGNED)) from `boss_uom`.uom_node_config;

SELECT max(cast(nodeid as SIGNED)) from `boss_uom`.uom_node;

SELECT * from `boss_uom`.uom_node_config where nodename = 'stl_导入话单数据mas-lltf_M';

SELECT * from `boss_uom`.uom_node where nodename = 'stl_导入话单数据mas-lltf_M';


INSERT INTO `boss_uom`.`uom_node_config` (`nodeid`, `nodename`, `createtime`, `tasktype`, `path`, `remarks`, `qname`, `method`, `serverip`, `username`, `auth`, `remotefile`, `localfile`) VALUES ('602', 'stl_导入话单数据newrcs', NOW(), 1, null, 'produce', '', 'STL_SYNC_BL_SETTLE_NEWRCS', 'http://boss-service-rater-special.bboss-1:9107/exec/settle', 'STLUDR', null, '/home/<USER>/omc_mrt/data/business_info/', '/home/<USER>/uom/business/');


INSERT INTO `boss_uom`.`uom_node` (`nodeid`, `nodetype`, `nodename`, `callbackcount`, `createtime`, `describe`) VALUES ('602', 'taskNode', 'stl_导入话单数据newrcs', 2, now(), NULL);



INSERT INTO `boss_uom`.`uom_node_config` (`nodeid`, `nodename`, `createtime`, `tasktype`, `path`, `remarks`, `qname`, `method`, `serverip`, `username`, `auth`, `remotefile`, `localfile`) VALUES ('615', 'stl_二批报表保底上下限计算', NOW(), 1, '', 'produce', '', 'RPT_LIMIT_CALC_UOM', 'http://boss-service-rater-special.bboss-1:9107/exec/settle', 'STLUDR', NULL, '/home/<USER>/omc_mrt/data/business_info/', '/home/<USER>/uom/business/');
INSERT INTO `boss_uom`.`uom_node` (`nodeid`, `nodetype`, `nodename`, `callbackcount`, `createtime`, `describe`) VALUES ('615', 'taskNode', 'stl_二批报表保底上下限计算', 2, now(), NULL);

-- 不要了
--  INSERT INTO `boss_uom`.`uom_task_config` (`nodeid`, `nodename`, `createtime`, `username`, `tbname`, `condition`, `remarks`) VALUES ('528', 'stl_报表批量下载预生产', now(), 'stludr', 'RVL_INFO_LOG', 'a.SETTLEMONTH=\'BILLING_MONTH\'and a.RPT_FILENAME =\'REPORT_BATCH\'', '报表批量下载预生产:');


-- stl_报表批量下载预生产
INSERT INTO `boss_uom`.`uom_node_config` (`nodeid`, `nodename`, `createtime`, `tasktype`, `path`, `remarks`, `qname`, `method`, `serverip`, `username`, `auth`, `remotefile`, `localfile`)
VALUES ('617', 'stl_报表批量下载预生产', NOW(), 1, '', 'service', '', 'evictReportBatch', 'http://boss-service-rater-special.bboss-1:9107/exec/settle', 'STLUDR', NULL, '', '');
INSERT INTO `boss_uom`.`uom_node` (`nodeid`, `nodetype`, `nodename`, `callbackcount`, `createtime`, `describe`)
VALUES ('617', 'taskNode', 'stl_报表批量下载预生产', 2, now(), NULL);

-- 结算单下发一批
INSERT INTO `boss_uom`.`uom_node_config` (`nodeid`, `nodename`, `createtime`, `tasktype`, `path`, `remarks`, `qname`, `method`, `serverip`, `username`, `auth`, `remotefile`, `localfile`)
VALUES ('631', 'stl_结算单下发一批数据', NOW(), 1, '', 'service', '', 'send2prov_first', 'http://boss-service-rater-special.bboss-1:9107/exec/settle', 'STLUDR', NULL, '', '');
INSERT INTO `boss_uom`.`uom_node` (`nodeid`, `nodetype`, `nodename`, `callbackcount`, `createtime`, `describe`)
VALUES ('631', 'taskNode', 'stl_结算单下发一批数据', 2, now(), NULL);

-- 结算单下发二批
INSERT INTO `boss_uom`.`uom_node_config` (`nodeid`, `nodename`, `createtime`, `tasktype`, `path`, `remarks`, `qname`, `method`, `serverip`, `username`, `auth`, `remotefile`, `localfile`)
VALUES ('632', 'stl_结算单下发二批数据', NOW(), 1, '', 'service', '', 'send2prov_second', 'http://boss-service-rater-special.bboss-1:9107/exec/settle', 'STLUDR', NULL, '', '');
INSERT INTO `boss_uom`.`uom_node` (`nodeid`, `nodetype`, `nodename`, `callbackcount`, `createtime`, `describe`)
VALUES ('632', 'taskNode', 'stl_结算单下发二批数据', 2, now(), NULL);

-- 结算单下发省专数据
INSERT INTO `boss_uom`.`uom_node_config` (`nodeid`, `nodename`, `createtime`, `tasktype`, `path`, `remarks`, `qname`, `method`, `serverip`, `username`, `auth`, `remotefile`, `localfile`)
VALUES ('633', 'stl_结算单下发省专数据', NOW(), 1, '', 'service', '', 'send2prov_company', 'http://boss-service-rater-special.bboss-1:9107/exec/settle', 'STLUDR', NULL, '', '');
INSERT INTO `boss_uom`.`uom_node` (`nodeid`, `nodetype`, `nodename`, `callbackcount`, `createtime`, `describe`)
VALUES ('633', 'taskNode', 'stl_结算单下发省专数据', 2, now(), NULL);

-- 结算单下发移动云数据
INSERT INTO `boss_uom`.`uom_node_config` (`nodeid`, `nodename`, `createtime`, `tasktype`, `path`, `remarks`, `qname`, `method`, `serverip`, `username`, `auth`, `remotefile`, `localfile`)
VALUES ('634', 'stl_结算单下发移动云数据', NOW(), 1, '', 'service', '', 'send2prov_ydy', 'http://boss-service-rater-special.bboss-1:9107/exec/settle', 'STLUDR', NULL, '', '');
INSERT INTO `boss_uom`.`uom_node` (`nodeid`, `nodetype`, `nodename`, `callbackcount`, `createtime`, `describe`)
VALUES ('634', 'taskNode', 'stl_结算单下发移动云数据', 2, now(), NULL);

-- 结算单下发S201数据
INSERT INTO `boss_uom`.`uom_node_config` (`nodeid`, `nodename`, `createtime`, `tasktype`, `path`, `remarks`, `qname`, `method`, `serverip`, `username`, `auth`, `remotefile`, `localfile`)
VALUES ('635', 'stl_结算单下发S201数据', NOW(), 1, '', 'service', '', 'send2prov_s201', 'http://boss-service-rater-special.bboss-1:9107/exec/settle', 'STLUDR', NULL, '', '');
INSERT INTO `boss_uom`.`uom_node` (`nodeid`, `nodetype`, `nodename`, `callbackcount`, `createtime`, `describe`)
VALUES ('635', 'taskNode', 'stl_结算单下发S201数据', 2, now(), NULL);

-- 结算单下发政企业务数据
INSERT INTO `boss_uom`.`uom_node_config` (`nodeid`, `nodename`, `createtime`, `tasktype`, `path`, `remarks`, `qname`, `method`, `serverip`, `username`, `auth`, `remotefile`, `localfile`)
VALUES ('636', 'stl_结算单下发政企业务数据', NOW(), 5, '', '290', '', 'send2prov_zq', '', 'STLUDR', NULL, '', '');
INSERT INTO `boss_uom`.`uom_node` (`nodeid`, `nodetype`, `nodename`, `callbackcount`, `createtime`, `describe`)
VALUES ('636', 'taskNode', 'stl_结算单下发政企业务数据', 2, now(), NULL);


-- 下省文件一批数据状态更新
INSERT INTO `boss_uom`.`uom_node_config` (`nodeid`, `nodename`, `createtime`, `tasktype`, `path`, `remarks`, `qname`, `method`, `serverip`, `username`, `auth`, `remotefile`, `localfile`)
VALUES ('637', 'stl_下省文件一批数据db2f状态更新N', NOW(), 1, '', 'service', '', 'send2prov_task_first', 'http://boss-service-rater-special.bboss-1:9107/exec/settle', 'STLUDR', NULL, '', '');
INSERT INTO `boss_uom`.`uom_node` (`nodeid`, `nodetype`, `nodename`, `callbackcount`, `createtime`, `describe`)
VALUES ('637', 'taskNode', 'stl_下省文件一批数据db2f状态更新N', 2, now(), NULL);

-- 下省文件二批数据状态更新
INSERT INTO `boss_uom`.`uom_node_config` (`nodeid`, `nodename`, `createtime`, `tasktype`, `path`, `remarks`, `qname`, `method`, `serverip`, `username`, `auth`, `remotefile`, `localfile`)
VALUES ('638', 'stl_下省文件二批数据db2f状态更新N', NOW(), 1, '', 'service', '', 'send2prov_task_second', 'http://boss-service-rater-special.bboss-1:9107/exec/settle', 'STLUDR', NULL, '', '');
INSERT INTO `boss_uom`.`uom_node` (`nodeid`, `nodetype`, `nodename`, `callbackcount`, `createtime`, `describe`)
VALUES ('638', 'taskNode', 'stl_下省文件二批数据db2f状态更新N', 2, now(), NULL);

-- 下省文件省专数据状态更新
INSERT INTO `boss_uom`.`uom_node_config` (`nodeid`, `nodename`, `createtime`, `tasktype`, `path`, `remarks`, `qname`, `method`, `serverip`, `username`, `auth`, `remotefile`, `localfile`)
VALUES ('639', 'stl_下省文件省专数据db2f状态更新N', NOW(), 1, '', 'service', '', 'send2prov_task_company', 'http://boss-service-rater-special.bboss-1:9107/exec/settle', 'STLUDR', NULL, '', '');
INSERT INTO `boss_uom`.`uom_node` (`nodeid`, `nodetype`, `nodename`, `callbackcount`, `createtime`, `describe`)
VALUES ('639', 'taskNode', 'stl_下省文件省专数据db2f状态更新N', 2, now(), NULL);

-- 下省文件移动云数据状态更新
INSERT INTO `boss_uom`.`uom_node_config` (`nodeid`, `nodename`, `createtime`, `tasktype`, `path`, `remarks`, `qname`, `method`, `serverip`, `username`, `auth`, `remotefile`, `localfile`)
VALUES ('640', 'stl_下省文件移动云数据db2f状态更新N', NOW(), 1, '', 'service', '', 'send2prov_task_ydy', 'http://boss-service-rater-special.bboss-1:9107/exec/settle', 'STLUDR', NULL, '', '');
INSERT INTO `boss_uom`.`uom_node` (`nodeid`, `nodetype`, `nodename`, `callbackcount`, `createtime`, `describe`)
VALUES ('640', 'taskNode', 'stl_下省文件移动云数据db2f状态更新N', 2, now(), NULL);

-- 下省文件S201数据状态更新
INSERT INTO `boss_uom`.`uom_node_config` (`nodeid`, `nodename`, `createtime`, `tasktype`, `path`, `remarks`, `qname`, `method`, `serverip`, `username`, `auth`, `remotefile`, `localfile`)
VALUES ('641', 'stl_下省文件S201数据db2f状态更新N', NOW(), 1, '', 'service', '', 'send2prov_task_s201', 'http://boss-service-rater-special.bboss-1:9107/exec/settle', 'STLUDR', NULL, '', '');
INSERT INTO `boss_uom`.`uom_node` (`nodeid`, `nodetype`, `nodename`, `callbackcount`, `createtime`, `describe`)
VALUES ('641', 'taskNode', 'stl_下省文件S201数据db2f状态更新N', 2, now(), NULL);

-- 下省文件政企数据状态更新
INSERT INTO `boss_uom`.`uom_node_config` (`nodeid`, `nodename`, `createtime`, `tasktype`, `path`, `remarks`, `qname`, `method`, `serverip`, `username`, `auth`, `remotefile`, `localfile`)
VALUES ('642', 'stl_下省文件政企数据db2f状态更新N', NOW(), 1, '', 'service', '', 'send2prov_task_zq', 'http://boss-service-rater-special.bboss-1:9107/exec/settle', 'STLUDR', NULL, '', '');
INSERT INTO `boss_uom`.`uom_node` (`nodeid`, `nodetype`, `nodename`, `callbackcount`, `createtime`, `describe`)
VALUES ('642', 'taskNode', 'stl_下省文件政企数据db2f状态更新N', 2, now(), NULL);




--统计节点配置
INSERT INTO `boss_uom`.`uom_node_config` (`nodeid`, `nodename`, `createtime`, `tasktype`, `path`, `remarks`, `qname`, `method`, `serverip`, `username`, `auth`, `remotefile`, `localfile`)
VALUES ('643', 'stl_省间结算文件统计', NOW(), 1, '', 'service', '', 'provFileStatistics_p2p', 'http://boss-service-rater-special.bboss-1:9107/exec/settle', 'STLUDR', NULL, '', '');
INSERT INTO `boss_uom`.`uom_node` (`nodeid`, `nodetype`, `nodename`, `callbackcount`, `createtime`, `describe`)
VALUES ('643', 'taskNode', 'stl_省间结算文件统计', 2, now(), NULL);
INSERT INTO `boss_uom`.`uom_manual_config` (`nodeid`, `nodename`, `createtime`, `username`, `type`, `script`, `remarks`)
VALUES ('643', 'stl_省间结算文件统计', now(), 'STLUSERS', '1', 'SELECT b.PROV_CD, b.PROV_NM, SUM(CASE WHEN substr(a.FILE_NAME, 1, 18) = \'BBOSS_ACC_E_BILL_2\' THEN 1 ELSE 0 END) AS \"2号文件\", SUM(CASE WHEN substr(a.FILE_NAME, 1, 18) = \'BBOSS_ACC_E_BILL_3\' THEN 1 ELSE 0 END) AS \"3号文件\", SUM(CASE WHEN substr(a.FILE_NAME, 1, 18) = \'BBOSS_ACC_E_BILL_7\' THEN 1 ELSE 0 END) AS \"7号文件\", SUM(CASE WHEN substr(a.FILE_NAME, 1, 18) = \'BBOSS_ACC_E_BILL_9\' THEN 1 ELSE 0 END) AS \"9号文件\" FROM stlusers.file_db2f_log a RIGHT JOIN stl_province_cd b ON substr(a.FILE_NAME, 33, 3) = b.PROV_CD AND a.ACCT_MONTH = ${acctMonth} AND a.CREATE_TIME >= to_date(${startTime}, \'yyyymmdd\') AND substr(a.FILE_NAME, 1, 18) IN (\'BBOSS_ACC_E_BILL_9\', \'BBOSS_ACC_E_BILL_2\', \'BBOSS_ACC_E_BILL_7\', \'BBOSS_ACC_E_BILL_3\') GROUP BY substr(a.FILE_NAME, 33, 3), b.PROV_CD, b.PROV_NM ORDER BY b.PROV_CD', 'stl_省间结算文件统计');

INSERT INTO `boss_uom`.`uom_node_config` (`nodeid`, `nodename`, `createtime`, `tasktype`, `path`, `remarks`, `qname`, `method`, `serverip`, `username`, `auth`, `remotefile`, `localfile`)
VALUES ('644', 'stl_S201结算文件统计', NOW(), 1, '', 'service', '', 'provFileStatistics_s201', 'http://boss-service-rater-special.bboss-1:9107/exec/settle', 'STLUDR', NULL, '', '');
INSERT INTO `boss_uom`.`uom_node` (`nodeid`, `nodetype`, `nodename`, `callbackcount`, `createtime`, `describe`)
VALUES ('644', 'taskNode', 'stl_S201结算文件统计', 2, now(), NULL);
INSERT INTO `boss_uom`.`uom_manual_config` (`nodeid`, `nodename`, `createtime`, `username`, `type`, `script`, `remarks`)
VALUES ('644', 'stl_S201结算文件统计', now(), 'STLUSERS', '1', 'SELECT b.PROV_CD, b.PROV_NM, SUM( CASE WHEN substr( a.FILE_NAME, 1, 15 ) = \'BBOSS_BD_SETT_2\' THEN 1 ELSE 0 END ) AS \"S201结算2号文件\", SUM( CASE WHEN substr( a.FILE_NAME, 1, 15 ) = \'BBOSS_BD_SETT_7\' THEN 1 ELSE 0 END ) AS \"S201结算7号文件\" FROM stlusers.file_db2f_log a RIGHT JOIN stl_province_cd b ON substr( a.FILE_NAME, 28, 3 ) = b.PROV_CD AND a.FILE_NAME LIKE \'BBOSS_BD_SETT%\' AND a.ACCT_MONTH = ${acctMonth} AND a.CREATE_TIME >= to_date(${startTime}, \'yyyymmdd\') GROUP BY b.PROV_CD, b.PROV_NM ORDER BY b.PROV_CD;', 'stl_S201结算文件统计');

INSERT INTO `boss_uom`.`uom_node_config` (`nodeid`, `nodename`, `createtime`, `tasktype`, `path`, `remarks`, `qname`, `method`, `serverip`, `username`, `auth`, `remotefile`, `localfile`)
VALUES ('645', 'stl_V101结算文件统计', NOW(), 1, '', 'service', '', 'provFileStatistics_v101', 'http://boss-service-rater-special.bboss-1:9107/exec/settle', 'STLUDR', NULL, '', '');
INSERT INTO `boss_uom`.`uom_node` (`nodeid`, `nodetype`, `nodename`, `callbackcount`, `createtime`, `describe`)
VALUES ('645', 'taskNode', 'stl_V101结算文件统计', 2, now(), NULL);
INSERT INTO `boss_uom`.`uom_manual_config` (`nodeid`, `nodename`, `createtime`, `username`, `type`, `script`, `remarks`)
VALUES ('645', 'stl_V101结算文件统计', now(), 'STLUSERS', '1', 'SELECT b.PROV_CD, b.PROV_NM, SUM(CASE WHEN substr(a.FILE_NAME, 1, 16) = \'BBOSS_IDC_SETT_2\' THEN 1 ELSE 0 END) AS \"V101结算2号文件\", SUM(CASE WHEN substr(a.FILE_NAME, 1, 16) = \'BBOSS_IDC_SETT_7\' THEN 1 ELSE 0 END) AS \"V101结算7号文件\" FROM stlusers.file_db2f_log a RIGHT JOIN stl_province_cd b ON substr(a.FILE_NAME, 29, 3) = b.PROV_CD AND a.FILE_NAME LIKE \'BBOSS_IDC_SETT_%\' AND a.ACCT_MONTH = ${acctMonth} AND a.CREATE_TIME >= to_date(${startTime}, \'yyyymmdd\') GROUP BY b.PROV_CD, b.PROV_NM ORDER BY b.PROV_CD;', 'stl_V101结算文件统计');

INSERT INTO `boss_uom`.`uom_node_config` (`nodeid`, `nodename`, `createtime`, `tasktype`, `path`, `remarks`, `qname`, `method`, `serverip`, `username`, `auth`, `remotefile`, `localfile`)
VALUES ('646', 'stl_移动云结算文件统计', NOW(), 1, '', 'service', '', 'provFileStatistics_ydy', 'http://boss-service-rater-special.bboss-1:9107/exec/settle', 'STLUDR', NULL, '', '');
INSERT INTO `boss_uom`.`uom_node` (`nodeid`, `nodetype`, `nodename`, `callbackcount`, `createtime`, `describe`)
VALUES ('646', 'taskNode', 'stl_移动云结算文件统计', 2, now(), NULL);
INSERT INTO `boss_uom`.`uom_manual_config` (`nodeid`, `nodename`, `createtime`, `username`, `type`, `script`, `remarks`)
VALUES ('646', 'stl_移动云结算文件统计', now(), 'STLUSERS', '1', 'SELECT b.PROV_CD, b.PROV_NM, SUM(CASE WHEN substr(a.FILE_NAME, 1, 16) = \'BBOSS_CLOUD_SETT\' THEN 1 ELSE 0 END) AS \"移动云文件\" FROM stlusers.file_db2f_log a RIGHT JOIN stl_province_cd b ON substr(a.FILE_NAME,29,3) = b.PROV_CD AND a.FILE_NAME LIKE \'BBOSS_CLOUD_SETT_%\' AND a.ACCT_MONTH = ${acctMonth} AND a.CREATE_TIME >= to_date(${startTime}, \'yyyymmdd\') GROUP BY b.PROV_CD, b.PROV_NM ORDER BY b.PROV_CD;', 'stl_移动云结算文件统计');

INSERT INTO `boss_uom`.`uom_node_config` (`nodeid`, `nodename`, `createtime`, `tasktype`, `path`, `remarks`, `qname`, `method`, `serverip`, `username`, `auth`, `remotefile`, `localfile`)
VALUES ('647', 'stl_省专结算文件统计', NOW(), 1, '', 'service', '', 'provFileStatistics_p2c', 'http://boss-service-rater-special.bboss-1:9107/exec/settle', 'STLUDR', NULL, '', '');
INSERT INTO `boss_uom`.`uom_node` (`nodeid`, `nodetype`, `nodename`, `callbackcount`, `createtime`, `describe`)
VALUES ('647', 'taskNode', 'stl_省专结算文件统计', 2, now(), NULL);
INSERT INTO `boss_uom`.`uom_manual_config` (`nodeid`, `nodename`, `createtime`, `username`, `type`, `script`, `remarks`)
VALUES ('647', 'stl_省专结算文件统计', now(), 'STLUSERS', '1', 'SELECT b.PROV_CD, b.PROV_NM, SUM( CASE WHEN substr( a.FILE_NAME, 1, 21 ) = \'BBOSS_PROV2SPEC_SETT_\' THEN 1 ELSE 0 END ) AS \"省专文件\" FROM stlusers.file_db2f_log a RIGHT JOIN stl_province_cd b ON substr( a.FILE_NAME, 33, 3 ) = b.PROV_CD AND a.FILE_NAME LIKE \'BBOSS_PROV2SPEC_SETT_%\' AND a.ACCT_MONTH = ${acctMonth} AND a.CREATE_TIME >= to_date(${startTime}, \'yyyymmdd\') GROUP BY b.PROV_CD, b.PROV_NM ORDER BY b.PROV_CD;', 'stl_省专结算文件统计');


-- 20250417
INSERT INTO `boss_uom`.`uom_node_config` (`nodeid`, `nodename`, `createtime`, `tasktype`, `path`, `remarks`, `qname`, `method`, `serverip`, `username`, `auth`, `remotefile`, `localfile`)
VALUES ('648', 'stl_局数据同步44_new', NOW(), 5, '', '10', '', 'billSyncStlusers44', '', 'STLUSERS', NULL, '', '');
INSERT INTO `boss_uom`.`uom_node` (`nodeid`, `nodetype`, `nodename`, `callbackcount`, `createtime`, `describe`)
VALUES ('648', 'taskNode', 'stl_局数据同步44_new', 2, now(), NULL);

INSERT INTO `boss_uom`.`uom_node_config` (`nodeid`, `nodename`, `createtime`, `tasktype`, `path`, `remarks`, `qname`, `method`, `serverip`, `username`, `auth`, `remotefile`, `localfile`)
VALUES ('649', 'stl_局数据同步46_new', NOW(), 5, '', '34', '', 'billSyncStlusers46', '', 'STLUSERS', NULL, '', '');
INSERT INTO `boss_uom`.`uom_node` (`nodeid`, `nodetype`, `nodename`, `callbackcount`, `createtime`, `describe`)
VALUES ('649', 'taskNode', 'stl_局数据同步46_new', 2, now(), NULL);

INSERT INTO `boss_uom`.`uom_node_config` (`nodeid`, `nodename`, `createtime`, `tasktype`, `path`, `remarks`, `qname`, `method`, `serverip`, `username`, `auth`, `remotefile`, `localfile`)
VALUES ('650', 'stl_号码运营商同步_M_new', NOW(), 5, '', '253', '', 'phoneNumSyncStlusers', '', 'STLUSERS', NULL, '', '');
INSERT INTO `boss_uom`.`uom_node` (`nodeid`, `nodetype`, `nodename`, `callbackcount`, `createtime`, `describe`)
VALUES ('650', 'taskNode', 'stl_号码运营商同步_M_new', 2, now(), NULL);


####  生产的 http://boss-service-rater-special.bboss-1:9090/exec/settle