INSERT /*+ AUTOCOMMIT_DURING_DML() */ INTO SYNC_BL_SETTLE_MID 
SELECT /*+ hash_join()*/ 
       NULL ,
       t.ticket_id AS stream_id,
       'BL' AS datasource,
       t.prod_order_mode AS ordermode,
       t.acct_month AS orgmonth,
       t.ec_code AS customernumber,
       t.offer_code AS pospecnumber,
       t.product_code AS sospecnumber,
       t.offer_order_id AS poid,
       t.product_order_id AS soid,
       DECODE(c.charge_item_ref, '1587', '157', c.charge_item_ref) AS feetype,
       '' AS dn,
       t.fee AS amount,
       '2' AS amount_type,
       TO_CHAR(SYSDATE, 'yyyymmddhh24miss') AS synctime,
       '0' AS status,
       '' AS errmesg,
       t.terminalvendor AS prov_cd,
       NULL AS accountid,
       '2' AS remark
FROM (
    SELECT /*+ hash_join() */
           MAX(a.TICKET_ID) AS ticket_id,
           a.ACCT_MONTH,
           a.EC_CODE,
           a.PROD_ORDER_MODE,
           a.OFFER_CODE,
           a.PRODUCT_CODE,
           a.OFFER_ORDER_ID,
           a.PRODUCT_ORDER_ID,
           ROUND(
               DECODE(a.rate_back_id, '1', NVL(f.cdr_rate, 1), a.bill_charge) 
               * 10 * COUNT(a.cerca_mark) * 0.3 
               * d.incentivecoefficient * c.evaluationscore / 10
           ) AS fee,
           a.charge_code,
           d.terminalvendor
    FROM stludr.UR_MAAPMMA_#inMonth#_T a
    INNER JOIN (
        SELECT x.terminalvendor, x.evaluationscore
        FROM stlusers.MAAP_TERMINAL_INFO x
        INNER JOIN (
            SELECT MAX(createdate) AS createdate, terminalvendor
            FROM stlusers.MAAP_TERMINAL_INFO
            WHERE '#inMonth#' BETWEEN startdate AND enddate
              AND (incentivecoefficient IS NULL OR incentivecoefficient = '')
            GROUP BY terminalvendor
        ) y ON x.createdate = y.createdate AND x.terminalvendor = y.terminalvendor
        WHERE (x.incentivecoefficient IS NULL OR x.incentivecoefficient = '')
          AND '#inMonth#' BETWEEN x.startdate AND x.enddate
    ) c ON a.manufacturer_code = ANY (SELECT terminal_brand 
                                     FROM stludr.MAAPMMA_VENDOR_CONF 
                                     WHERE terminal_vendor = c.terminalvendor)
    INNER JOIN (
        SELECT x.terminalvendor, x.incentivecoefficient
        FROM stlusers.MAAP_TERMINAL_INFO x
        INNER JOIN (
            SELECT MAX(createdate) AS createdate, terminalvendor
            FROM stlusers.MAAP_TERMINAL_INFO
            WHERE '#inMonth#' BETWEEN startdate AND enddate
              AND (evaluationscore IS NULL OR evaluationscore = '')
            GROUP BY terminalvendor
        ) y ON x.createdate = y.createdate AND x.terminalvendor = y.terminalvendor
        WHERE (x.evaluationscore IS NULL OR x.evaluationscore = '')
          AND '#inMonth#' BETWEEN x.startdate AND x.enddate
    ) d ON c.terminalvendor = d.terminalvendor
    INNER JOIN stludr.MAAPMMA_VENDOR_CONF e 
           ON a.manufacturer_code = e.terminal_brand
          AND e.terminal_vendor = c.terminalvendor
          AND e.terminal_vendor = d.terminalvendor
    LEFT JOIN (
        SELECT *
        FROM stludr.BIZ_MSG_MMM_STL
        WHERE acct_month = '#inMonth#'
          AND CHARGE_ITEM = 'Multimedia_Fee'
          AND bill_Type = '1'
    ) f ON a.product_order_id = f.subscriber_id
    WHERE a.offer_code = '50034'
      AND a.product_code = '5003401'
      AND a.cerca_mark = '1'
    GROUP BY a.ACCT_MONTH,
             a.EC_CODE,
             a.PROD_ORDER_MODE,
             a.OFFER_CODE,
             a.PRODUCT_CODE,
             a.OFFER_ORDER_ID,
             a.PRODUCT_ORDER_ID,
             a.rate_back_id,
             f.cdr_rate,
             a.bill_charge,
             a.charge_code,
             a.cerca_mark,
             d.incentivecoefficient,
             c.evaluationscore,
             d.terminalvendor
) t
INNER JOIN stlusers.STL_CHARGE_ITEM_DEF c 
        ON t.charge_code = c.charge_item;


## 添加资费编码字段
ALTER TABLE `stludr`.`ur_maapmma_202506_t` ADD COLUMN `RATEPLAN_ID` varchar(30)  DEFAULT NULL;
ALTER TABLE `stludr`.`ur_maapmma_202507_t` ADD COLUMN `RATEPLAN_ID` varchar(30)  DEFAULT NULL;


drop table if exists stludr.stl_5g_terminal_source;
CREATE TABLE stludr.stl_5g_terminal_source  (
  `ID` bigint(20) NOT NULL AUTO_INCREMENT,
  `ACCT_MONTH` varchar(10)  NOT NULL COMMENT '账期',
  `TERMINALVENDOR` varchar(100)  NOT NULL COMMENT '终端编码',
  `EVALUATIONSCORE` varchar(10)  NOT NULL COMMENT '评估系数',
  `INCENTIVECOEFFICIENT` varchar(10)  NULL DEFAULT NULL COMMENT '激励系数',
  `TERMINAL_BRAND` varchar(40)  NULL DEFAULT NULL COMMENT '终端品牌',
  `TERMINAL_NAME` VARCHAR(40) NULL DEFAULT NULL COMMENT '终端名称',
  `CREATE_TIME` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_terminalvendor`(`TERMINALVENDOR`) USING BTREE,
  INDEX `idx_terminalbrand`(`TERMINAL_BRAND`) USING BTREE
) ENGINE = greatdb CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_bin ;

drop table if exists stludr.maapnma_terminal_prod_total;
CREATE TABLE stludr.maapnma_terminal_prod_total (
  "id" bigint NOT NULL AUTO_INCREMENT,
  "acct_month" char(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '账期',
  "ticket_id" decimal(16,0) COMMENT '话单ID',
  "ec_code" varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '客户编码',
  "ec_prov" varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '客户省份',
  "order_mode" varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '订购模式',
  "offer_code" varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '商品编码',
  "product_code" varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL COMMENT '产品编码',
  "offer_order_id" decimal(20,0) DEFAULT NULL COMMENT '商品订购ID',
  "product_order_id" decimal(20,0) DEFAULT NULL COMMENT '产品订购ID',
  "rate_back_id" varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '叠加包套餐标识，1-叠加包',
  "bill_charge" decimal(12,0) DEFAULT NULL COMMENT '计费出账金额',
  "settle_fee" decimal(12,0) DEFAULT NULL COMMENT '结算金额',
  "charge_code" varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '费项编码 EN',
  "charge_item" varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '费项编码',
  "manufacturer_code" varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '终端品牌',
  "terminalvendor" varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '终端厂商编码',
  "rateplan_id" varchar(30) COLLATE utf8mb4_0900_bin DEFAULT NULL COMMENT '资费编码',
  "total_count" bigint NOT NULL DEFAULT '0' COMMENT '话单数量',
  PRIMARY KEY ("id"),
  index idx_acctmonth (acct_month),
  index idx_product_order_id(product_order_id),
  index idx_ec_code(ec_code)
) ENGINE=GreatDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin ENABLE_DTID=ON;


drop table if exists stludr.maapnma_terminal_prod_fee_rule;
create table stludr.maapnma_terminal_prod_fee_rule (
  id bigint,
  acct_month varchar(6),
  product_order_id decimal(20,0),
  total_count bigint,
  terminalvendor varchar(32),
  evaluationscore varchar(10),
  incentivecoefficient varchar(10),
  rule_1_fee decimal(14,2) comment '规则一结算金额',
  rule_2_fee decimal(14,2) comment '规则二结算金额',
  final_fee decimal(12) comment '最终结算金额',
  create_time datetime default current_timestamp
) ENGINE=GreatDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin ENABLE_DTID=ON;

drop table if exists stludr.maapnma_terminal_prod_fee_rule_swap;
create table stludr.maapnma_terminal_prod_fee_rule_swap (
  id bigint  NOT NULL AUTO_INCREMENT,
  product_order_id decimal(20,0),
  terminalvendor varchar(32),
  fee1_sum decimal(14,2) comment '规则一结算金额',
  fee2_sum decimal(14,2) comment '规则二结算金额',
  fee_rule int comment '最终判断的规则编码，1：取规则1的金额，2：取规则2的金额',
  create_time datetime default current_timestamp,
 PRIMARY KEY ("id"),
  index idx_product_order_id(product_order_id)
) ENGINE=GreatDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin ENABLE_DTID=ON;

alter table stludr.maapnma_terminal_prod_fee_rule_swap modify fee1_sum decimal(14,2) comment '规则一结算金额';
alter table stludr.maapnma_terminal_prod_fee_rule_swap modify fee2_sum decimal(14,2) comment '规则二结算金额';

alter table stludr.maapnma_terminal_prod_fee_rule modify rule_1_fee decimal(14,2) comment '规则一结算金额';
alter table stludr.maapnma_terminal_prod_fee_rule modify rule_2_fee decimal(14,2) comment '规则二结算金额';

delete from stludr.stl_5g_terminal_source where acct_month = '#inMonth#';
delete from stludr.maapnma_terminal_prod_total where acct_month = '#inMonth#';
delete from stludr.maapnma_terminal_prod_fee_rule where acct_month = '#inMonth#';

TRUNCATE TABLE stludr.maapnma_terminal_prod_fee_rule_swap;

-- 1、先获取终端系数
insert into stludr.stl_5g_terminal_source(acct_month, terminalvendor, evaluationscore, incentivecoefficient, terminal_brand, terminal_name)
SELECT '#inMonth#',b.TERMINALVENDOR,b.EVALUATIONSCORE,b.INCENTIVECOEFFICIENT,a.TERMINAL_BRAND,a.TERMINAL_NAME from stludr.MAAPMMA_VENDOR_CONF a JOIN (
SELECT 
    TERMINALVENDOR,
    MAX(EVALUATIONSCORE) as EVALUATIONSCORE,
    MAX(INCENTIVECOEFFICIENT) as INCENTIVECOEFFICIENT
FROM (
    -- 获取最新的评估系数
    SELECT 
        TERMINALVENDOR,
        EVALUATIONSCORE,
        NULL as INCENTIVECOEFFICIENT
    FROM (
        SELECT 
            TERMINALVENDOR,
            EVALUATIONSCORE,
            ROW_NUMBER() OVER(PARTITION BY TERMINALVENDOR ORDER BY CREATEDATE DESC) as rn
        FROM stlusers.maap_terminal_info  
        WHERE (INCENTIVECOEFFICIENT IS NULL OR INCENTIVECOEFFICIENT = '') 
          AND EVALUATIONSCORE IS NOT NULL 
          AND EVALUATIONSCORE != ''
          AND '#inMonth#' BETWEEN STARTDATE AND ENDDATE
    ) eval_ranked WHERE rn = 1
    
    UNION ALL
    
    -- 获取最新的激励系数
    SELECT 
        TERMINALVENDOR,
        NULL as EVALUATIONSCORE,
        INCENTIVECOEFFICIENT
    FROM (
        SELECT 
            TERMINALVENDOR,
            INCENTIVECOEFFICIENT,
            ROW_NUMBER() OVER(PARTITION BY TERMINALVENDOR ORDER BY CREATEDATE DESC) as rn
        FROM stlusers.maap_terminal_info  
        WHERE (EVALUATIONSCORE IS NULL OR EVALUATIONSCORE = '') 
          AND INCENTIVECOEFFICIENT IS NOT NULL 
          AND INCENTIVECOEFFICIENT != ''
          AND '#inMonth#' BETWEEN STARTDATE AND ENDDATE
    ) incent_ranked WHERE rn = 1
) combined
GROUP BY TERMINALVENDOR
ORDER BY TERMINALVENDOR ) b on b.TERMINALVENDOR = a.terminal_vendor


-- 2、获取话单汇总数据
insert into stludr.maapnma_terminal_prod_total
(acct_month, ticket_id, ec_code, ec_prov, order_mode, offer_code, product_code, offer_order_id, product_order_id, rate_back_id, bill_charge, charge_code, manufacturer_code, rateplan_id, total_count)
SELECT /*+ PARALLEL(a, 8) */
   '#inMonth#',
	MAX( a.TICKET_ID ) AS ticket_id,
	a.EC_CODE,
  a.ec_code_prov,
	a.PROD_ORDER_MODE,
	a.OFFER_CODE,
	a.PRODUCT_CODE,
	a.OFFER_ORDER_ID,
	a.PRODUCT_ORDER_ID,
	a.rate_back_id,
	a.bill_charge,
	a.charge_code,
	a.manufacturer_code,
	a.RATEPLAN_ID,
	COUNT(*) total_count
FROM
	stludr.UR_MAAPMMA_#inMonth#_T a 
WHERE
	a.offer_code = '50034' 
	AND a.product_code = '5003401' 
	AND a.cerca_mark = '1' 
GROUP BY
	a.EC_CODE,
  a.ec_code_prov,
	a.PROD_ORDER_MODE,
	a.OFFER_CODE,
	a.PRODUCT_CODE,
	a.OFFER_ORDER_ID,
	a.PRODUCT_ORDER_ID,
	a.rate_back_id,
	a.bill_charge,
	a.charge_code,
	a.manufacturer_code,
	a.RATEPLAN_ID,
	a.cerca_mark;



-- 3、关联终端费项表stlusers.STL_CHARGE_ITEM_DEF，更新charge_item
update stludr.maapnma_terminal_prod_total a
inner join stlusers.STL_CHARGE_ITEM_DEF b on a.charge_code = b.charge_item 
set a.charge_item = decode(b.charge_item_ref, '1587', '157', b.charge_item_ref)
where a.acct_month = '#inMonth#';

-- 4、关联终端系数表stludr.stl_5g_terminal_source，更新settle_fee，terminalvendor
update stludr.maapnma_terminal_prod_total a
inner join stludr.stl_5g_terminal_source b on a.manufacturer_code = b.TERMINAL_BRAND
LEFT JOIN (select * from stludr.biz_msg_mmm_stl where acct_month = '#inMonth#' and CHARGE_ITEM='Multimedia_Fee' and bill_Type= '1') c on a.product_order_id = c.subscriber_id
set a.settle_fee = round(decode(a.rate_back_id, '1', nvl(c.cdr_rate, 1), a.bill_charge) * 10 * total_count * 0.3 * b.incentivecoefficient * b.evaluationscore / 10),
    a.terminalvendor = b.TERMINALVENDOR
where a.acct_month = '#inMonth#' ;

-- 去掉left join
update stludr.maapnma_terminal_prod_total a
inner join stludr.stl_5g_terminal_source b on a.manufacturer_code = b.TERMINAL_BRAND
set a.settle_fee = round(decode(a.rate_back_id, '1', 54, a.bill_charge) * 10 * total_count * 0.3 * b.incentivecoefficient * b.evaluationscore / 10),
    a.bg_fee = round(decode(a.rate_back_id, '1', 54, a.bill_charge) * total_count
    a.terminalvendor = b.TERMINALVENDOR
where a.acct_month = '#inMonth#' ;


##2、定购“头部客户多媒体固定套餐-700001、头部客户多媒体畅享套餐-700002”叠加包资源，终端品牌（苹果、华为、小米）,终端枚举值（Appl，iPhone，Apple、HUAW，Huaw、Xiaomi，Mi）不区分大小写
#a>“后评估系数”为0的，不进行终端分成结算, total_count置为0。
#b>“后评估系数”为非0，按照每条0.05元/条进行终端结算
merge into stludr.maapnma_terminal_prod_total a
using (
   --评估系数为0的
    select 
        evaluationscore,
        incentivecoefficient,
        terminalvendor
    from stludr.stl_5g_terminal_source
    where acct_month = '#inMonth#' and terminalvendor in ('1', '4', '5') and evaluationscore = '0'
    group by evaluationscore,
        incentivecoefficient,
        terminalvendor
        
) b
on (a.terminalvendor = b.terminalvendor)
when matched then
update set a.settle_fee = 0 ,a.total_count = 0
where a.acct_month = '#inMonth#' and a.rateplan_id in ('1');

merge into stludr.maapnma_terminal_prod_total a
using (
    select 
        evaluationscore,
        incentivecoefficient,
        terminalvendor
    from stludr.stl_5g_terminal_source
    where acct_month = '#inMonth#' and terminalvendor in ('1', '4', '5') and evaluationscore != '0'
    group by evaluationscore,
        incentivecoefficient,
        terminalvendor
) c
on (a.terminalvendor = c.terminalvendor)
when matched then
update set a.settle_fee = round(50 * 10 * a.total_count * 0.3 * c.incentivecoefficient * c.evaluationscore / 10)
where a.acct_month = '#inMonth#' and a.rateplan_id in ('1') and a.rate_back_id = '1';

##3、当终端类型=6-荣耀；终端合同约定最低结算单价为2分/条（含税）*终端类型=6-荣耀认证话单条数进行计算结算金额，与规则一计算出的结算金额进行比较取二者中的高值作为结算单金额。
-- maapnma_terminal_prod_fee_rule 存储规则一和规则二的结算金额

-- 取出终端类型为6的金额，分别俩个规则计算，此时 bg_fee*0.3 = rule_1_fee。如果是免费资费，rule_2_fee = 0
INSERT INTO stludr.maapnma_terminal_prod_fee_rule
(id,acct_month,product_order_id,total_count,terminalvendor,evaluationscore,incentivecoefficient,rule_1_fee,rule_2_fee,final_fee)
SELECT DISTINCT
    a.id,
    a.acct_month,
    a.product_order_id,
    a.total_count,
    a.terminalvendor,
    b.EVALUATIONSCORE,
    b.INCENTIVECOEFFICIENT,
    a.bg_fee*0.3 as rule_1_fee,
    CASE WHEN a.bill_charge = 0 and a.rate_back_id is null THEN 0 ELSE a.total_count * 20 END AS rule_2_fee,
    null
FROM
    stludr.maapnma_terminal_prod_total a
        JOIN stludr.stl_5g_terminal_source b ON lower( a.manufacturer_code ) = lower( b.terminal_brand )
WHERE
    b.TERMINALVENDOR in ('2','6','8','15')
  AND a.ACCT_MONTH = '#inMonth#'
  AND b.ACCT_MONTH = '#inMonth#';



insert into stludr.maapnma_terminal_prod_fee_rule_swap
(product_order_id,terminalvendor,fee1_sum,fee2_sum,fee_rule)
select
    product_order_id,
    terminalvendor,
    sum(rule_1_fee) as fee1_sum,
    sum(rule_2_fee) as fee2_sum,
    case
        when sum(rule_1_fee) >= sum(rule_2_fee) then 1
        else 2
        end as fee_rule
from stludr.maapnma_terminal_prod_fee_rule
where acct_month = '#inMonth#'
group by product_order_id,terminalvendor;

COMMIT;

update stludr.maapnma_terminal_prod_fee_rule a
    inner join stludr.maapnma_terminal_prod_fee_rule_swap b on a.product_order_id = b.product_order_id and a.terminalvendor = b.terminalvendor
    set a.final_fee = case
        when b.fee_rule = 1 then a.rule_1_fee * a.evaluationscore * a.incentivecoefficient
        else a.rule_2_fee * a.evaluationscore * a.incentivecoefficient
    end
where a.acct_month = '#inMonth#';

COMMIT;

-- 取金额大的作为结算金额
update stludr.maapnma_terminal_prod_total a
    inner join stludr.maapnma_terminal_prod_fee_rule b on a.product_order_id = b.product_order_id and a.id=b.id
    set a.settle_fee = b.final_fee
where a.acct_month = '#inMonth#';


-- 更新bg_fee，当是应用规则2的金额时，bg_fee = 20 * total_count
-- 说明 当存在 b.final_fee = b.rule_2_fee =0 时，这时无法区分是bill_charge为0 还是系数为0 导致的，如果是取规则2金额，且bill_charge=0(免费套餐)，则bg_fee = 0
update stludr.maapnma_terminal_prod_total a
    inner join stludr.maapnma_terminal_prod_fee_rule_swap b on a.product_order_id = b.product_order_id and a.terminalvendor = b.terminalvendor
    set  a.bg_fee = CASE
        WHEN b.fee_rule = 2 THEN
            CASE
                WHEN a.bill_charge != 0 THEN 20 * a.total_count
                else 0
            end
        ELSE a.bg_fee
        END
where a.acct_month = '#inMonth#'


update stludr.maapnma_terminal_prod_total a
    inner join stludr.maapnma_terminal_prod_fee_rule b on a.product_order_id = b.product_order_id and a.id=b.id
    set a.settle_fee = b.final_fee,
        a.bg_fee = CASE
        WHEN b.final_fee = b.rule_2_fee THEN
                CASE
                    WHEN b.rule_2_fee != 0 THEN 20 * a.total_count
                    ELSE 0
                END
        ELSE a.bg_fee
        END
where a.acct_month = '#inMonth#'

INSERT /*+ AUTOCOMMIT_DURING_DML() */ INTO SYNC_BL_SETTLE_MID 
SELECT NULL, ticket_id stream_id, 
       'BL' datasource, 
       t.order_mode ordermode, 
       t.acct_month orgmonth, 
       t.ec_code customernumber, 
       t.offer_code pospecnumber, 
       t.product_code sospecnumber, 
       t.offer_order_id poid, 
       t.product_order_id soid, 
       t.charge_item feetype, 
       '' dn, 
       t.settle_fee amount, 
       '2' amount_type, 
       to_char(SYSDATE, 'yyyymmddhh24miss') synctime, 
       '0' status, 
       '' errmesg, 
       t.terminalvendor prov_cd, 
       null accountid, 
       '2' remark 
FROM maapnma_terminal_prod_total t
WHERE acct_month = '#inMonth#';



