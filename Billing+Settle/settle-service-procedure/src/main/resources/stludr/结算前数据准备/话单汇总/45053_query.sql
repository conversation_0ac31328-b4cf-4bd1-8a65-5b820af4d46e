select /*+ hash_join()*/
t.ec_code_prov ec_code,
t.terminal_name,
t.charge_out,
t.total  sum_num,
t.evaluationscore evaluation_score,
t.incentivecoefficient incentive_coefficient,
t.LOCATION location
from (select /*+ hash_join()*/ a.ec_code_prov ,
 f.terminal_name ,
 sum(decode(a.rate_back_id, '1', 54, a.bill_charge)) charge_out,
 count(a.cerca_mark) total,
 b.evaluationscore,
 c.incentivecoefficient,
 t.LOCATION
FROM stludr.UR_MAAPMMA_202502_T a,
stludr.customer_45053_temp t,
(select x.terminalvendor, x.evaluationscore  from 
stlusers.MAAP_TERMINAL_INFO x,
(select max(createdate) createdate, terminalvendor from stlusers.MAAP_TERMINAL_INFO 
	where '202502' between startdate and enddate and (incentivecoefficient is null or incentivecoefficient ='') group by terminalvendor) y
where x.createdate = y.createdate 
	and x.terminalvendor = y.terminalvendor
	and (incentivecoefficient is null or incentivecoefficient ='') 
	and '202502' between x.startdate and x.enddate) b,
(select x.terminalvendor, x.incentivecoefficient from 
stlusers.MAAP_TERMINAL_INFO x,
(select max(createdate) createdate, terminalvendor from stlusers.MAAP_TERMINAL_INFO where 
'202502' between startdate and enddate and (evaluationscore is null or evaluationscore = '') group by terminalvendor) y
where x.createdate = y.createdate
	and x.terminalvendor = y.terminalvendor
	and (x.evaluationscore is null or x.evaluationscore = '')
	and '202502' between x.startdate and x.enddate) c,
stludr.MAAPMMA_VENDOR_CONF f
WHERE a.offer_code = '50034'
AND a.product_code = '5003401'
AND a.cerca_mark = '1'
and a.EC_CODE=t.EC_CODE
and a.manufacturer_code = f.terminal_brand
and f.terminal_vendor = c.terminalvendor
and f.terminal_vendor = b.terminalvendor
group by ec_code_prov, terminal_name,  b.evaluationscore, c.incentivecoefficient,t.LOCATION) t