优化方案：
将步骤17~19独立出来形成一个新的存储过程，处理ur_newrcs的数据，与原存储过程并行调用。
新的存储过程调整：日志表中记录的_18 步骤耗时近1小时。此处调整如下：调整后约耗时30min。


声明两个变量
day_start int;
day_end int;  

 --结算只有3个shard，所以建议分批搞，每次3个分片。31天分成11个批次。循环处理。
 --acct_month,OFFER_CODE,PRODUCT_CODE明确的字段，直接使用常量替代，不放到分组group by里了。group by的字段越多，耗时越多。
  for i in 0 .. 10 loop
    day_start:=3*i+1;
    day_end:=3*i+3;
    
    --idx_offer_product_code 索引去掉，否则加上no_index 的hint。
   
   insert into sync_bl_rule_swap_05
  SELECT /*+ no_index(a idx_offer_product_code)*/ MAX(a.TICKET_ID) ticket_id,
         '202412' ACCT_MONTH,
         a.EC_CODE,
         a.PROD_ORDER_MODE,
         '50020'  OFFER_CODE,
         '5002001' PRODUCT_CODE,
         a.OFFER_ORDER_ID,
         a.PRODUCT_ORDER_ID,
         SUM(a.CHARGE / 10) fee,
         a.CHARGE_CODE,
         null
    FROM UR_NEWRCS_202412_T a
   where a.offer_code = '50020'
     and a.product_code = '5002001'
     and partition_id_day between day_start and day_end
   GROUP BY /*ACCT_MONTH,*/
            EC_CODE,
            PROD_ORDER_MODE,
            /*OFFER_CODE,
            PRODUCT_CODE,*/
            OFFER_ORDER_ID,
            PRODUCT_ORDER_ID,
            charge_code;
       end loop;



--下面语句使用临时表sync_bl_rule_swap_05时，对sync_bl_rule_swap_05 再做一次分组计算。为保证结果一致，此处取round，上面只sum

--参考下面的代替。
(select MAX(a.TICKET_ID) ticket_id,
       acct_month,
       ec_code,
       PROD_ORDER_MODE,
       OFFER_CODE,
       PRODUCT_CODE,
       OFFER_ORDER_ID,
       PRODUCT_ORDER_ID,
       round(sum(fee)),
       charge_code
  from sync_bl_rule_swap_05
  group by  ACCT_MONTH,
            EC_CODE,
            PROD_ORDER_MODE,
            OFFER_CODE,
            PRODUCT_CODE,
            OFFER_ORDER_ID,
            PRODUCT_ORDER_ID,
            charge_code) t


另外注意，显示open 游标后，要close游标，原存储过程中也存在一个游标，也要记得去close 。




大概逻辑如下:细节自行处理。
create procedure ##newname## as
   cursor newcrs_4210 is select subscriber_id, mmm_count from BIZ_MSG_MMM_STL where acct_month = inMonth and SERVICE_CODE ='5002001';

inSoid           number(15);
inCount          integer;
day_start int;
day_end int;

 begin  
   begin
        open newcrs_4210;
        loop
            fetch newcrs_4210 into inSoid, inCount;
            exit when newcrs_4210%NOTFOUND;


            

            execute immediate 'update /*+ AUTOCOMMIT_DURING_DML() */ ur_newrcs_' || inMonth || '_t as t ' ||

                              'set t.rate_back_id = ''1'' ,t.charge=''24'' ,t.charge_code=''Comm_Fee_4210'' where t.product_order_id = ' || inSoid  || ' limit ' || inCount ;

        end loop;
        close newcrs_4210;
    end;
    call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_17', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

    ----阅信(增值能力)
    --step 0 临时表数据清理
    SET @iv_Sql_Insert := 'truncate table sync_bl_rule_swap_05';
    SELECT @iv_Sql_Insert;
    PREPARE STMT FROM @iv_Sql_Insert;
    EXECUTE STMT;
    DEALLOCATE PREPARE STMT;

    --step 1 临时表数据插入
    for i in 0 .. 10 loop    
      day_start:=3*i+1;
      day_end:=3*i+3;
 
      SET @iv_Sql_Insert := 'insert into sync_bl_rule_swap_05 ' ||
                            ' SELECT MAX(a.TICKET_ID) ticket_id, '||inMonth ||'  ACCT_MONTH, a.EC_CODE,  a.PROD_ORDER_MODE, ''50020'' OFFER_CODE,  ''5002001'' PRODUCT_CODE,  a.OFFER_ORDER_ID,  a.PRODUCT_ORDER_ID, ' ||
                            ' SUM(a.CHARGE/10) fee, a.CHARGE_CODE,null  ' ||
                            ' FROM UR_NEWRCS_' || inMonth || '_T a  where a.offer_code = ''50020'' and a.product_code =''5002001''  ' ||
                            ' and partition_id_day between '|| day_start ||'and '||day_end ||
                            ' GROUP BY  EC_CODE, PROD_ORDER_MODE,  OFFER_ORDER_ID, PRODUCT_ORDER_ID,charge_code  ';
      SELECT @iv_Sql_Insert;
      PREPARE STMT FROM @iv_Sql_Insert;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;
    end loop;
    call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_18', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

    SET @iv_Sql_Insert := 'INSERT /*+ AUTOCOMMIT_DURING_DML() */ INTO SYNC_BL_SETTLE_MID ' ||
                          'SELECT ticket_id stream_id, '||
                          '''BL'' datasource,  '||
                          't.prod_order_mode ordermode, '||
                          't.acct_month orgmonth,    '||
                          't.ec_code customernumber,  '||
                          't.offer_code pospecnumber,   '||
                          't.product_code sospecnumber,    '||
                          't.offer_order_id poid,        '||
                          't.product_order_id soid,   '||
                          'c.charge_item_ref feetype,     '||
                          'NULL dn,      '||
                          't.fee amount,    '||
                          '''2'' amount_type,  '||
                          'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime,   '||
                          '''0'' status, '||
                          'NULL errmesg,   '||
                          'NULL prov_cd,   '||
                          'NULL accountid,   '||
                          '''2'' remark      '||
                          'FROM (select MAX(a.TICKET_ID) ticket_id,
                                 acct_month,
                                 ec_code,
                                 PROD_ORDER_MODE,
                                 OFFER_CODE,
                                 PRODUCT_CODE,
                                 OFFER_ORDER_ID,
                                 PRODUCT_ORDER_ID,
                                 round(sum(fee)),
                                 charge_code
                                from sync_bl_rule_swap_05
                                 group by  ACCT_MONTH,
                                  EC_CODE,
                             PROD_ORDER_MODE,
                            OFFER_CODE,
                            PRODUCT_CODE,
                            OFFER_ORDER_ID,
                            PRODUCT_ORDER_ID,
                             charge_code) t, stlusers.STL_CHARGE_ITEM_DEF c WHERE t.charge_code = c.charge_item';
    SELECT @iv_Sql_Insert;
    PREPARE STMT FROM @iv_Sql_Insert;
    EXECUTE STMT;
    DEALLOCATE PREPARE STMT;
    call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_19', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));
    
    end;
    
    
 