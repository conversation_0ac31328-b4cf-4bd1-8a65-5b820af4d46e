/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：结算前数据准备-CDN数据源计算-CDN华为结算金额计算（95峰）
**/
use stludr;
DROP PROCEDURE IF EXISTS stludr.`PROC_CDN_RATE`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE stludr.`PROC_CDN_RATE`(
                                          acctMonth IN VARCHAR2,
                                          nReturn   OUT NUMBER,
                                          szSysErr  OUT VARCHAR2
)
AS

    v_proc_name       VARCHAR2(30) := 'PROC_CDN_RATE';
    totalDays      NUMBER(2); --账期月总天数
    peakIndex      NUMBER(5); --峰值索引
    nextMonth      VARCHAR2(6); --账期月下一月
    acct_month_tbl VARCHAR2(20); --账期月UR表名
    next_month_tbl VARCHAR2(20); --账期月下月UR表名
    vSql           VARCHAR2(10240);

BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
ROLLBACK;


select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
END;

BEGIN


        acct_month_tbl := 'ur_cdn_' || acctMonth || '_t';

        --计算账期月总天数(=账期月总天数)
SELECT to_number(to_char(last_day(to_date(acctMonth, 'yyyyMM')), 'dd'))
INTO totalDays
FROM dual;

--计算峰值索引
SELECT ceil(288 * totalDays * 0.05) + 1 INTO peakIndex FROM dual;
--peakIndex := 1;

--账期月下月
SELECT to_char(add_months(to_date(acctMonth, 'yyyyMM'), 1), 'yyyyMM')
INTO nextMonth
FROM dual;

next_month_tbl := 'ur_cdn_' || nextMonth || '_t';

        --step 0 临时数据清理
        SET @vSql := 'TRUNCATE TABLE stludr.cdn_rate_swap_00_t';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

SET @vSql := 'TRUNCATE TABLE stludr.cdn_rate_swap_01';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

SET @vSql := 'TRUNCATE TABLE stludr.cdn_rate_swap_01_t';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

SET @vSql := 'TRUNCATE TABLE stludr.cdn_rate_swap_02_t';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

SET @vSql := 'TRUNCATE TABLE stludr.cdn_rate_swap_03_t';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

SET @vSql := 'TRUNCATE TABLE stludr.cdn_rate_swap_04_t';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

SET @vSql := 'TRUNCATE TABLE stludr.cdn_rate_swap_05_t';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

SET @vSql := 'TRUNCATE TABLE stludr.cdn_settle95_' || acctMonth;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

SET @vSql := 'DELETE FROM stludr.stl_cdn_total_fee WHERE acct_month =' ||
                acctMonth;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

SET @vSql := 'DELETE FROM stludr.stl_cdn_order_fee WHERE acct_month =' ||
                acctMonth;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

SELECT 'mark0';
SET @vSql := 'INSERT INTO stludr.cdn_rate_swap_00_t ' ||
						' SELECT DISTINCT null, t1.pospecnumber, ' ||
						' 							t1.sospecnumber, ' ||
						' 							t1.poid, ' ||
						' 							t1.soid, ' ||
						' 							t1.attr_value    business_type, ' ||
						' 							t2.attr_value    product_type, ' ||
						' 							t3.sub_group_num, ' ||
						' 							t3.rate_back_id, ' ||
						' 							t3.price ' ||
						' FROM (SELECT * ' ||
						' 				FROM stlusers.stl_sync_attr ' ||
						' 			 WHERE attr_id = ''***********'') t1  ' || -- 业务类型
						' LEFT JOIN (SELECT * ' ||
						' 						 FROM stlusers.stl_sync_attr  ' ||
						' 						WHERE attr_id = ''***********'') t2  ' || -- 产品类型
						' 	ON t1.poid = t2.poid ' ||
						'  AND t1.soid = t2.soid ' ||
						' LEFT JOIN stludr.cdn_cfg_t t3 ' ||
						' 	ON t1.attr_value = t3.business_type ' ||
						'  AND t2.attr_value = t3.product_type' ||
						' WHERE t1.attrstartmonth <= ' ||acctMonth||
						' AND t1.attrendmonth >= ' ||acctMonth||
						' AND t2.attrstartmonth <= ' ||acctMonth||
						' AND t2.attrendmonth >= ' ||acctMonth;

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

SELECT 'mark1';

--按订购、开始时间汇总 华为网内、网外 流量
--月底最后1天部分话单可能会入到下一账期月详单表中，所以取流量时需要关联本账期月详单表和下一账期月详单表。
--网内网外通过字段rate_back_id区别：1-网内、2-网外
--是否华为平面通过sub_group_num字段区分：1-华为、2-中兴、3-杭研
SET @vSql := 'INSERT INTO stludr.cdn_rate_swap_01  SELECT ' ||
            ' null,  ' ||
            ' t1.ec_code,  ' ||
            ' t1.offer_order_id, ' ||
            ' t1.product_order_id, ' ||
            ' t1.offer_code, ' ||
            ' t1.product_code, ' ||
            ' t1.order_mode, ' ||
            ' t1.sub_group_num, ' ||
            ' SUM(NVL(t1.accu_volume, 0)), ' ||
            ' SUM(NVL(t1.accu_duration, 0)), ' ||
            ' SUM(NVL(t1.accu_volume, 0)) + SUM(NVL(t1.accu_duration, 0)), ' ||
            ' t1.dup_time begin_time, ' ||
            ' t1.rate_back_id, ' ||
            ' null ' ||
            ' FROM (SELECT * FROM stludr.' ||  acct_month_tbl || ' where sub_group_num=''1''  and dup_time like ''' || acctMonth || '0%''  ) t1 ' ||
            ' GROUP BY t1.ec_code, t1.offer_order_id, t1.product_order_id, t1.offer_code, t1.product_code, t1.order_mode, t1.sub_group_num,  t1.cdr_type, t1.dup_time, t1.rate_back_id';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

SELECT 'mark1.1';

SET @vSql := 'INSERT INTO stludr.cdn_rate_swap_01  SELECT ' ||
            ' null,  ' ||
            ' t1.ec_code,  ' ||
            ' t1.offer_order_id, ' ||
            ' t1.product_order_id, ' ||
            ' t1.offer_code, ' ||
            ' t1.product_code, ' ||
            ' t1.order_mode, ' ||
            ' t1.sub_group_num, ' ||
            ' SUM(NVL(t1.accu_volume, 0)), ' ||
            ' SUM(NVL(t1.accu_duration, 0)), ' ||
            ' SUM(NVL(t1.accu_volume, 0)) + SUM(NVL(t1.accu_duration, 0)), ' ||
            ' t1.dup_time begin_time, ' ||
            ' t1.rate_back_id, ' ||
            ' null ' ||
            ' FROM (SELECT * FROM stludr.' ||  acct_month_tbl || ' where sub_group_num=''1''  and dup_time like ''' || acctMonth || '1%''  ) t1 ' ||
            ' GROUP BY t1.ec_code, t1.offer_order_id, t1.product_order_id, t1.offer_code, t1.product_code, t1.order_mode, t1.sub_group_num,  t1.cdr_type, t1.dup_time, t1.rate_back_id';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

SELECT 'mark1.2';

SET @vSql := 'INSERT INTO stludr.cdn_rate_swap_01  SELECT ' ||
            ' null,  ' ||
            ' t1.ec_code,  ' ||
            ' t1.offer_order_id, ' ||
            ' t1.product_order_id, ' ||
            ' t1.offer_code, ' ||
            ' t1.product_code, ' ||
            ' t1.order_mode, ' ||
            ' t1.sub_group_num, ' ||
            ' SUM(NVL(t1.accu_volume, 0)), ' ||
            ' SUM(NVL(t1.accu_duration, 0)), ' ||
            ' SUM(NVL(t1.accu_volume, 0)) + SUM(NVL(t1.accu_duration, 0)), ' ||
            ' t1.dup_time begin_time, ' ||
            ' t1.rate_back_id, ' ||
            ' null ' ||
            ' FROM (SELECT * FROM stludr.' ||  acct_month_tbl || ' where sub_group_num=''1''  and dup_time like ''' || acctMonth || '2%''  ) t1 ' ||
            ' GROUP BY t1.ec_code, t1.offer_order_id, t1.product_order_id, t1.offer_code, t1.product_code, t1.order_mode, t1.sub_group_num,  t1.cdr_type, t1.dup_time, t1.rate_back_id';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

SELECT 'mark1.3';

SET @vSql := 'INSERT INTO stludr.cdn_rate_swap_01  SELECT ' ||
            ' null,  ' ||
            ' t1.ec_code,  ' ||
            ' t1.offer_order_id, ' ||
            ' t1.product_order_id, ' ||
            ' t1.offer_code, ' ||
            ' t1.product_code, ' ||
            ' t1.order_mode, ' ||
            ' t1.sub_group_num, ' ||
            ' SUM(NVL(t1.accu_volume, 0)), ' ||
            ' SUM(NVL(t1.accu_duration, 0)), ' ||
            ' SUM(NVL(t1.accu_volume, 0)) + SUM(NVL(t1.accu_duration, 0)), ' ||
            ' t1.dup_time begin_time, ' ||
            ' t1.rate_back_id, ' ||
            ' null ' ||
            ' FROM (SELECT * FROM stludr.' ||  acct_month_tbl || ' where sub_group_num=''1''  and dup_time like ''' || acctMonth || '3%''  ) t1 ' ||
            ' GROUP BY t1.ec_code, t1.offer_order_id, t1.product_order_id, t1.offer_code, t1.product_code, t1.order_mode, t1.sub_group_num,  t1.cdr_type, t1.dup_time, t1.rate_back_id';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

SELECT 'mark1.4';

SET @vSql := 'INSERT INTO stludr.cdn_rate_swap_01  SELECT ' ||
            ' null,  ' ||
            ' t1.ec_code,  ' ||
            ' t1.offer_order_id, ' ||
            ' t1.product_order_id, ' ||
            ' t1.offer_code, ' ||
            ' t1.product_code, ' ||
            ' t1.order_mode, ' ||
            ' t1.sub_group_num, ' ||
            ' SUM(NVL(t1.accu_volume, 0)), ' ||
            ' SUM(NVL(t1.accu_duration, 0)), ' ||
            ' SUM(NVL(t1.accu_volume, 0)) + SUM(NVL(t1.accu_duration, 0)), ' ||
            ' t1.dup_time begin_time, ' ||
            ' t1.rate_back_id, ' ||
            ' null ' ||
            ' FROM (SELECT * FROM stludr.' ||  next_month_tbl ||  ' where sub_group_num=''1'' and dup_time like ''' || acctMonth || '%'' ) t1 ' ||
            ' GROUP BY t1.ec_code, t1.offer_order_id, t1.product_order_id, t1.offer_code, t1.product_code, t1.order_mode, t1.sub_group_num,  t1.cdr_type, t1.dup_time, t1.rate_back_id';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

SELECT 'mark1.5';

SET @vSql := 'insert into stludr.cdn_rate_swap_01_t   select  ' ||
            ' null,        ' ||
            ' t1.ec_code,        ' ||
            ' t1.offer_order_id, ' ||
            ' t1.product_order_id, ' ||
            ' t1.offer_code,    ' ||
            ' t1.product_code,  ' ||
            ' t1.order_mode,    ' ||
            ' t1.sub_group_num,  ' ||
            ' sum(t1.sum_order_volume),  ' ||
            ' sum(t1.sum_order_duration), ' ||
            ' sum(t1.sum_order_flow), ' ||
            ' t1.begin_time,    ' ||
            ' t1.rate_back_id,   ' ||
            ' t2.product_type,   ' ||
            ' t2.business_type,   ' ||
            ' t2.price,   ' ||
            ' null  ' ||
            ' from stludr.cdn_rate_swap_01 t1 LEFT JOIN stludr.cdn_rate_swap_00_t t2 ' ||
            ' on trim(t1.offer_order_id) = trim(t2.poid)
							 and trim(t1.product_order_id) = trim(t2.soid)
							 and trim(t1.sub_group_num) = trim(t2.sub_group_num)
							 and trim(t1.rate_back_id) = trim(t2.rate_back_id) ' ||
            ' group by ' ||
            ' t1.ec_code, ' ||
            ' t1.offer_order_id,  ' ||
            ' t1.product_order_id,  ' ||
            ' t1.offer_code, ' ||
            ' t1.product_code,  ' ||
            ' t1.order_mode,  ' ||
            ' t1.sub_group_num,  ' ||
            ' t1.begin_time,  ' ||
            ' t1.rate_back_id,'
            ' t2.product_type,'
            ' t2.business_type,'
            ' t2.price';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

/*   SELECT 'mark2';

 -- 更新EBOSS客户编码
         MERGE INTO stludr.cdn_rate_swap_01_t t1
         USING (SELECT DISTINCT eboss_customer_code,
                                                      prod_order_id,
                                                      order_id,
                                                      service_code,
                                                      customer_type
                          FROM stludr.cust_prod_info
                         WHERE acct_month = acctMonth) t2
         ON (trim(t1.offer_order_id) = trim(t2.prod_order_id) AND trim(t1.product_order_id) = trim(t2.order_id) AND trim(t1.product_code) = trim(t2.service_code))
         WHEN MATCHED THEN
             UPDATE
                  SET t1.ec_code       = t2.eboss_customer_code,
                          t1.customer_type = t2.customer_type;

         SELECT 'mark2.1';*/
-- 20240417 万里性能优化
SELECT 'mark2';
update STLUDR.CDN_RATE_SWAP_01_T T1
    join STLUDR.CUST_PROD_INFO T2 on T1.OFFER_ORDER_ID = TRIM(T2.PROD_ORDER_ID) AND T1.PRODUCT_ORDER_ID = TRIM(T2.ORDER_ID) AND T1.PRODUCT_CODE = TRIM(T2.SERVICE_CODE) and t2.ACCT_MONTH =  acctMonth
    SET T1.EC_CODE       = T2.EBOSS_CUSTOMER_CODE,
        T1.CUSTOMER_TYPE = T2.CUSTOMER_TYPE;
SELECT 'mark2.1';

-- EBOSS三网点播 网内
update stludr.cdn_rate_swap_01_t t
set t.product_type = '2', t.business_type = '1', t.price = 0
where t.product_code = '9200397'
  and t.sub_group_num = '1'
  and t.rate_back_id = '1';

SELECT 'mark2.2';
-- EBOSS三网点播 网外
update stludr.cdn_rate_swap_01_t t
set t.product_type = '2', t.business_type = '1', t.price = 6700
where t.product_code = '9200397'
  and t.sub_group_num = '1'
  and t.rate_back_id = '2';

SELECT 'mark3';

--峰值列表
INSERT INTO stludr.cdn_rate_swap_02_t
SELECT
    null,
    t.sub_group_num,
    t.rate_back_id,
    t.product_type,
    t.business_type,
    t.price,
    t.begin_time,
    SUM(t.sum_order_volume),
    SUM(t.sum_order_duration),
    SUM(t.sum_order_flow),
    row_number() OVER(PARTITION BY t.sub_group_num, t.rate_back_id, t.product_type, t.business_type ORDER BY SUM(t.sum_order_volume) DESC) raw_row_number
FROM stludr.cdn_rate_swap_01_t t
WHERE t.product_type is not null
  AND t.business_type is not null
GROUP BY t.sub_group_num,
         t.rate_back_id,
         t.product_type,
         t.business_type,
         t.price,
         t.begin_time;

SELECT 'mark4';

--2022-08新增：存储对应账期的每5分钟的95峰值。方便后期CDN业务三方（合作方客户、网络部和BBOSS）对账
SET @vSql := 'INSERT INTO stludr.cdn_settle95_' || acctMonth || '  SELECT NULL,raw_row_number, begin_time, total_volume, ceil(total_volume * 8 / 1024 / 300), rate_back_id,product_type, business_type FROM stludr.cdn_rate_swap_02_t';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

SELECT 'mark5';

--依据索引 peakIndex 取95峰值、单位转换（CDN流量话单单位是KB, 速率单位是Mbps，转换公式：KB/1024/300*8 转换成Mbps）
--band_width: 带宽
INSERT INTO stludr.cdn_rate_swap_03_t
SELECT null,
       sub_group_num,
       rate_back_id,
       product_type,
       business_type,
       price,
       begin_time,
       total_volume,
       total_duration,
       total_flow,
       raw_row_number,
       ceil(total_volume * 8 / 1024 / 300), --KB 转 Mbps
       ceil(total_volume * 8 / 1024 / 300) * price --费用(厘)
FROM stludr.cdn_rate_swap_02_t t
WHERE t.raw_row_number = peakIndex;

SELECT 'mark6';

--按订购汇总流量
INSERT INTO stludr.cdn_rate_swap_04_t
SELECT
    null,
    t.ec_code,
    t.offer_order_id,
    t.product_order_id,
    t.offer_code,
    t.product_code,
    t.order_mode,
    t.sub_group_num,
    t.rate_back_id,
    t.product_type,
    t.business_type,
    SUM(t.sum_order_volume) accu_volume,
    SUM(t.sum_order_duration) accu_duration,
    SUM(t.sum_order_flow) order_total_flow,
    t.customer_type
FROM stludr.cdn_rate_swap_01_t t
WHERE t.product_type is not null
  AND t.business_type is not null
GROUP BY t.ec_code,
         t.offer_order_id,
         t.product_order_id,
         t.offer_code,
         t.product_code,
         t.order_mode,
         t.sub_group_num,
         t.rate_back_id,
         t.product_type,
         t.business_type,
         t.customer_type;

SELECT 'mark7';
--此语句导致dn发生crash，先对语句进行了拆分执行。


--中间表存储group by的结果。

truncate table cdn_rate_swap_02_t_mid;

insert into cdn_rate_swap_02_t_mid
SELECT null,
       sub_group_num,
       rate_back_id,
       product_type,
       business_type,
       SUM(total_volume) total_flow_sum
FROM stludr.cdn_rate_swap_02_t
GROUP BY sub_group_num,
         rate_back_id,
         product_type,
         business_type;

INSERT INTO stludr.cdn_rate_swap_05_t
SELECT
    null,
    t2.ec_code,
    t2.offer_order_id,
    t2.product_order_id,
    t2.offer_code,
    t2.product_code,
    t2.order_mode,
    t1.sub_group_num,
    t1.rate_back_id,
    t1.product_type,
    t1.business_type,
    round(t0.total_amount / 10),
    t2.total_order_volume,
    t1.total_flow_sum,
    t2.total_order_volume / decode(t1.total_flow_sum, 0, 1, t1.total_flow_sum),
    row_number() OVER(PARTITION BY t1.sub_group_num, t1.rate_back_id, t1.product_type, t1.business_type ORDER BY t2.total_order_flow / decode(t1.total_flow_sum, 0, 1, t1.total_flow_sum) DESC) raw_row_number,
        -1,

    t2.customer_type
FROM stludr.cdn_rate_swap_03_t t0





         LEFT JOIN stludr.cdn_rate_swap_02_t_mid t1




                   ON t0.sub_group_num = t1.sub_group_num
                       AND t0.rate_back_id = t1.rate_back_id
                       AND t0.product_type = t1.product_type
                       AND t0.business_type = t1.business_type
         LEFT JOIN stludr.cdn_rate_swap_04_t t2
                   ON t0.sub_group_num = t2.sub_group_num
                       AND t0.rate_back_id = t2.rate_back_id
                       AND t0.product_type = t2.product_type
                       AND t0.business_type = t2.business_type
WHERE t2.product_order_id IS NOT NULL
  AND t1.total_flow_sum != 0;

SELECT 'mark8';

--计算非最大占比订购金额
UPDATE stludr.cdn_rate_swap_05_t t
SET t.order_amount = round(t.total_amount * t.percent)
WHERE t.raw_row_number > 1;

--计算最大占比订购金额(总金额 - 非最大占比订购金额之和)
UPDATE stludr.cdn_rate_swap_05_t t1
SET t1.order_amount =
        (SELECT t2.order_amount
         FROM (SELECT sub_group_num,
                      rate_back_id,
                      product_type,
                      business_type,
                      round(MAX(total_amount) - SUM(order_amount)) order_amount
               FROM stludr.cdn_rate_swap_05_t
               WHERE raw_row_number > 1
               GROUP BY sub_group_num, rate_back_id, product_type, business_type) t2
         WHERE t1.sub_group_num = t2.sub_group_num
           AND t1.rate_back_id = t2.rate_back_id
           AND t1.product_type = t2.product_type
           AND t1.business_type = t2.business_type)
WHERE t1.raw_row_number = 1;

SELECT 'mark9';

--总金额结果表
INSERT INTO stludr.stl_cdn_total_fee
SELECT NULL,acctMonth,
       round(t1.total_amount / 10), --华为网内CDN结算总金额
       round(t2.total_amount / 10), --华为网外CDN结算总金额
       t1.product_type, --产品类型
       t1.business_type --业务类型
FROM (SELECT sub_group_num, rate_back_id,  product_type, business_type, total_amount
      FROM stludr.cdn_rate_swap_03_t
      WHERE rate_back_id = '1') t1 --网内
         LEFT JOIN (SELECT sub_group_num, rate_back_id, product_type, business_type, total_amount
                    FROM stludr.cdn_rate_swap_03_t
                    WHERE rate_back_id = '2') t2 --网外
                   ON t1.sub_group_num = t2.sub_group_num
                       AND t1.product_type = t2.product_type
                       AND t1.business_type = t2.business_type;


--按订购结算
--既有网内流量又有网外流量订购
INSERT INTO stludr.stl_cdn_order_fee
SELECT NULL,acctMonth,
       t1.order_mode,
       t1.ec_code,
       t1.offer_code,
       t1.product_code,
       t1.offer_order_id,
       t1.product_order_id,
       '16',
       t1.order_amount, --订购华为网内CDN结算金额
       t2.order_amount, --订购华为网外CDN结算金额
       t1.customer_type
FROM (SELECT * FROM stludr.cdn_rate_swap_05_t WHERE rate_back_id = '1') t1 --网内
         INNER JOIN (SELECT *
                     FROM stludr.cdn_rate_swap_05_t
                     WHERE rate_back_id = '2') t2 --网外
                    ON t1.offer_order_id = t2.offer_order_id
                        AND t1.product_order_id = t2.product_order_id
                        AND t1.product_type = t2.product_type
                        AND t1.business_type = t2.business_type;

SELECT 'mark10';

--只有网内流量订购
INSERT INTO stludr.stl_cdn_order_fee
SELECT NULL,acctMonth,
       t1.order_mode,
       t1.ec_code,
       t1.offer_code,
       t1.product_code,
       t1.offer_order_id,
       t1.product_order_id,
       '16',
       t1.order_amount, --订购华为网内CDN结算金额
       0, --订购华为网外CDN结算金额（如果订购只有网内流量，且结算金额不为0，则此订购网外结算金额为0）
       t1.customer_type
FROM (SELECT * FROM stludr.cdn_rate_swap_05_t WHERE rate_back_id = '1') t1 --网内
         LEFT JOIN (SELECT *
                    FROM stludr.cdn_rate_swap_05_t
                    WHERE rate_back_id = '2') t2 --网外
                   ON t1.offer_order_id = t2.offer_order_id
                       AND t1.product_order_id = t2.product_order_id
WHERE t1.order_amount != 0 --过滤掉网内结算金额为0的数据（除以10转成分后再过滤，因为当对应订购计算金额比较小时转换为分后再四舍五入结果可能为0，这样结果表中就为0，如3.2厘）
             AND t2.product_order_id IS NULL;

SELECT 'mark11';

--只有网外流量订购
INSERT INTO stludr.stl_cdn_order_fee
SELECT NULL,acctMonth,
       t2.order_mode,
       t2.ec_code,
       t2.offer_code,
       t2.product_code,
       t2.offer_order_id,
       t2.product_order_id,
       '16',
       0, --订购华为网内CDN结算金额（如果订购只有网外流量，且结算金额不为0，则此订购网内结算金额为0）
       t2.order_amount, --订购华为网外CDN结算金额
       t2.customer_type
FROM (SELECT * FROM stludr.cdn_rate_swap_05_t WHERE rate_back_id = '1') t1 --网内
         RIGHT JOIN (SELECT *
                     FROM stludr.cdn_rate_swap_05_t
                     WHERE rate_back_id = '2') t2 --网外
                    ON t1.offer_order_id = t2.offer_order_id
                        AND t1.product_order_id = t2.product_order_id
WHERE t2.order_amount != 0 --过滤掉网外结算金额为0的数据
             AND t1.product_order_id IS NULL;


COMMIT;

szSysErr := 'OK';
        nReturn := 0;

SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || to_number(nReturn);
END;

END ;;
DELIMITER ;