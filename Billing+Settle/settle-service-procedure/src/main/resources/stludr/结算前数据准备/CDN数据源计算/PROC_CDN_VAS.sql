/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：结算前数据准备-CDN数据源计算-CDN华为结算金额计算（超低时延直播）
**/
use stludr;
DROP PROCEDURE IF EXISTS stludr.`PROC_CDN_VAS`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE stludr.`PROC_CDN_VAS`(acctMonth IN VARCHAR2,
                                          nReturn   OUT NUMBER,
                                          szSysErr  OUT VARCHAR2)
As


  vSql           VARCHAR2(10240);
  outReturn      int;
  v_proc_name    VARCHAR2(30) := 'PROC_CDN_VAS';
  nextMonth VARCHAR2(6);
BEGIN

    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        szSysErr := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;

    BEGIN
       	SELECT to_char(add_months(to_date(acctMonth, 'yyyyMM'), 1), 'yyyyMM')
    	INTO nextMonth
    	FROM dual;

	    SET @vSql := 'DELETE FROM CDN_VAS_INFO WHERE ACCT_MONTH = '|| acctMonth ;
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
        COMMIT;

        outReturn  := 0;
        szSysErr := 'OK';
    END;

    BEGIN
        SET @vSql := 'INSERT INTO CDN_VAS_INFO '||
                'SELECT '||
                    'null, ' ||
                    '''' || acctMonth || ''' ACCT_MONTH, ' ||
                    'a.EC_CODE,' ||
                    'a.PROD_ORDER_MODE as ORDER_MODE,' ||
                    'a.OFFER_ORDER_ID,' ||
                    'a.PRODUCT_ORDER_ID,' ||
                    'a.OFFER_CODE,' ||
                    'a.PRODUCT_CODE,' ||
                    'a.BUSI_TYPE,' ||
                    'a.RESOURCE_SPECODE,' ||
                    '(CASE ' ||
                    '    WHEN a.RESOURCE_SPECODE = ''101'' and a.product_order_id = ''60000003446'' THEN' ||
                    '        b.BYTE_SETTLE_PRICE * MAX(to_number(a.DATA_VALUE))' ||
                    '    WHEN a.RESOURCE_SPECODE = ''101'' and a.product_order_id != ''60000003446'' THEN' ||
                    '        b.SETTLE_PRICE * MAX(to_number(a.DATA_VALUE)) ' ||
                    '    WHEN a.RESOURCE_SPECODE = ''201'' and a.product_order_id = ''60000003446'' THEN' ||
                    '        b.BYTE_SETTLE_PRICE * CEIL(sum(a.DATA_VALUE)/1000) ' ||
                    '    WHEN a.RESOURCE_SPECODE = ''201'' and a.product_order_id != ''60000003446'' THEN' ||
                    '        b.SETTLE_PRICE * CEIL(sum(a.DATA_VALUE)/1000)  ' ||
                    '    WHEN a.RESOURCE_SPECODE in(''306'',''307'',''308'',''309'',''310'',''320'',''319'',''318'',''317'',''316'',''305'',''304'',''303'',''302'',''301'',''311'',''312'',''313'',''314'',''315'') and a.product_order_id = ''60000003446'' THEN ' ||
                    '        b.BYTE_SETTLE_PRICE * CEIL(sum(a.DATA_VALUE)) ' ||
                    '    WHEN a.RESOURCE_SPECODE in(''306'',''307'',''308'',''309'',''310'',''320'',''319'',''318'',''317'',''316'',''305'',''304'',''303'',''302'',''301'',''311'',''312'',''313'',''314'',''315'') and a.product_order_id != ''60000003446'' THEN ' ||
                    '        b.SETTLE_PRICE * CEIL(sum(a.DATA_VALUE))' ||
                    '    WHEN a.RESOURCE_SPECODE in(''407'',''409'',''412'',''413'',''416'',''408'',''421'') THEN' ||
                    '        b.SETTLE_PRICE * CEIL((sum(a.DATA_VALUE)/1024/1024/1024))' ||
                    '    WHEN a.RESOURCE_SPECODE in(''403'',''431'',''432'',''405'',''406'',''417'',''418'',''422'',''423'') THEN' ||
                    '        b.SETTLE_PRICE * CEIL((sum(a.DATA_VALUE)/1024/1024/1024))' ||
                    '    WHEN a.RESOURCE_SPECODE in(''410'',''411'',''414'',''415'',''419'',''420'') THEN' ||
                    '        b.SETTLE_PRICE * CEIL(sum(a.DATA_VALUE)/10000)' ||
                    'END * 10000) as DATA_VALUE,' ||
                    'a.RESOURCE_SPECODE fee_type ' ||
                'FROM (SELECT *
FROM (SELECT t1.*,
             row_number() OVER (PARTITION BY PRODUCT_ORDER_ID,RESOURCE_SPECODE,substr(begin_time,1,6)  ORDER BY dup_time DESC) rn
      FROM (SELECT *
            FROM stludr.UR_CDNVAS_' || acctMonth || '_T where substr(begin_time,1,6)  = ' || acctMonth ||
            ' and PRODUCT_CODE !=''2023999400085020'' ' ||
            ' UNION ALL
            SELECT *
            FROM stludr.UR_CDNVAS_' || nextMonth || '_T where substr(begin_time,1,6)  = ' || acctMonth ||  ' ' ||
            ' and PRODUCT_CODE !=''2023999400085020'') t1)  ' ||
' WHERE rn = 1
  AND  substr(begin_time,1,6) = ' || acctMonth ||
            ' and RESOURCE_SPECODE IN
                (''410'', ''411'', ''414'', ''415'', ''419'', ''420'', ''403'', ''405'', ''406'', ''417'', ''418'', ''422'', ''423'', ''407'',
                 ''409'', ''412'', ''413'', ''416'', ''408'', ''421'',''431'',''432'')
          UNION ALL
          SELECT t2.*, 1
          FROM (SELECT *
                FROM stludr.UR_CDNVAS_' || acctMonth || '_T where substr(begin_time,1,6)  = ' || acctMonth ||
            ' and PRODUCT_CODE !=''2023999400085020'' ' ||
            ' UNION ALL
             SELECT *
             FROM stludr.UR_CDNVAS_' || nextMonth || '_T where substr(begin_time,1,6)  = ' || acctMonth ||
			' and PRODUCT_CODE !=''2023999400085020'' ) t2  ' ||
            ' WHERE substr(begin_time,1,6) = ' || acctMonth ||
            ' AND  t2.RESOURCE_SPECODE NOT IN
      (''410'', ''411'', ''414'', ''415'', ''419'', ''420'', ''403'', ''405'', ''406'', ''417'', ''418'', ''422'', ''423'', ''407'',
       ''409'', ''412'', ''413'', ''416'', ''408'', ''421'',''431'',''432'')) a ' ||
            '    JOIN stludr.CDN_VAS_DICT b ' ||
            '    ON a.BUSI_TYPE = b.BUSI_TYPE ' ||
            '    AND a.RESOURCE_SPECODE = b.RESOURCE_SPECODE ' ||
            ' INNER JOIN stlusers.STL_ARCHIVE_WHITE_T c' ||
            ' on a.EC_CODE = c.CUSTOMER_NUM ' ||
            ' AND a.PRODUCT_ORDER_ID = c. PRODIST_SKU_NUM ' ||
            'GROUP BY ' ||
            '    a.EC_CODE, ' ||
            '    a.PROD_ORDER_MODE, ' ||
            '    a.OFFER_CODE, ' ||
            '    a.PRODUCT_CODE, ' ||
            '    a.OFFER_ORDER_ID, ' ||
            '    a.PRODUCT_ORDER_ID, ' ||
            '    a.BUSI_TYPE, ' ||
            '    a.RESOURCE_SPECODE, ' ||
            '    b.BYTE_SETTLE_PRICE, ' ||
            '    b.SETTLE_PRICE ';

        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        COMMIT;
        outReturn  := 0;
        szSysErr := 'OK';

				SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
		END;

END ;;
DELIMITER ;