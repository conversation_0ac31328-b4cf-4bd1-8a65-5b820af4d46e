/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：结算前数据准备-CDN数据源计算-CDN全站加速95峰
**/
use stludr;
DROP PROCEDURE IF EXISTS stludr.`PROC_CDNAPPEND_RATE`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE stludr.`PROC_CDNAPPEND_RATE`(acctMonth IN VARCHAR2,
                                   nReturn OUT NUMBER,
                                   szSysErr OUT VARCHAR2)
As

    totalDays        NUMBER(2); --账期月总天数
    peakIndex        NUMBER(5); --峰值索引
    nextMonth        VARCHAR2(6); --账期月下一月
    acct_month_tbl   VARCHAR2(20); --账期月UR表名
    next_month_tbl   VARCHAR2(20); --账期月下月UR表名
    vSql             VARCHAR2(10240);
    up_flow_unit     NUMBER(16, 2);
    down_flow_unit7  NUMBER(16, 2);
    down_flow_unit4  NUMBER(16, 2);
    request_num_unit NUMBER(16, 4);
    ip_num_unit      NUMBER(16, 2);
    v_proc_name    VARCHAR2(30) := 'PROC_CDNAPPEND_RATE';

begin
	nReturn := 0;
    up_flow_unit := 9000.00;
    down_flow_unit7 := 9000.00;
    down_flow_unit4 := 55000.00;
    request_num_unit := 3;
    ip_num_unit := 50000.00;

    DECLARE EXIT
	HANDLER FOR SQLEXCEPTION
BEGIN
GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
szSysErr := substr(@p2, 1, 1000);
	nReturn  := -1;
ROLLBACK;

--  日志写表
select ('exception: ' || nReturn || '|' || @p1 || '|' || szSysErr) AS error_msg;
END;
BEGIN
    --计算账期月总天数(=账期月总天数)
SELECT to_number(to_char(last_day(to_date(acctMonth, 'yyyyMM')), 'dd'))
INTO totalDays
FROM dual;

--计算峰值索引
SELECT CEIL(288 * totalDays * 0.05) + 1 INTO peakIndex FROM dual;
--peakIndex := 3;

--账期月下月
SELECT to_char(add_months(to_date(acctMonth, 'yyyyMM'), 1), 'yyyyMM')
INTO nextMonth
FROM dual;

--next_month_tbl := 'ur_cdnappend_' || nextMonth || '_t';

--step 0 临时数据清理
set @vSql := 'TRUNCATE TABLE stludr.cdnappend_rate_swap_00_t';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
deallocate prepare STMT;

set @vSql := 'TRUNCATE TABLE stludr.cdnappend_rate_swap_01_t';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
deallocate prepare STMT;

set @vSql := 'TRUNCATE TABLE stludr.cdnappend_rate_swap_02_t';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
deallocate prepare STMT;

set @vSql := 'TRUNCATE TABLE stludr.cdnappend_rate_swap_03_t';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
deallocate prepare STMT;

set @vSql := 'TRUNCATE TABLE stludr.cdnappend_rate_swap_04_t';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
deallocate prepare STMT;

set @vSql := 'TRUNCATE TABLE stludr.cdnappend_rate_swap_05_t';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
deallocate prepare STMT;

set @vSql := 'TRUNCATE TABLE stludr.cdnappend_rate_swap_06_t';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
deallocate prepare STMT;

set @vSql := 'TRUNCATE TABLE  stludr.stl_cdnappend_swap_prod_flow';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
deallocate prepare STMT;

set @vSql := 'TRUNCATE TABLE stludr.stl_cdnappend_swap_total_flow';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
deallocate prepare STMT;

set @vSql := 'DELETE FROM stludr.stl_cdnappend_total_fee WHERE acct_month =' ||
            acctMonth;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
deallocate prepare STMT;

set @vSql := 'DELETE FROM stludr.stl_cdnappend_order_fee WHERE acct_month =' ||
            acctMonth;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
deallocate prepare STMT;
commit;


---- 获取话单数据
set @vSql := 'INSERT INTO stludr.cdnappend_rate_swap_00_t
    SELECT NULL,
           EC_CODE,
           OFFER_ORDER_ID,
           PRODUCT_ORDER_ID,
           OFFER_CODE,
           PRODUCT_CODE,
           '''' FLOW_TYPE,
           DISTRIBUTION_PLANE,
           SUM(NVL(DOWN_FLOW,0)),
		   SUM(NVL(UP_FLOW,0)),
		   SUM(NVL(CONTENT_FLOW,0)),
           SUM(NVL(REQUEST_NUM,0)),
           SUM(NVL(IP_NUM,0)),
           DUP_TIME,
           NULL,
           NULL,
           SERVICE_TYPE,
           0,
           0
    FROM (SELECT *
            FROM  stludr.ur_cdnappend_' || acctMonth || '_t
             UNION ALL
          SELECT * FROM  stludr.ur_cdnappend_' || nextMonth || '_t ) t1
    WHERE substr(t1.DUP_TIME, 1, 6) =' || acctMonth || '
            AND t1.DISTRIBUTION_PLANE = ''1''
            AND t1.SERVICE_TYPE in (''1'',''2'')' ||
 			' AND t1.product_code !=''2023999400085020'' ' ||
            ' GROUP BY EC_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID, OFFER_CODE, PRODUCT_CODE, DISTRIBUTION_PLANE, DOWN_FLOW, UP_FLOW, CONTENT_FLOW, REQUEST_NUM, IP_NUM, DUP_TIME,SERVICE_TYPE ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
deallocate prepare STMT;
commit;

select('7层加速下行带宽月95峰及金额计算');
----计算峰值列表
INSERT INTO stludr.cdnappend_rate_swap_01_t
SELECT
    NULL,
    t.OFFER_CODE,
    t.PRODUCT_CODE,
    t.FLOW_TYPE,
    t.DISTRIBUTION_PLANE,
    SUM(NVL(DOWN_FLOW, 0)),
    t.BEGIN_TIME,
    t.SERVICE_TYPE,
    t.ORDER_MODE,
    row_number() OVER (PARTITION BY t.SERVICE_TYPE,t.FLOW_TYPE ORDER BY SUM(t.DOWN_FLOW) DESC) rn
FROM stludr.cdnappend_rate_swap_00_t t
WHERE t.DISTRIBUTION_PLANE = '1'
  AND t.SERVICE_TYPE = '1'
GROUP BY t.OFFER_CODE, t.PRODUCT_CODE, t.FLOW_TYPE, t.DISTRIBUTION_PLANE, t.begin_time, t.ORDER_MODE,
         t.SERVICE_TYPE;
commit;

--依据索引 peakIndex 取95峰值、单位转换（CDNAPPEND流量话单单位是KB, 速率单位是Mbps，转换公式：KB/1024/300*8 转换成Mbps）
INSERT INTO stludr.cdnappend_rate_swap_04_t
SELECT null,
       OFFER_CODE,
       PRODUCT_CODE,
       FLOW_TYPE,
       DISTRIBUTION_PLANE,
       BEGIN_TIME,
       SERVICE_TYPE,
       ORDER_MODE,
       SUM_DOWN_FLOW_S,
       peakIndex,
       ROUND(SUM_DOWN_FLOW_S * 8 / 1024 / 300, 4),              --KB 转 Mbps
       CEIL(SUM_DOWN_FLOW_S * 8 / 1024 / 300) * down_flow_unit7 --费用(厘)
FROM stludr.cdnappend_rate_swap_01_t t
WHERE t.PEAK_INDEX = peakIndex;
commit;

select('7层加速上行带宽月95峰及金额计算');
----计算峰值列表
INSERT INTO stludr.cdnappend_rate_swap_02_t
SELECT
    NULL,
    t.OFFER_CODE,
    t.PRODUCT_CODE,
    t.FLOW_TYPE,
    t.DISTRIBUTION_PLANE,
    SUM(NVL(UP_FLOW, 0)),
    t.BEGIN_TIME,
    t.SERVICE_TYPE,
    t.ORDER_MODE,
    row_number() OVER (PARTITION BY t.SERVICE_TYPE,t.FLOW_TYPE ORDER BY SUM(t.UP_FLOW) DESC) rn
FROM stludr.cdnappend_rate_swap_00_t t
WHERE t.DISTRIBUTION_PLANE = '1'
  AND t.SERVICE_TYPE = '1'
GROUP BY t.OFFER_CODE, t.PRODUCT_CODE, t.FLOW_TYPE, t.DISTRIBUTION_PLANE, t.begin_time, t.ORDER_MODE,
         t.SERVICE_TYPE;
commit;

--依据索引 peakIndex 取95峰值、单位转换（CDNAPPEND流量话单单位是KB, 速率单位是Mbps，转换公式：KB/1024/300*8 转换成Mbps）
INSERT INTO stludr.cdnappend_rate_swap_05_t
SELECT null,
       OFFER_CODE,
       PRODUCT_CODE,
       FLOW_TYPE,
       DISTRIBUTION_PLANE,
       BEGIN_TIME,
       SERVICE_TYPE,
       ORDER_MODE,
       SUM_UP_FLOW,
       peakIndex,
       ROUND(SUM_UP_FLOW * 8 / 1024 / 300, 4),              --KB 转 Mbps
       CEIL(SUM_UP_FLOW * 8 / 1024 / 300) * up_flow_unit --费用(厘)
FROM stludr.cdnappend_rate_swap_02_t t
WHERE t.PEAK_INDEX = peakIndex;
commit;

select('4层加速带宽月95峰及金额计算');
----计算峰值列表
INSERT INTO stludr.cdnappend_rate_swap_03_t
SELECT
    null,
    t.OFFER_CODE,
    t.PRODUCT_CODE,
    t.FLOW_TYPE,
    t.DISTRIBUTION_PLANE,
    SUM(NVL(DOWN_FLOW + CONTENT_FLOW, 0)),
    t.BEGIN_TIME,
    t.SERVICE_TYPE,
    t.ORDER_MODE,
    row_number() OVER (PARTITION BY t.SERVICE_TYPE,t.FLOW_TYPE ORDER BY SUM(t.DOWN_FLOW + t.CONTENT_FLOW) DESC) rn
FROM stludr.cdnappend_rate_swap_00_t t
WHERE t.DISTRIBUTION_PLANE = '1'
  AND t.SERVICE_TYPE = '2'
GROUP BY t.OFFER_CODE, t.PRODUCT_CODE, t.FLOW_TYPE, t.DISTRIBUTION_PLANE, t.begin_time, t.ORDER_MODE,
         t.SERVICE_TYPE;
commit;

--依据索引 peakIndex 取95峰值、单位转换（CDNAPPEND流量话单单位是KB, 速率单位是Mbps，转换公式：KB/1024/300*8 转换成Mbps）
INSERT INTO stludr.cdnappend_rate_swap_06_t
SELECT null,
       OFFER_CODE,
       PRODUCT_CODE,
       FLOW_TYPE,
       DISTRIBUTION_PLANE,
       BEGIN_TIME,
       SERVICE_TYPE,
       ORDER_MODE,
       SUM_DOWN_FLOW_F,
       peakIndex,
       ROUND(SUM_DOWN_FLOW_F * 8 / 1024 / 300, 4),              --KB 转 Mbps
       CEIL(SUM_DOWN_FLOW_F * 8 / 1024 / 300) * down_flow_unit4 --费用(厘)
FROM stludr.cdnappend_rate_swap_03_t t
WHERE t.PEAK_INDEX = peakIndex;
commit;

select('计算华为结算总金额');
INSERT INTO stludr.stl_cdnappend_total_fee
SELECT null, acctMonth, SUM(NVL(TOTAL_AMOUNT, 0)), 0
FROM (SELECT TOTAL_AMOUNT
      FROM stludr.cdnappend_rate_swap_04_t
      UNION ALL
      SELECT TOTAL_AMOUNT
      FROM stludr.cdnappend_rate_swap_05_t
      UNION ALL
      SELECT TOTAL_AMOUNT
      FROM stludr.cdnappend_rate_swap_06_t);
commit;

UPDATE stludr.stl_cdnappend_total_fee sctf
SET sctf.CMCC_FEE =
            sctf.CMCC_FEE + (SELECT SUM(NVL(free, 0)) FROM
            (SELECT CEIL(request_num_unit * SUM(NVL(a.REQUEST_NUM, 0)) / 10000) free
             FROM stludr.cdnappend_rate_swap_00_t a
             UNION ALL
             SELECT CEIL(ip_num_unit * SUM(NVL(a.IP_NUM, 0))) free
             FROM stludr.cdnappend_rate_swap_00_t a));
commit;

select('计算全国月累计流量');
INSERT INTO STLUDR.STL_CDNAPPEND_SWAP_TOTAL_FLOW
SELECT null,acctMonth,
       '',
       SUM(NVL(DOWN_FLOW, 0)) + SUM(NVL(CONTENT_FLOW, 0)) + SUM(NVL(UP_FLOW, 0))
FROM stludr.cdnappend_rate_swap_00_t;
commit;

select('计算单个订购累计流量');
INSERT INTO stludr.stl_cdnappend_swap_prod_flow
SELECT null,acctMonth,
       t.EC_CODE,
       t.OFFER_ORDER_ID,
       t.PRODUCT_ORDER_ID,
       t.OFFER_CODE,
       t.PRODUCT_CODE,
       t.ORDER_MODE,
       SUM(NVL(t.DOWN_FLOW, 0)) + SUM(NVL(t.CONTENT_FLOW, 0)) + SUM(NVL(t.UP_FLOW, 0))
FROM stludr.cdnappend_rate_swap_00_t t
GROUP BY t.EC_CODE, t.OFFER_ORDER_ID, t.PRODUCT_ORDER_ID, t.OFFER_CODE, t.PRODUCT_CODE, t.ORDER_MODE;
commit;

select('计算最终单个订购金额');
--按订购结算
INSERT INTO STLUDR.STL_CDNAPPEND_ORDER_FEE
SELECT null,t1.ACCT_MONTH,
       t1.ORDER_MODE,
       t1.EC_CODE,
       t1.OFFER_CODE,
       t1.PRODUCT_CODE,
       t1.OFFER_ORDER_ID,
       t1.PRODUCT_ORDER_ID,
       '4018',
       ROUND(t1.CMCC_FEE / 10 * t1.TOTAL_FLOW / t1.AMOUNT_FLOW),
       0,
       ''
FROM (select b.*, a.AMOUNT_FLOW, c.CMCC_FEE
      from stludr.stl_cdnappend_swap_total_flow a,
           stludr.stl_cdnappend_swap_prod_flow b,
           stludr.stl_cdnappend_total_fee c,
           stlusers.STL_ARCHIVE_WHITE_T d
      WHERE b.EC_CODE = d.CUSTOMER_NUM
        AND b.PRODUCT_ORDER_ID = d.PRODIST_SKU_NUM
        AND a.ACCT_MONTH = b.ACCT_MONTH
        AND b.ACCT_MONTH = c.ACCT_MONTH
        AND a.ACCT_MONTH = acctMonth) t1;
commit;

--更新受理模式(受理模式)
set @vSql := ' UPDATE STLUDR.STL_CDNAPPEND_ORDER_FEE a
	SET a.ORDER_MODE = (select distinct(b.ORDER_MODE)
                    from STLUDR.UR_CDNAPPEND_'|| acctMonth ||'_T b
                    where a.PRODUCT_ORDER_ID = b.PRODUCT_ORDER_ID
                    union select distinct(c.ORDER_MODE)
                    from STLUDR.UR_CDNAPPEND_'|| nextMonth ||'_T c
                    where a.PRODUCT_ORDER_ID = c.PRODUCT_ORDER_ID )
	where a.ACCT_MONTH = ' || acctMonth ;


SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
deallocate prepare STMT;
commit;

nReturn := 0;
	szSysErr := 'OK';

SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || to_number(nReturn);
END;
END;;
DELIMITER ;