/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：结算前数据准备-CDN数据源计算-CDN华为结算金额计算（日峰）
**/
use stludr;
DROP PROCEDURE IF EXISTS stludr.`PROC_CDN_RATE_RIF`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE stludr.`PROC_CDN_RATE_RIF`(acctMonth IN VARCHAR2,
                                          nReturn   OUT NUMBER,
                                          szSysErr  OUT VARCHAR2)
AS

    nextMonth      VARCHAR2(6);   -- 账期月下一月
    acct_month_tbl VARCHAR2(64);  -- 账期月UR表名
    next_month_tbl VARCHAR2(64);  -- 账期月下月UR表名
    vSql           VARCHAR2(10240);
    v_proc_name    VARCHAR2(30) := 'PROC_CDN_RATE_RIF';
BEGIN nReturn := 0;

DECLARE EXIT
HANDLER FOR SQLEXCEPTION
BEGIN
GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
szSysErr := substr(@p2, 1, 1000);
nReturn  := -1;
ROLLBACK;

--  日志写表
select ('exception: ' || nReturn || '|' || @p1 || '|' || szSysErr) AS error_msg;
END;
BEGIN
acct_month_tbl := 'stludr.ur_cdnappend_' || acctMonth || '_t';

-- 账期月下月
select to_char(add_months(to_date(acctMonth, 'yyyyMM'), 1), 'yyyyMM')
into nextMonth
from dual;

next_month_tbl := 'stludr.ur_cdnappend_' || nextMonth || '_t';

-- step 0 临时数据清理
select('step1');
set @vSql := 'TRUNCATE TABLE stludr.cdn_proc_rate_swap_t_00';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
deallocate prepare STMT;


set @vSql  := 'TRUNCATE TABLE stludr.cdn_proc_rate_swap_t_01';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
deallocate prepare STMT;

set @vSql := 'DELETE FROM stludr.STL_CDN_TOTAL_FEE_RIF WHERE acct_month =' || acctMonth;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
deallocate prepare STMT;


set @vSql := 'DELETE FROM stludr.STL_CDN_TOTAL_FLOW  WHERE acct_month =' || acctMonth;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
deallocate prepare STMT;

set @vSql := 'DELETE FROM stludr.STL_CDN_TOTAL_FLOW_01  WHERE acct_month =' || acctMonth;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
deallocate prepare STMT;

set @vSql := 'DELETE FROM stludr.STL_CDN_ORDER_FEE_RIF WHERE acct_month =' || acctMonth;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
deallocate prepare STMT;

-- step1：按天汇总话单数据
-- 月底最后1天部分话单可能会入到下一账期表中，
select('step2');
set @vSql := 'INSERT INTO stludr.cdn_proc_rate_swap_t_00
    SELECT /*+ parallel(t1 ,8) */
        null,
        t1.ec_code,
        t1.offer_order_id,
        t1.product_order_id,
        t1.offer_code,
        t1.product_code,
        '''',  --流量类型
        t1.DISTRIBUTION_PLANE,
        SUM(NVL(t1.DOWN_FLOW, 0)),
        SUM(NVL(t1.CONTENT_FLOW, 0)),
        SUM(NVL(t1.DOWN_FLOW, 0)) + SUM(NVL(t1.CONTENT_FLOW, 0)),
        t1.DUP_TIME begin_time,
        ''5'',
        ''0'',
        NULL,  --受理模式
        t1.ACCT_MONTH
    FROM ( SELECT *
          FROM ' || acct_month_tbl ||
            ' UNION ALL
             SELECT *
             FROM ' || next_month_tbl || ' ) t1
    WHERE substr(t1.dup_time, 1, 6) = ' || acctMonth ||
            ' AND t1.DISTRIBUTION_PLANE = ''1'' ' ||
            'AND t1.FLOW_TYPE = ''1''' ||
           'AND t1.SERVICE_TYPE = ''3'' '||
			'AND t1.product_code !=''2023999400085020'' ' ||
          ' GROUP BY t1.ec_code, t1.offer_order_id, t1.product_order_id, t1.offer_code, t1.product_code,
                  t1.DISTRIBUTION_PLANE, t1.DUP_TIME, t1.ACCT_MONTH';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
deallocate prepare STMT;

-- step2:  计算全网客户华为平面计算日峰值
select('step3');
INSERT INTO stludr.cdn_proc_rate_swap_t_01
SELECT null,
       EC_CODE,
       OFFER_ORDER_ID,
       PRODUCT_ORDER_ID,
       OFFER_CODE,
       PRODUCT_CODE,
       ceil(data_flow * 8 / 1024 / 300),
       BEGIN_TIME,
       null
FROM (SELECT EC_CODE,
             OFFER_ORDER_ID,
             PRODUCT_ORDER_ID,
             OFFER_CODE,
             PRODUCT_CODE,
             t1.begin_time,
             NVL(t1.SUM_ORDER_FLOW, 0)                                                                               data_flow,
             row_number() OVER (PARTITION BY PRODUCT_ORDER_ID,SUBSTR(begin_time, 0, 8) ORDER BY SUM_ORDER_FLOW DESC) rn
      FROM stludr.cdn_proc_rate_swap_t_00 t1)
WHERE rn = 1;
COMMIT;

-- step3:  根据峰值计算 单价 填到 cdn_proc_rate_swap_t_01   按厘
select('step4');
UPDATE stludr.cdn_proc_rate_swap_t_01
SET UNIT_PRICE =
        CASE
            WHEN DATA_FLOW >= 0 AND DATA_FLOW <= 100 THEN '580.5'
            WHEN DATA_FLOW > 100 AND DATA_FLOW <= 500 THEN '549'
            WHEN DATA_FLOW > 500 AND DATA_FLOW <= 5 * 1024 THEN '521.1'
            WHEN DATA_FLOW > 5 * 1024 AND DATA_FLOW <= 20 * 1024 THEN '506.7'
            ELSE '492.3' END;
COMMIT;

-- step3:  根据每天的峰值计单价计算每个订购的总价 填到 cdn_proc_rate_swap_t_00  按分
-- ROUND(ceil(DATA_FLOW * 580.5)  / 10)
-- ROUND(ceil(DATA_FLOW * 549) / 10)
-- ROUND( ceil(DATA_FLOW * 521.1) / 10)
-- ROUND( ceil(DATA_FLOW * 506.7) / 10)
-- ROUND( ceil(DATA_FLOW * 492.3) / 10)
select('step5');
UPDATE stludr.cdn_proc_rate_swap_t_00 a
SET a.price =
        ROUND(ceil(a.SUM_ORDER_FLOW * 8 / 1024 / 300 *
                   (SELECT b.UNIT_PRICE
                    FROM stludr.cdn_proc_rate_swap_t_01 b
                    WHERE a.BEGIN_TIME = b.BEGIN_TIME
                      AND a.PRODUCT_ORDER_ID = b.PRODUCT_ORDER_ID)
              ) / 10);
COMMIT;

-- step4: 华为结算 总金额结果表
select('step6');
INSERT INTO stludr.STL_CDN_TOTAL_FEE_RIF
SELECT null,acctMonth,
       round(sum(t1.PRICE)),  	-- 华为网内CDN结算总金额
       '0', 				-- 华为网外CDN结算总金额
       '5',
       null 				-- 产品类型
FROM stludr.cdn_proc_rate_swap_t_00 t1;
COMMIT;

-- step5: 华为结算 总流量结果表
select('step7');
INSERT INTO stludr.stl_cdn_total_flow
SELECT null,acctMonth,
       '5',
       sum(t1.SUM_ORDER_FLOW)	 -- 华为网内CDN结算总流量
FROM stludr.cdn_proc_rate_swap_t_00 t1;
COMMIT;

-- step6: 华为结算 分订购按月汇总流量表
select('step8');
insert into stludr.STL_CDN_TOTAL_FLOW_01
SELECT null,acctMonth,
       t1.EC_CODE,
       t1.OFFER_ORDER_ID,
       t1.PRODUCT_ORDER_ID,
       t1.OFFER_CODE,
       t1.PRODUCT_CODE,
       t1.ORDER_MODE,
       sum(t1.SUM_ORDER_FLOW) 		-- 华为网内CDN结算总流量
FROM stludr.cdn_proc_rate_swap_t_00 t1
group by t1.OFFER_CODE, t1.PRODUCT_CODE, t1.OFFER_ORDER_ID, t1.PRODUCT_ORDER_ID, t1.ORDER_MODE, t1.EC_CODE;

COMMIT;

--	按订购结算
-- step7: 华为结算 总金额结果表
select('step10');
INSERT INTO stludr.STL_CDN_ORDER_FEE_RIF
SELECT null,acctMonth,
       t1.order_mode,
       t1.ec_code,
       t1.offer_code,
       t1.product_code,
       t1.offer_order_id,
       t1.product_order_id,
       '1102',
       round(t1.CMCC_FEE * t1.TOTAL_FLOW / t1.AMOUNT_FLOW), -- 订购华为网内CDN结算金额
       0, 													-- 订购华为网外CDN结算金额
       null
FROM (SELECT a.*, b.AMOUNT_FLOW, c.CMCC_FEE
      FROM stludr.STL_CDN_TOTAL_FLOW_01 a,
           stludr.stl_cdn_total_flow b,
           stludr.STL_CDN_TOTAL_FEE_RIF c,
           stlusers.STL_ARCHIVE_WHITE_T d
      WHERE a.EC_CODE = d.CUSTOMER_NUM
        AND a.PRODUCT_ORDER_ID = d.PRODIST_SKU_NUM
        AND a.ACCT_MONTH = b.ACCT_MONTH
        AND b.ACCT_MONTH = c.ACCT_MONTH
        AND a.ACCT_MONTH = acctMonth) t1;
COMMIT;

--更新受理模式(受理模式)
set @vSql :=  ' UPDATE STLUDR.STL_CDN_ORDER_FEE_RIF a
	SET a.ORDER_MODE = (select distinct(b.ORDER_MODE)
                    from STLUDR.UR_CDNAPPEND_'|| acctMonth ||'_T b
                    where a.PRODUCT_ORDER_ID = b.PRODUCT_ORDER_ID
                    union select distinct(c.ORDER_MODE)
                    from STLUDR.UR_CDNAPPEND_'|| nextMonth ||'_T c
                    where a.PRODUCT_ORDER_ID = c.PRODUCT_ORDER_ID )
	where a.ACCT_MONTH = ' || acctMonth ;

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
deallocate prepare STMT;
COMMIT;
nReturn := 0;
szSysErr := 'OK';

SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || to_number(nReturn);
END;
END ;;
DELIMITER ;