/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：结算前数据准备-CDN数据源计算-海外CDN华为结算金额计算
**/
use stludr;
DROP PROCEDURE IF EXISTS stludr.`PROC_HWCDN_RATE`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE stludr.`PROC_HWCDN_RATE`(acctMonth IN VARCHAR2,
                                 nReturn OUT NUMBER,
                                 szSysErr OUT VARCHAR2)
As


    vSql       VARCHAR2(10240);
	nextMonth  VARCHAR2(6); -- 账期月下一月
	db_unit    NUMBER(12, 4);
	zb_unit    NUMBER(12, 4);
	js_unit    NUMBER(12, 4);
	r_num_unit NUMBER(12, 4);
    v_proc_name    VARCHAR2(30) := 'PROC_HWCDN_RATE';


BEGIN
    szSysErr := '';
    nReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
szSysErr := substr(@p2, 1, 1000);
        nReturn := -1;
ROLLBACK;
--  日志写表
select ('exception: ' || nReturn || '|' || @p1 || '|' || szSysErr) AS error_msg;
END;

BEGIN
DELETE FROM STLUDR.STL_HWCDN_ORDER_FEE_SWAP WHERE ACCT_MONTH = acctMonth;
DELETE FROM STLUDR.STL_HWCDN_ORDER_FEE WHERE ACCT_MONTH = acctMonth;
---- 账期月下月
SELECT to_char(add_months(to_date(acctMonth, 'yyyyMM'), 1), 'yyyyMM')
INTO nextMonth
FROM dual;

        db_unit := '0.0560';
        zb_unit := '0.3280';
        js_unit := '0.0560';
        r_num_unit := '0.0300';

        ---- 单价 * 10000 *数量   单价配置的是元  转化为分   再乘100扩位
        set @vSql := 'INSERT INTO STLUDR.STL_HWCDN_ORDER_FEE_SWAP ' ||
                     ' SELECT ' ||
                     ' NULL, ' ||
                     '''' || acctMonth || ''' ACCT_MONTH, ' ||
                     ' a.PROD_ORDER_MODE as ORDER_MODE,' ||
                     ' a.EC_CODE,' ||
                     ' a.OFFER_CODE,' ||
                     ' a.PRODUCT_CODE,' ||
                     ' a.OFFER_ORDER_ID,' ||
                     ' a.PRODUCT_ORDER_ID,' ||
                     ' a.RESOURCE_SPECODE,' ||
                     ' b.SETTLE_PRICE * 100 * CEIL(sum(a.DATA_VALUE)) as DATA_VALUE,' ||
                     ' ''0'',null ' ||
                     ' FROM (SELECT * FROM  (SELECT * ' ||
                     ' FROM stludr.UR_CDNVAS_' || acctMonth || '_T where substr(begin_time,1,6)  = ' ||
                     acctMonth ||
                     ' AND PRODUCT_CODE = ''2023999400085020'' AND PROVIDER = ''1'' ' ||
                     ' UNION ALL SELECT * ' ||
                     ' FROM stludr.UR_CDNVAS_' || nextMonth || '_T where substr(begin_time,1,6)  = ' ||
                     acctMonth ||
                     ' AND PRODUCT_CODE =''2023999400085020'' AND PROVIDER = ''1'' ) )a ' ||
                     '    JOIN stludr.HWCDN_VAS_DICT b ' ||
                     '    ON a.BUSI_TYPE = b.BUSI_TYPE ' ||
                     '    AND a.RESOURCE_SPECODE = b.RESOURCE_SPECODE ' ||
                     ' GROUP BY ' ||
                     '    a.EC_CODE, ' ||
                     '    a.PROD_ORDER_MODE, ' ||
                     '    a.OFFER_CODE, ' ||
                     '    a.PRODUCT_CODE, ' ||
                     '    a.OFFER_ORDER_ID, ' ||
                     '    a.PRODUCT_ORDER_ID, ' ||
                     '    a.BUSI_TYPE, ' ||
                     '    a.RESOURCE_SPECODE, ' ||
                     '    b.BYTE_SETTLE_PRICE, ' ||
                     '    b.SETTLE_PRICE ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;

---- 开始计算CDN话单 点播  直播  单价 * 用量
set @vSql := 'INSERT INTO STLUDR.STL_HWCDN_ORDER_FEE_SWAP ' ||
                     ' SELECT ' ||
                     ' NULL ,' ||
                     ' ''' || acctMonth || ''' ACCT_MONTH, ' ||
                     ' ORDER_MODE,' ||
                     ' EC_CODE, ' ||
                     ' OFFER_CODE,' ||
                     ' PRODUCT_CODE,' ||
                     ' OFFER_ORDER_ID,' ||
                     ' PRODUCT_ORDER_ID,' ||
                     ' ''116'',' ||
                     ' DECODE(SERVICE_TYPE, ''1'', sum(ACCU_VOLUME)/1024/1024 * 100 *' || db_unit ||
                     ', ''2'', sum(ACCU_VOLUME)/1024/1024 * 100 *' || zb_unit ||
                     ', 0), ' ||
                     ' ''0'', ' ||
                     ' NULL ' ||
                     ' FROM stludr.UR_CDN_' || acctMonth || '_T ' ||
                     ' WHERE substr(DUP_TIME, 1, 6) = ' || acctMonth ||
                     ' AND PRODUCT_CODE = ''2023999400085020'' ' ||
                     ' AND RATE_BACK_ID = ''3'' ' ||
                     ' AND SUB_GROUP_NUM = ''1'' ' ||
                     ' GROUP BY ORDER_MODE, ' ||
                     ' EC_CODE, ' ||
                     ' OFFER_CODE, ' ||
                     ' PRODUCT_CODE, ' ||
                     ' OFFER_ORDER_ID, ' ||
                     ' PRODUCT_ORDER_ID,' ||
                     ' SERVICE_TYPE ' ||
                     ' UNION ALL ' ||
                     ' SELECT ' ||
                     ' NULL ,' ||
                     ' ''' || acctMonth || ''' ACCT_MONTH, ' ||
                     ' ORDER_MODE,' ||
                     ' EC_CODE, ' ||
                     ' OFFER_CODE,' ||
                     ' PRODUCT_CODE,' ||
                     ' OFFER_ORDER_ID,' ||
                     ' PRODUCT_ORDER_ID,' ||
                     ' ''116'',' ||
                     ' DECODE(SERVICE_TYPE, ''1'', sum(ACCU_VOLUME)/1024/1024 * 100 *' || db_unit ||
                     ', ''2'', sum(ACCU_VOLUME)/1024/1024 * 100 *' || zb_unit ||
                     ', 0), ' ||
                     ' ''0'', ' ||
                     ' NULL ' ||
                     ' FROM stludr.UR_CDN_' || nextMonth || '_T ' ||
                     ' WHERE substr(DUP_TIME, 1, 6) = ' || acctMonth ||
                     ' AND PRODUCT_CODE = ''2023999400085020'' ' ||
                     ' AND RATE_BACK_ID = ''3'' ' ||
                     ' AND SUB_GROUP_NUM = ''1'' ' ||
                     ' GROUP BY ORDER_MODE, ' ||
                     ' EC_CODE, ' ||
                     ' OFFER_CODE, ' ||
                     ' PRODUCT_CODE, ' ||
                     ' OFFER_ORDER_ID, ' ||
                     ' PRODUCT_ORDER_ID,' ||
                     ' SERVICE_TYPE ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;
---- 开始计算CDNAPPEND话单   单上行流量加下行 * 单价  + 请求项万次 * 单价  因为有扩位还原 所以请求数不除10000
set @vSql := 'INSERT INTO STLUDR.STL_HWCDN_ORDER_FEE_SWAP ' ||
                     ' SELECT ' ||
                     ' NULL ,' ||
                     ' ''' || acctMonth || ''' ACCT_MONTH, ' ||
                     ' ORDER_MODE,' ||
                     ' EC_CODE, ' ||
                     ' OFFER_CODE,' ||
                     ' PRODUCT_CODE,' ||
                     ' OFFER_ORDER_ID,' ||
                     ' PRODUCT_ORDER_ID,' ||
                     ' ''116'',' ||
                     ' sum(UP_FLOW) /1024/1024 * 100 * ' || js_unit || ' + sum(DOWN_FLOW)/1024/1024 * 100 * ' || js_unit ||
                     ' + sum(REQUEST_NUM)/100  * ' || r_num_unit ||
                     ', ''0'', ' ||
                     ' NULL ' ||
                     ' FROM stludr.UR_CDNAPPEND_' || acctMonth || '_T ' ||
                     ' WHERE substr(DUP_TIME, 1, 6) = ' || acctMonth ||
                     ' AND PRODUCT_CODE = ''2023999400085020'' ' ||
                     ' AND FLOW_TYPE = ''3'' ' ||
                     ' AND DISTRIBUTION_PLANE = ''1'' ' ||
                     ' AND SERVICE_TYPE =''1'' ' ||
                     ' GROUP BY ORDER_MODE, ' ||
                     ' EC_CODE, ' ||
                     ' OFFER_CODE, ' ||
                     ' PRODUCT_CODE, ' ||
                     ' OFFER_ORDER_ID, ' ||
                     ' PRODUCT_ORDER_ID ' ||
                     ' UNION ALL ' ||
                     ' SELECT ' ||
                     ' NULL ,' ||
                     ' ''' || acctMonth || ''' ACCT_MONTH, ' ||
                     ' ORDER_MODE,' ||
                     ' EC_CODE, ' ||
                     ' OFFER_CODE,' ||
                     ' PRODUCT_CODE,' ||
                     ' OFFER_ORDER_ID,' ||
                     ' PRODUCT_ORDER_ID,' ||
                     ' ''116'',' ||
                     ' sum(UP_FLOW) /1024/1024 * 100 * ' || js_unit || ' + sum(DOWN_FLOW)/1024/1024 * 100 * ' || js_unit ||
                     ' + sum(REQUEST_NUM)/100  * ' || r_num_unit ||
                     ', ''0'', ' ||
                     ' NULL ' ||
                     ' FROM stludr.UR_CDNAPPEND_' || nextMonth || '_T ' ||
                     ' WHERE substr(DUP_TIME, 1, 6) = ' || acctMonth ||
                     ' AND PRODUCT_CODE = ''2023999400085020'' ' ||
                     ' AND FLOW_TYPE = ''3'' ' ||
                     ' AND DISTRIBUTION_PLANE = ''1'' ' ||
                     ' AND SERVICE_TYPE =''1'' ' ||
                     ' GROUP BY ORDER_MODE, ' ||
                     ' EC_CODE, ' ||
                     ' OFFER_CODE, ' ||
                     ' PRODUCT_CODE, ' ||
                     ' OFFER_ORDER_ID, ' ||
                     ' PRODUCT_ORDER_ID ';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;
---- 结果汇总入最终库
set @vSql := 'INSERT INTO STLUDR.STL_HWCDN_ORDER_FEE ' ||
                     ' SELECT NULL,ACCT_MONTH, ' ||
                     ' ORDER_MODE,' ||
                     ' CUSTOMER_CODE,' ||
                     ' OFFER_CODE,' ||
                     ' PRODUCT_CODE,' ||
                     ' OFFER_ORDER_ID,' ||
                     ' PRODUCT_ORDER_ID,' ||
                     ' CHARGE_ITEM,' ||
                     ' ROUND(sum(CMCC_FEE),0),' ||
                     ' ''0'', ' ||
                     ' CUSTOMER_TYPE' ||
                     ' FROM STLUDR.STL_HWCDN_ORDER_FEE_SWAP b  ' ||
                     ' INNER JOIN stlusers.STL_ARCHIVE_WHITE_T c' ||
            		 ' on b.CUSTOMER_CODE = c.CUSTOMER_NUM ' ||
            		 ' AND b.PRODUCT_ORDER_ID = c.PRODIST_SKU_NUM ' ||
                     ' GROUP BY ACCT_MONTH, ORDER_MODE, CUSTOMER_CODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID,' ||
                     ' CHARGE_ITEM, CUSTOMER_TYPE';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
COMMIT;
nReturn := 0;
        szSysErr := ' OK ';
SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || nReturn;
END;
END ;;
DELIMITER ;