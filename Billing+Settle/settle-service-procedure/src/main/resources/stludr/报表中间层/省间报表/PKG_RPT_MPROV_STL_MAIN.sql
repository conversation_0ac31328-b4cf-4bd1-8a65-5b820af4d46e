/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层-省间报表-受理模式4报表中间表总调用
**/
DROP PROCEDURE IF EXISTS stludr.`PKG_RPT_MPROV_STL_MAIN`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "PKG_RPT_MPROV_STL_MAIN"(RPT_SETTLEMONTH IN VARCHAR2,
                                        FLAG_VERSION IN CHAR,
                                        PROC_OUT OUT VARCHAR2,
                                        szSysErr OUT VARCHAR2(1000),
                                        nReturn OUT NUMBER(4) )
AS
    PROC_MAIN_OUT VARCHAR2(2000);
    v_proc_name   VARCHAR2(30) := 'PKG_RPT_MPROV_STL_MAIN';
BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
        ROLLBACK;

        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;
    BEGIN
      PROC_MAIN_OUT:='';
      call STL_AR_INTERMPROV(RPT_SETTLEMONTH, FLAG_VERSION, PROC_OUT,szSysErr,nReturn);
      PROC_MAIN_OUT:=PROC_MAIN_OUT||'|'||PROC_OUT;
      call STL_BL_INTERMPROV(RPT_SETTLEMONTH, FLAG_VERSION, PROC_OUT,szSysErr,nReturn);
      PROC_MAIN_OUT:=PROC_MAIN_OUT||'|'||PROC_OUT;
      PROC_OUT:=PROC_MAIN_OUT;
      COMMIT;
    END;
END ;;
DELIMITER ;