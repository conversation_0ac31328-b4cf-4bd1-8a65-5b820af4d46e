/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层-省间报表-受理模式3实收结算中间表计算
**/
DROP PROCEDURE IF EXISTS stludr.`STL_AR_INTERPROV`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "STL_AR_INTERPROV"(RPT_SETTLEMONTH IN VARCHAR2,
                                                FLAG_VERSION IN CHAR,
                                                PROC_OUT OUT VARCHAR2,
                                                szSysErr OUT VARCHAR2(1000),
                                                nReturn OUT NUMBER(4)   )
AS
    G_VERSION RVL_CONF_VERSION.VERSION%TYPE; -- 版本号变量
    G_RESNUM  RVL_CONF_VERSION.VERSIONNUM%TYPE; -- 版本序号
    ERRCODE   VARCHAR2(32);
    ERRMSG    VARCHAR2(2048);
    VER_TABLE VARCHAR2(64);
    RPT_TABLE VARCHAR2(64);
    RPT_RUNSITE VARCHAR2(64);
    RPT_SQL VARCHAR2(4096);
    v_proc_name  VARCHAR2(30) := 'STL_AR_INTERPROV';
BEGIN

    -- alter table UR_PAID_202307_T add index idx_om_ds(ORDER_MODE,DEST_SOURCE);
    -- 将GET_PRODUCT_NAME函数直接写入SQL中
    -- 将update 合入insert 中
    -- 分片表STL_CUSTOMER、STL_SERVICE、STL_PRODUCT、stl_charge_item_def的同一个月分区，必须在同一个shard上
    -- call STL_AR_INTERPROV_WL('202307','1',@1,@2,@3);

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
PROC_OUT := 'N';
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
        ROLLBACK;

        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;
  BEGIN
    VER_TABLE   := 'RPT_AR_INTERPROV';
    RPT_RUNSITE := '0';
    RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表开始时间：' ||TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
    -- 中间表获取版本号
    call SETTLE_REPORT_VERSION(RPT_SETTLEMONTH,VER_TABLE,G_VERSION,G_RESNUM,FLAG_VERSION,szSysErr,nReturn);
    RPT_RUNSITE := '1';
    RPT_TABLE   := 'UR_PAID_' || RPT_SETTLEMONTH || '_T';

    set @RPT_SQL     := 'INSERT INTO RPT_AR_INTERPROV(SETTLEMONTH, VERSION, INPROV, OUTPROV, CUSTOMERNUMBER, MEMBER_COUNT, PRODUCT_CODE, PRODUCT_NAME, FEENUMBER, TAXRATE,'||chr(10)
                     ||'        SETTNOTAXFEE, SETTTAXFEE, ORG_MONTH, PAID_MONTH, BAT,POSPEC_CODE,SOSPEC_CODE,PRODUCT_ORDER_ID,CUSTOMERNAME,FEETYPE,FEENAME,SOSPEC_NAME,POSPEC_NAME, INPROVNAME, OUTPROVNAME,settle_mode) '||chr(10)
                     ||' SELECT ''' || RPT_SETTLEMONTH || ''', ''' || G_VERSION ||''', T.IN_OBJECT,T.OUT_OBJECT, T.CUSTOMER_CODE, T.num_member_code,DECODE(T.PRODUCT_ORDER_ID, NULL, T.OFFER_CODE, T.PRODUCT_CODE), '||chr(10)
                     ||'        case when (T.PRODUCT_ORDER_ID is null) then NVL(p.NAME, '''') else concat(nvl(p.name, ''''),''_'',nvl(s.name, '''')) end as product_name, '||chr(10)
                     ||'        T.CHARGE_CODE,T.TAX_RATE,t.sum_NOTAXFEE,t.sum_TAXFEE,T.ORG_MONTH,T.PAID_MONTH,T.PHASE,T.OFFER_CODE,T.PRODUCT_CODE,T.PRODUCT_ORDER_ID, '||chr(10)
                     ||'        NVL(T1.FIRST_NAME, ''''), case when T.CHARGE_CODE=''YWJS'' THEN 0 ELSE t.feetype end feetype,'||chr(10)
                     ||'        case when T.CHARGE_CODE=''YWJS'' THEN ''异网结算'' ELSE t2.DESCRIPTION end feename, T3.NAME, T4.NAME, t5.PROV_NM, T6.PROV_NM,T.settle_mode'||chr(10)
                     ||'   from (SELECT T.IN_OBJECT,T.OUT_OBJECT, T.CUSTOMER_CODE,T.PRODUCT_CODE,T.CHARGE_CODE,T.TAX_RATE,T.ORG_MONTH,T.PHASE,T.PAID_MONTH,T.OFFER_CODE,T.PRODUCT_ORDER_ID,t.feetype, '||chr(10)
                     ||'                COUNT(DISTINCT T.MEMBER_CODE) num_member_code,SUM(NVL(T.SETTLE_NOTAXFEE, 0)) as sum_NOTAXFEE,SUM(NVL(T.SETTLE_TAXFEE, 0)) as sum_TAXFEE,T.settle_mode '||chr(10)
                     ||'           FROM ' || RPT_TABLE ||' T '||chr(10)
                     ||'          WHERE (T.ORDER_MODE = ''3'' AND T.DEST_SOURCE = ''0'' and t.out_object != ''030'' and t.in_object != ''030'' and t.occurence not in (''YDY'', ''ESP'')) '||chr(10)
                     ||'             OR (T.ORDER_MODE = ''5'' AND T.DEST_SOURCE = ''1'' and t.out_object != ''030'' and t.in_object != ''030'') '||chr(10)
                     ||'          GROUP BY T.IN_OBJECT,T.OUT_OBJECT,T.CUSTOMER_CODE,T.PRODUCT_CODE,T.CHARGE_CODE,T.TAX_RATE,T.ORG_MONTH,T.PHASE,T.PAID_MONTH,T.OFFER_CODE,T.PRODUCT_ORDER_ID,t.feetype,T.settle_mode) t '||chr(10)
                     ||'   left join STL_PRODUCT p on p.PRODUCT_CODE = t.OFFER_CODE AND p.ACCT_MONTH = ''' || RPT_SETTLEMONTH || ''' '||chr(10)
                     ||'   left join STL_SERVICE s on s.SERVICE_CODE = t.PRODUCT_CODE AND s.ACCT_MONTH = ''' || RPT_SETTLEMONTH || ''' '||chr(10)
                     ||'   LEFT JOIN stlusers.STL_CUSTOMER T1 ON TO_DATE(T.ORG_MONTH, ''yyyyMM'') >= TO_DATE(TO_CHAR(T1.EFFECTIVE_DATE, ''yyyyMM''), ''yyyyMM'') '||CHR(10)
                     ||'         AND TO_DATE(T.ORG_MONTH, ''yyyyMM'') < TO_DATE(TO_CHAR(T1.EXPIRY_DATE, ''yyyyMM''), ''yyyyMM'')  AND T1.CUSTOMER_CODE = T.CUSTOMER_CODE'||CHR(10)
                     ||'   LEFT JOIN (select distinct NVL(DESCRIPTION,'''') DESCRIPTION, CHARGE_ITEM_REF from STL_CHARGE_ITEM_DEF '||chr(10)
                     ||'               where ACCT_MONTH = ''' || RPT_SETTLEMONTH || ''') T2 on T.CHARGE_CODE <> ''YWJS'' and T2.CHARGE_ITEM_REF = T.CHARGE_CODE'||CHR(10)
                     ||'   LEFT JOIN (select distinct NVL(NAME, '''') NAME, SERVICE_CODE from STL_SERVICE where  ACCT_MONTH = ''' || RPT_SETTLEMONTH || ''') T3 ON T.PRODUCT_CODE = T3.SERVICE_CODE'||CHR(10)
                     ||'   LEFT JOIN (select distinct NVL(NAME, '''') NAME, PRODUCT_CODE from STL_PRODUCT where  ACCT_MONTH = ''' || RPT_SETTLEMONTH || ''') T4 ON T.OFFER_CODE = T4.PRODUCT_CODE'||CHR(10)
                     ||'   LEFT JOIN STL_PROVINCE_CD T5 ON T.IN_OBJECT = T5.PROV_CD'||CHR(10)
                     ||'   LEFT JOIN STL_PROVINCE_CD T6 ON T.OUT_OBJECT = T6.PROV_CD';

    SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
RPT_SQL := '执行成功';
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
    -- 插入移动云账单数据
    RPT_RUNSITE := '5';
    set @RPT_SQL     := 'INSERT INTO RPT_AR_INTERPROV(SETTLEMONTH, VERSION, INPROV, OUTPROV, CUSTOMERNUMBER, customername, MEMBER_COUNT,PRODUCT_CODE, PRODUCT_NAME, FEENUMBER, '
                     ||'        feename, TAXRATE, SETTNOTAXFEE, SETTTAXFEE, ORG_MONTH, paid_month, BAT,POSPEC_CODE,pospec_name,SOSPEC_CODE,sospec_name,PRODUCT_ORDER_ID, feetype, INPROVNAME,OUTPROVNAME) '
                     ||' SELECT ''' || RPT_SETTLEMONTH || ''', ''' || G_VERSION || ''', T1.settlement_party_in,T1.settlement_party_out, T.customer_province_number, t.customer_name, 0, '
                     ||'        t.one_product_id, t.po_name || decode(t.one_product_id, ''111601'', ''_'' || t.product_name, null), T.one_pro_charge_code, t.one_pro_charge_name, T.TAX_RATE * 100, '
                     ||'        SUM(NVL(round(T1.settlement_amount / 10), 0)), 0, T.billing_term,t.pay_term, ''2'',T.po_id,t.po_name,t.one_product_id,decode(t.one_product_id, ''111601'', t.product_name, ''''), '
                     ||'        T.co_product_id,t.fee_flag - 1, T2.PROV_NM INPROVNAME, T3.PROV_NM OUTPROVNAME '
                     ||'   FROM sync_interface_mc_' || RPT_SETTLEMONTH || ' T, sync_interface_mc_p2p_' || RPT_SETTLEMONTH || ' t1, STL_PROVINCE_CD T2, STL_PROVINCE_CD T3 '
                     ||'  WHERE T.ORDER_MODE = 3 and t.id = t1.id and t.file_name = t1.file_name and t.status = 0 and t1.status = 0 and t.pay_tag = 2  and t.prov_code !=''000'' '
                     ||'    AND T1.settlement_party_in=t2.PROV_CD(+) and T1.settlement_party_out=t3.PROV_CD(+)'
                     ||'  GROUP BY T1.settlement_party_in,T1.settlement_party_out, T.prov_code, T.customer_province_number, t.customer_name,T.po_id, t.one_product_id, '
                     ||'        t.po_name, t.one_product_id, T.co_product_id, T.one_pro_charge_code,t.one_pro_charge_name, T.TAX_RATE, T.billing_term, t.pay_term, t.fee_flag, t.product_name'
                     ||'  union all '
                     ||' SELECT ''' || RPT_SETTLEMONTH || ''', ''' || G_VERSION || ''',t1.settlement_party_in, t1.settlement_party_out, t.eboss_customer_number, t.eboss_customer_name, 0, t.product_id, t.product_name, '
                     ||'        t.charge_code, t.charge_code_name, t.tax_rate*100, SUM(round(T1.settlement_amount / 10)), 0, t.billing_term, t.pay_term,''2'', t.product_id, t.product_name, '''', '''', '
                     ||'        t.subs_id, t.fee_flag - 1, T2.PROV_NM INPROVNAME, T3.PROV_NM OUTPROVNAME '
                     ||'   from sync_interface_esp_' || RPT_SETTLEMONTH || ' T, sync_interface_esp_p2p_' || RPT_SETTLEMONTH || ' T1, STL_PROVINCE_CD T2, STL_PROVINCE_CD T3 '
                     ||'  where t.busi_mode = 3 and t.id = t1.id and t.file_name = t1.file_name and t.status = 0 and t1.status = 0 and t.pay_tag = 1 '
                     ||'    AND T1.settlement_party_in=t2.PROV_CD(+) and T1.settlement_party_out=t3.PROV_CD(+)'
                     ||'  group by t1.settlement_party_in, t1.settlement_party_out, t.prov_code, t.eboss_customer_number, t.eboss_customer_name,t.product_id, '
                     ||'        t.product_name, t.charge_code, t.charge_code_name, t.tax_rate, t.billing_term, t.pay_term, t.subs_id, t.fee_flag ';
    SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

    SELECT 'procedure  node  '|| RPT_RUNSITE || '  completed successfully ';

    RPT_RUNSITE := '6';
    RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表结束时间：' ||TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
    PROC_OUT:='Y';
    COMMIT;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
    szSysErr := 'OK';
    nReturn := 0;

    SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;

  END;
END ;;
DELIMITER ;