/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层-省间报表-受理模式4报表中间表数据清理
**/
DROP PROCEDURE IF EXISTS stludr.`PKG_RPT_MPROV_STL_CLEAN`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "PKG_RPT_MPROV_STL_CLEAN"(RPT_SETTLEMONTH IN VARCHAR2,
                    STL_VER IN INT,
                    FLAG IN VARCHAR2,
                    PROC_OUT OUT VARCHAR2,
                    szSysErr OUT VARCHAR2(1000),
                    nReturn  OUT NUMBER(4) )
AS
    -- RPT_SETTLEMONTH 结算账期
    -- STL_VER  版本号码
    -- FLAG      1 : 大于等于版本号码的版本的数据全部清理
    --           其他值 : 只清理对应版本的数据
    RPT_TABLE VARCHAR2(64);
    RPT_SQL VARCHAR2(4096);
    v_proc_name  VARCHAR2(30) := 'PKG_RPT_MPROV_STL_CLEAN';

    CURSOR TABLE_LIST IS
      SELECT T.TABLE_NAME FROM RVL_TABLE_ALL T WHERE T.VALID = 'Y' AND T.PKG='MPROV';
BEGIN

  DECLARE EXIT HANDLER FOR SQLEXCEPTION
  BEGIN
      GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
      szSysErr := substr(@p2, 1, 1000);
      nReturn  := -1;
      ROLLBACK;

      select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
  END;
  BEGIN
    IF FLAG = '1' THEN
      -- 清理中间表的数据
      OPEN TABLE_LIST;
      LOOP
        FETCH TABLE_LIST INTO RPT_TABLE;
        EXIT WHEN TABLE_LIST%NOTFOUND;
        set @RPT_SQL := 'DELETE FROM '||RPT_TABLE||' T WHERE T.VERSION IN (SELECT DISTINCT VERSION FROM RVL_CONF_VERSION T1 WHERE T1.VERSIONNUM >= '||STL_VER||' AND T1.TABLE_NAME = '''||RPT_TABLE||''' AND T1.SETTLEMONTH = '''||RPT_SETTLEMONTH||''') AND T.SETTLEMONTH = '''||RPT_SETTLEMONTH||'''';
        SELECT @RPT_SQL;
          PREPARE STMT FROM @RPT_SQL;
          EXECUTE STMT;
          DEALLOCATE PREPARE STMT;
      END LOOP;
      CLOSE TABLE_LIST;
      -- 清理版本表里的数据
      DELETE FROM RVL_CONF_VERSION T
       WHERE T.VERSIONNUM >= STL_VER
         AND T.TABLE_NAME IN (SELECT T1.TABLE_NAME FROM RVL_TABLE_ALL T1 WHERE T1.VALID = 'Y' AND T1.PKG='MPROV')
         AND T.SETTLEMONTH = RPT_SETTLEMONTH;
    ELSE
      -- 清理中间表的数据
      OPEN TABLE_LIST;
      LOOP
        FETCH TABLE_LIST INTO RPT_TABLE;
        EXIT WHEN TABLE_LIST%NOTFOUND;
        set @RPT_SQL := 'DELETE FROM '||RPT_TABLE||' T WHERE T.VERSION IN (SELECT DISTINCT VERSION FROM RVL_CONF_VERSION T1 WHERE T1.VERSIONNUM = '||STL_VER||' AND T1.TABLE_NAME = '''||RPT_TABLE||''' AND T1.SETTLEMONTH = '''||RPT_SETTLEMONTH||''') AND T.SETTLEMONTH = '''||RPT_SETTLEMONTH||'''';
        SELECT @RPT_SQL;
          PREPARE STMT FROM @RPT_SQL;
          EXECUTE STMT;
          DEALLOCATE PREPARE STMT;
      END LOOP;
      CLOSE TABLE_LIST;
      -- 清理版本表里的数据
      DELETE FROM RVL_CONF_VERSION T
       WHERE T.VERSIONNUM = STL_VER
         AND T.TABLE_NAME IN (SELECT T1.TABLE_NAME FROM RVL_TABLE_ALL T1 WHERE T1.VALID = 'Y' AND T1.PKG='MPROV')
         AND T.SETTLEMONTH = RPT_SETTLEMONTH;
    END IF;
    PROC_OUT:='Y';
    COMMIT;

    nReturn := 0;
    szSysErr := 'OK';

    SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;
  END;
END ;;
DELIMITER ;