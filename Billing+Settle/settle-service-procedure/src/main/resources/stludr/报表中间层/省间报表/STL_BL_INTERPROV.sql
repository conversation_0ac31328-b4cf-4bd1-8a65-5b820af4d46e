/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层-省间报表-受理模式3实收结算中间表计算
**/
DELIMITER ;;
CREATE OR REPLACE DEFINER="stludr"@"10.%" PROCEDURE "STL_BL_INTERPROV"(RPT_SETTLEMONTH IN VARCHAR2,
                                                FLAG_VERSION IN CHAR,
                                                PROC_OUT OUT VARCHAR2,
                                                szSysErr OUT VARCHAR2(1000),
                                                nReturn OUT NUMBER(4)   )
AS
    G_VERSION RVL_CONF_VERSION.VERSION%TYPE; -- 版本号变量
    G_RESNUM  RVL_CONF_VERSION.VERSIONNUM%TYPE; -- 版本序号
    ERRCODE   VARCHAR2(32);
    ERRMSG    VARCHAR2(2048);
    VER_TABLE VARCHAR2(64);
    RPT_TABLE VARCHAR2(64);
    RPT_RUNSITE VARCHAR2(64);
    RPT_SQL VARCHAR2(4096);
    v_proc_name  VARCHAR2(30) := 'STL_BL_INTERPROV';
BEGIN

   DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
PROC_OUT := 'N';
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
ROLLBACK;

select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
END;
BEGIN
    VER_TABLE   := 'RPT_BL_INTERPROV';
    RPT_RUNSITE := '0';
    RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表开始时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
-- 中间表获取版本号
call SETTLE_REPORT_VERSION(RPT_SETTLEMONTH, VER_TABLE, G_VERSION, G_RESNUM, FLAG_VERSION,szSysErr,nReturn);
RPT_TABLE   := 'UR_RECV_' || RPT_SETTLEMONTH || '_T';
    RPT_RUNSITE := '1';
    -- 异网(T.ORDER_MODE = ''5'' AND T.DEST_SOURCE = ''1'')

    set @RPT_SQL     := 'INSERT INTO RPT_BL_INTERPROV(SETTLEMONTH, VERSION, INPROV, OUTPROV, CUSTOMERNUMBER, MEMBER_COUNT,  PRODUCT_CODE, PRODUCT_NAME,' ||CHR(10)
                     ||'        FEENUMBER, TAXRATE, SETTNOTAXFEE, SETTTAXFEE, ORG_MONTH, BAT,POSPEC_CODE,SOSPEC_CODE,PRODUCT_ORDER_ID,FEETYPE, OFFER_ORDER_ID, ' ||CHR(10)
                     ||'        CUSTOMERNAME, FEENAME, SOSPEC_NAME, POSPEC_NAME, INPROVNAME, OUTPROVNAME,settle_mode) ' ||CHR(10)
                     ||' SELECT /*+ hash_join(P,S,T1,T5,T6)*/ ''' || RPT_SETTLEMONTH || ''', ''' || G_VERSION || ''', T.IN_OBJECT,T.OUT_OBJECT, T.CUSTOMER_CODE, NUM_MEMBER_CODE, DECODE(T.PRODUCT_ORDER_ID, NULL, T.OFFER_CODE, T.PRODUCT_CODE), ' ||CHR(10)
                     ||'        CASE WHEN (T.PRODUCT_ORDER_ID IS NULL) THEN NVL(P.NAME, '''') ELSE CONCAT(NVL(P.NAME, ''''), ''_'', NVL(S.NAME, '''')) END AS PRODUCT_NAME,' ||CHR(10)
                     ||'        T.CHARGE_CODE, T.TAX_RATE, T.SUM_SETTLE_NOTAXFEE, T.SUM_SETTLE_TAXFEE, T.ORG_MONTH,  T.PHASE,T.OFFER_CODE, T.PRODUCT_CODE,T.PRODUCT_ORDER_ID, T.FEETYPE, T.OFFER_ORDER_ID,' ||CHR(10)
                     ||'        NVL(T1.FIRST_NAME, ''''), T2.DESCRIPTION, T3.NAME, T4.NAME, t5.PROV_NM, T6.PROV_NM,T.settle_mode' ||CHR(10)
                     ||'   FROM (SELECT T.IN_OBJECT,T.OUT_OBJECT, T.CUSTOMER_CODE, COUNT(DISTINCT T.MEMBER_CODE) NUM_MEMBER_CODE,  ' ||CHR(10)
                     ||'                T.CHARGE_CODE, T.TAX_RATE, SUM(NVL(T.SETTLE_NOTAXFEE, 0)) SUM_SETTLE_NOTAXFEE, SUM(NVL(T.SETTLE_TAXFEE, 0)) SUM_SETTLE_TAXFEE, T.ORG_MONTH,  ' ||CHR(10)
                     ||'                T.PHASE,T.OFFER_CODE, T.PRODUCT_CODE,T.PRODUCT_ORDER_ID, T.FEETYPE, T.OFFER_ORDER_ID,T.settle_mode  ' ||CHR(10)
                     ||'           FROM ' || RPT_TABLE || ' T' ||CHR(10)
                     ||'          WHERE (T.ORDER_MODE = ''3'' AND T.DEST_SOURCE = ''0'' and t.out_object <> ''030'' and t.in_object <> ''030'' and t.occurence not in(''YDY'',''ESP'')) ' ||CHR(10)
                     ||'             OR (T.ORDER_MODE = ''5'' AND T.DEST_SOURCE = ''1'' and t.out_object <> ''030'' and t.in_object <> ''030'')   ' ||CHR(10)
                     ||'          GROUP BY T.IN_OBJECT, T.OUT_OBJECT, T.CUSTOMER_CODE, T.PRODUCT_CODE, T.CHARGE_CODE, T.TAX_RATE, T.PHASE,T.ORG_MONTH,T.OFFER_CODE,T.PRODUCT_ORDER_ID, T.FEETYPE, T.OFFER_ORDER_ID,T.settle_mode) T' ||CHR(10)
                     ||'   LEFT JOIN STL_PRODUCT P ON P.PRODUCT_CODE = T.OFFER_CODE AND P.ACCT_MONTH = ''' || RPT_SETTLEMONTH || '''' ||CHR(10)
                     ||'   LEFT JOIN STL_SERVICE S ON S.SERVICE_CODE = T.PRODUCT_CODE AND S.ACCT_MONTH = ''' || RPT_SETTLEMONTH || '''' ||CHR(10)
                     ||'   LEFT JOIN stlusers.STL_CUSTOMER T1 ON TO_DATE(T.ORG_MONTH, ''yyyyMM'') >= TO_DATE(TO_CHAR(T1.EFFECTIVE_DATE, ''yyyyMM''), ''yyyyMM'') '||CHR(10)
                     ||'         AND TO_DATE(T.ORG_MONTH, ''yyyyMM'') < TO_DATE(TO_CHAR(T1.EXPIRY_DATE, ''yyyyMM''), ''yyyyMM'') AND T1.CUSTOMER_CODE = T.CUSTOMER_CODE'||CHR(10)
                     ||'   LEFT JOIN (select distinct NVL(DESCRIPTION,'''') DESCRIPTION, CHARGE_ITEM_REF from STL_CHARGE_ITEM_DEF where ACCT_MONTH = ''' || RPT_SETTLEMONTH || ''') T2 on T.CHARGE_CODE <> ''YWJS'' and T2.CHARGE_ITEM_REF = T.CHARGE_CODE'||CHR(10)
                     ||'   LEFT JOIN (select distinct NVL(NAME, '''') NAME, SERVICE_CODE from STL_SERVICE where  ACCT_MONTH = ''' || RPT_SETTLEMONTH || ''') T3 ON T.PRODUCT_CODE = T3.SERVICE_CODE'||CHR(10)
                     ||'   LEFT JOIN (select distinct NVL(NAME, '''') NAME, PRODUCT_CODE from STL_PRODUCT where  ACCT_MONTH = ''' || RPT_SETTLEMONTH || ''') T4 ON T.OFFER_CODE = T4.PRODUCT_CODE'||CHR(10)
                     ||'   LEFT JOIN STL_PROVINCE_CD T5 ON T.IN_OBJECT = T5.PROV_CD'||CHR(10)
                     ||'   LEFT JOIN STL_PROVINCE_CD T6 ON T.OUT_OBJECT = T6.PROV_CD';
SELECT @RPT_SQL;
PREPARE STMT FROM @RPT_SQL;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


---- 插入移动云账单数据
RPT_RUNSITE := '5';
    set @RPT_SQL     := 'INSERT INTO RPT_BL_INTERPROV(SETTLEMONTH, VERSION, INPROV, OUTPROV, CUSTOMERNUMBER, customername, MEMBER_COUNT,PRODUCT_CODE, PRODUCT_NAME, FEENUMBER, feename, TAXRATE, '||chr(10)
                     ||'        SETTNOTAXFEE, SETTTAXFEE, ORG_MONTH, BAT,POSPEC_CODE,pospec_name,SOSPEC_CODE,sospec_name,PRODUCT_ORDER_ID, feetype, offer_order_id, INPROVNAME,OUTPROVNAME) '||chr(10)
                     ||' SELECT ''' || RPT_SETTLEMONTH || ''', ''' || G_VERSION || ''', T1.settlement_party_in,T1.settlement_party_out, T.customer_province_number, t.customer_name, 0, '||chr(10)
                     ||'        t.one_product_id, t.po_name ||  decode(t.one_product_id, ''111601'', ''_'' || t.product_name, null), T.one_pro_charge_code, t.one_pro_charge_name, T.TAX_RATE * 100, '||chr(10)
                     ||'        SUM(NVL(round(T1.settlement_amount / 10), 0)), 0, T.billing_term, ''2'',T.po_id,t.po_name,t.one_product_id,decode(t.one_product_id, ''111601'', t.product_name, ''''), '||chr(10)
                     ||'        T.co_product_id,t.fee_flag - 1,T.co_product_id, T2.PROV_NM INPROVNAME, T3.PROV_NM OUTPROVNAME'||chr(10)
                     ||'   FROM sync_interface_mc_' || RPT_SETTLEMONTH || ' T, sync_interface_mc_p2p_' || RPT_SETTLEMONTH || ' t1, STL_PROVINCE_CD T2, STL_PROVINCE_CD T3 '||chr(10)
                     ||'  WHERE T.ORDER_MODE = 3  and t.id = t1.id and t.file_name = t1.file_name and t.status = 0 and t1.status = 0 and t.pay_tag = 1  and t.prov_code !=''000'' '||chr(10)
                     ||'    AND T1.settlement_party_in=t2.PROV_CD(+) and T1.settlement_party_out=t3.PROV_CD(+)'||chr(10)
                     ||'  GROUP BY T1.settlement_party_in,T1.settlement_party_out, T.prov_code, T.customer_province_number, t.customer_name,T.po_id, t.one_product_id, t.po_name, '||chr(10)
                     ||'        t.one_product_id, T.co_product_id, T.one_pro_charge_code,t.one_pro_charge_name, T.TAX_RATE, T.billing_term, t.fee_flag, T2.PROV_NM, T3.PROV_NM, t.product_name '||chr(10)
                     ||'  union all '||chr(10)
                     ||' SELECT ''' || RPT_SETTLEMONTH || ''', ''' || G_VERSION || ''', t1.settlement_party_in, t1.settlement_party_out, T.eboss_customer_number, t.eboss_customer_name, 0, '||chr(10)
                     ||'        t.product_id, t.product_name, t.charge_code, t.charge_code_name, t.tax_rate * 100, SUM(round(T1.settlement_amount / 10)), 0, t.billing_term, ''2'', t.product_id, '||chr(10)
                     ||'        t.product_name,'''','''',t.subs_id, t.fee_flag - 1,t.subs_id, T2.PROV_NM INPROVNAME, T3.PROV_NM OUTPROVNAME'||chr(10)
                     ||'   FROM sync_interface_esp_' || RPT_SETTLEMONTH || '  T, sync_interface_esp_p2p_' || RPT_SETTLEMONTH || ' t1, STL_PROVINCE_CD T2, STL_PROVINCE_CD T3 '||chr(10)
                     ||'  WHERE T.busi_mode = 3 and t.id = t1.id and t.file_name = t1.file_name and t.status = 0 and t1.status = 0 and t.pay_tag = 0 '||chr(10)
                     ||'    AND T1.settlement_party_in=t2.PROV_CD(+) and T1.settlement_party_out=t3.PROV_CD(+)'||chr(10)
                     ||'  group by t1.settlement_party_in, t1.settlement_party_out, T.prov_code, t.eboss_customer_number, t.eboss_customer_name, t.product_id, '||chr(10)
                     ||'        t.product_name, t.charge_code, t.charge_code_name, t.tax_rate, t.billing_term, t.subs_id, t.fee_flag, T2.PROV_NM, T3.PROV_NM ';
SELECT @RPT_SQL;
PREPARE STMT FROM @RPT_SQL;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

RPT_RUNSITE := '6';
    RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表结束时间：' ||TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
    PROC_OUT:='Y';
COMMIT;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
    szSysErr := 'OK';
    nReturn := 0;

    SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;

    END;
END;;
DELIMITER ;
