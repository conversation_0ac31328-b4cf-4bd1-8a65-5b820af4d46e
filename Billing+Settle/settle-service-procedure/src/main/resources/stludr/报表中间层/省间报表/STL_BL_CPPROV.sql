/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层-省间报表-受理模式3基地应收结算中间表计算
**/
DROP PROCEDURE IF EXISTS stludr.`STL_BL_CPPROV`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "STL_BL_CPPROV"(RPT_SETTLEMONTH IN VARCHAR2,
                                            FLAG_VERSION IN CHAR,
                                            PROC_OUT OUT VARCHAR2,
                                            szSysErr OUT VARCHAR2(1000),
                                            nReturn OUT NUMBER(4)   )
AS
    G_VERSION RVL_CONF_VERSION.VERSION%TYPE; -- 版本号变量
    G_RESNUM  RVL_CONF_VERSION.VERSIONNUM%TYPE; -- 版本序号
    ERRCODE   VARCHAR2(32);
    ERRMSG    VARCHAR2(2048);
    VER_TABLE VARCHAR2(64);
    RPT_TABLE VARCHAR2(64);
    RPT_RUNSITE VARCHAR2(64);
    RPT_SQL VARCHAR2(4096);
    v_proc_name         VARCHAR2(30) := 'STL_BL_CPPROV';

BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
PROC_OUT := 'N';
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
        ROLLBACK;

        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;
  BEGIN
    VER_TABLE   := 'RPT_BL_CPPROV';
    RPT_RUNSITE := '0';
    RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表开始时间：' ||TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
    -- 中间表获取版本号
    call SETTLE_REPORT_VERSION(RPT_SETTLEMONTH,VER_TABLE,G_VERSION,G_RESNUM,FLAG_VERSION,szSysErr,nReturn);
    RPT_RUNSITE := '1';
    RPT_TABLE   := 'UR_CPR_' || RPT_SETTLEMONTH || '_T';
    set @RPT_SQL     := 'INSERT INTO RPT_BL_CPPROV(SETTLEMONTH,VERSION,INPROV,OUTPROV,CUSTOMERNUMBER,'
                     ||'ORG_MONTH,BAT,PRODUCT_CODE,FEENUMBER,CPNOTTAXFEE,CPTAXFEE,TAXRATE,OFFER_CODE,feetype)'
                     ||' SELECT ''' || RPT_SETTLEMONTH || ''', ''' || G_VERSION || ''', T.IN_OBJECT, T.OUT_OBJECT, T.CUSTOMER_CODE, '
                     ||'T.ORG_MONTH, T.PHASE, T.PRODUCT_CODE, T.CHARGE_CODE, SUM(T.SETTLE_NOTAXFEE), SUM(T.SETTLE_TAXFEE), T.TAX_RATE,T.OFFER_CODE,t.feetype '
                     ||' FROM ' ||RPT_TABLE || ' T '
                     ||' WHERE T.DEST_SOURCE = ''7'' AND T.ORDER_MODE = ''3'' and t.out_object <> ''030'' and t.in_object <> ''030'''
                     ||' GROUP BY T.IN_OBJECT, T.OUT_OBJECT, T.CUSTOMER_CODE,  T.ORG_MONTH, T.PHASE, T.PRODUCT_CODE, T.CHARGE_CODE, T.TAX_RATE,T.OFFER_CODE,t.feetype';
    SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
    RPT_RUNSITE := '2';
    RPT_SQL     := '更新表中省公司的中文名称... ...';
    UPDATE RPT_BL_CPPROV T
       SET T.INPROVNAME  = (SELECT T1.PROV_NM FROM STL_PROVINCE_CD T1 WHERE T1.PROV_CD = T.INPROV),
           T.OUTPROVNAME = (SELECT T1.PROV_NM FROM STL_PROVINCE_CD T1 WHERE T1.PROV_CD = T.OUTPROV)
     WHERE T.VERSION = G_VERSION AND T.SETTLEMONTH = RPT_SETTLEMONTH;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

    RPT_RUNSITE := '3';
    RPT_SQL     := '更新表中客户名称的中文名称... ...';
    UPDATE RPT_BL_CPPROV T
       SET T.CUSTOMERNAME = (SELECT NVL(T1.FIRST_NAME, '') FROM stlusers.STL_CUSTOMER T1 WHERE T.ORG_MONTH BETWEEN DATE_FORMAT(T1.effective_date, '%Y%m') AND DATE_FORMAT(T1.expiry_date, '%Y%m') AND T1.CUSTOMER_CODE = T.CUSTOMERNUMBER)
     WHERE T.VERSION = G_VERSION AND T.SETTLEMONTH = RPT_SETTLEMONTH;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
    RPT_RUNSITE := '4';
    RPT_SQL     := '更新表中产品和商品的中文名称... ...';
    UPDATE RPT_BL_CPPROV T
       SET T.PRODUCT_NAME = (SELECT DISTINCT NVL(T1.NAME, '') FROM STL_SERVICE T1 WHERE T1.SERVICE_CODE = T.PRODUCT_CODE AND T1.ACCT_MONTH = RPT_SETTLEMONTH),
           T.OFFER_NAME   = (SELECT DISTINCT NVL(T1.NAME, '') FROM STL_PRODUCT T1 WHERE T1.PRODUCT_CODE = T.OFFER_CODE AND T1.ACCT_MONTH = RPT_SETTLEMONTH)
     WHERE T.VERSION = G_VERSION AND T.SETTLEMONTH = RPT_SETTLEMONTH;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
    RPT_RUNSITE := '5';
    RPT_SQL     := '更新表费项分类和费项的中文名称（201811后不再更新费项分类）... ...';
    UPDATE RPT_BL_CPPROV T
       SET T.FEENAME =(SELECT DISTINCT NVL(T1.DESCRIPTION, '') FROM STL_CHARGE_ITEM_DEF T1 WHERE T1.CHARGE_ITEM_REF = T.FEENUMBER AND T1.ACCT_MONTH = RPT_SETTLEMONTH) /*,
           T.FEETYPE     = CASE WHEN T.FEENUMBER > 0 AND T.FEENUMBER <= 50 THEN '0'
                                WHEN T.FEENUMBER >= 1000 AND T.FEENUMBER < 5000 THEN '0'
                                WHEN T.FEENUMBER > 50 AND T.FEENUMBER <= 100 THEN '1'
                                WHEN T.FEENUMBER >= 5000 AND T.FEENUMBER < 10000 THEN '1' END*/
     WHERE T.VERSION = G_VERSION AND T.SETTLEMONTH = RPT_SETTLEMONTH;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
    /*添加下个账期分区--start*/
    -- SELECT TO_CHAR(ADD_MONTHS(TO_DATE(RPT_SETTLEMONTH, 'yyyymm'), 1), 'yyyymm') INTO RPT_RUNSITE;

    -- SELECT COUNT(1) FROM information_schema.partitions T WHERE TABLE_NAME = ''' || VER_TABLE || ''' AND SUBSTR(T.PARTITION_NAME, -6) = '''||RPT_RUNSITE||''' INTO PROC_OUT;

    -- IF PROC_OUT = 0 THEN
    -- set @RPT_SQL := 'ALTER TABLE '|| VER_TABLE ||' ADD PARTITION (PARTITION SETTLEMONTH' || RPT_RUNSITE || ' VALUES IN (''' || RPT_RUNSITE || '''))' ;
    -- SELECT @RPT_SQL;
    --     PREPARE STMT FROM @RPT_SQL;
    --     EXECUTE STMT;
    --     DEALLOCATE PREPARE STMT;
    -- END IF;

   /*添加下个账期分区--end*/

    RPT_RUNSITE := '6';
    RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表结束时间：' ||TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
    PROC_OUT:='Y';
    COMMIT;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
    szSysErr := 'OK';
    nReturn := 0;

    SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;

  END;
END ;;
DELIMITER ;