/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层-省间报表-BBOSS发票开具明细报表数据生成
**/
DELIMITER ;;
CREATE or replace DEFINER="stludr"@"10.%" PROCEDURE "RPT_OTHER_STL_RECEIPT_DETAIL"(
    RPT_SETTLEMONTH IN VARCHAR2,
    FLAG_VERSION IN CHAR,
    PROC_OUT OUT VARCHAR2,
    szSysErr OUT VARCHAR2(1000),
    nReturn OUT NUMBER(4)
)
AS

    v_proc_name varchar2(100) := 'RPT_OTHER_STL_RECEIPT_DETAIL';
    G_VERSION   RVL_CONF_VERSION.VERSION%TYPE; --版本号变量
    G_RESNUM    RVL_CONF_VERSION.VERSIONNUM%TYPE; --版本序号
    ERRCODE     VARCHAR2(32);
    ERRMSG      VARCHAR2(2048);
    P_SQL       VARCHAR2(1024);
    P_SWAP_SQL       VARCHAR2(1024);
    RPT_CODE    VARCHAR2(32);
    VER_TABLE   VARCHAR2(64);
    PROC_NUMBER int;
    RPT_TAXRATE STL_CONF_DICT%ROWTYPE;
    RPT_RUNSITE VARCHAR2(32); --运行定位标识
    RPT_SQL     VARCHAR2(4000); --拼SQL的字符串变量
    RPT_TABLE   VARCHAR2(64); --拼表名的字符串变量

    dict_value  VARCHAR2(32);

    outReturn  int;

CURSOR AR_TAXRATE_LIST IS
SELECT t.dictvalue  FROM STL_CONF_DICT T WHERE T.ITEM = 'TAXRATE'   AND TO_DATE('' || RPT_SETTLEMONTH || '', 'yyyyMM') BETWEEN  TO_DATE(TO_CHAR(T.EFFECTIVE_DATE, 'yyyyMM'), 'yyyyMM') AND
    TO_DATE(TO_CHAR(T.EXPIRY_DATE, 'yyyyMM'), 'yyyyMM')   AND t.remark='AR'  ORDER BY t.DICTORDER;

CURSOR BL_TAXRATE_LIST IS
SELECT t.dictvalue  FROM STL_CONF_DICT T WHERE T.ITEM = 'TAXRATE'   AND TO_DATE('' || RPT_SETTLEMONTH || '', 'yyyyMM') BETWEEN  TO_DATE(TO_CHAR(T.EFFECTIVE_DATE, 'yyyyMM'), 'yyyyMM') AND
    TO_DATE(TO_CHAR(T.EXPIRY_DATE, 'yyyyMM'), 'yyyyMM')   AND T.REMARK = 'BL' ORDER BY T.DICTORDER;
BEGIN

    PROC_OUT := '';
    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
            PROC_OUT := 'N';
            szSysErr := substr(@p2, 1, 1000);
            nReturn  := -1;
        ROLLBACK;

        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;

BEGIN
        if ( length(RPT_SETTLEMONTH) < 6 )  then
            SELECT 'RPT_SETTLEMONTH length less than 6. RPT_SETTLEMONTH=[' || RPT_SETTLEMONTH || ']';
            PROC_OUT := 'N';
            outReturn := -1;
            ROLLBACK;
            RETURN;
        end if;

SELECT 'RPT_SETTLEMONTH=' ||  RPT_SETTLEMONTH;

SELECT 'mark1';
VER_TABLE   := 'RPT_RECEIPT_DETAIL';
        RPT_RUNSITE := '0';
        RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表开始时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
SELECT 'mark2';

--中间表获取版本号
call SETTLE_REPORT_VERSION(RPT_SETTLEMONTH, VER_TABLE, G_VERSION, G_RESNUM, FLAG_VERSION,PROC_OUT,outReturn);
SELECT 'mark3';

P_SQL := 'INSERT INTO RPT_RECEIPT_DETAIL(SETTLEMONTH,VERSION,SETT_NUM,CODE,BIZ,INPROV_CODE,OUTPROV_CODE,TAXRATE,SETT_NOTAXFEE,SETT_TAXFEE,SETTMODE)';

        RPT_RUNSITE := '1';
        PROC_NUMBER := 0;
SELECT 'mark4';

--应收数据
/*CR1218   201805041815   税率 11>10  17>16*/
--for RPT_TAXRATE in BL_TAXRATE_LIST LOOP
OPEN BL_TAXRATE_LIST;
LOOP
SELECT 'mark4.0';
fetch  BL_TAXRATE_LIST INTO dict_value;
EXIT WHEN BL_TAXRATE_LIST%NOTFOUND ;
BEGIN
                PROC_NUMBER := ROUND(PROC_NUMBER+1);
                RPT_RUNSITE := to_char(PROC_NUMBER);
SELECT 'mark4.1 PROC_NUMBER=' || PROC_NUMBER;
SELECT 'mark4.1 RPT_RUNSITE=' || RPT_RUNSITE;

RPT_TABLE := 'UR_RECV_' || RPT_SETTLEMONTH || '_T';
                IF (dict_value='11') THEN
                  RPT_CODE:='D208-A';
                ELSIF (dict_value='6') THEN
                  RPT_CODE:='D208-B';
                ELSIF (dict_value='17') THEN
                  RPT_CODE:='D208-C';
                ELSIF (dict_value='0') THEN
                  RPT_CODE:='D208-D';
                ELSIF (dict_value='10') THEN
                  RPT_CODE:='D208-E';
                ELSIF (dict_value='16') THEN
                  RPT_CODE:='D208-F';
                ELSIF (dict_value='9') THEN
                  RPT_CODE:='D208-G';
                ELSIF (dict_value='13') THEN
                  RPT_CODE:='D208-H';
END IF;
SELECT 'BL_TAXRATE_LIST. RPT_CODE=' || RPT_CODE;
SELECT 'mark4.2';
SET @RPT_SQL   := P_SQL ||' SELECT /*+ hash_join(T,T3)*/ ''' || RPT_SETTLEMONTH || ''',''' || G_VERSION || ''',1,'''||RPT_CODE||''',''省公司主办集团客户业务（' || dict_value || '%税率）'',T.INPROV_CODE,T.OUTPROV_CODE,''' || dict_value || ''',NVL(T3.SETTLE_NOTAXFEE, 0),NVL(T3.SETTLE_TAXFEE, 0),''1'' '
                                   ||' FROM (SELECT T1.PROV_CD AS INPROV_CODE, T2.PROV_CD AS OUTPROV_CODE '
                                         ||' FROM STL_PROVINCE_CD T1, STL_PROVINCE_CD T2) T '
                                   ||' LEFT JOIN '
                                   ||' ( SELECT /*+ no_index(T3 idx_om_ds)*/ T3.OUT_OBJECT, T3.IN_OBJECT, NVL(SUM(T3.SETTLE_NOTAXFEE), 0) SETTLE_NOTAXFEE, NVL(SUM(T3.SETTLE_TAXFEE), 0) SETTLE_TAXFEE  FROM '|| RPT_TABLE || ' T3 WHERE (T3.PRODUCT_CODE NOT IN (''99902'', ''911601'') OR T3.PRODUCT_CODE IS NULL) '
                                   ||'             AND T3.OFFER_CODE NOT IN (''0103004'', ''0103005'') AND T3.ORDER_MODE = ''3'' AND T3.DEST_SOURCE = ''0''  '
                                   ||'             AND T3.TAX_RATE = ''' || dict_value || ''' and t3.out_object <> ''030'' and t3.in_object <> ''030''  group by T3.IN_OBJECT, T3.OUT_OBJECT)   T3 '
                                   ||' ON T.INPROV_CODE = T3.IN_OBJECT '
                                       ||' AND T.OUTPROV_CODE = T3.OUT_OBJECT ';

SELECT @RPT_SQL;
PREPARE STMT FROM @RPT_SQL;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, @RPT_SQL);
SELECT 'mark4.4';
END;
END LOOP;
CLOSE BL_TAXRATE_LIST;
SELECT 'mark5';

--实收数据
OPEN AR_TAXRATE_LIST;
LOOP
SELECT 'mark5.0';
fetch  AR_TAXRATE_LIST INTO dict_value;
EXIT WHEN AR_TAXRATE_LIST%NOTFOUND ;
        --for RPT_TAXRATE in AR_TAXRATE_LIST LOOP
        --OPEN AR_TAXRATE_LIST;
        --LOOP
            --FETCH AR_TAXRATE_LIST INTO RPT_TAXRATE;
            --EXIT WHEN AR_TAXRATE_LIST%NOTFOUND;
            RPT_TABLE := 'UR_PAID_' || RPT_SETTLEMONTH || '_T';
            PROC_NUMBER := ROUND(PROC_NUMBER+1);
SELECT 'mark5.1 PROC_NUMBER=' || PROC_NUMBER;
RPT_RUNSITE := to_char(PROC_NUMBER);

SELECT 'mark5.1 RPT_RUNSITE=' || RPT_RUNSITE;

IF (dict_value='11') THEN
              RPT_CODE:='D208-A';
            ELSIF (dict_value='6') THEN
              RPT_CODE:='D208-B';
            ELSIF (dict_value='17') THEN
              RPT_CODE:='D208-C';
            ELSIF (dict_value='0') THEN
              RPT_CODE:='D208-D';
            ELSIF (dict_value='10') THEN
              RPT_CODE:='D208-E';
            ELSIF (dict_value='16') THEN
              RPT_CODE:='D208-F';
            ELSIF (dict_value='9') THEN
              RPT_CODE:='D208-G';
            ELSIF (dict_value='13') THEN
              RPT_CODE:='D208-H';
END IF;
SELECT 'AR_TAXRATE_LIST. RPT_CODE=' || RPT_CODE;
SELECT 'mark5.2';
SET @RPT_SQL   := P_SQL ||' SELECT ''' || RPT_SETTLEMONTH || ''',''' || G_VERSION || ''',1,'''||RPT_CODE||''',''省公司主办集团客户业务（' || dict_value || '%税率）'',T.INPROV_CODE,T.OUTPROV_CODE,''' || dict_value || ''',NVL(T3.SETTLE_NOTAXFEE, 0),NVL(T3.SETTLE_TAXFEE, 0),''2'' '
                ||' FROM (SELECT T1.PROV_CD AS INPROV_CODE, T2.PROV_CD AS OUTPROV_CODE '
                ||'       FROM STL_PROVINCE_CD T1, STL_PROVINCE_CD T2 ) T '
                ||' LEFT JOIN '
                ||' (SELECT /*+ no_index(T3 idx_om_ds)*/
                                  T3.IN_OBJECT,
                                  T3.OUT_OBJECT,
                                  NVL(SUM(T3.SETTLE_NOTAXFEE), 0) SETTLE_NOTAXFEE,
                                  NVL(SUM(T3.SETTLE_TAXFEE), 0) SETTLE_TAXFEE   FROM ' || RPT_TABLE || ' T3 WHERE (DECODE(T3.PRODUCT_ORDER_ID, NULL, T3.OFFER_CODE, T3.PRODUCT_CODE) IN '
                ||'          (''99902'', ''911601'') OR T3.OFFER_CODE IN (''0103004'', ''0103005'')) AND T3.ORDER_MODE = ''3'' AND T3.DEST_SOURCE = ''0'' '
                ||'          AND T3.TAX_RATE = ''' || dict_value || ''' and t3.out_object <> ''030'' and t3.in_object <> ''030'' group by T3.IN_OBJECT, T3.OUT_OBJECT) T3 '
                ||' ON T.INPROV_CODE = T3.IN_OBJECT '
                ||' AND T.OUTPROV_CODE = T3.OUT_OBJECT '
                ;

SELECT @RPT_SQL;
PREPARE STMT FROM @RPT_SQL;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
SELECT 'mark5.3';
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, @RPT_SQL);
SELECT 'mark5.4';
END LOOP;
CLOSE AR_TAXRATE_LIST;
SELECT 'mark6';

PROC_NUMBER := PROC_NUMBER+1;
        RPT_RUNSITE := to_char(PROC_NUMBER);
        RPT_TABLE := 'UR_CPP_' || RPT_SETTLEMONTH || '_T';
        SET @RPT_SQL   := P_SQL ||' SELECT ''' || RPT_SETTLEMONTH || ''',''' || G_VERSION || ''',2,''D232'',''车务通'',''240'',T.OUTPROV_CODE,''6'',NVL(SUM(T3.SETTLE_NOTAXFEE), 0),NVL(SUM(T3.SETTLE_TAXFEE), 0),''2'' '
            ||' FROM (SELECT ''SIACN016'' AS INPROV_CODE, T2.PROV_CD AS OUTPROV_CODE '
                  ||' FROM STL_PROVINCE_CD T2 ) T '
            ||' LEFT JOIN (SELECT *  FROM ' || RPT_TABLE || ' T3 WHERE T3.PRODUCT_CODE IN (''110901'', ''110903'') AND T3.TAX_RATE = ''6'' and t3.out_object <> ''030'' and t3.in_object <> ''030'') T3 '
            ||' ON T.INPROV_CODE = T3.IN_OBJECT '
            ||' AND T.OUTPROV_CODE = T3.OUT_OBJECT '
            ||' GROUP BY T.INPROV_CODE, T.OUTPROV_CODE';

SELECT @RPT_SQL;
PREPARE STMT FROM @RPT_SQL;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, @RPT_SQL);
SELECT 'mark7';

PROC_NUMBER := PROC_NUMBER+1;
        RPT_RUNSITE := to_char(PROC_NUMBER);
        RPT_SQL     := '更新表中省公司的中文名称... ...';
UPDATE RPT_RECEIPT_DETAIL T
SET T.INPROV_NAME  = (SELECT T1.PROV_NM FROM STL_PROVINCE_CD T1 WHERE T1.PROV_CD = T.INPROV_CODE),
    T.OUTPROV_NAME = (SELECT T1.PROV_NM FROM STL_PROVINCE_CD T1 WHERE T1.PROV_CD = T.OUTPROV_CODE)
WHERE T.VERSION = G_VERSION AND T.SETTLEMONTH = RPT_SETTLEMONTH;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
SELECT 'mark8';

PROC_NUMBER := PROC_NUMBER+1;
        RPT_RUNSITE := to_char(PROC_NUMBER);
        RPT_SQL     := '更新表中结算含税费用... ...';
UPDATE RPT_RECEIPT_DETAIL T
SET T.SETT_FEE     = T.SETT_NOTAXFEE + T.SETT_TAXFEE
WHERE T.VERSION = G_VERSION AND T.SETTLEMONTH = RPT_SETTLEMONTH;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
SELECT 'mark9';

PROC_NUMBER := PROC_NUMBER+1;
        RPT_RUNSITE := to_char(PROC_NUMBER);
        RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表结束时间：' ||TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
SELECT 'mark10';

commit;

PROC_OUT:='Y';
        outReturn := 0;

SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
END;
END;;
DELIMITER ;