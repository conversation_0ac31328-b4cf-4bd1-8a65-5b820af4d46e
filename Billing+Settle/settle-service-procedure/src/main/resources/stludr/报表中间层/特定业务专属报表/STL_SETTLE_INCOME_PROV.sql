/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层-特定业务专属报表-集团客户业务结算单明细表数据生成
**/
use stludr;
DROP PROCEDURE IF EXISTS stludr.`STL_SETTLE_INCOME_PROV`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE stludr."STL_SETTLE_INCOME_PROV"(RPT_SETTLEMONTH IN VARCHAR2,
                                   FLAG_VERSION    IN CHAR,
                                   PROC_OUT        OUT VARCHAR2,
                                   szSysErr OUT VARCHAR2(1000),
                                   nReturn OUT NUMBER(4)   )
AS
    RPT_SQL     VARCHAR2(8000); --拼SQL的字符串变量
    RPT_TABLE   VARCHAR2(64); --获取政企和省公司的临时数据表
    ARQ_TABLE   VARCHAR2(64); --报表中间表名
    PAM_TABLE   VARCHAR2(64); --对照配置表
    VER_TABLE   VARCHAR2(64); --存储过程名称
    RPT_RUNSITE VARCHAR2(64); --位置编码
    v_proc_name VARCHAR2(30) := 'STL_SETTLE_INCOME_PROV';


    CURSOR PROV_LIST IS SELECT T.PROV_CD, T.PROV_NM FROM STLUDR.STL_PROVINCE_CD T ORDER BY T.PROV_CD;

BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        PROC_OUT := 'N';
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
        ROLLBACK;

        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;

  BEGIN

    ARQ_TABLE := 'STLUDR.RPT_SETTLE_INCOME_PROV';

    RPT_TABLE := 'STLUDR.RPT_SETTLE_INCOME_PROV_TMP';

    PAM_TABLE := 'STLUDR.STL_COMMODITY_MAPPING';

    VER_TABLE   := 'STL_SETTLE_INCOME_PROV';
    RPT_RUNSITE := '2000';
    /*开始时间*/
    RPT_SQL := VER_TABLE || '存储过程' || RPT_SETTLEMONTH || '账期数据入库报表中间表开始时间：' ||
               TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

    /*
       如果flag为0
       清空报表中间表的本账期数据
    */
    IF FLAG_VERSION = 'Y' THEN
      /*清理报表中间表当前账期数据*/
      set @RPT_SQL := 'DELETE ' || ARQ_TABLE || ' t WHERE t.SETTLEMONTH=''' ||
                 RPT_SETTLEMONTH || '''';
      SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

      /*日志*/
      RPT_SQL := VER_TABLE || '存储过程,清理报表中间表' || RPT_SETTLEMONTH ||
                 '账期数据,执行时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

      /*跳出存储过程*/
      RETURN;
    END IF;

    /*清理临时表表当前账期数据*/
    set @RPT_SQL := 'DELETE ' || RPT_TABLE || ' t WHERE t.settlemonth=''' ||
               RPT_SETTLEMONTH || '''';
    SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

    /*清理报表中间表当前账期数据*/
    set @RPT_SQL := 'DELETE ' || ARQ_TABLE || ' t WHERE t.SETTLEMONTH=''' ||
               RPT_SETTLEMONTH || '''';
    SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

    /*临时表插入表当前账期数据--start */
    /*政企公司--start*/
    /* 结入*/
    -- 最新优化sql1
    set  @RPT_SQL := 'INSERT INTO ' || RPT_TABLE || ' ' ||
               'SELECT null, T1.SETTLEMONTH, ' ||
       'T1.PROV_CODE, ' ||
       'T1.PROV_NAME, ' ||
       'T1.POSPEC_CODE, ' ||
       'T1.POSPEC_NAME, ' ||
       'T1.SOSPEC_CODE, ' ||
       'T1.SOSPEC_NAME, ' ||
       'T1.CHARGE_CODE, ' ||
       'T1.CHARGE_NAME, ' ||
       'T1.TAXRATE AS TAXRATE, ' ||
       'SUM(T1.SETTNOTAXFEE) AS SETTNOTAXFEE ' ||
  'FROM (SELECT ''' || rpt_settlemonth || ''' as settlemonth, ' ||
               'T1.in_object AS PROV_CODE, ' ||
               'prov.prov_nm AS PROV_NAME, ' ||
               'T1.offer_code as POSPEC_CODE, ' ||
               'T1.offer_code as POSPEC_NAME, ' ||
               'T1.product_code as SOSPEC_CODE, ' ||
               'T1.product_code as SOSPEC_NAME, ' ||
               'T1.CHARGE_CODE, ' ||
               'T1.CHARGE_CODE as charge_name, ' ||
               'T1.TAX_RATE AS TAXRATE, ' ||
               'SUM(NVL(T1.SETTLE_NOTAXFEE, 0)) AS SETTNOTAXFEE ' ||
          'FROM STLUDR.ur_recv_' || rpt_settlemonth || '_t T1 join stl_province_cd prov on t1.in_object = prov.prov_cd ' ||
         'WHERE T1.DEST_SOURCE = ''0'' ' ||
           'AND T1.ORDER_MODE = ''1'' ' ||
         'GROUP BY T1.in_object, ' ||
                  'prov.prov_nm, ' ||
                  'T1.offer_code, ' ||
                  'T1.product_code, ' ||
                  'T1.CHARGE_CODE, ' ||
                  'T1.TAX_RATE ' ||
        'UNION ALL ' ||
        'SELECT ''' || rpt_settlemonth || ''', ' ||
               'T3.in_object, ' ||
               'prov.prov_nm, ' ||
               'T3.offer_code as POSPEC_CODE, ' ||
               'T3.offer_code as POSPEC_NAME, ' ||
               'T3.product_code as SOSPEC_CODE, ' ||
               'T3.product_code as SOSPEC_NAME, ' ||
               'T3.CHARGE_CODE, ' ||
               'T3.charge_code as CHARGE_NAME, ' ||
               'T3.TAX_RATE, ' ||
               'SUM(NVL(T3.SETTLE_NOTAXFEE, 0)) ' ||
          'FROM STLUDR.ur_cpr_' || rpt_settlemonth || '_t T3 join stl_province_cd prov on t3.in_object = prov.prov_cd ' ||
         'WHERE T3.DEST_SOURCE = ''7'' ' ||
           'AND T3.ORDER_MODE = ''1'' ' ||
         'GROUP BY T3.in_object, ' ||
                  'prov.prov_nm, ' ||
                  'T3.offer_code, ' ||
                  'T3.product_code, ' ||
                  'T3.CHARGE_CODE, ' ||
                  'T3.TAX_RATE) T1 ' ||
 'GROUP BY T1.SETTLEMONTH, ' ||
          'T1.PROV_CODE, ' ||
          'T1.PROV_NAME, ' ||
          'T1.POSPEC_CODE, ' ||
          'T1.POSPEC_NAME, ' ||
          'T1.SOSPEC_CODE, ' ||
          'T1.SOSPEC_NAME, ' ||
          'T1.CHARGE_CODE, ' ||
          'T1.CHARGE_NAME, ' ||
          'T1.TAXRATE';
    SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    COMMIT;


    /*日志*/
    RPT_RUNSITE := '2001';

    /*结出*/
    -- 最新优化sql2
    set  @RPT_SQL := 'INSERT INTO ' || RPT_TABLE || ' ' ||
               'SELECT null, T1.SETTLEMONTH, ' ||
       'T1.PROV_CODE, ' ||
       'T1.PROV_NAME, ' ||
       'T1.POSPEC_CODE, ' ||
       'T1.POSPEC_NAME, ' ||
       'T1.SOSPEC_CODE, ' ||
       'T1.SOSPEC_NAME, ' ||
       'T1.CHARGE_CODE, ' ||
       'T1.CHARGE_NAME, ' ||
       'T1.TAXRATE AS TAXRATE, ' ||
       'SUM(T1.SETTNOTAXFEE) AS SETTNOTAXFEE ' ||
  'FROM (SELECT ''' || rpt_settlemonth || ''' settlemonth, ' ||
               'T4.out_object AS PROV_CODE, ' ||
               'prov.prov_nm AS PROV_NAME, ' ||
               'T4.offer_code as POSPEC_CODE, ' ||
               'T4.offer_code as POSPEC_NAME, ' ||
               'T4.product_code as SOSPEC_CODE, ' ||
               'T4.product_code as SOSPEC_NAME, ' ||
               'T4.CHARGE_CODE, ' ||
               'T4.charge_code as CHARGE_NAME, ' ||
               'T4.tax_rate as TAXRATE, ' ||
               'SUM(-NVL(T4.SETTLE_NOTAXFEE, 0)) AS SETTNOTAXFEE ' ||
          'FROM STLUDR.ur_recv_' || rpt_settlemonth || '_t T4 join stl_province_cd prov on t4.out_object = prov.prov_cd ' ||
         'WHERE t4.dest_source = ''0'' ' ||
           'and t4.order_mode = ''1'' ' ||
         'GROUP BY T4.out_object, ' ||
                  'prov.prov_nm, ' ||
                  'T4.offer_code, ' ||
                  'T4.product_code, ' ||
                  'T4.CHARGE_CODE, ' ||
                  'T4.TAX_RATE ' ||
        'UNION ALL ' ||
        'SELECT ''' || rpt_settlemonth || ''', ' ||
               'T2.out_object, ' ||
               'prov.prov_nm, ' ||
               'T2.offer_code, ' ||
               'T2.offer_code, ' ||
               'T2.product_code, ' ||
               'T2.product_code, ' ||
               'T2.CHARGE_CODE, ' ||
               'T2.CHARGE_CODE, ' ||
               'T2.TAX_RATE, ' ||
               'SUM(-NVL(T2.SETTLE_NOTAXFEE, 0)) ' ||
          'FROM STLUDR.ur_cpr_' || rpt_settlemonth || '_t T2 join stl_province_cd prov on t2.out_object = prov.prov_cd ' ||
         'WHERE t2.dest_source = ''7'' ' ||
           'and t2.order_mode = ''1'' ' ||
         'GROUP BY T2.out_object, ' ||
                  'prov.prov_nm, ' ||
                  'T2.offer_code, ' ||
                  'T2.product_code, ' ||
                  'T2.CHARGE_CODE, ' ||
                  'T2.TAX_RATE) T1 ' ||
 'GROUP BY T1.SETTLEMONTH, ' ||
          'T1.PROV_CODE, ' ||
          'T1.PROV_NAME, ' ||
          'T1.POSPEC_CODE, ' ||
          'T1.POSPEC_NAME, ' ||
          'T1.SOSPEC_CODE, ' ||
          'T1.SOSPEC_NAME, ' ||
          'T1.CHARGE_CODE, ' ||
          'T1.CHARGE_NAME, ' ||
          'T1.TAXRATE';
    SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    COMMIT;



    /*日志*/

    RPT_RUNSITE := '2002';
    /*政企公司--end*/

  -- 最新优化sql3
   set @RPT_SQL := 'INSERT INTO ' || RPT_TABLE || ' ' ||
               'SELECT null, T1.SETTLEMONTH, ' ||
       'T1.PROV_CODE, ' ||
       'T1.PROV_NAME, ' ||
       'T1.POSPEC_CODE, ' ||
       'T1.POSPEC_NAME, ' ||
       'T1.SOSPEC_CODE, ' ||
       'T1.SOSPEC_NAME, ' ||
       'T1.CHARGE_CODE, ' ||
       'T1.CHARGE_NAME, ' ||
       'T1.TAXRATE AS TAXRATE, ' ||
       'SUM(T1.SETTNOTAXFEE) AS SETTNOTAXFEE ' ||
       'FROM (SELECT ''' || rpt_settlemonth || ''' as settlemonth, ' ||
               'T1.in_object AS PROV_CODE, ' ||
               'prov.prov_nm AS PROV_NAME, ' ||
               'T1.offer_code as POSPEC_CODE, ' ||
               'T1.offer_code as POSPEC_NAME, ' ||
               'T1.product_code as SOSPEC_CODE, ' ||
               'T1.product_code as SOSPEC_NAME, ' ||
               'T1.CHARGE_CODE, ' ||
               'T1.charge_code CHARGE_NAME, ' ||
               'T1.tax_rate as TAXRATE, ' ||
               'SUM(NVL(T1.SETTLE_NOTAXFEE, 0)) AS SETTNOTAXFEE ' ||
          'FROM STLUDR.ur_recv_' || rpt_settlemonth || '_t T1 join stl_province_cd prov on t1.in_object = prov.prov_cd ' ||
         'WHERE T1.in_object <> T1.out_object and ((T1.ORDER_MODE = ''3'' AND T1.DEST_SOURCE = ''0'') ' ||
            'OR (T1.ORDER_MODE = ''5'' AND T1.DEST_SOURCE = ''1'')) ' ||
         'GROUP BY T1.in_object, ' ||
                  'prov.prov_nm, ' ||
                  'T1.offer_code, ' ||
                  'T1.product_code, ' ||
                  'T1.charge_code, ' ||
                  'T1.TAX_RATE ' ||
        'UNION ALL ' ||
        'SELECT ''' || rpt_settlemonth || ''', ' ||
               'T2.in_object, ' ||
               'prov.prov_nm, ' ||
               'T2.OFFER_CODE, ' ||
               'T2.offer_code, ' ||
               'T2.PRODUCT_CODE, ' ||
               'T2.product_code, ' ||
               'T2.charge_code, ' ||
               'T2.charge_code, ' ||
               'T2.TAX_RATE, ' ||
               'SUM(NVL(T2.SETTLE_NOTAXFEE, 0)) ' ||
          'FROM STLUDR.ur_cpr_' || rpt_settlemonth || '_t T2 join stl_province_cd prov on t2.in_object = prov.prov_cd ' ||
         'WHERE t2.dest_source = ''7'' ' ||
           'and t2.order_mode = ''3'' ' ||
           'AND T2.in_object <> T2.out_object ' ||
         'GROUP BY T2.in_object, ' ||
                  'prov.prov_nm, ' ||
                  'T2.OFFER_CODE, ' ||
                  'T2.PRODUCT_CODE, ' ||
                  'T2.charge_code, ' ||
                  'T2.TAX_RATE) T1 ' ||
 'GROUP BY T1.SETTLEMONTH, ' ||
          'T1.PROV_CODE, ' ||
          'T1.PROV_NAME, ' ||
          'T1.POSPEC_CODE, ' ||
          'T1.POSPEC_NAME, ' ||
          'T1.SOSPEC_CODE, ' ||
          'T1.SOSPEC_NAME, ' ||
          'T1.CHARGE_CODE, ' ||
          'T1.CHARGE_NAME, ' ||
          'T1.TAXRATE';
      SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

    /*省公司--start*/

        /*结出*/
    -- 最新优化sql4
    set @RPT_SQL := 'INSERT INTO ' || RPT_TABLE || ' ' ||
               'SELECT null, T1.SETTLEMONTH, ' ||
       'T1.PROV_CODE, ' ||
       'T1.PROV_NAME, ' ||
       'T1.POSPEC_CODE, ' ||
       'T1.POSPEC_NAME, ' ||
       'T1.SOSPEC_CODE, ' ||
       'T1.SOSPEC_NAME, ' ||
       'T1.CHARGE_CODE, ' ||
       'T1.CHARGE_NAME, ' ||
       'T1.TAXRATE AS TAXRATE, ' ||
       'SUM(T1.SETTNOTAXFEE) SETTNOTAXFEE ' ||
  'FROM (SELECT ''' || rpt_settlemonth || ''' as SETTLEMONTH, ' ||
               'T3.out_object AS PROV_CODE, ' ||
               'prov.prov_nm AS PROV_NAME, ' ||
               'T3.offer_code as POSPEC_CODE, ' ||
               'T3.offer_code as POSPEC_NAME, ' ||
               'T3.product_code as SOSPEC_CODE, ' ||
               'T3.product_code as SOSPEC_NAME, ' ||
               'T3.CHARGE_CODE, ' ||
               'T3.charge_code AS CHARGE_NAME, ' ||
               'T3.tax_rate as TAXRATE, ' ||
               'SUM(-NVL(T3.SETTLE_NOTAXFEE, 0)) AS SETTNOTAXFEE ' ||
          'FROM STLUDR.ur_recv_' || rpt_settlemonth || '_t T3 join stl_province_cd prov on t3.out_object = prov.prov_cd ' ||
         'WHERE T3.in_object <> T3.out_object and ((T3.ORDER_MODE = ''3'' AND T3.DEST_SOURCE = ''0'') ' ||
            'OR (T3.ORDER_MODE = ''5'' AND T3.DEST_SOURCE = ''1'')) ' ||
         'GROUP BY T3.out_object, ' ||
                  'prov.prov_nm, ' ||
                  'T3.offer_code, ' ||
                  'T3.product_code, ' ||
                  'T3.charge_code, ' ||
                  'T3.TAX_RATE ' ||
        'UNION ALL ' ||
        'SELECT ''' || rpt_settlemonth || ''', ' ||
               'T2.out_object, ' ||
               'prov.prov_nm, ' ||
               'T2.OFFER_CODE, ' ||
               'T2.offer_code, ' ||
               'T2.PRODUCT_CODE, ' ||
               'T2.PRODUCT_CODE, ' ||
               'T2.charge_code, ' ||
               'T2.charge_code, ' ||
               'T2.TAX_RATE, ' ||
               'SUM(-NVL(T2.SETTLE_NOTAXFEE, 0)) ' ||
          'FROM STLUDR.ur_cpr_' || rpt_settlemonth || '_t T2 join stl_province_cd prov on t2.out_object = prov.prov_cd ' ||
         'WHERE t2.dest_source = ''7'' ' ||
           'and t2.order_mode = ''3'' ' ||
           'AND T2.in_object <> T2.out_object ' ||
         'GROUP BY T2.out_object, ' ||
                  'prov.prov_nm, ' ||
                  'T2.OFFER_CODE, ' ||
                  'T2.PRODUCT_CODE, ' ||
                  'T2.charge_code, ' ||
                  'T2.TAX_RATE) T1 ' ||
 'GROUP BY T1.SETTLEMONTH, ' ||
          'T1.PROV_CODE, ' ||
          'T1.PROV_NAME, ' ||
          'T1.POSPEC_CODE, ' ||
          'T1.POSPEC_NAME, ' ||
          'T1.SOSPEC_CODE, ' ||
          'T1.SOSPEC_NAME, ' ||
          'T1.CHARGE_CODE, ' ||
          'T1.CHARGE_NAME, ' ||
          'T1.TAXRATE';
    SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;



    /*日志*/
    RPT_RUNSITE := '2004';
    /*省公司--end*/
    /*临时表插入表当前账期数据--end*/

    /*分省公司执行*/
     FOR PROV IN PROV_LIST LOOP

    -- 优化后sql
      /*产品编码为空*/
    set @RPT_SQL := ' INSERT INTO ' || ARQ_TABLE || '(SETTLEMONTH,SHOW_PONAME,SHOW_SONAME,SHOW_CHARGE,PROV_CD,PROV_NM,SHOW_PONAME_NM,SHOW_SONAME_NM,SHOW_CHARGE_NM,SETTAXFEE,SETTNOTAXFEE,SETTNOTAXFEE0,SETTNOTAXFEE1,SETTNOTAXFEE2,SETTNOTAXFEE3,SETTNOTAXFEE4) ' ||
                 ' SELECT /*T1.SETTLEMONTH*/ '||RPT_SETTLEMONTH||',                            ' ||
                 ' T1.SHOW_PONAME, T1.SHOW_SONAME, T1.SHOW_CHARGE, T1.PROV_CODE ,                ' ||
                 ' T1.PROV_NAME, T1.SHOW_PONAME_NM, T1.SHOW_SONAME_NM, T1.SHOW_CHARGE_NM,        ' ||
                 ' ROUND(SUM(T1.SETTAXFEE), 2) AS SETTAXFEE , ROUND(SUM(T1.SETTNOTAXFEE), 2) AS SETTNOTAXFEE,  ' ||
                 ' 0 AS SETTNOTAXFEE0, 0 AS SETTNOTAXFEE1, 0 AS SETTNOTAXFEE2,                   ' ||
                 ' 0 AS SETTNOTAXFEE3 , 0 AS SETTNOTAXFEE4 FROM (SELECT T1.SHOW_PONAME,          ' ||
                 ' T1.SHOW_SONAME, T1.SHOW_CHARGE, T1.SHOW_CHARGE_NM, T1.SHOW_SONAME_NM ,        ' ||
                 ' T1.SHOW_PONAME_NM, T2.TAXRATE, T2.PROV_CODE, T2.PROV_NAME, T2.SETTLEMONTH ,   ' ||
                 ' ROUND(SUM(NVL(T2.SETTNOTAXFEE, 0)) / (1 + NVL(T2.TAXRATE, 0) / 100), 2) AS    ' ||
                 ' SETTNOTAXFEE,ROUND(SUM(NVL(T2.SETTNOTAXFEE, 0)), 2) AS SETTAXFEE              ' ||
                 ' FROM ' || PAM_TABLE ||
                 ' T1 LEFT JOIN (SELECT T1.SETTLEMONTH,                          ' ||
                 ' T1.PROV_CODE, T1.PROV_NAME, T1.POSPEC_CODE, T1.POSPEC_NAME , T1.SOSPEC_CODE,  ' ||
                 ' T1.SOSPEC_NAME, T1.CHARGE_CODE, T1.CHARGE_NAME, T1.TAXRATE ,                  ' ||
                 ' SUM(T1.SETTNOTAXFEE) / 100 AS SETTNOTAXFEE FROM ' ||
                 RPT_TABLE || ' T1 WHERE      ' ||
                 ' T1.SOSPEC_CODE IS NULL AND T1.PROV_CODE = ''' ||
                 PROV.PROV_CD || ''' AND          ' ||
                 ' T1.SETTLEMONTH = ''' || RPT_SETTLEMONTH ||
                 ''' GROUP BY T1.SETTLEMONTH,           ' ||
                 ' T1.PROV_CODE, T1.PROV_NAME, T1.POSPEC_CODE, T1.POSPEC_NAME, T1.SOSPEC_CODE,   ' ||
                 ' T1.SOSPEC_NAME, T1.CHARGE_CODE, T1.CHARGE_NAME, T1.TAXRATE ) T2 ON            ' ||
                 ' T1.PRODUCT_CODE = T2.POSPEC_CODE AND T1.CHARGE_ITEM_REF = T2.CHARGE_CODE      ' ||
                 ' WHERE T1.SERVICE_CODE IS NULL GROUP BY T1.SHOW_PONAME, T1.SHOW_SONAME,        ' ||
                 ' T1.SHOW_CHARGE, T1.SHOW_CHARGE_NM, T1.SHOW_SONAME_NM, T1.SHOW_PONAME_NM,      ' ||
                 ' T2.TAXRATE, T2.PROV_CODE, T2.PROV_NAME, T2.SETTLEMONTH ) T1 GROUP BY          ' ||
                 ' T1.SETTLEMONTH, T1.SHOW_PONAME, T1.SHOW_SONAME, T1.SHOW_CHARGE,               ' ||
                 ' T1.PROV_CODE, T1.PROV_NAME, T1.SHOW_PONAME_NM,                                ' ||
                 ' T1.SHOW_SONAME_NM, T1.SHOW_CHARGE_NM';
      SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
        COMMIT;



      /*产品编码不为空*/
      -- 优化后sql
      set @RPT_SQL := ' INSERT INTO ' || ARQ_TABLE || '(SETTLEMONTH,SHOW_PONAME,SHOW_SONAME,SHOW_CHARGE,PROV_CD,PROV_NM,SHOW_PONAME_NM,SHOW_SONAME_NM,SHOW_CHARGE_NM,SETTAXFEE,SETTNOTAXFEE,SETTNOTAXFEE0,SETTNOTAXFEE1,SETTNOTAXFEE2,SETTNOTAXFEE3,SETTNOTAXFEE4) ' ||
                 ' SELECT /*T1.SETTLEMONTH*/ '||RPT_SETTLEMONTH||',                            ' ||
                 ' T1.SHOW_PONAME, T1.SHOW_SONAME, T1.SHOW_CHARGE, T1.PROV_CODE ,                ' ||
                 ' T1.PROV_NAME, T1.SHOW_PONAME_NM, T1.SHOW_SONAME_NM, T1.SHOW_CHARGE_NM,        ' ||
                 ' ROUND(SUM(T1.SETTAXFEE), 2) AS SETTAXFEE , ROUND(SUM(T1.SETTNOTAXFEE), 2) AS SETTNOTAXFEE,   ' ||
                 ' 0 AS SETTNOTAXFEE0, 0 AS SETTNOTAXFEE1, 0 AS SETTNOTAXFEE2,                   ' ||
                 ' 0 AS SETTNOTAXFEE3 , 0 AS SETTNOTAXFEE4 FROM (SELECT T1.SHOW_PONAME,          ' ||
                 ' T1.SHOW_SONAME, T1.SHOW_CHARGE, T1.SHOW_CHARGE_NM, T1.SHOW_SONAME_NM ,        ' ||
                 ' T1.SHOW_PONAME_NM, T2.TAXRATE, T2.PROV_CODE, T2.PROV_NAME, T2.SETTLEMONTH ,   ' ||
                 ' ROUND(SUM(NVL(T2.SETTNOTAXFEE, 0)) / (1 + NVL(T2.TAXRATE, 0) / 100), 2) AS    ' ||
                 ' SETTNOTAXFEE, ROUND(SUM(NVL(T2.SETTNOTAXFEE, 0)), 2) AS SETTAXFEE             ' ||
                 ' FROM ' || PAM_TABLE ||
                 ' T1 LEFT JOIN (SELECT T1.SETTLEMONTH,                          ' ||
                 ' T1.PROV_CODE, T1.PROV_NAME, T1.POSPEC_CODE, T1.POSPEC_NAME , T1.SOSPEC_CODE,  ' ||
                 ' T1.SOSPEC_NAME, T1.CHARGE_CODE, T1.CHARGE_NAME, T1.TAXRATE ,                  ' ||
                 ' SUM(T1.SETTNOTAXFEE) / 100 AS SETTNOTAXFEE FROM ' ||
                 RPT_TABLE || ' T1 WHERE      ' ||
                 ' T1.SOSPEC_CODE IS NOT NULL AND T1.PROV_CODE = ''' ||
                 PROV.PROV_CD || ''' AND      ' || ' T1.SETTLEMONTH = ''' ||
                 RPT_SETTLEMONTH ||
                 ''' GROUP BY T1.SETTLEMONTH,           ' ||
                 ' T1.PROV_CODE, T1.PROV_NAME, T1.POSPEC_CODE, T1.POSPEC_NAME, T1.SOSPEC_CODE,   ' ||
                 ' T1.SOSPEC_NAME, T1.CHARGE_CODE, T1.CHARGE_NAME, T1.TAXRATE ) T2 ON            ' ||
                 ' T1.PRODUCT_CODE = T2.POSPEC_CODE AND T1.SERVICE_CODE = T2.SOSPEC_CODE         ' ||
                 ' AND  T1.CHARGE_ITEM_REF = T2.CHARGE_CODE                                      ' ||
                 ' WHERE T1.SERVICE_CODE IS NOT NULL GROUP BY T1.SHOW_PONAME, T1.SHOW_SONAME,    ' ||
                 ' T1.SHOW_CHARGE, T1.SHOW_CHARGE_NM, T1.SHOW_SONAME_NM, T1.SHOW_PONAME_NM,      ' ||
                 ' T2.TAXRATE, T2.PROV_CODE, T2.PROV_NAME, T2.SETTLEMONTH ) T1 GROUP BY          ' ||
                 ' T1.SETTLEMONTH, T1.SHOW_PONAME, T1.SHOW_SONAME, T1.SHOW_CHARGE,               ' ||
                 ' T1.PROV_CODE, T1.PROV_NAME, T1.SHOW_PONAME_NM,                                ' ||
                 ' T1.SHOW_SONAME_NM, T1.SHOW_CHARGE_NM';

    SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
        COMMIT;

       UPDATE STLUDR.RPT_SETTLE_INCOME_PROV T
          SET T.PROV_CD     = PROV.PROV_CD,
              T.PROV_NM     = PROV.PROV_NM
           /*  T.SETTLEMONTH = RPT_SETTLEMONTH*/
        WHERE T.PROV_CD IS NULL
          AND T.PROV_NM IS NULL;
         /*AND T.SETTLEMONTH IS NULL*/
       COMMIT;

    END LOOP;

    UPDATE STLUDR.RPT_SETTLE_INCOME_PROV T
       SET T.SETTNOTAXFEE0 = T.SETTNOTAXFEE
     WHERE T.SHOW_CHARGE_NM = '0'
       AND T.SETTLEMONTH = RPT_SETTLEMONTH;

    UPDATE STLUDR.RPT_SETTLE_INCOME_PROV T
       SET T.SETTNOTAXFEE1 = T.SETTNOTAXFEE
     WHERE T.SHOW_CHARGE_NM = '1'
       AND T.SETTLEMONTH = RPT_SETTLEMONTH;

    UPDATE STLUDR.RPT_SETTLE_INCOME_PROV T
       SET T.SETTNOTAXFEE2 = T.SETTNOTAXFEE
     WHERE T.SHOW_CHARGE_NM = '2'
       AND T.SETTLEMONTH = RPT_SETTLEMONTH;


    UPDATE STLUDR.RPT_SETTLE_INCOME_PROV T
       SET T.SETTNOTAXFEE3 = T.SETTNOTAXFEE
     WHERE T.SHOW_CHARGE_NM = '3'
       AND T.SETTLEMONTH = RPT_SETTLEMONTH;


    UPDATE STLUDR.RPT_SETTLE_INCOME_PROV T
       SET T.SETTNOTAXFEE4 = T.SETTNOTAXFEE
     WHERE T.SHOW_CHARGE_NM = '4'
       AND T.SETTLEMONTH = RPT_SETTLEMONTH;


    /* 结束时间*/
    RPT_RUNSITE := '2000';
    RPT_SQL     := VER_TABLE || '存储过程' || RPT_SETTLEMONTH ||
                   '账期数据入库报表中间表结束时间：' ||
                   TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
    COMMIT;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
    PROC_OUT := 'Y';
    szSysErr := 'OK';
    nReturn := 0;

    SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;

  END;
END ;;
DELIMITER ;