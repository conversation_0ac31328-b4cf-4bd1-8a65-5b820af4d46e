select
    T1.SHOW_PONAME,
    T1.SHOW_SONAME,
    T1.SHOW_CHARGE,
    T1.SHOW_PONAME_NM,
    T1.SHOW_SONAME_NM,
    T1.SHOW_CHARGE_NM,
    ROUND(SUM(T1.SETTAXFEE), 2) as SE<PERSON><PERSON><PERSON><PERSON><PERSON> ,
    ROUND(SUM(T1.SETTNOTAXFEE), 2) as SETTNOTAXFEE,
    0 as SETTNOTAXFEE0,
    0 as SETTNOTAXFEE1,
    0 as SETTNOTAXFEE2,
    0 as SETTNOTAXFEE3 ,
    0 as SETTNOTAXFEE4
from
    (
        select
            T1.SHOW_PONAME,
            T1.SHOW_SONAME,
            T1.SHOW_CHARGE,
            T1.SHOW_CHARGE_NM,
            T1.SHOW_SONAME_NM ,
            T1.SHOW_PONAME_NM,
            T2.TAXRATE,
            T2.PROV_CODE,
            T2.PROV_NAME,
            T2.SETTLEMONTH ,
            ROUND(SUM(NVL(T2.SETTN<PERSON>AXFEE, 0)) / (1 + NVL(T2.TAXRATE, 0) / 100), 2) as SETTNOTAXFEE,
            ROUND(SUM(NVL(T2.SETTNOTAXFEE, 0)), 2) as SETTAXFEE
        from
            STLUDR.STL_COMMODITY_MAPPING T1
                left join (
                select
                    T1.SETTLEMONTH,
                    T1.PROV_CODE,
                    T1.PROV_NAME,
                    T1.POSPEC_CODE,
                    T1.POSPEC_NAME ,
                    T1.SOSPEC_CODE,
                    T1.SOSPEC_NAME,
                    T1.CHARGE_CODE,
                    T1.CHARGE_NAME,
                    T1.TAXRATE ,
                    SUM(T1.SETTNOTAXFEE) / 100 as SETTNOTAXFEE
                from
                    STLUDR.RPT_SETTLE_INCOME_PROV_TMP T1
                where
                    T1.SOSPEC_CODE is not null
                  and T1.PROV_CODE = '000'
                  and T1.SETTLEMONTH = '202407'
                group by
                    T1.SETTLEMONTH,
                    T1.PROV_CODE,
                    T1.PROV_NAME,
                    T1.POSPEC_CODE,
                    T1.POSPEC_NAME,
                    T1.SOSPEC_CODE,
                    T1.SOSPEC_NAME,
                    T1.CHARGE_CODE,
                    T1.CHARGE_NAME,
                    T1.TAXRATE
            ) T2 ON T1.PRODUCT_CODE = T2.POSPEC_CODE
                AND T1.SERVICE_CODE = T2.SOSPEC_CODE
                AND T1.CHARGE_ITEM_REF = T2.CHARGE_CODE
        WHERE
            T1.SERVICE_CODE is not  NULL
        GROUP BY
            T1.SHOW_PONAME,
            T1.SHOW_SONAME,
            T1.SHOW_CHARGE,
            T1.SHOW_CHARGE_NM,
            T1.SHOW_SONAME_NM,
            T1.SHOW_PONAME_NM,
            T2.TAXRATE,
            T2.PROV_CODE,
            T2.PROV_NAME,
            T2.SETTLEMONTH
    ) T1
GROUP BY
    T1.SETTLEMONTH,
    T1.SHOW_PONAME,
    T1.SHOW_SONAME,
    T1.SHOW_CHARGE,
    T1.PROV_CODE,
    T1.PROV_NAME,
    T1.SHOW_PONAME_NM,
    T1.SHOW_SONAME_NM,
    T1.SHOW_CHARGE_NM