/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层-特定业务专属报表-总部一点支付客户收入报表数据生成
**/
DROP PROCEDURE IF EXISTS stludr.`RPT_OTHER_STL_PAYMENT`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "RPT_OTHER_STL_PAYMENT"(
    RPT_SETTLEMONTH IN VARCHAR2,
    FLAG_VERSION IN CHAR,
    PROC_OUT OUT VARCHAR2,
    szSysErr OUT VARCHAR2(1000),
    nReturn OUT NUMBER(4)
)
AS
    v_proc_name varchar2(100) := 'RPT_OTHER_STL_PAYMENT';
    G_VERSION   RVL_CONF_VERSION.VERSION%TYPE; --版本号变量
    G_RESNUM    RVL_CONF_VERSION.VERSIONNUM%TYPE; --版本序号
    ERRCODE     VARCHAR2(32);
    ERRMSG      VARCHAR2(2048);
    VER_TABLE   VARCHAR2(64);
    STL_MONTH       VARCHAR2(6);
    STL_NUMBER      NUMBER(18);/*SQL返回数*/
    RPT_RUNSITE VARCHAR2(32); --运行定位标识
    RPT_SQL     VARCHAR2(4000); --拼SQL的字符串变量
    RPT_TABLE   VARCHAR2(64); --拼表名的字符串变量

    COUNT_V_STL_PAYMENT0 NUMBER(18);
    COUNT_V_STL_PAYMENT1 NUMBER(18);
    COUNT_V_STL_PAYMENT9 NUMBER(18);

    cursor C_V_STL_PAYMENT0 IS select PROV_CODE,MOBLIE_COUNT,FEE from V_STL_PAYMENT0 ;
    cursor C_V_STL_PAYMENT1 IS select PROV_CODE,MOBLIE_COUNT,FEE from V_STL_PAYMENT1 ;
    cursor C_V_STL_PAYMENT9 IS select PROV_CODE,MOBLIE_COUNT,FEE from V_STL_PAYMENT9 ;
    A_PROV_CODE VARCHAR2(32);
    A_MOBLIE_COUNT decimal(18);
    A_FEE decimal(18);

    CURSOR TABLE_LIST IS
      SELECT T.SETTLEMONTH
        FROM V_PAYMENT_MONTH T
       WHERE (T.SETTLEMONTH >=
             TO_CHAR(ADD_MONTHS(TO_DATE(RPT_SETTLEMONTH, 'yyyyMM'), -12), 'yyyy') || '01' AND
             T.SETTLEMONTH <=
             TO_CHAR(ADD_MONTHS(TO_DATE(RPT_SETTLEMONTH, 'yyyyMM'), -12), 'yyyyMM'))
          OR (T.SETTLEMONTH < RPT_SETTLEMONTH AND
             T.SETTLEMONTH >=
             TO_CHAR(TO_DATE(RPT_SETTLEMONTH, 'yyyyMM'), 'yyyy') || '01')
       ORDER BY 1 ASC;

    outReturn  int;

BEGIN

    PROC_OUT := '';
    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        PROC_OUT := 'N';
        szSysErr := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || PROC_OUT ) AS error_msg ;
    END;

    BEGIN
        if ( length(RPT_SETTLEMONTH) < 6 )  then
            SELECT 'RPT_SETTLEMONTH length less than 6. RPT_SETTLEMONTH=[' || RPT_SETTLEMONTH || ']';
            outReturn := -1;
            ROLLBACK;
            RETURN;
        end if;

        SELECT 'RPT_SETTLEMONTH=' ||  RPT_SETTLEMONTH;

        SET @RPT_SQL := 'truncate table V_STL_PAYMENT0';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @RPT_SQL := 'truncate table V_STL_PAYMENT1';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @RPT_SQL := 'truncate table V_STL_PAYMENT9';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        VER_TABLE   := 'RPT_PAYMENT';
        RPT_RUNSITE := '0';
        RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表开始时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
        --中间表获取版本号
        call SETTLE_REPORT_VERSION(RPT_SETTLEMONTH, VER_TABLE, G_VERSION, G_RESNUM, FLAG_VERSION, PROC_OUT, outReturn);
        RPT_RUNSITE := '1';
        RPT_SQL     := '基础数据据插入中间表... ...';
        INSERT INTO RPT_PAYMENT
            (SETTLEMONTH, VERSION, FEETYPE, PAY_CDDE)
            SELECT RPT_SETTLEMONTH, G_VERSION, '0', '01' FROM DUAL
            UNION ALL
            SELECT RPT_SETTLEMONTH, G_VERSION, '0', '02' FROM DUAL
            UNION ALL
            SELECT RPT_SETTLEMONTH, G_VERSION, '1', '01' FROM DUAL
            UNION ALL
            SELECT RPT_SETTLEMONTH, G_VERSION, '1', '02' FROM DUAL
            UNION ALL
            SELECT RPT_SETTLEMONTH, G_VERSION, '9', '01' FROM DUAL
            UNION ALL
            SELECT RPT_SETTLEMONTH, G_VERSION, '9', '02' FROM DUAL;
        INSERT INTO RPT_PAYMENT
            (SETTLEMONTH, VERSION, FEETYPE, PAY_CDDE,PROV_CODE,PROV_NAME)
            SELECT RPT_SETTLEMONTH, G_VERSION, '0', '03', T1.PROV_CD, T1.PROV_NM FROM STL_PROVINCE_CD T1  WHERE T1.PROV_CD not in ('000', '030')
            UNION ALL
            SELECT RPT_SETTLEMONTH, G_VERSION, '0', '10', T2.PROV_CD, T2.PROV_NM FROM STL_PROVINCE_CD T2  WHERE T2.PROV_CD not in ('000', '030')
            UNION ALL
            SELECT RPT_SETTLEMONTH, G_VERSION, '1', '03', T3.PROV_CD, T3.PROV_NM FROM STL_PROVINCE_CD T3  WHERE T3.PROV_CD not in ('000', '030')
            UNION ALL
            SELECT RPT_SETTLEMONTH, G_VERSION, '1', '10', T4.PROV_CD, T4.PROV_NM FROM STL_PROVINCE_CD T4  WHERE T4.PROV_CD not in ('000', '030')
            UNION ALL
            SELECT RPT_SETTLEMONTH, G_VERSION, '9', '03', T5.PROV_CD, T5.PROV_NM FROM STL_PROVINCE_CD T5  WHERE T5.PROV_CD not in ('000', '030')
            UNION ALL
            SELECT RPT_SETTLEMONTH, G_VERSION, '9', '10', T6.PROV_CD, T6.PROV_NM FROM STL_PROVINCE_CD T6  WHERE T6.PROV_CD not in ('000', '030');
        UPDATE RPT_PAYMENT T
          SET T.N_MONTH=0,T.N_LASTMONTH=0,T.N_YEAR=0,T.L_MONTH=0,T.L_YEAR=0
         WHERE T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH ;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        --RPT_TABLE := 'LILEI_RECV_' || RPT_SETTLEMONTH || '_T';
        RPT_TABLE := 'UR_RECV_' || RPT_SETTLEMONTH || '_T';
        ----本月数据----
        --客户数
        /*普通*/
        SET @RPT_SQL := 'truncate table STL_PAYMENT_SWAP';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        RPT_RUNSITE := '2';
        RPT_SQL     := '计算本月普通客户数：'|| RPT_SQL;
        SELECT RPT_SQL;
        SET @RPT_SQL := 'insert into STL_PAYMENT_SWAP SELECT COUNT(DISTINCT T.CUSTOMER_CODE) FROM '||RPT_TABLE||' T WHERE ((T.CHARGE_CODE > 0 AND T.CHARGE_CODE <= 50) OR (T.CHARGE_CODE >= 1000 AND T.CHARGE_CODE < 5000)) AND T.OFFER_CODE IN (''*********'', ''*********'') AND T.CUSTOMER_CODE <> ''00021069001614'' AND T.DEST_SOURCE = ''0'' AND T.ORDER_MODE = ''1'' and t.out_object <> ''030'' and t.in_object <> ''030''';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SELECT * FROM STL_PAYMENT_SWAP INTO STL_NUMBER;
        SELECT 'mark 1. STL_NUMBER=' || STL_NUMBER;

        UPDATE RPT_PAYMENT T
          SET T.N_MONTH = STL_NUMBER
         WHERE T.PAY_CDDE='01' AND T.FEETYPE='0' AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

         /*调账*/
        RPT_RUNSITE := '3';
        RPT_SQL     := '计算本月调账客户数：'|| RPT_SQL;
        SELECT RPT_SQL;

        SET @RPT_SQL := 'truncate table STL_PAYMENT_SWAP';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @RPT_SQL := 'insert into STL_PAYMENT_SWAP SELECT COUNT(DISTINCT T.CUSTOMER_CODE) FROM '||RPT_TABLE||' T WHERE ((T.CHARGE_CODE > 50 AND T.CHARGE_CODE <= 100) OR (T.CHARGE_CODE >= 5000 AND T.CHARGE_CODE < 10000)) AND T.OFFER_CODE IN (''*********'', ''*********'') AND T.CUSTOMER_CODE <> ''00021069001614'' AND T.DEST_SOURCE = ''0'' AND T.ORDER_MODE = ''1''';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SELECT * FROM STL_PAYMENT_SWAP INTO STL_NUMBER;
        SELECT 'mark 2. STL_NUMBER=' || STL_NUMBER;

        UPDATE RPT_PAYMENT T
          SET T.N_MONTH = STL_NUMBER
         WHERE T.PAY_CDDE='01' AND T.FEETYPE='1' AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
         /*全部*/
        RPT_RUNSITE := '4';
        RPT_SQL     := '计算本月全部客户数：'|| RPT_SQL;
        SELECT RPT_SQL;

        SET @RPT_SQL := 'truncate table STL_PAYMENT_SWAP';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @RPT_SQL := 'insert into STL_PAYMENT_SWAP SELECT COUNT(DISTINCT T.CUSTOMER_CODE) FROM '||RPT_TABLE||' T WHERE T.OFFER_CODE IN (''*********'', ''*********'') AND T.CUSTOMER_CODE <> ''00021069001614'' AND T.DEST_SOURCE = ''0'' AND T.ORDER_MODE = ''1''';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SELECT * FROM STL_PAYMENT_SWAP INTO STL_NUMBER;
        SELECT 'mark 2. STL_NUMBER=' || STL_NUMBER;

        UPDATE RPT_PAYMENT T
          SET T.N_MONTH = STL_NUMBER
         WHERE T.PAY_CDDE='01' AND T.FEETYPE='9' AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        --分省成员数、收入
        /*普通*/
        SET @RPT_SQL := 'insert into V_STL_PAYMENT0 SELECT A.IN_OBJECT, SUM(A.MEMBER_COUNT), SUM(A.FEE)               '
                    || '  FROM (SELECT T.CUSTOMER_CODE,                                   '
                    || '               COUNT(DISTINCT T.MEMBER_CODE) AS MEMBER_COUNT,     '
                    || '               T.IN_OBJECT,                                       '
                    || '               SUM(T.SETTLE_NOTAXFEE + T.SETTLE_TAXFEE) AS FEE    '
                    || '          FROM '||RPT_TABLE||' T                                 '
                    || '         WHERE ((T.CHARGE_CODE > 0 AND T.CHARGE_CODE <= 50) OR    '
                    || '               (T.CHARGE_CODE >= 1000 AND T.CHARGE_CODE < 5000))  '
                    || '           AND T.OFFER_CODE IN (''*********'', ''*********'')     '
                    || '           AND T.CUSTOMER_CODE <> ''00021069001614''              '
                    || '           AND T.DEST_SOURCE = ''0''                              '
                    || '           AND T.ORDER_MODE = ''1''                               '
                    || '         GROUP BY T.CUSTOMER_CODE, T.IN_OBJECT) A                 '
                    || ' GROUP BY A.IN_OBJECT                                             ';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        RPT_RUNSITE := '5';
        RPT_SQL     := '计算本月普通分省成员数和收入：'|| RPT_SQL;
        SELECT RPT_SQL;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
        /*调账*/
        SET @RPT_SQL :=     'insert into V_STL_PAYMENT1 SELECT A.IN_OBJECT, SUM(A.MEMBER_COUNT), SUM(A.FEE)               '
                    || '  FROM (SELECT T.CUSTOMER_CODE,                                   '
                    || '               COUNT(DISTINCT T.MEMBER_CODE) AS MEMBER_COUNT,     '
                    || '               T.IN_OBJECT,                                       '
                    || '               SUM(T.SETTLE_NOTAXFEE + T.SETTLE_TAXFEE) AS FEE    '
                    || '          FROM '||RPT_TABLE||' T                                 '
                    || '         WHERE ((T.CHARGE_CODE > 50 AND T.CHARGE_CODE <= 100) OR  '
                    || '               (T.CHARGE_CODE >= 5000 AND T.CHARGE_CODE < 10000)) '
                    || '           AND T.OFFER_CODE IN (''*********'', ''*********'')     '
                    || '           AND T.CUSTOMER_CODE <> ''00021069001614''              '
                    || '           AND T.DEST_SOURCE = ''0''                              '
                    || '           AND T.ORDER_MODE = ''1''                               '
                    || '         GROUP BY T.CUSTOMER_CODE, T.IN_OBJECT) A                 '
                    || ' GROUP BY A.IN_OBJECT                                             ';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        RPT_RUNSITE := '6';
        RPT_SQL     := '计算本月调账分省成员数和收入：'|| RPT_SQL;
        SELECT RPT_SQL;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
        /*全部*/
        RPT_SQL :=     'insert into V_STL_PAYMENT9 SELECT A.IN_OBJECT, SUM(A.MEMBER_COUNT), SUM(A.FEE)            '
                    || '  FROM (SELECT T.CUSTOMER_CODE,                                '
                    || '               COUNT(DISTINCT T.MEMBER_CODE) AS MEMBER_COUNT,  '
                    || '               T.IN_OBJECT,                                    '
                    || '               SUM(T.SETTLE_NOTAXFEE + T.SETTLE_TAXFEE) AS FEE '
                    || '          FROM '||RPT_TABLE||' T                              '
                    || '         WHERE T.OFFER_CODE IN (''*********'', ''*********'')  '
                    || '           AND T.CUSTOMER_CODE <> ''00021069001614''           '
                    || '           AND T.DEST_SOURCE = ''0''                           '
                    || '           AND T.ORDER_MODE = ''1''                            '
                    || '         GROUP BY T.CUSTOMER_CODE, T.IN_OBJECT) A              '
                    || ' GROUP BY A.IN_OBJECT                                          ';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        RPT_RUNSITE := '7';
        RPT_SQL     := '计算本月全部分省成员数和收入：'|| RPT_SQL;
        SELECT RPT_SQL;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        SELECT COUNT(*) FROM V_STL_PAYMENT0 INTO COUNT_V_STL_PAYMENT0;

        IF COUNT_V_STL_PAYMENT0 <> 0 THEN
            --打开游标逐一进行处理
            OPEN C_V_STL_PAYMENT0;
            LOOP
                --获取字段值
                FETCH C_V_STL_PAYMENT0 INTO A_PROV_CODE, A_MOBLIE_COUNT, A_FEE;
                EXIT WHEN C_V_STL_PAYMENT0%NOTFOUND ;
                BEGIN
                    UPDATE RPT_PAYMENT T
                      SET T.N_MONTH=A_MOBLIE_COUNT
                         WHERE T.PAY_CDDE='03' AND T.FEETYPE='0' AND T.PROV_CODE=A_PROV_CODE AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
                    UPDATE RPT_PAYMENT T
                      SET T.N_MONTH=A_FEE
                         WHERE T.PAY_CDDE='10' AND T.FEETYPE='0' AND T.PROV_CODE=A_PROV_CODE AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
                END;
            END LOOP;
            CLOSE C_V_STL_PAYMENT0;
        END IF;

        RPT_RUNSITE := '8';
        RPT_SQL     := '循环更新本月普通的分省成员数和收入... ...';
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        SELECT COUNT(*) FROM V_STL_PAYMENT1 INTO COUNT_V_STL_PAYMENT1;

        IF COUNT_V_STL_PAYMENT1 <> 0 THEN
            --打开游标逐一进行处理
            OPEN C_V_STL_PAYMENT1;
            LOOP
                --获取字段值
                FETCH C_V_STL_PAYMENT1 INTO A_PROV_CODE, A_MOBLIE_COUNT, A_FEE;
                EXIT WHEN C_V_STL_PAYMENT1%NOTFOUND ;
                BEGIN
                    UPDATE RPT_PAYMENT T
                      SET T.N_MONTH=A_MOBLIE_COUNT
                         WHERE T.PAY_CDDE='03' AND T.FEETYPE='1' AND T.PROV_CODE=A_PROV_CODE AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
                    UPDATE RPT_PAYMENT T
                      SET T.N_MONTH=A_FEE
                         WHERE T.PAY_CDDE='10' AND T.FEETYPE='1' AND T.PROV_CODE=A_PROV_CODE AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
                END;
            END LOOP;
            CLOSE C_V_STL_PAYMENT1;
        END IF;

        RPT_RUNSITE := '9';
        RPT_SQL     := '循环更新本月调账的分省成员数和收入... ...';
        SELECT RPT_SQL;
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        SELECT COUNT(*) FROM V_STL_PAYMENT9 INTO COUNT_V_STL_PAYMENT9;

        IF COUNT_V_STL_PAYMENT9 <> 0 THEN
            --打开游标逐一进行处理
            OPEN C_V_STL_PAYMENT9;
            LOOP
                --获取字段值
                FETCH C_V_STL_PAYMENT9 INTO A_PROV_CODE, A_MOBLIE_COUNT, A_FEE;
                EXIT WHEN C_V_STL_PAYMENT9%NOTFOUND ;
                BEGIN
                    UPDATE RPT_PAYMENT T
                      SET T.N_MONTH=A_MOBLIE_COUNT
                         WHERE T.PAY_CDDE='03' AND T.FEETYPE='9' AND T.PROV_CODE=A_PROV_CODE AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
                    UPDATE RPT_PAYMENT T
                      SET T.N_MONTH=A_FEE
                         WHERE T.PAY_CDDE='10' AND T.FEETYPE='9' AND T.PROV_CODE=A_PROV_CODE AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
                END;
            END LOOP;
            CLOSE C_V_STL_PAYMENT9;
        END IF;

        RPT_RUNSITE := '10';
        RPT_SQL     := '循环更新本月全部的分省成员数和收入... ...';
        SELECT RPT_SQL;
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
        ----本月数据----

        ----以往数据----
        OPEN TABLE_LIST;
        LOOP
            FETCH TABLE_LIST INTO STL_MONTH;
            EXIT WHEN TABLE_LIST%NOTFOUND;
            --客户数
            /*普通*/
            RPT_RUNSITE := '11';
            RPT_SQL     := '循环更新'||STL_MONTH||'月份普通的客户数... ...';
            SELECT RPT_SQL;

            SELECT T.N_MONTH INTO STL_NUMBER FROM RPT_PAYMENT T
             WHERE T.PAY_CDDE='01' AND T.FEETYPE='0'
               AND T.VERSION IN (SELECT MAX(VERSION) FROM RVL_CONF_VERSION WHERE TABLE_NAME = 'RPT_PAYMENT' AND SETTLEMONTH = STL_MONTH)
               AND T.SETTLEMONTH=STL_MONTH;
            IF (TO_CHAR(TO_DATE(STL_MONTH, 'yyyyMM'), 'yyyy') = TO_CHAR(TO_DATE(RPT_SETTLEMONTH, 'yyyyMM'), 'yyyy')) THEN
                /*本年*/
                IF ( TO_CHAR(ADD_MONTHS(TO_DATE(STL_MONTH, 'yyyyMM'), 1), 'yyyyMM') = RPT_SETTLEMONTH ) THEN
                  /*上月*/
                  UPDATE RPT_PAYMENT T
                    SET T.N_LASTMONTH=STL_NUMBER,T.N_YEAR=T.N_YEAR+STL_NUMBER
                   WHERE T.PAY_CDDE='01' AND T.FEETYPE='0' AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH ;
                ELSE
                  UPDATE RPT_PAYMENT T
                    SET T.N_YEAR=T.N_YEAR+STL_NUMBER
                   WHERE T.PAY_CDDE='01' AND T.FEETYPE='0' AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH ;
                END IF;
            ELSE
                /*上年*/
                IF (TO_CHAR(ADD_MONTHS(TO_DATE(STL_MONTH, 'yyyyMM'), 12), 'yyyyMM')=RPT_SETTLEMONTH) THEN
                  /*上年本月*/
                  UPDATE RPT_PAYMENT T
                    SET T.L_MONTH=STL_NUMBER,T.L_YEAR=T.L_YEAR+STL_NUMBER
                   WHERE T.PAY_CDDE='01' AND T.FEETYPE='0' AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH ;
                ELSE
                  UPDATE RPT_PAYMENT T
                    SET T.L_YEAR=T.L_YEAR+STL_NUMBER
                   WHERE T.PAY_CDDE='01' AND T.FEETYPE='0' AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH ;
                END IF;
            END IF;
            CALL STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
            /*调账*/
            RPT_RUNSITE := '12';
            RPT_SQL     := '循环更新'||STL_MONTH||'月份调账的客户数... ...';
            SELECT RPT_SQL;

            SELECT T.N_MONTH INTO STL_NUMBER FROM RPT_PAYMENT T
             WHERE T.PAY_CDDE='01' AND T.FEETYPE='1'
               AND T.VERSION IN (SELECT MAX(VERSION) FROM RVL_CONF_VERSION WHERE TABLE_NAME = 'RPT_PAYMENT' AND SETTLEMONTH = STL_MONTH)
               AND T.SETTLEMONTH=STL_MONTH;

            IF (TO_CHAR(TO_DATE(STL_MONTH, 'yyyyMM'), 'yyyy') = TO_CHAR(TO_DATE(RPT_SETTLEMONTH, 'yyyyMM'), 'yyyy')) THEN
                /*本年*/
                IF ( TO_CHAR(ADD_MONTHS(TO_DATE(STL_MONTH, 'yyyyMM'), 1), 'yyyyMM') = RPT_SETTLEMONTH ) THEN
                  /*上月*/
                  UPDATE RPT_PAYMENT T
                    SET T.N_LASTMONTH=STL_NUMBER,T.N_YEAR=T.N_YEAR+STL_NUMBER
                   WHERE T.PAY_CDDE='01' AND T.FEETYPE='1' AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH ;
                ELSE
                  UPDATE RPT_PAYMENT T
                    SET T.N_YEAR=T.N_YEAR+STL_NUMBER
                   WHERE T.PAY_CDDE='01' AND T.FEETYPE='1' AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH ;
                END IF;
            ELSE
                /*上年*/
                IF (TO_CHAR(ADD_MONTHS(TO_DATE(STL_MONTH, 'yyyyMM'), 12), 'yyyyMM')=RPT_SETTLEMONTH) THEN
                  /*上年本月*/
                  UPDATE RPT_PAYMENT T
                    SET T.L_MONTH=STL_NUMBER,T.L_YEAR=T.L_YEAR+STL_NUMBER
                   WHERE T.PAY_CDDE='01' AND T.FEETYPE='1' AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH ;
                ELSE
                  UPDATE RPT_PAYMENT T
                    SET T.L_YEAR=T.L_YEAR+STL_NUMBER
                   WHERE T.PAY_CDDE='01' AND T.FEETYPE='1' AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH ;
                END IF;
            END IF;
            CALL STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
            /*全部*/
            RPT_RUNSITE := '13';
            RPT_SQL     := '循环更新'||STL_MONTH||'月份全部的客户数... ...';
            SELECT RPT_SQL;

            SELECT T.N_MONTH INTO STL_NUMBER FROM RPT_PAYMENT T
             WHERE T.PAY_CDDE='01' AND T.FEETYPE='9'
               AND T.VERSION IN (SELECT MAX(VERSION) FROM RVL_CONF_VERSION WHERE TABLE_NAME = 'RPT_PAYMENT' AND SETTLEMONTH = STL_MONTH)
               AND T.SETTLEMONTH=STL_MONTH;

            IF (TO_CHAR(TO_DATE(STL_MONTH, 'yyyyMM'), 'yyyy') = TO_CHAR(TO_DATE(RPT_SETTLEMONTH, 'yyyyMM'), 'yyyy')) THEN
                /*本年*/
                IF ( TO_CHAR(ADD_MONTHS(TO_DATE(STL_MONTH, 'yyyyMM'), 1), 'yyyyMM') = RPT_SETTLEMONTH ) THEN
                  /*上月*/
                  UPDATE RPT_PAYMENT T
                    SET T.N_LASTMONTH=STL_NUMBER,T.N_YEAR=T.N_YEAR+STL_NUMBER
                   WHERE T.PAY_CDDE='01' AND T.FEETYPE='9' AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH ;
                ELSE
                  UPDATE RPT_PAYMENT T
                    SET T.N_YEAR=T.N_YEAR+STL_NUMBER
                   WHERE T.PAY_CDDE='01' AND T.FEETYPE='9' AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH ;
                END IF;
            ELSE
                /*上年*/
                IF (TO_CHAR(ADD_MONTHS(TO_DATE(STL_MONTH, 'yyyyMM'), 12), 'yyyyMM')=RPT_SETTLEMONTH) THEN
                  /*上年本月*/
                  UPDATE RPT_PAYMENT T
                    SET T.L_MONTH=STL_NUMBER,T.L_YEAR=T.L_YEAR+STL_NUMBER
                   WHERE T.PAY_CDDE='01' AND T.FEETYPE='9' AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH ;
                ELSE
                  UPDATE RPT_PAYMENT T
                    SET T.L_YEAR=T.L_YEAR+STL_NUMBER
                   WHERE T.PAY_CDDE='01' AND T.FEETYPE='9' AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH ;
                END IF;
            END IF;
            CALL STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

            --分省成员数、收入
            /*普通*/
            RPT_RUNSITE := '14';
            RPT_SQL     := '循环更新'||STL_MONTH||'月份普通的分省成员数和收入... ...';
            SELECT RPT_SQL;


            SET @RPT_SQL := 'truncate table V_STL_PAYMENT0';
            SELECT @RPT_SQL;
            PREPARE STMT FROM @RPT_SQL;
            EXECUTE STMT;
            DEALLOCATE PREPARE STMT;

            INSERT INTO V_STL_PAYMENT0
            SELECT A.PROV_CODE, A.N_MONTH AS MOBLIE_COUNT, B.N_MONTH AS FEE
            FROM (SELECT T1.PROV_CODE, T1.N_MONTH
                    FROM RPT_PAYMENT T1
                   WHERE T1.PAY_CDDE = '03'
                     AND T1.FEETYPE = '0'
                     AND T1.VERSION IN (SELECT MAX(VERSION) FROM RVL_CONF_VERSION WHERE TABLE_NAME = 'RPT_PAYMENT' AND SETTLEMONTH = STL_MONTH)
                     AND T1.SETTLEMONTH = STL_MONTH) A
            LEFT JOIN (SELECT T2.PROV_CODE, T2.N_MONTH
                         FROM RPT_PAYMENT T2
                        WHERE T2.PAY_CDDE = '10'
                          AND T2.FEETYPE = '0'
                          AND T2.VERSION IN (SELECT MAX(VERSION) FROM RVL_CONF_VERSION WHERE TABLE_NAME = 'RPT_PAYMENT' AND SETTLEMONTH = STL_MONTH)
                          AND T2.SETTLEMONTH = STL_MONTH) B ON A.PROV_CODE = B.PROV_CODE;

            SELECT COUNT(*) FROM V_STL_PAYMENT0 INTO COUNT_V_STL_PAYMENT0;

            IF (COUNT_V_STL_PAYMENT0 <> 0) THEN
                --打开游标逐一进行处理
                OPEN C_V_STL_PAYMENT0;
                LOOP
                    --获取字段值
                    FETCH C_V_STL_PAYMENT0 INTO A_PROV_CODE, A_MOBLIE_COUNT, A_FEE;
                    EXIT WHEN C_V_STL_PAYMENT0%NOTFOUND ;
                    BEGIN
                        RPT_RUNSITE := '14';
                        RPT_SQL     := '循环更新'||STL_MONTH||'月份全部的'||A_PROV_CODE||'省成员数和收入... ...';
                        SELECT RPT_SQL;
                        IF (TO_CHAR(TO_DATE(STL_MONTH, 'yyyyMM'), 'yyyy') = TO_CHAR(TO_DATE(RPT_SETTLEMONTH, 'yyyyMM'), 'yyyy')) THEN
                            /*本年*/
                            IF ( TO_CHAR(ADD_MONTHS(TO_DATE(STL_MONTH, 'yyyyMM'), 1), 'yyyyMM') = RPT_SETTLEMONTH ) THEN
                              /*上月*/
                              UPDATE RPT_PAYMENT T
                                SET T.N_LASTMONTH=A_MOBLIE_COUNT,T.N_YEAR = T.N_YEAR+A_MOBLIE_COUNT
                                   WHERE T.PAY_CDDE='03' AND T.FEETYPE='0' AND T.PROV_CODE=A_PROV_CODE AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
                              UPDATE RPT_PAYMENT T
                                SET T.N_LASTMONTH=A_FEE,T.N_YEAR = T.N_YEAR + A_FEE
                                   WHERE T.PAY_CDDE='10' AND T.FEETYPE='0' AND T.PROV_CODE=A_PROV_CODE AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
                            ELSE
                              UPDATE RPT_PAYMENT T
                                SET T.N_YEAR = T.N_YEAR+A_MOBLIE_COUNT
                                   WHERE T.PAY_CDDE='03' AND T.FEETYPE='0' AND T.PROV_CODE=A_PROV_CODE AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
                              UPDATE RPT_PAYMENT T
                                SET T.N_YEAR = T.N_YEAR+A_FEE
                                   WHERE T.PAY_CDDE='10' AND T.FEETYPE='0' AND T.PROV_CODE=A_PROV_CODE AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
                            END IF;
                        ELSE
                            /*上年*/
                            IF (TO_CHAR(ADD_MONTHS(TO_DATE(STL_MONTH, 'yyyyMM'), 12), 'yyyyMM')=RPT_SETTLEMONTH) THEN
                              /*上年本月*/
                              UPDATE RPT_PAYMENT T
                                SET T.L_MONTH=A_MOBLIE_COUNT,T.L_YEAR = T.L_YEAR+A_MOBLIE_COUNT
                                   WHERE T.PAY_CDDE='03' AND T.FEETYPE='0' AND T.PROV_CODE=A_PROV_CODE AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
                              UPDATE RPT_PAYMENT T
                                SET T.L_MONTH=A_FEE,T.L_YEAR = T.L_YEAR + A_FEE
                                   WHERE T.PAY_CDDE='10' AND T.FEETYPE='0' AND T.PROV_CODE=A_PROV_CODE AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
                            ELSE
                              UPDATE RPT_PAYMENT T
                                SET T.L_YEAR = T.L_YEAR+A_MOBLIE_COUNT
                                   WHERE T.PAY_CDDE='03' AND T.FEETYPE='0' AND T.PROV_CODE=A_PROV_CODE AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
                              UPDATE RPT_PAYMENT T
                                SET T.L_YEAR = T.L_YEAR + A_FEE
                                   WHERE T.PAY_CDDE='10' AND T.FEETYPE='0' AND T.PROV_CODE=A_PROV_CODE AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
                            END IF;
                        END IF;
                        CALL STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
                    END;
                END LOOP;
                CLOSE C_V_STL_PAYMENT0;
            END IF;
            call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

            /*调账*/
            RPT_RUNSITE := '15';
            RPT_SQL     := '循环更新'||STL_MONTH||'月份调账的分省成员数和收入... ...';
            SELECT RPT_SQL;

            SET @RPT_SQL := 'truncate table V_STL_PAYMENT1';
            SELECT @RPT_SQL;
            PREPARE STMT FROM @RPT_SQL;
            EXECUTE STMT;
            DEALLOCATE PREPARE STMT;

            INSERT INTO V_STL_PAYMENT1
            SELECT A.PROV_CODE, A.N_MONTH AS MOBLIE_COUNT, B.N_MONTH AS FEE
            FROM (SELECT T1.PROV_CODE, T1.N_MONTH
                    FROM RPT_PAYMENT T1
                   WHERE T1.PAY_CDDE = '03'
                     AND T1.FEETYPE = '1'
                     AND T1.VERSION IN (SELECT MAX(VERSION) FROM RVL_CONF_VERSION WHERE TABLE_NAME = 'RPT_PAYMENT' AND SETTLEMONTH = STL_MONTH)
                     AND T1.SETTLEMONTH = STL_MONTH) A
            LEFT JOIN (SELECT T2.PROV_CODE, T2.N_MONTH
                         FROM RPT_PAYMENT T2
                        WHERE T2.PAY_CDDE = '10'
                          AND T2.FEETYPE = '1'
                          AND T2.VERSION IN (SELECT MAX(VERSION) FROM RVL_CONF_VERSION WHERE TABLE_NAME = 'RPT_PAYMENT' AND SETTLEMONTH = STL_MONTH)
                          AND T2.SETTLEMONTH = STL_MONTH) B ON A.PROV_CODE = B.PROV_CODE;

            SELECT COUNT(*) FROM V_STL_PAYMENT1 INTO COUNT_V_STL_PAYMENT1;

            IF (COUNT_V_STL_PAYMENT1 <> 0) THEN
                --打开游标逐一进行处理
                OPEN C_V_STL_PAYMENT1;
                LOOP
                    --获取字段值
                    FETCH C_V_STL_PAYMENT1 INTO A_PROV_CODE, A_MOBLIE_COUNT, A_FEE;
                    EXIT WHEN C_V_STL_PAYMENT1%NOTFOUND ;
                    BEGIN
                        RPT_RUNSITE := '15';
                        RPT_SQL     := '循环更新'||STL_MONTH||'月份全部的'||A_PROV_CODE||'省成员数和收入... ...';
                        IF (TO_CHAR(TO_DATE(STL_MONTH, 'yyyyMM'), 'yyyy') = TO_CHAR(TO_DATE(RPT_SETTLEMONTH, 'yyyyMM'), 'yyyy')) THEN
                            /*本年*/
                            IF ( TO_CHAR(ADD_MONTHS(TO_DATE(STL_MONTH, 'yyyyMM'), 1), 'yyyyMM') = RPT_SETTLEMONTH ) THEN
                              /*上月*/
                              UPDATE RPT_PAYMENT T
                                SET T.N_LASTMONTH=A_MOBLIE_COUNT,T.N_YEAR = T.N_YEAR+A_MOBLIE_COUNT
                                   WHERE T.PAY_CDDE='03' AND T.FEETYPE='1' AND T.PROV_CODE=A_PROV_CODE AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
                              UPDATE RPT_PAYMENT T
                                SET T.N_LASTMONTH=A_FEE,T.N_YEAR = T.N_YEAR + A_FEE
                                   WHERE T.PAY_CDDE='10' AND T.FEETYPE='1' AND T.PROV_CODE=A_PROV_CODE AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
                            ELSE
                              UPDATE RPT_PAYMENT T
                                SET T.N_YEAR = T.N_YEAR+A_MOBLIE_COUNT
                                   WHERE T.PAY_CDDE='03' AND T.FEETYPE='1' AND T.PROV_CODE=A_PROV_CODE AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
                              UPDATE RPT_PAYMENT T
                                SET T.N_YEAR = T.N_YEAR+A_FEE
                                   WHERE T.PAY_CDDE='10' AND T.FEETYPE='1' AND T.PROV_CODE=A_PROV_CODE AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
                            END IF;
                        ELSE
                            /*上年*/
                            IF (TO_CHAR(ADD_MONTHS(TO_DATE(STL_MONTH, 'yyyyMM'), 12), 'yyyyMM')=RPT_SETTLEMONTH) THEN
                              /*上年本月*/
                              UPDATE RPT_PAYMENT T
                                SET T.L_MONTH=A_MOBLIE_COUNT,T.L_YEAR = T.L_YEAR+A_MOBLIE_COUNT
                                   WHERE T.PAY_CDDE='03' AND T.FEETYPE='1' AND T.PROV_CODE=A_PROV_CODE AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
                              UPDATE RPT_PAYMENT T
                                SET T.L_MONTH=A_FEE,T.L_YEAR = T.L_YEAR + A_FEE
                                   WHERE T.PAY_CDDE='10' AND T.FEETYPE='1' AND T.PROV_CODE=A_PROV_CODE AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
                            ELSE
                              UPDATE RPT_PAYMENT T
                                SET T.L_YEAR = T.L_YEAR+A_MOBLIE_COUNT
                                   WHERE T.PAY_CDDE='03' AND T.FEETYPE='1' AND T.PROV_CODE=A_PROV_CODE AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
                              UPDATE RPT_PAYMENT T
                                SET T.L_YEAR = T.L_YEAR + A_FEE
                                   WHERE T.PAY_CDDE='10' AND T.FEETYPE='1' AND T.PROV_CODE=A_PROV_CODE AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
                            END IF;
                        END IF;
                        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
                    END;
                END LOOP;
                CLOSE C_V_STL_PAYMENT1;
            END IF;
            call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

            /*全部*/
            RPT_RUNSITE := '16';
            RPT_SQL     := '循环更新'||STL_MONTH||'月份全部的分省成员数和收入... ...';

            SET @RPT_SQL := 'truncate table V_STL_PAYMENT9';
            SELECT @RPT_SQL;
            PREPARE STMT FROM @RPT_SQL;
            EXECUTE STMT;
            DEALLOCATE PREPARE STMT;

            INSERT INTO V_STL_PAYMENT9
            SELECT A.PROV_CODE, A.N_MONTH AS MOBLIE_COUNT, B.N_MONTH AS FEE
            FROM (SELECT T1.PROV_CODE, T1.N_MONTH
                    FROM RPT_PAYMENT T1
                   WHERE T1.PAY_CDDE = '03'
                     AND T1.FEETYPE = '9'
                     AND T1.VERSION IN (SELECT MAX(VERSION) FROM RVL_CONF_VERSION WHERE TABLE_NAME = 'RPT_PAYMENT' AND SETTLEMONTH = STL_MONTH)
                     AND T1.SETTLEMONTH = STL_MONTH) A
            LEFT JOIN (SELECT T2.PROV_CODE, T2.N_MONTH
                         FROM RPT_PAYMENT T2
                        WHERE T2.PAY_CDDE = '10'
                          AND T2.FEETYPE = '9'
                          AND T2.VERSION IN (SELECT MAX(VERSION) FROM RVL_CONF_VERSION WHERE TABLE_NAME = 'RPT_PAYMENT' AND SETTLEMONTH = STL_MONTH)
                          AND T2.SETTLEMONTH = STL_MONTH) B ON A.PROV_CODE = B.PROV_CODE;

            SELECT COUNT(*) FROM V_STL_PAYMENT9 INTO COUNT_V_STL_PAYMENT9;

            IF (COUNT_V_STL_PAYMENT9 <> 0) THEN
                OPEN C_V_STL_PAYMENT9;
                LOOP
                    --获取字段值
                    FETCH C_V_STL_PAYMENT9 INTO A_PROV_CODE, A_MOBLIE_COUNT, A_FEE;
                    EXIT WHEN C_V_STL_PAYMENT9%NOTFOUND ;
                    BEGIN
                        RPT_RUNSITE := '16';
                        RPT_SQL     := '循环更新'||STL_MONTH||'月份全部的'||A_PROV_CODE||'省成员数和收入... ...';
                        IF (TO_CHAR(TO_DATE(STL_MONTH, 'yyyyMM'), 'yyyy') = TO_CHAR(TO_DATE(RPT_SETTLEMONTH, 'yyyyMM'), 'yyyy')) THEN
                            /*本年*/
                            IF ( TO_CHAR(ADD_MONTHS(TO_DATE(STL_MONTH, 'yyyyMM'), 1), 'yyyyMM') = RPT_SETTLEMONTH ) THEN
                              /*上月*/
                              UPDATE RPT_PAYMENT T
                                SET T.N_LASTMONTH=A_MOBLIE_COUNT,T.N_YEAR = T.N_YEAR+A_MOBLIE_COUNT
                                   WHERE T.PAY_CDDE='03' AND T.FEETYPE='9' AND T.PROV_CODE=A_PROV_CODE AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
                              UPDATE RPT_PAYMENT T
                                SET T.N_LASTMONTH=A_FEE,T.N_YEAR = T.N_YEAR + A_FEE
                                   WHERE T.PAY_CDDE='10' AND T.FEETYPE='9' AND T.PROV_CODE=A_PROV_CODE AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
                            ELSE
                              UPDATE RPT_PAYMENT T
                                SET T.N_YEAR = T.N_YEAR+A_MOBLIE_COUNT
                                   WHERE T.PAY_CDDE='03' AND T.FEETYPE='9' AND T.PROV_CODE=A_PROV_CODE AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
                              UPDATE RPT_PAYMENT T
                                SET T.N_YEAR = T.N_YEAR+A_FEE
                                   WHERE T.PAY_CDDE='10' AND T.FEETYPE='9' AND T.PROV_CODE=A_PROV_CODE AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
                            END IF;
                        ELSE
                            /*上年*/
                            IF (TO_CHAR(ADD_MONTHS(TO_DATE(STL_MONTH, 'yyyyMM'), 12), 'yyyyMM')=RPT_SETTLEMONTH) THEN
                              /*上年本月*/
                              UPDATE RPT_PAYMENT T
                                SET T.L_MONTH=A_MOBLIE_COUNT,T.L_YEAR = T.L_YEAR+A_MOBLIE_COUNT
                                   WHERE T.PAY_CDDE='03' AND T.FEETYPE='9' AND T.PROV_CODE=A_PROV_CODE AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
                              UPDATE RPT_PAYMENT T
                                SET T.L_MONTH=A_FEE,T.L_YEAR = T.L_YEAR + A_FEE
                                   WHERE T.PAY_CDDE='10' AND T.FEETYPE='9' AND T.PROV_CODE=A_PROV_CODE AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
                            ELSE
                              UPDATE RPT_PAYMENT T
                                SET T.L_YEAR = T.L_YEAR+A_MOBLIE_COUNT
                                   WHERE T.PAY_CDDE='03' AND T.FEETYPE='9' AND T.PROV_CODE=A_PROV_CODE AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
                              UPDATE RPT_PAYMENT T
                                SET T.L_YEAR = T.L_YEAR + A_FEE
                                   WHERE T.PAY_CDDE='10' AND T.FEETYPE='9' AND T.PROV_CODE=A_PROV_CODE AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
                            END IF;
                        END IF;
                        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
                    END;
                END LOOP;
                CLOSE C_V_STL_PAYMENT9;
            END IF;
            call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
        END LOOP;
        CLOSE TABLE_LIST;

        ----以往数据----
        UPDATE RPT_PAYMENT T
          SET T.N_YEAR = T.N_YEAR+T.N_MONTH
         WHERE T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;

        UPDATE RPT_PAYMENT T
          SET (T.N_MONTH,T.N_LASTMONTH,T.N_YEAR,T.L_MONTH,T.L_YEAR) = ( SELECT a.* FROM  ( SELECT SUM(T1.N_MONTH),SUM(T1.N_LASTMONTH),SUM(T1.N_YEAR),SUM(T1.L_MONTH),SUM(T1.L_YEAR)
                                                                         FROM RPT_PAYMENT T1
                                                                        WHERE T1.PAY_CDDE = '03' AND T1.FEETYPE = T.FEETYPE AND T1.VERSION = T.VERSION AND T1.SETTLEMONTH = T.SETTLEMONTH) a )
         WHERE T.PAY_CDDE='02' AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
        -----------------------------------------------------------------------------------------
        RPT_RUNSITE := '7';
        RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表结束时间：' ||TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
        SELECT RPT_SQL;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        commit;

        PROC_OUT:='Y';
        outReturn := 0;

        SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
    END;
END ;;
DELIMITER ;