/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层-特定业务专属报表-华为CDN合作伙伴结算报表数据生成
**/
use stludr;
DROP PROCEDURE IF EXISTS stludr.`RPT_OTHER_STL_HWCDN_PARTNER`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE stludr."RPT_OTHER_STL_HWCDN_PARTNER"(
    RPT_SETTLEMONTH IN VARCHAR2,
    FLAG_VERSION IN CHAR,
    PROC_OUT OUT VARCHAR2,
    szSysErr OUT VARCHAR2(1000),
    nReturn OUT NUMBER(4)
)
AS
    -- 华为CDN中间表(50004,9200397) RPT_HWCDN_PARTNER
    v_proc_name       VARCHAR2(30) := 'RPT_OTHER_STL_HWCDN_PARTNER';
    ERRCODE   VARCHAR2(32);
    ERRMSG    VARCHAR2(2048);
    VER_TABLE  VARCHAR2(64);
    RPT_RUNSITE VARCHAR2(32); --运行定位标识
    RPT_SQL     VARCHAR2(4000); --拼SQL的字符串变量
    RPT_TABLE   VARCHAR2(64); --拼表名的字符串变量
    outReturn  int;

BEGIN

    PROC_OUT := '';
    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        PROC_OUT := 'N';
        szSysErr := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || PROC_OUT ) AS error_msg ;
    END;

    BEGIN
        if ( length(RPT_SETTLEMONTH) < 6 )  then
            SELECT 'RPT_SETTLEMONTH length less than 6. RPT_SETTLEMONTH=[' || RPT_SETTLEMONTH || ']';
            outReturn := -1;
            ROLLBACK;
            RETURN;
        end if;

        SELECT 'RPT_SETTLEMONTH=' ||  RPT_SETTLEMONTH;


        VER_TABLE   := 'RPT_HWCDN_PARTNER';
        RPT_RUNSITE := '0';
        RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表开始时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE,  RPT_SQL);

        RPT_RUNSITE := '1';
        SET @RPT_SQL := 'delete from stludr.RPT_HWCDN_PARTNER where settle_month = ''' || RPT_SETTLEMONTH || '''';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE,  RPT_SQL);

        -------华为CDN
        SET @RPT_SQL     := 'INSERT INTO stludr.RPT_HWCDN_PARTNER(SETTLE_MONTH,CUSTOMER_PROV,CUSTOMER_PROV_NAME,CUSTOMER_CODE,CUSTOMER_NAME,OFFER_CODE,OFFER_NAME,PRODUCT_CODE, '||
          'PRODUCT_NAME,TAX_RATE,NO_TAX_FEE,TAX_FEE,PARTNER_CODE,PARTNER_NAME,CHARGE_CODE,CHARGE_NAME,FEE_FLAG,PRODUCT_ORDER_ID,OUT_OBJECT,OUT_OBJECT_NAME) SELECT a.SETTLE_MONTH,a.ORDER_PROV,b.PROV_NM, '||
          'a.CUSTOMER_CODE,c.FIRST_NAME,a.OFFER_CODE,d.NAME,a.PRODUCT_CODE,e.NAME,a.TAX_RATE,round(sum(to_char(a.SETTLE_NOTAXFEE , ''FM999999999999990.00'')) / decode(a.offer_code, ''50004'', 100, 1)) AS SETTLE_NOTAXFEE, '||
          'a.TAX_FEE,f.PARTNER_CODE,f.PARTNER_NAME,a.CHARGE_CODE,decode(substr(a.charge_code, 0, 1), ''6'', ''软件服务费-调账'', ''软件服务费''),decode(a.feetype, ''0'', ''1'', ''1'', ''2''),a.PRODUCT_ORDER_ID,a.OUT_OBJECT,b1.PROV_NM '||
          'FROM STLUDR.UR_EBOSS_' || RPT_SETTLEMONTH || '_T a LEFT JOIN STLUDR.STL_PROVINCE_CD b ON a.ORDER_PROV = b.PROV_CD '||
          'LEFT JOIN STLUDR.STL_PROVINCE_CD b1 ON a.OUT_OBJECT = b1.PROV_CD '||
          'LEFT JOIN STLUSERS.STL_CUSTOMER c ON a.CUSTOMER_CODE = c.CUSTOMER_CODE '||
          'LEFT JOIN STLUDR.STL_PRODUCT d ON a.OFFER_CODE =d.PRODUCT_CODE AND  a.SETTLE_MONTH =d.ACCT_MONTH '||
          'LEFT JOIN STLUDR.STL_SERVICE e ON a.PRODUCT_CODE = e.SERVICE_CODE AND a.SETTLE_MONTH =e.ACCT_MONTH '||
          'LEFT JOIN stludr.rvl_bboss_partner f ON a.PRODUCT_CODE =f.PRODUCT_CODE '||
          'WHERE (a.OFFER_CODE = ''50004'' or a.offer_code = ''9200397'') and a.dest_source = ''5'' and a.SETTLE_NOTAXFEE != 0  '||
          'GROUP BY a.SETTLE_MONTH,a.ORDER_PROV,b.PROV_NM,a.CUSTOMER_CODE,c.FIRST_NAME,a.OFFER_CODE,d.NAME,a.PRODUCT_CODE,e.NAME,a.TAX_RATE, '||
          'a.TAX_FEE,f.PARTNER_CODE,f.PARTNER_NAME,a.CHARGE_CODE,a.feetype,a.PRODUCT_ORDER_ID,a.OUT_OBJECT,b1.PROV_NM ';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, @RPT_SQL);
        RPT_RUNSITE := '2';
        RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表结束时间：' ||TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);


        UPDATE stludr.RPT_HWCDN_PARTNER a
        JOIN stlusers.stl_serv_biz_code s ON s.order_id = a.product_order_id
        SET a.customer_prov_name = '云能力中心'
        WHERE a.settle_month = RPT_SETTLEMONTH
        AND a.offer_code = '9200397'
        AND s.sign_entity = '0385'
        AND RPT_SETTLEMONTH BETWEEN DATE_FORMAT(s.effective_date, '%Y%m') AND DATE_FORMAT(s.expiry_date, '%Y%m');

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, @RPT_SQL);
        RPT_RUNSITE := '3';
                RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表结束时间：' ||TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        -- 处理逻辑：比较cdn_rate_swap_05_t表和RPT_HWCDN_PARTNER表的数据，并在不相等时更新
        RPT_RUNSITE := '4';
        RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据与cdn_rate_swap_05_t表数据比对更新开始时间：' ||TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        -- 清空物理表
        SET @RPT_SQL := 'TRUNCATE TABLE stludr.cdn_report_diff_amount';
        
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        -- 向物理表插入数据，计算差值
        SET @RPT_SQL := 'INSERT INTO stludr.cdn_report_diff_amount(product_order_id, order_amount, report_amount, diff_amount)
                        SELECT a.PRODUCT_ORDER_ID, 
                               a.order_amount AS order_amount, 
                               IFNULL(SUM(b.NO_TAX_FEE), 0) AS report_amount,
                               (a.order_amount - IFNULL(SUM(b.NO_TAX_FEE), 0)) AS diff_amount
                        FROM (
                            SELECT PRODUCT_ORDER_ID, SUM(order_amount) AS order_amount
                            FROM stludr.cdn_rate_swap_05_t 
                            WHERE product_code = ''5000402'' 
                            AND order_amount > 0   
                            GROUP BY PRODUCT_ORDER_ID
                        ) a 
                        LEFT JOIN RPT_HWCDN_PARTNER b ON a.PRODUCT_ORDER_ID = b.PRODUCT_ORDER_ID
                        WHERE b.SETTLE_MONTH = ''' || RPT_SETTLEMONTH || '''
                        AND b.product_code = ''5000402'' 
                        AND b.charge_code = ''16''
                        GROUP BY a.PRODUCT_ORDER_ID, a.order_amount
                        HAVING a.order_amount <> IFNULL(SUM(b.NO_TAX_FEE), 0)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        -- 更新每个订购中no_tax_fee最大的记录，将差值加到该记录上
SET @RPT_SQL := 'UPDATE RPT_HWCDN_PARTNER b
                JOIN (
                    SELECT b1.PRODUCT_ORDER_ID, b1.OUT_OBJECT, b1.NO_TAX_FEE, t.diff_amount,
                           ROW_NUMBER() OVER (PARTITION BY b1.PRODUCT_ORDER_ID ORDER BY b1.NO_TAX_FEE DESC) as rn
                    FROM RPT_HWCDN_PARTNER b1
                    JOIN stludr.cdn_report_diff_amount t ON b1.PRODUCT_ORDER_ID = t.PRODUCT_ORDER_ID
                    JOIN (
                        SELECT PRODUCT_ORDER_ID, MAX(NO_TAX_FEE) AS max_fee
                        FROM RPT_HWCDN_PARTNER
                        WHERE SETTLE_MONTH = ''' || RPT_SETTLEMONTH || '''
                        AND product_code = ''5000402'' 
                        AND charge_code = ''16''
                        GROUP BY PRODUCT_ORDER_ID
                    ) b2 ON b1.PRODUCT_ORDER_ID = b2.PRODUCT_ORDER_ID AND b1.NO_TAX_FEE = b2.max_fee
                    WHERE b1.SETTLE_MONTH = ''' || RPT_SETTLEMONTH || '''
                    AND b1.product_code = ''5000402'' 
                    AND b1.charge_code = ''16''
                ) update_data ON b.PRODUCT_ORDER_ID = update_data.PRODUCT_ORDER_ID 
                               AND b.OUT_OBJECT = update_data.OUT_OBJECT
                               AND b.NO_TAX_FEE = update_data.NO_TAX_FEE
                SET b.NO_TAX_FEE = b.NO_TAX_FEE + update_data.diff_amount
                WHERE update_data.rn = 1';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        RPT_RUNSITE := '5';
        RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据与cdn_rate_swap_05_t表数据比对更新结束时间：' ||TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        COMMIT;

        PROC_OUT:='Y';
        outReturn := 0;

        SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
    END;
END ;;
DELIMITER ;