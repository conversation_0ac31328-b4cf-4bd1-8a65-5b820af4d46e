/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层-特定业务专属报表-热线彩印专属省专报表数据生成
**/
DROP PROCEDURE IF EXISTS stludr.`RPT_OTHER_STL_RXCY_P2C`;
DELIMITER ;;
create or replace DEFINER="stludr"@"10.%" PROCEDURE "RPT_OTHER_STL_RXCY_P2C"(
    RPT_SETTLEMONTH IN VARCHAR2,
    FLAG_VERSION IN CHAR,
    PROC_OUT OUT VARCHAR2,
    szSysErr OUT VARCHAR2(1000),
    nReturn OUT NUMBER(4)
)
AS
    -- 热线彩印
    v_proc_name varchar2(100) := 'RPT_OTHER_STL_RXCY_P2C';
    ERRCODE   VARCHAR2(32);
    ERRMSG    VARCHAR2(2048);
    VER_TABLE  VARCHAR2(64);
    RPT_RUNSITE VARCHAR2(32); --运行定位标识
    RPT_SQL     VARCHAR2(4000); --拼SQL的字符串变量
    RPT_TABLE   VARCHAR2(64); --拼表名的字符串变量

    outReturn  int;

BEGIN

    PROC_OUT := '';
    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        PROC_OUT := 'N';
        szSysErr := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || PROC_OUT ) AS error_msg ;
    END;

    BEGIN
        if ( length(RPT_SETTLEMONTH) < 6 )  then
            SELECT 'RPT_SETTLEMONTH length less than 6. RPT_SETTLEMONTH=[' || RPT_SETTLEMONTH || ']';
            outReturn := -1;
            ROLLBACK;
            RETURN;
        end if;

        SELECT 'RPT_SETTLEMONTH=' ||  RPT_SETTLEMONTH;

        VER_TABLE   := 'RPT_RXCY_P2C';
        RPT_RUNSITE := '0';
        RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表开始时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
        SELECT RPT_SQL;
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE,  RPT_SQL);

        RPT_RUNSITE := '1';
        SET @RPT_SQL := 'delete from rpt_rxcy_p2c where settle_month = ''' || RPT_SETTLEMONTH || '''';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
		-- modify by zhaohx 20240104 location字段已经在STL_CUSTOMER中扩了，不需要取crm同步过来的 abs_cus表中数据关联
        -------热线彩印
       /* SET @RPT_SQL := 'insert into rpt_rxcy_p2c(settle_month, out_object, prov_name, customer_code, customer_name, offer_code, offer_name, product_code, product_name, offer_order_id,  '||
                      'product_order_id, bill_fee, total, yd_total, lt_total, dx_total, total_fee , location_name)  '||
                      'SELECT ''' || RPT_SETTLEMONTH || ''', a.OUT_OBJECT AS out_object, b.PROV_NM AS prov_name, a.CUSTOMER_CODE AS customer_code, c.FIRST_NAME AS customer_name, a.offer_code AS offer_code, ''企业彩印'' AS offer_name,  '||
                      'a.PRODUCT_CODE AS product_code, ''热线彩印'' AS product_name, a.OFFER_ORDER_ID AS offer_order_id, a.PRODUCT_ORDER_ID AS product_order_id,   '||
                      'a.no_tax_fee * 10 AS bill_fee, e.total, e.LT, e.DX, e.YD, a.settle_notaxfee / 100 AS total_fee,   '||
                      'y.location_name AS location_name   '||
                      'FROM STLUDR.UR_EBOSS_' || RPT_SETTLEMONTH || '_T a   '||
                      'LEFT JOIN STLUDR.STL_PROVINCE_CD b ON a.OUT_OBJECT = b.PROV_CD  '||
                      'LEFT JOIN STLUSERS.STL_CUSTOMER c ON a.CUSTOMER_CODE = c.CUSTOMER_CODE  '||
                      'LEFT JOIN (SELECT product_code,total, max(case when CARRIER_TYPE=''LT'' then nums else 0 end) LT, ' ||
                      ' max(case when CARRIER_TYPE=''DX'' then nums else 0 end) DX, ' ||
                      ' max(case when CARRIER_TYPE=''YD'' then nums else 0 end) YD ' ||
                      ' FROM (SELECT f.*, h.total FROM (SELECT product_code, CARRIER_TYPE, COUNT(CARRIER_TYPE) AS nums  '||
                      'FROM STLUDR.UR_CPM_' || RPT_SETTLEMONTH || '_T WHERE product_code = ''5002202''   '||
                      'GROUP BY product_code, CARRIER_TYPE) f LEFT JOIN (SELECT product_code, count(PRODUCT_CODE) AS total FROM STLUDR.UR_CPM_' || RPT_SETTLEMONTH || '_T   '||
                      'WHERE product_code = ''5002202'' GROUP BY product_code) h  '||
                      'ON h.product_code = f.product_code) t group by product_code,total ) e ON a.product_code = e.product_code   '||
                      'LEFT JOIN STLUSERS.abs_cus x ON  a.customer_code = x.customer_number  '||
                      'LEFT JOIN STLUDR.abs_location y ON x.location = y.location_number  '||
                      'AND a.out_object = y.province_id WHERE a.product_code =''5002202''  '||
                      'GROUP BY a.OUT_OBJECT, b.PROV_NM, a.CUSTOMER_CODE, c.FIRST_NAME, a.PRODUCT_CODE,a.OFFER_ORDER_ID, a.PRODUCT_ORDER_ID,   '||
                      'a.TAX_RATE, a.no_tax_fee, a.settle_notaxfee, y.location_name, a.offer_code, e.total, e.LT, e.DX, e.YD';*/
           SET @RPT_SQL := 'insert into rpt_rxcy_p2c(settle_month, out_object, prov_name, customer_code, customer_name, offer_code, offer_name, product_code, product_name, offer_order_id,  '||
                      'product_order_id, bill_fee, total, yd_total, lt_total, dx_total, total_fee , location_name)  '||
                      'SELECT ''' || RPT_SETTLEMONTH || ''', a.OUT_OBJECT AS out_object, b.PROV_NM AS prov_name, a.CUSTOMER_CODE AS customer_code, c.FIRST_NAME AS customer_name, a.offer_code AS offer_code, ''企业彩印'' AS offer_name,  '||
                      'a.PRODUCT_CODE AS product_code, ''热线彩印'' AS product_name, a.OFFER_ORDER_ID AS offer_order_id, a.PRODUCT_ORDER_ID AS product_order_id,   '||
                      'to_char(a.no_tax_fee * 10, ''FM999999999999990'') AS bill_fee, e.total, e.LT, e.DX, e.YD, to_char(a.settle_notaxfee / 100, ''FM999999999999990.00'') AS total_fee,   '||
                      'y.location_name AS location_name   '||
                      'FROM STLUDR.UR_EBOSS_' || RPT_SETTLEMONTH || '_T a   '||
                      'LEFT JOIN STLUDR.STL_PROVINCE_CD b ON a.OUT_OBJECT = b.PROV_CD  '||
                      'LEFT JOIN STLUSERS.STL_CUSTOMER c ON a.CUSTOMER_CODE = c.CUSTOMER_CODE  '||
                      'LEFT JOIN (SELECT product_code,total, max(case when CARRIER_TYPE=''LT'' then nums else NULL end) LT, ' ||
                      ' max(case when CARRIER_TYPE=''DX'' then nums else NULL end) DX, ' ||
                      ' max(case when CARRIER_TYPE=''YD'' then nums else NULL end) YD ' ||
                      ' FROM (SELECT f.*, h.total FROM (SELECT product_code, CARRIER_TYPE, COUNT(CARRIER_TYPE) AS nums  '||
                      'FROM STLUDR.UR_CPM_' || RPT_SETTLEMONTH || '_T WHERE product_code = ''5002202''   '||
                      'GROUP BY product_code, CARRIER_TYPE) f LEFT JOIN (SELECT product_code, count(PRODUCT_CODE) AS total FROM STLUDR.UR_CPM_' || RPT_SETTLEMONTH || '_T   '||
                      'WHERE product_code = ''5002202'' GROUP BY product_code) h  '||
                      'ON h.product_code = f.product_code) t group by product_code,total ) e ON a.product_code = e.product_code   '||
                      'LEFT JOIN STLUDR.abs_location y ON c.location = y.location_number  '||
                      'AND a.out_object = y.province_id WHERE a.product_code =''5002202''  '||
                      'GROUP BY a.OUT_OBJECT, b.PROV_NM, a.CUSTOMER_CODE, c.FIRST_NAME, a.PRODUCT_CODE,a.OFFER_ORDER_ID, a.PRODUCT_ORDER_ID,   '||
                      'a.TAX_RATE, a.no_tax_fee, a.settle_notaxfee, y.location_name, a.offer_code, e.total, e.LT, e.DX, e.YD';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        RPT_RUNSITE := '2';
        RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表结束时间：' ||TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
        SELECT RPT_SQL;
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        commit;

        PROC_OUT:='Y';
        outReturn := 0;

        SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
    END;
END;;
DELIMITER ;