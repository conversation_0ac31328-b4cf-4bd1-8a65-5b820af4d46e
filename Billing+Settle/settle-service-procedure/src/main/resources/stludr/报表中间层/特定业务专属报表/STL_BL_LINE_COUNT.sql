/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层-特定业务专属报表-数据专线业务结算客户数报表数据生成
**/
DROP PROCEDURE IF EXISTS stludr.`STL_BL_LINE_COUNT`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "STL_BL_LINE_COUNT"(RPT_SETTLEMONTH IN VARCHAR2,
                                                FLAG_VERSION IN CHAR,
                                                PROC_OUT OUT VARCHAR2,
                                                szSysErr    OUT VARCHAR2(1000),
                                                nReturn     OUT NUMBER(4)  )
AS
    G_VERSION RVL_CONF_VERSION.VERSION%TYPE; -- 版本号变量
    G_RESNUM  RVL_CONF_VERSION.VERSIONNUM%TYPE; -- 版本序号
    ERRCODE   VARCHAR2(32);
    ERRMSG    VARCHAR2(2048);
    VER_TABLE VARCHAR2(64);
    RPT_TABLE VARCHAR2(64);
    RPT_RUNSITE VARCHAR2(64);
    RPT_SQL VARCHAR2(4096);
    v_proc_name  VARCHAR2(30) := 'STL_BL_LINE_COUNT';


    -- 定义自定义类型 类型 STL_LINE 是一个包含两个字段的记录类型,分别是 PROV_CODE 和 C_COUNT
    -- TYPE STL_LINE IS RECORD(
    --      PROV_CODE VARCHAR2(32),
    --      C_COUNT NUMBER(18)
    -- );

    -- 存取两组不同sql查询的数据
    -- TYPE T_STL_LINE IS TABLE OF STL_LINE INDEX BY BINARY_INTEGER;
    -- V_STL_C_COUNT T_STL_LINE;
    -- V_STL_P_COUNT T_STL_LINE;


    STL_MONTH   VARCHAR2(6);
      -- TYPE I_CURSOR_TYPE IS REF CURSOR; -- 动态游标
      -- TABLE_LIST I_CURSOR_TYPE;
    CURSOR C_COUNT_LIST IS SELECT CUSTOMER_PROV,C_COUNT FROM STL_LINE_C_COUNT;

    CURSOR P_COUNT_LIST IS SELECT CUSTOMER_PROV,P_COUNT FROM STL_LINE_P_COUNT;

    CUSTOMER_PROV VARCHAR2(32);
    C_COUNT NUMBER(18);
    P_COUNT NUMBER(18);

    CURSOR TABLE_LIST IS
      SELECT T.SETTLEMONTH
        FROM V_PAYMENT_MONTH T
       WHERE (T.SETTLEMONTH >=
             CONCAT(TO_CHAR(ADD_MONTHS(TO_DATE(RPT_SETTLEMONTH, 'yyyyMM'), -12), 'yyyy'),'01') AND
             T.SETTLEMONTH <=
             TO_CHAR(ADD_MONTHS(TO_DATE(RPT_SETTLEMONTH, 'yyyyMM'), -12), 'yyyyMM'))
          OR (T.SETTLEMONTH < RPT_SETTLEMONTH AND
             T.SETTLEMONTH >=
             CONCAT(TO_CHAR(TO_DATE(RPT_SETTLEMONTH, 'yyyyMM'), 'yyyy'), '01'))
       ORDER BY 1 ASC;

BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        PROC_OUT := 'N';
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
        ROLLBACK;

        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;
  BEGIN
    VER_TABLE   := 'RPT_BL_LINE_COUNT';
    RPT_RUNSITE := '0';
    RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表开始时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
    -- 中间表获取版本号
    CALL SETTLE_REPORT_VERSION(RPT_SETTLEMONTH, VER_TABLE,G_VERSION, G_RESNUM, FLAG_VERSION,szSysErr,nReturn);

    RPT_RUNSITE := '1';
    RPT_SQL     := '基础数据据插入中间表... ... ';
    INSERT INTO RPT_BL_LINE_COUNT
      (SETTLEMONTH, VERSION, FEETYPE, PAY_CDDE, PROV_CODE, PROV_NAME, N_MONTH, N_LASTMONTH, N_YEAR, L_MONTH, L_YEAR)
    SELECT RPT_SETTLEMONTH, G_VERSION, A.FEETYPE, B.PAY_CODE, T.PROV_CD, T.PROV_NM, 0, 0, 0, 0, 0
      FROM STL_PROVINCE_CD T,
           (SELECT '0' AS FEETYPE FROM DUAL
            UNION ALL
            SELECT '1' FROM DUAL
            UNION ALL
            SELECT '9' FROM DUAL) A,
           (SELECT '01' AS PAY_CODE FROM DUAL
            UNION ALL
            SELECT '10' FROM DUAL) B
     WHERE T.PROV_CD not in ('000', '030');
    --本月数据--
    RPT_TABLE := 'UR_RECV_' || RPT_SETTLEMONTH || '_T';
    set @RPT_SQL :=    'insert into STL_LINE_C_COUNT(CUSTOMER_PROV,C_COUNT) '
            ||' SELECT T.CUSTOMER_PROV, COUNT(DISTINCT T.CUSTOMER_CODE) '
            ||'  FROM '||RPT_TABLE||' T                                '
            ||' WHERE ((T.CHARGE_CODE > 0 AND T.CHARGE_CODE <= 50) OR  '
            ||'       (T.CHARGE_CODE >= 1000 AND T.CHARGE_CODE < 5000))'
            ||'   AND T.OFFER_CODE IN (''01011301'', ''01011306'')     '
            ||'   AND T.ORDER_MODE IN (''3'', ''4'')                   '
            ||'   and t.out_object <> ''030'' and t.in_object <> ''030'''
            ||' GROUP BY T.CUSTOMER_PROV                               ';
    SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

    RPT_RUNSITE := '2';
    set @RPT_SQL :=  'insert into STL_LINE_P_COUNT(CUSTOMER_PROV,P_COUNT) '
				||'SELECT T.CUSTOMER_PROV, COUNT(DISTINCT T.PRODUCT_ORDER_ID) '
                ||'  FROM '||RPT_TABLE||' T                                   '
                ||' WHERE ((T.CHARGE_CODE > 0 AND T.CHARGE_CODE <= 50) OR     '
                ||'       (T.CHARGE_CODE >= 1000 AND T.CHARGE_CODE < 5000))   '
                ||'   AND T.OFFER_CODE IN (''01011301'', ''01011306'')        '
                ||'   AND T.ORDER_MODE IN (''3'', ''4'')                      '
                ||'   and t.out_object <> ''030'' and t.in_object <> ''030''  '
                ||' GROUP BY T.CUSTOMER_PROV                                  ';
      SELECT @RPT_SQL;
            PREPARE STMT FROM @RPT_SQL;
            EXECUTE STMT;
            DEALLOCATE PREPARE STMT;
      commit;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

    RPT_RUNSITE := '3';
    RPT_SQL := '按省更新当月普通的集团客户数... ...';
    --IF C_COUNT_LIST <> 0 THEN
    OPEN C_COUNT_LIST;
      LOOP
        CUSTOMER_PROV:='';
        C_COUNT:= 0;
        FETCH C_COUNT_LIST INTO CUSTOMER_PROV, C_COUNT;
        EXIT WHEN C_COUNT_LIST%NOTFOUND;
        select ('CUSTOMER_PROV: ' || CUSTOMER_PROV || ', C_COUNT: ' || C_COUNT);
          begin
          UPDATE RPT_BL_LINE_COUNT T SET T.N_MONTH = C_COUNT
          WHERE T.PROV_CODE= CUSTOMER_PROV  AND T.PAY_CDDE='01' AND T.FEETYPE='0'
          AND T.VERSION= G_VERSION  AND T.SETTLEMONTH= RPT_SETTLEMONTH;
          end;
      END LOOP;
    CLOSE C_COUNT_LIST;
    --END IF;

    delete from STL_LINE_C_COUNT;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

    RPT_RUNSITE := '4';
    RPT_SQL := '按省更新当月普通的专线数... ...';
    -- IF P_COUNT_LIST.COUNT <> 0 THEN
    OPEN P_COUNT_LIST;
      LOOP
        CUSTOMER_PROV:='';
        P_COUNT:= 0;
        FETCH P_COUNT_LIST INTO CUSTOMER_PROV, P_COUNT;
        EXIT WHEN P_COUNT_LIST%NOTFOUND;
        select ('CUSTOMER_PROV: ' || CUSTOMER_PROV || ', P_COUNT: ' || P_COUNT);
          begin
          UPDATE RPT_BL_LINE_COUNT T SET T.N_MONTH = P_COUNT
          WHERE T.PROV_CODE= CUSTOMER_PROV AND T.PAY_CDDE= '10' AND T.FEETYPE= '0'
          AND T.VERSION= G_VERSION  AND T.SETTLEMONTH= RPT_SETTLEMONTH;
          end;
      END LOOP;
    CLOSE P_COUNT_LIST;
    --END IF;

    delete from STL_LINE_P_COUNT;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

     /*调账*/
    set @RPT_SQL :=  'insert into STL_LINE_C_COUNT(CUSTOMER_PROV,C_COUNT) '
                ||' SELECT T.CUSTOMER_PROV, COUNT(DISTINCT T.CUSTOMER_CODE)  '
                ||'  FROM '||RPT_TABLE||' T                                  '
                ||' WHERE ((T.CHARGE_CODE > 50 AND T.CHARGE_CODE <= 100) OR  '
                ||'       (T.CHARGE_CODE >= 5000 AND T.CHARGE_CODE < 10000)) '
                ||'   AND T.OFFER_CODE IN (''01011301'', ''01011306'')       '
                ||'   AND T.ORDER_MODE IN (''3'', ''4'')                     '
                ||'   and t.out_object <> ''030'' and t.in_object <> ''030'' '
                ||' GROUP BY T.CUSTOMER_PROV                                 ';
    SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

    RPT_RUNSITE := '5';
    set @RPT_SQL :=  'insert into STL_LINE_P_COUNT(CUSTOMER_PROV,P_COUNT) '
                || 'SELECT T.CUSTOMER_PROV, COUNT(DISTINCT T.PRODUCT_ORDER_ID) '
                ||'  FROM '||RPT_TABLE||' T                                   '
                ||' WHERE ((T.CHARGE_CODE > 50 AND T.CHARGE_CODE <= 100) OR   '
                ||'       (T.CHARGE_CODE >= 5000 AND T.CHARGE_CODE < 10000))  '
                ||'   AND T.OFFER_CODE IN (''01011301'', ''01011306'')        '
                ||'   AND T.ORDER_MODE IN (''3'', ''4'')                      '
                ||'   and t.out_object <> ''030'' and t.in_object <> ''030''  '
                ||' GROUP BY T.CUSTOMER_PROV                                  ';
    SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

    RPT_RUNSITE := '6';
    RPT_RUNSITE := '7';
    RPT_SQL := '按省更新当月调账的集团客户数... ...';
    -- IF C_COUNT_LIST.COUNT <> 0 THEN
      OPEN C_COUNT_LIST;
      LOOP
        FETCH C_COUNT_LIST INTO CUSTOMER_PROV, C_COUNT;
        EXIT WHEN C_COUNT_LIST%NOTFOUND;
        UPDATE RPT_BL_LINE_COUNT T
          SET T.N_MONTH = C_COUNT
         WHERE T.PROV_CODE=CUSTOMER_PROV AND T.PAY_CDDE='01' AND T.FEETYPE='1'
           AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
      END LOOP;
      CLOSE C_COUNT_LIST;
    -- END IF;

    delete from STL_LINE_C_COUNT;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);


    RPT_RUNSITE := '8';
    RPT_SQL := '按省更新当月调账的专线数... ...';
    -- IF P_COUNT_LIST.COUNT <> 0 THEN
      OPEN P_COUNT_LIST;
      LOOP
        FETCH P_COUNT_LIST INTO CUSTOMER_PROV, P_COUNT;
        EXIT WHEN P_COUNT_LIST%NOTFOUND;
        UPDATE RPT_BL_LINE_COUNT T
          SET T.N_MONTH = P_COUNT
         WHERE T.PROV_CODE=CUSTOMER_PROV AND T.PAY_CDDE='10' AND T.FEETYPE='1'
           AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
      END LOOP;
      CLOSE P_COUNT_LIST;
    -- END IF;

    delete from STL_LINE_P_COUNT;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

     /*全部*/
    set @RPT_SQL :=  'insert into STL_LINE_C_COUNT(CUSTOMER_PROV,C_COUNT) '
                || 'SELECT T.CUSTOMER_PROV, COUNT(DISTINCT T.CUSTOMER_CODE) '
                ||'  FROM '||RPT_TABLE||' T                                '
                ||' WHERE T.OFFER_CODE IN (''01011301'', ''01011306'')     '
                ||'   AND T.ORDER_MODE IN (''3'', ''4'')                   '
                ||'   and t.out_object <> ''030'' and t.in_object <> ''030'''
                ||' GROUP BY T.CUSTOMER_PROV                               ';
    SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

    RPT_RUNSITE := '9';
    set @RPT_SQL :=  'insert into STL_LINE_P_COUNT(CUSTOMER_PROV,P_COUNT) '
                || 'SELECT T.CUSTOMER_PROV, COUNT(DISTINCT T.PRODUCT_ORDER_ID) '
                ||'  FROM '||RPT_TABLE||' T                                   '
                ||' WHERE T.OFFER_CODE IN (''01011301'', ''01011306'')        '
                ||'   AND T.ORDER_MODE IN (''3'', ''4'')                      '
                ||'   and t.out_object <> ''030'' and t.in_object <> ''030''  '
                ||' GROUP BY T.CUSTOMER_PROV                                  ';
    SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


    RPT_RUNSITE := '10';
    RPT_RUNSITE := '11';
    RPT_SQL := '按省更新当月全部的集团客户数... ...';
    -- IF C_COUNT_LIST.COUNT <> 0 THEN
      OPEN C_COUNT_LIST;
      LOOP
        FETCH C_COUNT_LIST INTO CUSTOMER_PROV, C_COUNT;
        EXIT WHEN C_COUNT_LIST%NOTFOUND;
        UPDATE RPT_BL_LINE_COUNT T
          SET T.N_MONTH = C_COUNT
         WHERE T.PROV_CODE=CUSTOMER_PROV AND T.PAY_CDDE='01' AND T.FEETYPE='9'
           AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
      END LOOP;
      CLOSE C_COUNT_LIST;
    -- END IF;

    delete from STL_LINE_C_COUNT;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

    RPT_RUNSITE := '12';
    RPT_SQL := '按省更新当月全部的专线数... ...';
    -- IF P_COUNT_LIST.COUNT <> 0 THEN
      OPEN P_COUNT_LIST;
      LOOP
        FETCH P_COUNT_LIST INTO CUSTOMER_PROV, P_COUNT;
        EXIT WHEN P_COUNT_LIST%NOTFOUND;
        UPDATE RPT_BL_LINE_COUNT T
          SET T.N_MONTH = P_COUNT
         WHERE T.PROV_CODE=CUSTOMER_PROV AND T.PAY_CDDE='10' AND T.FEETYPE='9'
           AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
      END LOOP;
      CLOSE P_COUNT_LIST;
    -- END IF;

    delete from STL_LINE_P_COUNT;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

    SELECT 'procedure 12 completed successfully ';

    --本月数据--
    --以往数据--
    OPEN TABLE_LIST;
    LOOP
      FETCH TABLE_LIST INTO STL_MONTH;
      EXIT WHEN TABLE_LIST%NOTFOUND;
      select ('TABLE_LIST: ' || STL_MONTH );
      /*普通*/
      RPT_RUNSITE := '13';
      RPT_SQL     := '循环更新'||STL_MONTH||'月份普通的集团客户数... ...';

      insert into STL_LINE_C_COUNT(CUSTOMER_PROV,C_COUNT)
      SELECT T.PROV_CODE, T.N_MONTH
        FROM RPT_BL_LINE_COUNT T
       WHERE T.PAY_CDDE = '01'
         AND T.FEETYPE = '0'
         AND T.VERSION IN (SELECT MAX(VERSION)
                              FROM RVL_CONF_VERSION
                             WHERE TABLE_NAME = 'RPT_BL_LINE_COUNT'
                               AND SETTLEMONTH = STL_MONTH)
         AND T.SETTLEMONTH = STL_MONTH;
      SELECT 'procedure 13 completed successfully ';

      -- IF (C_COUNT_LIST.COUNT <> 0) THEN
        OPEN C_COUNT_LIST;
        LOOP
          FETCH C_COUNT_LIST INTO CUSTOMER_PROV, C_COUNT;
          EXIT WHEN C_COUNT_LIST%NOTFOUND;
          RPT_RUNSITE := '1301';
          RPT_SQL     := '循环更新'||STL_MONTH||'月份全部的'|| CUSTOMER_PROV ||'省普通集团客户数... ...';
          IF (TO_CHAR(TO_DATE(STL_MONTH, 'yyyyMM'), 'yyyy') = TO_CHAR(TO_DATE(RPT_SETTLEMONTH, 'yyyyMM'), 'yyyy')) THEN
            /*本年*/
            IF ( TO_CHAR(ADD_MONTHS(TO_DATE(STL_MONTH, 'yyyyMM'), 1), 'yyyyMM') = RPT_SETTLEMONTH ) THEN
              /*上月*/
              UPDATE RPT_BL_LINE_COUNT T
                SET T.N_LASTMONTH = C_COUNT,
                    T.N_YEAR = T.N_YEAR + C_COUNT
               WHERE T.PROV_CODE=CUSTOMER_PROV AND T.PAY_CDDE='01' AND T.FEETYPE='0'
                 AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
            ELSE
              UPDATE RPT_BL_LINE_COUNT T
                SET T.N_YEAR = T.N_YEAR + C_COUNT
               WHERE T.PROV_CODE=CUSTOMER_PROV AND T.PAY_CDDE='01' AND T.FEETYPE='0'
                 AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
            END IF;
          ELSE
            /*上年*/
            IF (TO_CHAR(ADD_MONTHS(TO_DATE(STL_MONTH, 'yyyyMM'), 12), 'yyyyMM')=RPT_SETTLEMONTH) THEN
              /*上年本月*/
              UPDATE RPT_BL_LINE_COUNT T
                SET T.L_MONTH = C_COUNT,
                    T.L_YEAR = T.L_YEAR + C_COUNT
               WHERE T.PROV_CODE=CUSTOMER_PROV AND T.PAY_CDDE='01' AND T.FEETYPE='0'
                 AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
            ELSE
              UPDATE RPT_BL_LINE_COUNT T
                SET T.L_YEAR = T.L_YEAR + C_COUNT
               WHERE T.PROV_CODE=CUSTOMER_PROV AND T.PAY_CDDE='01' AND T.FEETYPE='0'
                 AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
            END IF;
          END IF;
        END LOOP;
        CLOSE C_COUNT_LIST;
      -- END IF;

      delete from STL_LINE_C_COUNT;


      RPT_RUNSITE := '14';
      RPT_SQL     := '循环更新'||STL_MONTH||'月份普通的专线数... ...';
      insert into STL_LINE_P_COUNT(CUSTOMER_PROV,P_COUNT)
      SELECT T.PROV_CODE, T.N_MONTH
        FROM RPT_BL_LINE_COUNT T
       WHERE T.PAY_CDDE = '10'
         AND T.FEETYPE = '0'
         AND T.VERSION IN (SELECT MAX(VERSION)
                              FROM RVL_CONF_VERSION
                             WHERE TABLE_NAME = 'RPT_BL_LINE_COUNT'
                               AND SETTLEMONTH = STL_MONTH)
         AND T.SETTLEMONTH = STL_MONTH;


      -- IF (P_COUNT_LIST.COUNT <> 0) THEN
        OPEN P_COUNT_LIST;
        LOOP
          FETCH P_COUNT_LIST INTO CUSTOMER_PROV, P_COUNT;
          EXIT WHEN P_COUNT_LIST%NOTFOUND;
          RPT_RUNSITE := '1401';
          RPT_SQL     := '循环更新'||STL_MONTH||'月份全部的'|| CUSTOMER_PROV ||'省普通专线数... ...';
          IF (TO_CHAR(TO_DATE(STL_MONTH, 'yyyyMM'), 'yyyy') = TO_CHAR(TO_DATE(RPT_SETTLEMONTH, 'yyyyMM'), 'yyyy')) THEN
            /*本年*/
            IF ( TO_CHAR(ADD_MONTHS(TO_DATE(STL_MONTH, 'yyyyMM'), 1), 'yyyyMM') = RPT_SETTLEMONTH ) THEN
              /*上月*/
              UPDATE RPT_BL_LINE_COUNT T
                SET T.N_LASTMONTH = P_COUNT,
                    T.N_YEAR = T.N_YEAR + P_COUNT
               WHERE T.PROV_CODE=CUSTOMER_PROV AND T.PAY_CDDE='10' AND T.FEETYPE='0'
                 AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
            ELSE
              UPDATE RPT_BL_LINE_COUNT T
                SET T.N_YEAR = T.N_YEAR + P_COUNT
               WHERE T.PROV_CODE=CUSTOMER_PROV AND T.PAY_CDDE='10' AND T.FEETYPE='0'
                 AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
            END IF;
          ELSE
            /*上年*/
            IF (TO_CHAR(ADD_MONTHS(TO_DATE(STL_MONTH, 'yyyyMM'), 12), 'yyyyMM')=RPT_SETTLEMONTH) THEN
              /*上年本月*/
              UPDATE RPT_BL_LINE_COUNT T
                SET T.L_MONTH = P_COUNT,
                    T.L_YEAR = T.L_YEAR + P_COUNT
               WHERE T.PROV_CODE=CUSTOMER_PROV AND T.PAY_CDDE='10' AND T.FEETYPE='0'
                 AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
            ELSE
              UPDATE RPT_BL_LINE_COUNT T
                SET T.L_YEAR = T.L_YEAR + P_COUNT
               WHERE T.PROV_CODE=CUSTOMER_PROV AND T.PAY_CDDE='10' AND T.FEETYPE='0'
                 AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
            END IF;
          END IF;
        END LOOP;
        CLOSE P_COUNT_LIST;
      -- END IF;

      delete from STL_LINE_P_COUNT;



      /*调账*/
      RPT_RUNSITE := '15';
      RPT_SQL     := '循环更新'||STL_MONTH||'月份调账的集团客户数... ...';
      insert into STL_LINE_C_COUNT(CUSTOMER_PROV,C_COUNT)
      SELECT T.PROV_CODE, T.N_MONTH
        FROM RPT_BL_LINE_COUNT T
       WHERE T.PAY_CDDE = '01'
         AND T.FEETYPE = '1'
         AND T.VERSION IN (SELECT MAX(VERSION)
                              FROM RVL_CONF_VERSION
                             WHERE TABLE_NAME = 'RPT_BL_LINE_COUNT'
                               AND SETTLEMONTH = STL_MONTH)
         AND T.SETTLEMONTH = STL_MONTH;



      -- IF (C_COUNT_LIST.COUNT <> 0) THEN
        OPEN C_COUNT_LIST;
        LOOP
          FETCH C_COUNT_LIST INTO CUSTOMER_PROV, C_COUNT;
          EXIT WHEN C_COUNT_LIST%NOTFOUND;
          RPT_RUNSITE := '1501';
          RPT_SQL     := '循环更新'||STL_MONTH||'月份全部的'|| CUSTOMER_PROV ||'省集团客户数... ...';
          IF (TO_CHAR(TO_DATE(STL_MONTH, 'yyyyMM'), 'yyyy') = TO_CHAR(TO_DATE(RPT_SETTLEMONTH, 'yyyyMM'), 'yyyy')) THEN
            /*本年*/
            IF ( TO_CHAR(ADD_MONTHS(TO_DATE(STL_MONTH, 'yyyyMM'), 1), 'yyyyMM') = RPT_SETTLEMONTH ) THEN
              /*上月*/
              UPDATE RPT_BL_LINE_COUNT T
                SET T.N_LASTMONTH = C_COUNT,
                    T.N_YEAR = T.N_YEAR + C_COUNT
               WHERE T.PROV_CODE=CUSTOMER_PROV AND T.PAY_CDDE='01' AND T.FEETYPE='1'
                 AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
            ELSE
              UPDATE RPT_BL_LINE_COUNT T
                SET T.N_YEAR = T.N_YEAR + C_COUNT
               WHERE T.PROV_CODE=CUSTOMER_PROV AND T.PAY_CDDE='01' AND T.FEETYPE='1'
                 AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
            END IF;
          ELSE
            /*上年*/
            IF (TO_CHAR(ADD_MONTHS(TO_DATE(STL_MONTH, 'yyyyMM'), 12), 'yyyyMM')=RPT_SETTLEMONTH) THEN
              /*上年本月*/
              UPDATE RPT_BL_LINE_COUNT T
                SET T.L_MONTH = C_COUNT,
                    T.L_YEAR = T.L_YEAR + C_COUNT
               WHERE T.PROV_CODE=CUSTOMER_PROV AND T.PAY_CDDE='01' AND T.FEETYPE='1'
                 AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
            ELSE
              UPDATE RPT_BL_LINE_COUNT T
                SET T.L_YEAR = T.L_YEAR + C_COUNT
               WHERE T.PROV_CODE=CUSTOMER_PROV AND T.PAY_CDDE='01' AND T.FEETYPE='1'
                 AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
            END IF;
          END IF;
        END LOOP;
        CLOSE C_COUNT_LIST;
      -- END IF;

      delete from STL_LINE_C_COUNT;


      RPT_RUNSITE := '16';
      RPT_SQL     := '循环更新'||STL_MONTH||'月份调账的专线数... ...';
      insert into STL_LINE_P_COUNT(CUSTOMER_PROV,P_COUNT)
      SELECT T.PROV_CODE, T.N_MONTH
        FROM RPT_BL_LINE_COUNT T
       WHERE T.PAY_CDDE = '10'
         AND T.FEETYPE = '1'
         AND T.VERSION IN (SELECT MAX(VERSION)
                              FROM RVL_CONF_VERSION
                             WHERE TABLE_NAME = 'RPT_BL_LINE_COUNT'
                               AND SETTLEMONTH = STL_MONTH)
         AND T.SETTLEMONTH = STL_MONTH;



      -- IF (P_COUNT_LIST.COUNT <> 0) THEN
        OPEN P_COUNT_LIST;
        LOOP
          FETCH P_COUNT_LIST INTO CUSTOMER_PROV, P_COUNT;
          EXIT WHEN P_COUNT_LIST%NOTFOUND;
          RPT_RUNSITE := '1601';
          RPT_SQL     := '循环更新'||STL_MONTH||'月份全部的'||CUSTOMER_PROV||'省调账专线数... ...';
          IF (TO_CHAR(TO_DATE(STL_MONTH, 'yyyyMM'), 'yyyy') = TO_CHAR(TO_DATE(RPT_SETTLEMONTH, 'yyyyMM'), 'yyyy')) THEN
            /*本年*/
            IF ( TO_CHAR(ADD_MONTHS(TO_DATE(STL_MONTH, 'yyyyMM'), 1), 'yyyyMM') = RPT_SETTLEMONTH ) THEN
              /*上月*/
              UPDATE RPT_BL_LINE_COUNT T
                SET T.N_LASTMONTH = P_COUNT,
                    T.N_YEAR = T.N_YEAR + P_COUNT
               WHERE T.PROV_CODE=CUSTOMER_PROV AND T.PAY_CDDE='10' AND T.FEETYPE='1'
                 AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
            ELSE
              UPDATE RPT_BL_LINE_COUNT T
                SET T.N_YEAR = T.N_YEAR + P_COUNT
               WHERE T.PROV_CODE=CUSTOMER_PROV AND T.PAY_CDDE='10' AND T.FEETYPE='1'
                 AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
            END IF;
          ELSE
            /*上年*/
            IF (TO_CHAR(ADD_MONTHS(TO_DATE(STL_MONTH, 'yyyyMM'), 12), 'yyyyMM')=RPT_SETTLEMONTH) THEN
              /*上年本月*/
              UPDATE RPT_BL_LINE_COUNT T
                SET T.L_MONTH = P_COUNT,
                    T.L_YEAR = T.L_YEAR + P_COUNT
               WHERE T.PROV_CODE=CUSTOMER_PROV AND T.PAY_CDDE='10' AND T.FEETYPE='1'
                 AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
            ELSE
              UPDATE RPT_BL_LINE_COUNT T
                SET T.L_YEAR = T.L_YEAR + P_COUNT
               WHERE T.PROV_CODE=CUSTOMER_PROV AND T.PAY_CDDE='10' AND T.FEETYPE='1'
                 AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
            END IF;
          END IF;
        END LOOP;
        CLOSE P_COUNT_LIST;
      -- END IF;

      delete from STL_LINE_P_COUNT;


      /*全部*/
      RPT_RUNSITE := '17';
      RPT_SQL     := '循环更新'||STL_MONTH||'月份全部的集团客户数... ...';
      insert into STL_LINE_C_COUNT(CUSTOMER_PROV,C_COUNT)
      SELECT T.PROV_CODE, T.N_MONTH
        FROM RPT_BL_LINE_COUNT T
       WHERE T.PAY_CDDE = '01'
         AND T.FEETYPE = '9'
         AND T.VERSION IN (SELECT MAX(VERSION)
                              FROM RVL_CONF_VERSION
                             WHERE TABLE_NAME = 'RPT_BL_LINE_COUNT'
                               AND SETTLEMONTH = STL_MONTH)
         AND T.SETTLEMONTH = STL_MONTH;

      SELECT 'procedure 17 completed successfully ';


      -- IF (C_COUNT_LIST.COUNT <> 0) THEN
        OPEN C_COUNT_LIST;
        LOOP
          FETCH C_COUNT_LIST INTO CUSTOMER_PROV, C_COUNT;
          EXIT WHEN C_COUNT_LIST%NOTFOUND;
          RPT_RUNSITE := '1701';
          RPT_SQL     := '循环更新'||STL_MONTH||'月份全部的'||CUSTOMER_PROV||'省集团客户数... ...';
          IF (TO_CHAR(TO_DATE(STL_MONTH, 'yyyyMM'), 'yyyy') = TO_CHAR(TO_DATE(RPT_SETTLEMONTH, 'yyyyMM'), 'yyyy')) THEN
            /*本年*/
            IF ( TO_CHAR(ADD_MONTHS(TO_DATE(STL_MONTH, 'yyyyMM'), 1), 'yyyyMM') = RPT_SETTLEMONTH ) THEN
              /*上月*/
              UPDATE RPT_BL_LINE_COUNT T
                SET T.N_LASTMONTH = C_COUNT,
                    T.N_YEAR = T.N_YEAR + C_COUNT
               WHERE T.PROV_CODE=CUSTOMER_PROV AND T.PAY_CDDE='01' AND T.FEETYPE='9'
                 AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
            ELSE
              UPDATE RPT_BL_LINE_COUNT T
                SET T.N_YEAR = T.N_YEAR + C_COUNT
               WHERE T.PROV_CODE=CUSTOMER_PROV AND T.PAY_CDDE='01' AND T.FEETYPE='9'
                 AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
            END IF;
          ELSE
            /*上年*/
            IF (TO_CHAR(ADD_MONTHS(TO_DATE(STL_MONTH, 'yyyyMM'), 12), 'yyyyMM')=RPT_SETTLEMONTH) THEN
              /*上年本月*/
              UPDATE RPT_BL_LINE_COUNT T
                SET T.L_MONTH = C_COUNT,
                    T.L_YEAR = T.L_YEAR + C_COUNT
               WHERE T.PROV_CODE=CUSTOMER_PROV AND T.PAY_CDDE='01' AND T.FEETYPE='9'
                 AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
            ELSE
              UPDATE RPT_BL_LINE_COUNT T
                SET T.L_YEAR = T.L_YEAR + C_COUNT
               WHERE T.PROV_CODE=CUSTOMER_PROV AND T.PAY_CDDE='01' AND T.FEETYPE='9'
                 AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
            END IF;
          END IF;
        END LOOP;
        CLOSE C_COUNT_LIST;
      -- END IF;

      delete from STL_LINE_C_COUNT;
      SELECT 'procedure 1701 completed successfully ';


      RPT_RUNSITE := '18';
      RPT_SQL     := '循环更新'||STL_MONTH||'月份全部的专线数... ...';
      insert into STL_LINE_P_COUNT(CUSTOMER_PROV,P_COUNT)
      SELECT T.PROV_CODE, T.N_MONTH
        FROM RPT_BL_LINE_COUNT T
       WHERE T.PAY_CDDE = '10'
         AND T.FEETYPE = '9'
         AND T.VERSION IN (SELECT MAX(VERSION)
                              FROM RVL_CONF_VERSION
                             WHERE TABLE_NAME = 'RPT_BL_LINE_COUNT'
                               AND SETTLEMONTH = STL_MONTH)
         AND T.SETTLEMONTH = STL_MONTH;
      SELECT 'procedure 18 completed successfully ';

      -- IF (P_COUNT_LIST.COUNT <> 0) THEN
        OPEN P_COUNT_LIST;
        LOOP
          FETCH P_COUNT_LIST INTO CUSTOMER_PROV, P_COUNT;
          EXIT WHEN P_COUNT_LIST%NOTFOUND;
          RPT_RUNSITE := '1801';
          RPT_SQL     := '循环更新'||STL_MONTH||'月份全部的'||CUSTOMER_PROV||'省全部专线数... ...';
          IF (TO_CHAR(TO_DATE(STL_MONTH, 'yyyyMM'), 'yyyy') = TO_CHAR(TO_DATE(RPT_SETTLEMONTH, 'yyyyMM'), 'yyyy')) THEN
            /*本年*/
            IF ( TO_CHAR(ADD_MONTHS(TO_DATE(STL_MONTH, 'yyyyMM'), 1), 'yyyyMM') = RPT_SETTLEMONTH ) THEN
              /*上月*/
              UPDATE RPT_BL_LINE_COUNT T
                SET T.N_LASTMONTH = P_COUNT,
                    T.N_YEAR = T.N_YEAR + P_COUNT
               WHERE T.PROV_CODE=CUSTOMER_PROV AND T.PAY_CDDE='10' AND T.FEETYPE='9'
                 AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
            ELSE
              UPDATE RPT_BL_LINE_COUNT T
                SET T.N_YEAR = T.N_YEAR + P_COUNT
               WHERE T.PROV_CODE=CUSTOMER_PROV AND T.PAY_CDDE='10' AND T.FEETYPE='9'
                 AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
            END IF;
          ELSE
            /*上年*/
            IF (TO_CHAR(ADD_MONTHS(TO_DATE(STL_MONTH, 'yyyyMM'), 12), 'yyyyMM')=RPT_SETTLEMONTH) THEN
              /*上年本月*/
              UPDATE RPT_BL_LINE_COUNT T
                SET T.L_MONTH = P_COUNT,
                    T.L_YEAR = T.L_YEAR + P_COUNT
               WHERE T.PROV_CODE=CUSTOMER_PROV AND T.PAY_CDDE='10' AND T.FEETYPE='9'
                 AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
            ELSE
              UPDATE RPT_BL_LINE_COUNT T
                SET T.L_YEAR = T.L_YEAR + P_COUNT
               WHERE T.PROV_CODE=CUSTOMER_PROV AND T.PAY_CDDE='10' AND T.FEETYPE='9'
                 AND T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
            END IF;
          END IF;
        END LOOP;
        CLOSE P_COUNT_LIST;
      -- END IF;

    delete from STL_LINE_P_COUNT;
    SELECT 'procedure 1801 completed successfully' ;

    END LOOP;
    CLOSE TABLE_LIST;
    --以往数据--

    UPDATE RPT_BL_LINE_COUNT T
      SET T.N_YEAR = T.N_YEAR+T.N_MONTH
     WHERE T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;
    RPT_RUNSITE := '19';
    RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表结束时间：' ||TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
    PROC_OUT:='Y';
    COMMIT;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

    szSysErr := 'OK';
    nReturn := 0;

    SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;

  END;
END ;;
DELIMITER ;