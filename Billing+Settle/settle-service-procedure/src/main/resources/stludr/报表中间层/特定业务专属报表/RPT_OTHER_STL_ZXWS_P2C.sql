/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层-特定业务专属报表-专线卫士专属省专报表数据生成
**/
DROP PROCEDURE IF EXISTS stludr.`RPT_OTHER_STL_ZXWS_P2C`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "RPT_OTHER_STL_ZXWS_P2C"(
    RPT_SETTLEMONTH IN VARCHAR2,
    FLAG_VERSION IN CHAR,
    PROC_OUT OUT VARCHAR2,
    szSysErr OUT VARCHAR2(1000),
    nReturn OUT NUMBER(4)
)
AS
    -- 专线卫士(500090)明细表RPT_ZXWS_P2C
    v_proc_name varchar2(100) := 'RPT_OTHER_STL_ZXWS_P2C';
    ERRCODE   VARCHAR2(32);
    ERRMSG    VARCHAR2(2048);
    VER_TABLE  VARCHAR2(64);
    RPT_RUNSITE VARCHAR2(32); --运行定位标识
    RPT_SQL     VARCHAR2(4000); --拼SQL的字符串变量
    RPT_TABLE   VARCHAR2(64); --拼表名的字符串变量

    outReturn  int;

BEGIN

    PROC_OUT := '';
    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        PROC_OUT := 'N';
        szSysErr := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || PROC_OUT ) AS error_msg ;
    END;

    BEGIN
        if ( length(RPT_SETTLEMONTH) < 6 )  then
            SELECT 'RPT_SETTLEMONTH length less than 6. RPT_SETTLEMONTH=[' || RPT_SETTLEMONTH || ']';
            outReturn := -1;
            ROLLBACK;
            RETURN;
        end if;

        SELECT 'RPT_SETTLEMONTH=' ||  RPT_SETTLEMONTH;

        VER_TABLE   := 'STL_ZXWS_P2C';
        RPT_RUNSITE := '0';
        RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表开始时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
        SELECT RPT_SQL;
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE,  RPT_SQL);

        RPT_RUNSITE := '1';
        SET @RPT_SQL := 'delete from rpt_zxws_p2c where settle_month = ''' || RPT_SETTLEMONTH || '''';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        -------专线卫士
--         SET @RPT_SQL     := 'insert into stludr.rpt_zxws_p2c(settle_month, prov_cd, prov_name, customer_code, customer_name, offer_code, offer_name, product_code, product_name, offer_order_id,  '||
--                       'product_order_id, device_amount,unit_price, charge_code)  '||
--                       'SELECT a.settle_month, b.PROV_CD AS prov_cd,b.PROV_NM AS prov_name,a.CUSTOMER_CODE AS customer_code,c.FIRST_NAME AS customer_name,a.offer_code AS offer_code, '||
--                       '''专线卫士'' AS offer_name, a.PRODUCT_CODE AS product_code, ''专线卫士'' AS product_name, a.OFFER_ORDER_ID AS offer_order_id, a.PRODUCT_ORDER_ID AS product_order_id, ''1'' AS device_amount, '||
--                       'to_char(a.NO_TAX_FEE /(1 + to_number(a.tax_rate) / 100), ''FM999999999999990'') AS unit_price, a.charge_code FROM STLUDR.UR_EBOSS_' || RPT_SETTLEMONTH || '_T a LEFT JOIN STLUDR.STL_PROVINCE_CD b ON a.OUT_OBJECT = b.PROV_CD LEFT JOIN STLUSERS.STL_CUSTOMER c ON a.CUSTOMER_CODE = c.CUSTOMER_CODE '||
--                       'LEFT JOIN STLUDR.STL_AMOUNT_RULE e ON a.PRODUCT_CODE = e.PRODUCT_CODE AND a.OFFER_CODE = e.OFFER_CODE AND a.ORDER_MODE =e.ORDER_MODE AND a.CHARGE_CODE =e.CHARGE_CODE '||
--                       'WHERE a.offer_code in(''50090'',''50118'')  GROUP BY a.settle_month, b.PROV_CD, b.PROV_NM, a.offer_code, a.CUSTOMER_CODE, c.FIRST_NAME, a.PRODUCT_CODE, a.PRODUCT_ORDER_ID, a.OFFER_ORDER_ID, to_char(a.NO_TAX_FEE /(1 + to_number(a.tax_rate) / 100), ''FM999999999999990''), a.charge_code ';
     -- 添加plan_type字段
       SET @RPT_SQL     := 'insert into stludr.rpt_zxws_p2c(settle_month, prov_cd, prov_name, customer_code, customer_name, offer_code, offer_name, product_code, product_name, offer_order_id,  '||
              'product_order_id, device_amount,unit_price, charge_code,tax_unit_price,PLAN_TYPE)  '||
              'SELECT a.settle_month, b.PROV_CD AS prov_cd,b.PROV_NM AS prov_name,a.CUSTOMER_CODE AS customer_code,c.FIRST_NAME AS customer_name,a.offer_code AS offer_code, '||
        '''专线卫士'' AS offer_name, a.PRODUCT_CODE AS product_code, ''专线卫士'' AS product_name, a.OFFER_ORDER_ID AS offer_order_id, a.PRODUCT_ORDER_ID AS product_order_id, ''1'' AS device_amount, '||
              'to_char(a.NO_TAX_FEE /(1 + to_number(a.tax_rate) / 100), ''FM999999999999990'') AS unit_price, a.charge_code,to_char(a.NO_TAX_FEE, ''FM999999999999990'') AS tax_unit_price,d.PLAN_TYPE FROM STLUDR.UR_EBOSS_' || RPT_SETTLEMONTH || '_T a LEFT JOIN STLUDR.STL_PROVINCE_CD b ON a.OUT_OBJECT = b.PROV_CD LEFT JOIN STLUSERS.STL_CUSTOMER c ON a.CUSTOMER_CODE = c.CUSTOMER_CODE '||
      		  'LEFT JOIN STLUDR.rvl_zxws_config d ON a.CHARGE_CODE =d.FEE_TYPE '||
              'LEFT JOIN STLUDR.STL_AMOUNT_RULE e ON a.PRODUCT_CODE = e.PRODUCT_CODE AND a.OFFER_CODE = e.OFFER_CODE AND a.ORDER_MODE =e.ORDER_MODE AND a.CHARGE_CODE =e.CHARGE_CODE '||
      'WHERE a.offer_code in(''50090'',''50118'')  GROUP BY a.settle_month, b.PROV_CD, b.PROV_NM, a.offer_code, a.CUSTOMER_CODE, c.FIRST_NAME, a.PRODUCT_CODE, a.PRODUCT_ORDER_ID, a.OFFER_ORDER_ID, to_char(a.NO_TAX_FEE /(1 + to_number(a.tax_rate) / 100), ''FM999999999999990''), a.charge_code,to_char(a.NO_TAX_FEE, ''FM999999999999990'') ,d.PLAN_TYPE';


        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        SET @RPT_SQL     := 'insert into stludr.rpt_zxws_p2c(settle_month, prov_cd, prov_name, customer_code, customer_name, offer_code, offer_name, product_code, product_name, offer_order_id, '||
                            'product_order_id, DEVICE_AMOUNT  ,unit_price, charge_code,tax_unit_price,PLAN_TYPE,ORDER_YEAR) '||
                            'SELECT a.settle_month, a.prov_cd,a.prov_name,a.CUSTOMER_CODE,a.customer_name,a.offer_code,a.offer_name, a.PRODUCT_CODE, '||
                            'a.product_name, a.OFFER_ORDER_ID, a.product_order_id, b.cnt,a.unit_price,a.charge_code,a.tax_unit_price,a.PLAN_TYPE, '||
                            'CASE WHEN b.planid BETWEEN 1 AND 15 THEN NULL ELSE b.yearcnt END AS yearcnt '||
                            'FROM ( '||
                            'SELECT a.settle_month, b.PROV_CD AS prov_cd,b.PROV_NM AS prov_name,a.CUSTOMER_CODE AS customer_code,c.FIRST_NAME AS customer_name,a.offer_code AS offer_code, '||
                            '''专线卫士安全增值服务'' AS offer_name, a.PRODUCT_CODE AS product_code, ''安全增值服务'' AS product_name, a.OFFER_ORDER_ID AS offer_order_id, a.PRODUCT_ORDER_ID AS product_order_id, '||
                            'to_char(a.settle_notaxfee /(1 + to_number(a.tax_rate) / 100), ''FM999999999999990'') AS unit_price, a.charge_code,to_char(a.settle_notaxfee, ''FM999999999999990'') AS tax_unit_price,d.PLAN_TYPE,d.PLAN_ID  '||
                            ' FROM STLUDR.UR_EBOSS_' || RPT_SETTLEMONTH || '_T a '||
                            'LEFT JOIN STLUDR.STL_PROVINCE_CD b ON a.OUT_OBJECT = b.PROV_CD '||
                            'LEFT JOIN STLUSERS.STL_CUSTOMER c ON a.CUSTOMER_CODE = c.CUSTOMER_CODE '||
                            'LEFT JOIN STLUDR.rvl_zxws_config d ON a.CHARGE_CODE =d.FEE_TYPE '||
                            'WHERE a.offer_code =''60006'' '||
                            'GROUP BY a.settle_month, b.PROV_CD, b.PROV_NM, a.offer_code, a.CUSTOMER_CODE, c.FIRST_NAME, a.PRODUCT_CODE, '||
                            'a.PRODUCT_ORDER_ID, a.OFFER_ORDER_ID, '||
                            'a.charge_code,d.PLAN_TYPE,d.PLAN_ID,a.settle_notaxfee,a.tax_rate ) a '||
                            'LEFT JOIN (SELECT SOID,ATTR_GROUP_NUM,MAX(cnt) cnt , MAX(yearCnt) yearCnt ,MAX(planid) planid '||
                            'FROM( SELECT SOID,ATTR_GROUP_NUM, '||
                            'CASE WHEN attr_id = ''6000602004'' THEN CAST(attr_value AS SIGNED) ELSE NULL END AS cnt, '||
                            'CASE WHEN attr_id = ''6000602005'' THEN CAST(attr_value AS SIGNED) ELSE NULL END AS yearCnt, '||
                            'CASE WHEN attr_id = ''6000602011'' THEN CAST(attr_value AS SIGNED) ELSE NULL END AS planid '||
                            'FROM STLUSERS.STL_SYNC_ATTR '||
                            'WHERE POSPECNUMBER = ''60006'' AND SOSPECNUMBER = ''2024999480005517'') '||
                            'GROUP BY ATTR_GROUP_NUM,SOID) b ON a.product_order_id=b.soid AND a.plan_id=b.planid ';


        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        RPT_RUNSITE := '2';
        RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表结束时间：' ||TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
        SELECT RPT_SQL;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        commit;

        PROC_OUT:='Y';
        outReturn := 0;

        SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
    END;
END ;;
DELIMITER ;