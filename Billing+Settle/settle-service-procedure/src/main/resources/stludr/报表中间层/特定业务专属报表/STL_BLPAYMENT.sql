/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层-特定业务专属报表-受理模式一一点支付中间表（应收）结算单数据生成
**/
DROP PROCEDURE IF EXISTS stludr.`STL_BLPAYMENT`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "STL_BLPAYMENT"(RPT_SETTLEMONTH IN VARCHAR2,
                                          FLAG_VERSION IN CHAR,
                                          PROC_OUT OUT VARCHAR2,
                                          szSysErr OUT VARCHAR2(1000),
                                          nReturn OUT NUMBER(4)   )
AS
    G_VERSION RVL_CONF_VERSION.VERSION%TYPE; --版本号变量
    G_RESNUM  RVL_CONF_VERSION.VERSIONNUM%TYPE; --版本序号
    ERRCODE   VARCHAR2(32);
    ERRMSG    VARCHAR2(2048);
    VER_TABLE VARCHAR2(64);
    RPT_TABLE VARCHAR2(64);
    RPT_RUNSITE VARCHAR2(64);
    RPT_SQL VARCHAR2(4096);
    v_proc_name  VARCHAR2(30) := 'STL_BLPAYMENT';

BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        PROC_OUT := 'N';
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
        ROLLBACK;

        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;
  BEGIN
    VER_TABLE   := 'RPT_CMCC_BLPAYMENT';
    RPT_RUNSITE := '0';
    RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期受理模式一应收数据入库报表中间表开始时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

    -- 中间表获取版本号
    call SETTLE_REPORT_VERSION(RPT_SETTLEMONTH, VER_TABLE, G_VERSION, G_RESNUM, FLAG_VERSION,szSysErr,nReturn);
    -- 结果表处理数据
    RPT_TABLE   := 'UR_RECV_' || RPT_SETTLEMONTH || '_T';
    RPT_RUNSITE := '1';
    RPT_SQL     := 'INSERT INTO RPT_CMCC_BLPAYMENT(SETTLEMONTH,VERSION,INPROV,OUTPROV,CUSTOMERNUMBER,MEMBER_COUNT,PRODUCT_CODE,OFFER_NAME,PRODUCT_NAME,CHARGE_CODE,SETTNOTAXFEE,SETTTAXFEE,feetype)';
    set @RPT_SQL     := RPT_SQL || ' SELECT '''|| RPT_SETTLEMONTH ||''','''|| G_VERSION ||''',T.IN_OBJECT,T.OUT_OBJECT,T.CUSTOMER_CODE,COUNT(DISTINCT T.MEMBER_CODE),DECODE(T.PRODUCT_ORDER_ID, NULL, T.OFFER_CODE, T.PRODUCT_CODE),T.OFFER_CODE,DECODE(T.PRODUCT_ORDER_ID, NULL, '''', T.PRODUCT_CODE),T.CHARGE_CODE,SUM(T.SETTLE_NOTAXFEE),SUM(T.SETTLE_TAXFEE), t.feetype'
                           || ' FROM '||RPT_TABLE||' T WHERE T.ORDER_MODE = ''1'' AND T.DEST_SOURCE = ''0'' and t.out_object <> ''030'' and t.in_object <> ''030'''
                           || ' GROUP BY T.IN_OBJECT,T.OUT_OBJECT,T.CUSTOMER_CODE,DECODE(T.PRODUCT_ORDER_ID, NULL, T.OFFER_CODE, T.PRODUCT_CODE),T.OFFER_CODE,DECODE(T.PRODUCT_ORDER_ID, NULL, '''', T.PRODUCT_CODE),T.CHARGE_CODE,t.feetype';
    SELECT @RPT_SQL;
      PREPARE STMT FROM @RPT_SQL;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;

    RPT_RUNSITE := '3';
    RPT_SQL     := '更新表中需要的相关信息... ...';
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

    UPDATE RPT_CMCC_BLPAYMENT T
       SET T.INPROVNAME  = (SELECT T1.PROV_NM FROM STL_PROVINCE_CD T1 WHERE T1.PROV_CD = T.INPROV),
           T.OUTPROVNAME = (SELECT T1.PROV_NM FROM STL_PROVINCE_CD T1 WHERE T1.PROV_CD = T.OUTPROV),
           T.CUSTOMERNAME =  (SELECT NVL(T1.FIRST_NAME, '') FROM stlusers.STL_CUSTOMER T1 WHERE T.Settlemonth BETWEEN DATE_FORMAT(T1.effective_date, '%Y%m') AND DATE_FORMAT(T1.expiry_date, '%Y%m') AND T1.CUSTOMER_CODE = T.CUSTOMERNUMBER),
           T.PRODUCT_NAME = (SELECT DISTINCT NVL(T1.NAME, '') FROM STL_SERVICE T1 WHERE T1.SERVICE_CODE = T.PRODUCT_NAME AND T1.ACCT_MONTH = RPT_SETTLEMONTH),
           T.OFFER_NAME   = (SELECT DISTINCT NVL(T1.NAME, '') FROM STL_PRODUCT T1 WHERE T1.PRODUCT_CODE = T.OFFER_NAME AND T1.ACCT_MONTH = RPT_SETTLEMONTH),
           T.CHARGE_NAME =(SELECT DISTINCT NVL(T1.DESCRIPTION, '') FROM STL_CHARGE_ITEM_DEF T1 WHERE T1.CHARGE_ITEM_REF = T.CHARGE_CODE AND T1.ACCT_MONTH = RPT_SETTLEMONTH) /*,
           T.FEETYPE     = CASE WHEN T.CHARGE_CODE > 0 AND T.CHARGE_CODE <= 50 THEN '0'
                                WHEN T.CHARGE_CODE >= 1000 AND T.CHARGE_CODE < 5000 THEN '0'
                                WHEN T.CHARGE_CODE > 50 AND T.CHARGE_CODE <= 100 THEN '1'
                                WHEN T.CHARGE_CODE >= 5000 AND T.CHARGE_CODE < 10000 THEN '1' END*/
     WHERE T.VERSION = G_VERSION AND T.SETTLEMONTH = RPT_SETTLEMONTH;

    RPT_RUNSITE := '4';
    RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
    PROC_OUT := 'Y';
    COMMIT;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

    szSysErr := 'OK';
    nReturn := 0;

    SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;

  END;
end ;;
DELIMITER ;