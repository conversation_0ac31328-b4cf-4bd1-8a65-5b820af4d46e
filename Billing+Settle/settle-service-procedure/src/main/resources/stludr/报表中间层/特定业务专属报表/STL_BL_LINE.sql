/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层-特定业务专属报表-数据专线业务专有报表数据生成
**/
DROP PROCEDURE IF EXISTS stludr.`STL_BL_LINE`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "STL_BL_LINE"(RPT_SETTLEMONTH IN VARCHAR2,
                                            FLAG_VERSION IN CHAR,
                                            PROC_OUT OUT VARCHAR2,
                                            szSysErr OUT VARCHAR2(1000),
                                            nReturn OUT NUMBER(4)   )
AS
    G_VERSION RVL_CONF_VERSION.VERSION%TYPE; -- 版本号变量
    G_RESNUM  RVL_CONF_VERSION.VERSIONNUM%TYPE; -- 版本序号
    ERRCODE   VARCHAR2(32);
    ERRMSG    VARCHAR2(2048);
    VER_TABLE VARCHAR2(64);
    RPT_TABLE VARCHAR2(64);
    RPT_RUNSITE VARCHAR2(64);
    RPT_SQL VARCHAR2(4096);
    v_proc_name VARCHAR2(30) := 'STL_BL_LINE';


  BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        PROC_OUT := 'N';
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
        ROLLBACK;

        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;
  BEGIN
    VER_TABLE   := 'RPT_BL_LINE';
    RPT_RUNSITE := '0';
    RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表开始时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
    -- 中间表获取版本号
    call SETTLE_REPORT_VERSION(RPT_SETTLEMONTH, VER_TABLE,G_VERSION, G_RESNUM, FLAG_VERSION,szSysErr,nReturn);
    RPT_RUNSITE := '1';
    RPT_TABLE   := 'UR_RECV_' || RPT_SETTLEMONTH || '_T';

    /*
    set @RPT_SQL     := 'INSERT INTO RPT_BL_LINE(SETTLEMONTH,VERSION,INPROVCODE,OUTPROVCODE,CUSTOMERNUMBER,'
                     ||'PRODUCT_CODE,PRODUCT_NAME,POSPEC_CODE,SOSPEC_CODE,CHARGE,CHARGE_CODE,'
                     ||'TAXRATE,SETTLE_NOTAXFEE,SETTLE_TAXFEE,PRODUCT_ORDER_ID,ECPROVCODE)'
                     ||' SELECT ''' || RPT_SETTLEMONTH || ''', ''' || G_VERSION || ''', T.IN_OBJECT, T.OUT_OBJECT, T.CUSTOMER_CODE, '
                     ||'DECODE(T.PRODUCT_ORDER_ID, NULL, T.OFFER_CODE, T.PRODUCT_CODE), DECODE(T.PRODUCT_ORDER_ID, NULL, GET_PRODUCT_NAME(T.OFFER_CODE, T.PRODUCT_CODE, ''' || RPT_SETTLEMONTH || ''', ''1''), GET_PRODUCT_NAME(T.OFFER_CODE, T.PRODUCT_CODE, ''' || RPT_SETTLEMONTH || ''', ''3'')), T.OFFER_CODE, T.PRODUCT_CODE,T.CHARGE, T.CHARGE_CODE, '
                     ||'T.TAX_RATE, SUM(T.SETTLE_NOTAXFEE), SUM(T.SETTLE_TAXFEE),T.PRODUCT_ORDER_ID, T.CUSTOMER_PROV '
                     ||' FROM ' || RPT_TABLE || ' T '
                     ||' WHERE T.OFFER_CODE IN (''01011301'',''01011306'') AND T.ORDER_MODE IN ( ''3'' , ''4'') and t.out_object <> ''030'' and t.in_object <> ''030'''
                     ||' GROUP BY T.IN_OBJECT, T.OUT_OBJECT, T.CUSTOMER_CODE, T.PRODUCT_ORDER_ID, T.OFFER_CODE, T.PRODUCT_CODE, T.CHARGE, T.CHARGE_CODE, T.TAX_RATE,T.CUSTOMER_PROV ';
    */

    set @RPT_SQL     :=  'INSERT INTO RPT_BL_LINE (SETTLEMONTH,VERSION,INPROVCODE,OUTPROVCODE,CUSTOMERNUMBER, '
                    ||'      PRODUCT_CODE,PRODUCT_NAME,POSPEC_CODE,SOSPEC_CODE,CHARGE,CHARGE_CODE,'
                    ||'  TAXRATE,SETTLE_NOTAXFEE,SETTLE_TAXFEE,PRODUCT_ORDER_ID,ECPROVCODE)  '
                    ||' SELECT ''' || RPT_SETTLEMONTH || ''', ''' || G_VERSION || ''', T.IN_OBJECT, T.OUT_OBJECT, T.CUSTOMER_CODE, '
                    ||' DECODE(T.PRODUCT_ORDER_ID, NULL, T.OFFER_CODE, T.PRODUCT_CODE),  '
                    ||'       case when (T.PRODUCT_ORDER_ID is null) then NVL(p.NAME, '''') else concat(nvl(p.name, ''''), ''_'', nvl(s.name, '''')) end as product_name, '
                    ||'       T.OFFER_CODE, T.PRODUCT_CODE,T.CHARGE, T.CHARGE_CODE, T.TAX_RATE, SUM(T.SETTLE_NOTAXFEE), SUM(T.SETTLE_TAXFEE),T.PRODUCT_ORDER_ID, T.CUSTOMER_PROV   '
                    ||'  FROM ' || RPT_TABLE || ' T '
                    ||'  left join STL_PRODUCT p ignore index (primary,PK_STL_PRODUCT_CODE) on p.PRODUCT_CODE = t.OFFER_CODE AND p.ACCT_MONTH = ''' || RPT_SETTLEMONTH || ''''
                    ||'  left join STL_SERVICE s ignore index (primary,PK_STL_SERVICE_CODE) on s.SERVICE_CODE = t.PRODUCT_CODE AND s.ACCT_MONTH = ''' || RPT_SETTLEMONTH || ''''
                    ||' WHERE T.OFFER_CODE IN (''01011301'',''01011306'') AND T.ORDER_MODE IN ( ''3'' , ''4'') and t.out_object <> ''030'' and t.in_object <> ''030''  '
                    ||' GROUP BY T.IN_OBJECT, T.OUT_OBJECT, T.CUSTOMER_CODE, T.PRODUCT_ORDER_ID, T.OFFER_CODE, T.PRODUCT_CODE, T.CHARGE, T.CHARGE_CODE, T.TAX_RATE,T.CUSTOMER_PROV ';


    SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
    RPT_RUNSITE := '2';
    RPT_SQL     := '更新表中省公司的中文名称... ...';
    UPDATE RPT_BL_LINE T
       SET T.INPROVNAME  = (SELECT T1.PROV_NM FROM STL_PROVINCE_CD T1 WHERE T1.PROV_CD = T.INPROVCODE),
           T.OUTPROVNAME = (SELECT T1.PROV_NM FROM STL_PROVINCE_CD T1 WHERE T1.PROV_CD = T.OUTPROVCODE),
           T.ECPROVNAME  = (SELECT T1.PROV_NM FROM STL_PROVINCE_CD T1 WHERE T1.PROV_CD = T.ECPROVCODE)
     WHERE T.VERSION = G_VERSION AND T.SETTLEMONTH = RPT_SETTLEMONTH;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
    RPT_RUNSITE := '3';
    RPT_SQL     := '更新表中客户名称的中文名称... ...';
    UPDATE RPT_BL_LINE T
       SET T.CUSTOMERNAME = (SELECT NVL(T1.FIRST_NAME, '') FROM stlusers.STL_CUSTOMER T1 WHERE T.SETTLEMONTH BETWEEN DATE_FORMAT(T1.effective_date, '%Y%m') AND DATE_FORMAT(T1.expiry_date, '%Y%m') AND T1.CUSTOMER_CODE = T.CUSTOMERNUMBER)
     WHERE T.VERSION = G_VERSION AND T.SETTLEMONTH = RPT_SETTLEMONTH;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
    RPT_RUNSITE := '4';
    RPT_SQL     := '更新表费项分类和费项的中文名称... ...';
    UPDATE RPT_BL_LINE T
       SET T.CHARGE_NAME =(SELECT DISTINCT NVL(T1.DESCRIPTION, '') FROM STL_CHARGE_ITEM_DEF T1 WHERE T1.CHARGE_ITEM_REF = T.CHARGE_CODE AND T1.ACCT_MONTH = RPT_SETTLEMONTH) ,
           T.FEETYPE     = CASE WHEN T.CHARGE_CODE > 0 AND T.CHARGE_CODE <= 50 THEN '0'
                                WHEN T.CHARGE_CODE >= 1000 AND T.CHARGE_CODE < 5000 THEN '0'
                                WHEN T.CHARGE_CODE > 50 AND T.CHARGE_CODE <= 100 THEN '1'
                                WHEN T.CHARGE_CODE >= 5000 AND T.CHARGE_CODE < 10000 THEN '1' END
     WHERE T.VERSION = G_VERSION AND T.SETTLEMONTH = RPT_SETTLEMONTH;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
    RPT_RUNSITE := '5';
    RPT_SQL     := '更新表中产品和商品的中文名称... ...';
    UPDATE RPT_BL_LINE T
       SET T.POSPEC_NAME = (SELECT DISTINCT NVL(T1.NAME, '') FROM STL_PRODUCT T1 WHERE T1.PRODUCT_CODE = T.POSPEC_CODE AND T1.ACCT_MONTH = RPT_SETTLEMONTH),
           T.SOSPEC_NAME = (SELECT DISTINCT NVL(T1.NAME, '') FROM STL_SERVICE T1 WHERE T1.SERVICE_CODE = T.SOSPEC_CODE AND T1.ACCT_MONTH = RPT_SETTLEMONTH)
     WHERE T.VERSION = G_VERSION AND T.SETTLEMONTH = RPT_SETTLEMONTH;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
    RPT_RUNSITE := '6';
    RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表结束时间：' ||TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
    PROC_OUT:='Y';
    COMMIT;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
    szSysErr := 'OK';
    nReturn := 0;

    SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;
  END;

END ;;
DELIMITER ;