/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层-特定业务专属报表-流量统付全网业务分客户结算明细表应收数据生成
**/
DROP PROCEDURE IF EXISTS stludr.`RPT_OTHER_STL_GPRS_BL`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "RPT_OTHER_STL_GPRS_BL"(
    RPT_SETTLEMONTH IN VARCHAR2,
	FLAG_VERSION IN CHAR,
	PROC_OUT OUT VARCHAR2,
	szSysErr OUT VARCHAR2(1000),
	nReturn OUT NUMBER(4)
)
AS
    -- 流量统付全网业务分客户结算明细表
    v_proc_name       VARCHAR2(30) := 'RPT_OTHER_STL_GPRS_BL';
    G_VERSION RVL_CONF_VERSION.VERSION%TYPE; --版本号变量
    G_RESNUM  RVL_CONF_VERSION.VERSIONNUM%TYPE; --版本序号
    ERRCODE   VARCHAR2(32);
    ERRMSG    VARCHAR2(2048);
    VER_TABLE VARCHAR2(64);
    RPT_RUNSITE VARCHAR2(32); --运行定位标识
    RPT_SQL     VARCHAR2(4000); --拼SQL的字符串变量
    RPT_TABLE   VARCHAR2(64); --拼表名的字符串变量
    outReturn  int;

BEGIN

    PROC_OUT := '';
    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        PROC_OUT := 'N';
        szSysErr := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || PROC_OUT ) AS error_msg ;
    END;

    BEGIN
        if ( length(RPT_SETTLEMONTH) < 6 )  then
            SELECT 'RPT_SETTLEMONTH length less than 6. RPT_SETTLEMONTH=[' || RPT_SETTLEMONTH || ']';
            outReturn := -1;
            ROLLBACK;
            RETURN;
        end if;

        SELECT 'RPT_SETTLEMONTH=' ||  RPT_SETTLEMONTH;

        VER_TABLE   := 'RPT_GPRS_BL';
        RPT_RUNSITE := '0';
        RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '特定业务专属报表-流量统付全网业务分客户结算明细表应收数据生成开始时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
        --中间表获取版本号 -- 调用内部存储过程
        call SETTLE_REPORT_VERSION(RPT_SETTLEMONTH, VER_TABLE, G_VERSION, G_RESNUM, FLAG_VERSION, @PROC_OUT, @outReturn);
        RPT_TABLE   := 'UR_RECV_' || RPT_SETTLEMONTH || '_T';
        RPT_RUNSITE := '1';
        SET @RPT_SQL     := 'INSERT INTO RPT_GPRS_BL(SETTLEMONTH, VERSION, PROV_CODE, CUSTOMERNUMBER, TAXRATE, ORG_FEE, INCOMING_FEE, OUTGOING_FEE)'
                         ||' SELECT ''' || RPT_SETTLEMONTH || ''',''' || G_VERSION || ''',T.PROV_CODE,T.CUSTOMERNUMBER,T.TAX_RATE,SUM(T.ORG_FEE) AS ORG_FEE,SUM(T.INCOMING_FEE) AS INCOMING_FEE,SUM(T.OUTGOING_FEE) AS OUTGOING_FEE '
                         ||' FROM (SELECT T1.CUSTOMER_CODE AS CUSTOMERNUMBER,T1.OUT_OBJECT AS PROV_CODE,T1.TAX_RATE,SUM(T1.CHARGE) AS ORG_FEE,0 AS INCOMING_FEE,SUM(T1.OUTGOING_FEE) AS OUTGOING_FEE '
                                ||' FROM (SELECT V.STREAM_ID,V.CUSTOMER_CODE,V.OUT_OBJECT,V.TAX_RATE,V.CHARGE,SUM(V.SETTLE_NOTAXFEE + V.SETTLE_TAXFEE) AS OUTGOING_FEE '
                                     ||' FROM ' || RPT_TABLE || ' V '
                                     ||' WHERE V.ORDER_MODE IN (''1'',''3'') AND V.PRODUCT_CODE IN (SELECT DISTINCT V1.SOSPECNUMBER FROM STL_SETTLE_CONFIG V1 WHERE V1.DESCRIPTION = ''gprsdom'') and v.out_object <> ''030'' and v.in_object <> ''030'''
                                     ||' GROUP BY V.STREAM_ID,V.CHARGE,V.TAX_RATE,V.OUT_OBJECT,V.CUSTOMER_CODE) T1 '
                                ||' GROUP BY T1.CUSTOMER_CODE, T1.OUT_OBJECT, T1.TAX_RATE '
                                ||' UNION ALL '
                                ||' SELECT T2.CUSTOMER_CODE AS CUSTOMERNUMBER,T2.IN_OBJECT AS PROV_CODE,T2.TAX_RATE, 0 AS ORG_FEE, SUM(T2.SETTLE_NOTAXFEE + T2.SETTLE_TAXFEE) AS INCOMING_FEE,0 AS OUTGOING_FEE '
                                ||' FROM ' || RPT_TABLE || ' T2 '
                                ||' WHERE T2.ORDER_MODE IN (''1'',''3'') AND T2.PRODUCT_CODE IN (SELECT DISTINCT V1.SOSPECNUMBER FROM STL_SETTLE_CONFIG V1 WHERE V1.DESCRIPTION = ''gprsdom'') and t2.out_object <> ''030'' and t2.in_object <> ''030'''
                                ||' GROUP BY T2.CUSTOMER_CODE, T2.IN_OBJECT, T2.TAX_RATE) T '
                         ||' GROUP BY T.CUSTOMERNUMBER, T.PROV_CODE, T.TAX_RATE';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, @RPT_SQL);

        RPT_RUNSITE := '2';
        RPT_SQL     := '更新表中省公司的中文名称... ...';
        UPDATE RPT_GPRS_BL T
           SET T.PROV_NAME       = (SELECT T1.PROV_NM FROM STL_PROVINCE_CD T1 WHERE T1.PROV_CD = T.PROV_CODE)
         WHERE T.VERSION = G_VERSION AND T.SETTLEMONTH = RPT_SETTLEMONTH;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        RPT_RUNSITE := '3';
        RPT_SQL     := '更新表中客户名称的中文名称... ...';
        UPDATE RPT_GPRS_BL T
           SET T.CUSTOMERNAME = (SELECT NVL(T1.FIRST_NAME, '') FROM stlusers.STL_CUSTOMER T1 WHERE RPT_SETTLEMONTH BETWEEN DATE_FORMAT(T1.effective_date, '%Y%m') AND DATE_FORMAT(T1.expiry_date, '%Y%m') AND T1.CUSTOMER_CODE = T.CUSTOMERNUMBER)
         WHERE T.VERSION = G_VERSION AND T.SETTLEMONTH = RPT_SETTLEMONTH;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        RPT_RUNSITE := '4';
        RPT_SQL     := '更新表中净收入... ...';
        UPDATE RPT_GPRS_BL T
           SET T.NETINCOMING_FEE = T.ORG_FEE + T.INCOMING_FEE - T.OUTGOING_FEE
         WHERE T.VERSION = G_VERSION AND T.SETTLEMONTH = RPT_SETTLEMONTH;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        RPT_RUNSITE := '5';
        RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '特定业务专属报表-流量统付全网业务分客户结算明细表应收数据生成结束时间：' ||TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        commit;

        PROC_OUT:='Y';
        outReturn := 0;

        SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
    END;
END ;;
DELIMITER ;