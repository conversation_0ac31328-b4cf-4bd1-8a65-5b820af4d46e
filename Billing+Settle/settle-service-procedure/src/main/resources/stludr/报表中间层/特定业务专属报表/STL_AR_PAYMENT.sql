/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层-特定业务专属报表-受理模式三一点支付中间表（实收）结算单数据生成
**/
DROP PROCEDURE IF EXISTS stludr.`STL_AR_PAYMENT`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "STL_AR_PAYMENT"(RPT_SETTLEMONTH IN VARCHAR2,
                                            FLAG_VERSION IN CHAR,
                                            PROC_OUT OUT VARCHAR2,
                                            szSysErr OUT VARCHAR2(1000),
                                            nReturn OUT NUMBER(4)   )
AS
    G_VERSION RVL_CONF_VERSION.VERSION%TYPE; -- 版本号变量
    G_RESNUM  RVL_CONF_VERSION.VERSIONNUM%TYPE; -- 版本序号
    ERRCODE   VARCHAR2(32);
    ERRMSG    VARCHAR2(2048);
    VER_TABLE VARCHAR2(64);
    RPT_TABLE VARCHAR2(64);
    RPT_RUNSITE VARCHAR2(64);
    RPT_SQL VARCHAR2(4096);
    v_proc_name  VARCHAR2(30) := 'STL_AR_PAYMENT';

BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        PROC_OUT := 'N';
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
        ROLLBACK;

        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;

  BEGIN
    VER_TABLE   := 'RPT_AR_PAYMENT';
    RPT_RUNSITE := '0';
    RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期受理模式三实收数据入库报表中间表开始时间：' ||TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
    -- 中间表获取版本号
    call SETTLE_REPORT_VERSION(RPT_SETTLEMONTH,VER_TABLE,G_VERSION,G_RESNUM,FLAG_VERSION,szSysErr,nReturn);
    RPT_RUNSITE := '1';
    RPT_TABLE   := 'UR_PAID_' || RPT_SETTLEMONTH || '_T';
    RPT_SQL     := 'INSERT INTO RPT_AR_PAYMENT(SETTLEMONTH,VERSION,INPROV,OUTPROV,CUSTOMERNUMBER,ORG_MONTH,PRODUCT_CODE,OFFER_NAME,PRODUCT_NAME,CHARGE_CODE,SETTNOTAXFEE,SETTTAXFEE)';
    set @RPT_SQL     := RPT_SQL || ' SELECT '''|| RPT_SETTLEMONTH ||''','''|| G_VERSION ||''',T.IN_OBJECT,T.OUT_OBJECT,T.CUSTOMER_CODE,T.ORG_MONTH,'
                           || ' DECODE(T.PRODUCT_ORDER_ID, NULL, T.OFFER_CODE, T.PRODUCT_CODE),T.OFFER_CODE,DECODE(T.PRODUCT_ORDER_ID, NULL, '''', T.PRODUCT_CODE),T.CHARGE_CODE,SUM(T.SETTLE_NOTAXFEE),SUM(T.SETTLE_TAXFEE)'
                           || ' FROM '||RPT_TABLE||' T WHERE T.ORDER_MODE = ''3'' and t.out_object <> ''030'' and t.in_object <> ''030'' and t.dest_source in (''0'', ''1'')'
                           || ' GROUP BY T.IN_OBJECT,T.OUT_OBJECT,T.CUSTOMER_CODE,T.ORG_MONTH,DECODE(T.PRODUCT_ORDER_ID, NULL, T.OFFER_CODE, T.PRODUCT_CODE),T.OFFER_CODE,DECODE(T.PRODUCT_ORDER_ID, NULL, '''', T.PRODUCT_CODE),T.CHARGE_CODE';
    SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
    RPT_RUNSITE := '3';
    RPT_SQL     := '更新表中需要的相关信息... ...';

   Update /*+join_order(T1,T) */RPT_AR_PAYMENT T
join STL_PROVINCE_CD T1 on T1.PROV_CD = T.INPROV
join STL_PROVINCE_CD T2 on T2.PROV_CD = T.OUTPROV
set T.INPROVNAME  =T1.PROV_NM,
    T.OUTPROVNAME = T2.PROV_NM
WHERE T.VERSION = 'V01' AND T.SETTLEMONTH = RPT_SETTLEMONTH;

SELECT 'update 3';
	UPDATE RPT_AR_PAYMENT T
	JOIN stlusers.STL_CUSTOMER T1 ON  T1.CUSTOMER_CODE = T.CUSTOMERNUMBER
	SET T.CUSTOMERNAME = NVL(T1.FIRST_NAME, '')
	WHERE  T.VERSION = G_VERSION AND T.SETTLEMONTH = RPT_SETTLEMONTH AND TO_DATE(T.Settlemonth, 'yyyyMM') >= TO_DATE(TO_CHAR(T1.EFFECTIVE_DATE, 'yyyyMM'), 'yyyyMM') AND TO_DATE(T.Settlemonth, 'yyyyMM') < TO_DATE(TO_CHAR(T1.EXPIRY_DATE, 'yyyyMM'), 'yyyyMM')
	;

	SELECT 'Update 4';
	UPDATE RPT_AR_PAYMENT T
	JOIN (SELECT DISTINCT SERVICE_CODE,NVL(NAME, '') NAME FROM STL_SERVICE WHERE ACCT_MONTH = RPT_SETTLEMONTH ) T1 ON T1.SERVICE_CODE = T.PRODUCT_NAME
	SET T.PRODUCT_NAME=T1.name
	WHERE T.VERSION = G_VERSION AND T.SETTLEMONTH = RPT_SETTLEMONTH;

	SELECT 'Update 5';
	UPDATE RPT_AR_PAYMENT T
	JOIN (SELECT DISTINCT PRODUCT_CODE,NVL(NAME, '') NAME FROM STL_PRODUCT WHERE ACCT_MONTH = RPT_SETTLEMONTH ) T1 ON T1.PRODUCT_CODE = T.OFFER_NAME
	SET T.OFFER_NAME = T1.name
	WHERE T.VERSION = G_VERSION AND T.SETTLEMONTH = RPT_SETTLEMONTH;

	SELECT 'Update 6';
	UPDATE RPT_AR_PAYMENT T
	JOIN (SELECT DISTINCT CHARGE_ITEM_REF,NVL(DESCRIPTION, '') description FROM STL_CHARGE_ITEM_DEF WHERE ACCT_MONTH = RPT_SETTLEMONTH ) T1 ON  T1.CHARGE_ITEM_REF = T.CHARGE_CODE
	SET T.CHARGE_NAME = T1.description
	WHERE T.VERSION = G_VERSION AND T.SETTLEMONTH = RPT_SETTLEMONTH;

	SELECT 'Update 7';
	UPDATE  RPT_AR_PAYMENT T
	SET    T.FEETYPE     = CASE WHEN T.CHARGE_CODE > 0 AND T.CHARGE_CODE <= 50 THEN '0'
					WHEN T.CHARGE_CODE >= 1000 AND T.CHARGE_CODE < 5000 THEN '0'
					WHEN T.CHARGE_CODE > 50 AND T.CHARGE_CODE <= 100 THEN '1'
					WHEN T.CHARGE_CODE >= 5000 AND T.CHARGE_CODE < 10000 THEN '1' END
	WHERE T.VERSION = G_VERSION AND T.SETTLEMONTH = RPT_SETTLEMONTH;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
    RPT_RUNSITE := '4';
    RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表结束时间：' ||TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
    PROC_OUT:='Y';
    COMMIT;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
    szSysErr := 'OK';
    nReturn := 0;

    SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;

  END;
END ;;
DELIMITER ;