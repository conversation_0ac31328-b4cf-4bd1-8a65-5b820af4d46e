SELECT
    T1.SHOW_PONAME,
    T1.SHOW_SONAME,
    T1.SHOW_CHARGE,
    T1.SHOW_PONAME_NM,
    T1.SHOW_SONAME_NM,
    T1.SHOW_CHARGE_NM,
    ROUND( SUM( T1.SETTAXFEE ), 2 ) AS SETTAXFEE,
    ROUND( SUM( T1.SETTNOTAXFEE ), 2 ) AS SETTNOTAXFEE,
    0 AS SETTNOTAXFEE0,
    0 AS SETTNOTAXFEE1,
    0 AS SETTNOTAXFEE2,
    0 AS SETTNOTAXFEE3,
    0 AS SETTNOTAXFEE4
FROM
    (
        SELECT
            T1.SHOW_PONAME,
            T1.SHOW_SONAME,
            T1.SHOW_CHARGE,
            T1.SHOW_CHARGE_NM,
            T1.SHOW_SONAME_NM,
            T1.SHOW_PONAME_NM,
            T2.TAXRATE,
            T2.PROV_CODE,
            T2.PROV_NAME,
            T2.SETTLEMONTH,
            ROUND( SUM( NVL ( T2.SETTNOTAXFEE, 0 )) / ( 1 + NVL ( T2.TAXRATE, 0 ) / 100 ), 2 ) AS SETTNOTAXFEE,
            ROUND( SUM( NVL ( T2.SETTNOTAXFEE, 0 )), 2 ) AS SETTAXFEE
        FROM
            STLUDR.STL_COMMODITY_MAPPING T1
                LEFT JOIN (
                SELECT
                    T1.SETTLEMONTH,
                    T1.PROV_CODE,
                    T1.PROV_NAME,
                    T1.POSPEC_CODE,
                    T1.POSPEC_NAME,
                    T1.SOSPEC_CODE,
                    T1.SOSPEC_NAME,
                    T1.CHARGE_CODE,
                    T1.CHARGE_NAME,
                    T1.TAXRATE,
                    SUM( T1.SETTNOTAXFEE ) / 100 AS SETTNOTAXFEE
                FROM
                    STLUDR.RPT_SETTLE_INCOME_PROV_TMP T1
                WHERE
                    T1.SOSPEC_CODE is not  NULL
                  AND T1.PROV_CODE ='100'
                  AND T1.SETTLEMONTH = '202407'
                GROUP BY
                    T1.SETTLEMONTH,
                    T1.PROV_CODE,
                    T1.PROV_NAME,
                    T1.POSPEC_CODE,
                    T1.POSPEC_NAME,
                    T1.SOSPEC_CODE,
                    T1.SOSPEC_NAME,
                    T1.CHARGE_CODE,
                    T1.CHARGE_NAME,
                    T1.TAXRATE
            ) T2 ON T1.PRODUCT_CODE = T2.POSPEC_CODE
                AND T1.SERVICE_CODE = T2.SOSPEC_CODE
                AND T1.CHARGE_ITEM_REF = T2.CHARGE_CODE
        WHERE
            T1.SERVICE_CODE is not  NULL
        GROUP BY
            T1.SHOW_PONAME,
            T1.SHOW_SONAME,
            T1.SHOW_CHARGE,
            T1.SHOW_CHARGE_NM,
            T1.SHOW_SONAME_NM,
            T1.SHOW_PONAME_NM,
            T2.TAXRATE,
            T2.PROV_CODE,
            T2.PROV_NAME,
            T2.SETTLEMONTH
    ) T1
GROUP BY
    T1.SETTLEMONTH,
    T1.SHOW_PONAME,
    T1.SHOW_SONAME,
    T1.SHOW_CHARGE,
    T1.PROV_CODE,
    T1.PROV_NAME,
    T1.SHOW_PONAME_NM,
    T1.SHOW_SONAME_NM,
    T1.SHOW_CHARGE_NM