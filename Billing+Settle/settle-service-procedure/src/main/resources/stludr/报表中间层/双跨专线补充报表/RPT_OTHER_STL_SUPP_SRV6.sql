/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层 -双跨专线补充报表（SRV6）数据生成
**/
use stludr;
delimiter //
CREATE or replace DEFINER="stludr"@"10.%"  PROCEDURE "RPT_OTHER_STL_SUPP_SRV6"(
    RPT_SETTLEMONTH IN VARCHAR2,
    FLAG_VERSION IN CHAR,
    PROC_OUT OUT VARCHAR2,
    szSysErr OUT VARCHAR2(1000),
    nReturn OUT NUMBER(4)
)
AS
    -- 双跨专线补充报表（SRV6）
    v_proc_name varchar2(100) := 'RPT_OTHER_STL_SUPP_SRV6';
    G_VERSION RVL_CONF_VERSION.VERSION%TYPE; --版本号变量
    G_RESNUM  RVL_CONF_VERSION.VERSIONNUM%TYPE; --版本序号
    ERRCODE   VARCHAR2(32);
    ERRMSG    VARCHAR2(2048);
    VER_TABLE VARCHAR2(64);
    inter_sql   varchar2(4096);
    outReturn  int;

    RPT_RUNSITE VARCHAR2(32); --运行定位标识
    RPT_SQL     VARCHAR2(4000); --拼SQL的字符串变量
    RPT_TABLE   VARCHAR2(64); --拼表名的字符串变量

BEGIN

    PROC_OUT := '';
    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
PROC_OUT := 'N';
        szSysErr := substr(@p2, 1, 1000);
        outReturn  := -1;
ROLLBACK;

--  日志写表
select ('exception: ' || outReturn || '|'  || @p1 || '|' || PROC_OUT ) AS error_msg ;
END;

BEGIN
        if ( length(RPT_SETTLEMONTH) < 6 )  then
SELECT 'RPT_SETTLEMONTH length less than 6. RPT_SETTLEMONTH=[' || RPT_SETTLEMONTH || ']';
outReturn := -1;
ROLLBACK;
RETURN;
end if;

SELECT 'RPT_SETTLEMONTH=' ||  RPT_SETTLEMONTH;


VER_TABLE   := 'RPT_INTER_SUPP';
        RPT_RUNSITE := '0';
        RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表开始时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
SELECT RPT_SQL;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

--更新CRM与结算的基础信息接口表
--SRV6
RPT_RUNSITE := '1';
update stl_baseinfo_srv6 set end_serv_rent_rate = 100, end_serv_once_rate = 100, end_setup_rate = 100, end_rent_rate = 100
where start_prov = end_prov;

update stl_baseinfo_srv6 set start_serv_rent_rate = 100 - end_serv_rent_rate, start_serv_once_rate = 100 - end_serv_once_rate, start_setup_rate = 100 - end_setup_rate, start_rent_rate = 100 - end_rent_rate;

update stl_baseinfo_srv6 set start_serv_rent_rate = null, end_serv_rent_rate = null, start_serv_once_rate = null, end_serv_once_rate = null,
                            start_setup_rate = null, end_setup_rate = null, start_rent_rate = null, end_rent_rate = null
where start_prov = '030' or end_prov = '030';

update stl_baseinfo_srv6 a set customername = (select DISTINCT first_name from stlusers.stl_customer c
                                              where customer_code = a.customernumber and RPT_SETTLEMONTH BETWEEN DATE_FORMAT(effective_date, '%Y%m') AND DATE_FORMAT(expiry_date, '%Y%m'));

SET @RPT_SQL := 'truncate table rpt_inter_supp';
SELECT @RPT_SQL;
PREPARE STMT FROM @RPT_SQL;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

SET @inter_sql := 'insert into rpt_inter_supp(order_mode, offer_code, product_code, offer_order_id, product_order_id, charge_type, charge_category, ' ||
           'feetype, out_object, in_object, settle_notaxfee) ' ||
        'select order_mode, offer_code, product_code, offer_order_id, product_order_id,  ' ||
				       'decode(charge_code, ''1850'', ''RENT'', ''08'', ''SETUP'', ''1658'', ''SERV_RENT'', ''1657'', ''SERV_ONCE'', ''5850'', ''RENT'', ''58'', ''SETUP'', ''5658'', ''SERV_RENT'', ''5657'', ''SERV_ONCE'', ' ||
							                      '''3967'', ''RENT'', ''1424'', ''SETUP'', ''1665'', ''SERV_RENT'', ''1663'', ''SERV_ONCE'', ''7967'', ''RENT'', ''5424'', ''SETUP'', ''5665'', ''SERV_RENT'', ''5663'', ''SERV_ONCE'', ' ||
							                      '''3968'', ''RENT'', ''1425'', ''SETUP'', ''1666'', ''SERV_RENT'', ''1664'', ''SERV_ONCE'', ''7968'', ''RENT'', ''5425'', ''SETUP'', ''5666'', ''SERV_RENT'', ''5664'', ''SERV_ONCE'', ' ||
							                      '''3965'', ''RENT'', ''1305'', ''SETUP'', ''1661'', ''SERV_RENT'', ''1659'', ''SERV_ONCE'', ''7965'', ''RENT'', ''5305'', ''SETUP'', ''5661'', ''SERV_RENT'', ''5659'', ''SERV_ONCE'', ' ||
							                      '''3966'', ''RENT'', ''1421'', ''SETUP'', ''1662'', ''SERV_RENT'', ''1660'', ''SERV_ONCE'', ''7966'', ''RENT'', ''5421'', ''SETUP'', ''5662'', ''SERV_RENT'', ''5660'', ''SERV_ONCE'') charge_type, ' ||
							 'decode(charge_code, ''1850'', ''PROV'', ''08'', ''PROV'', ''1658'', ''PROV'', ''1657'', ''PROV'', ''5850'', ''PROV'', ''58'', ''PROV'', ''5658'', ''PROV'', ''5657'', ''PROV'', ' ||
							                     '''3967'', ''DOMESTIC'', ''1424'', ''DOMESTIC'', ''1665'', ''DOMESTIC'', ''1663'', ''DOMESTIC'', ''7967'', ''DOMESTIC'', ''5424'', ''DOMESTIC'', ''5665'', ''DOMESTIC'', ''5663'', ''DOMESTIC'', ' ||
																	 '''3965'', ''DOMESTIC'', ''1305'', ''DOMESTIC'', ''1661'', ''DOMESTIC'', ''1659'', ''DOMESTIC'', ''7965'', ''DOMESTIC'', ''5305'', ''DOMESTIC'', ''5661'', ''DOMESTIC'', ''5659'', ''DOMESTIC'', ' ||
																	 '''3968'', ''OVERSEAS'', ''1425'', ''OVERSEAS'', ''1666'', ''OVERSEAS'', ''1664'', ''OVERSEAS'', ''7968'', ''OVERSEAS'', ''5425'', ''OVERSEAS'', ''5666'', ''OVERSEAS'', ''5664'', ''OVERSEAS'', ' ||
																	 '''3966'', ''OVERSEAS'', ''1421'', ''OVERSEAS'', ''1662'', ''OVERSEAS'', ''1660'', ''OVERSEAS'', ''7966'', ''OVERSEAS'', ''5421'', ''OVERSEAS'', ''5662'', ''OVERSEAS'', ''5660'', ''OVERSEAS'') charge_category, ' ||
               'feetype, out_object, in_object, sum(settle_notaxfee) ' ||
         'from ur_recv_' || RPT_SETTLEMONTH || '_t ' ||
        'where product_code in (''2023999400073789'', ''2023999400073790'', ''2023999400073791'') and settle_notaxfee <> 0 ' ||
        'group by order_mode, offer_code, product_code, offer_order_id, product_order_id, charge_code, feetype, out_object, in_object';
SELECT @inter_sql;
PREPARE STMT FROM @inter_sql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


SET @RPT_SQL := 'delete from rpt_supp_srv6 where settlemonth = ''' || RPT_SETTLEMONTH || '''';
SELECT @RPT_SQL;
PREPARE STMT FROM @RPT_SQL;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
SET @inter_sql := 'insert into rpt_supp_srv6 ' ||
         'select null,' || RPT_SETTLEMONTH || ' settlemonth, a.product_order_id soid, b.customername, b.pospecname || ''_'' || b.sospecname po_soname, b.start_prov_nm start_company, b.start_prov, ' ||
                'b.start_city, b.manager_nm, b.manager_con, b.end_prov_nm end_prov_country, b.end_prov, b.end_city, b.end_distr, b.address, b.cp_nm, b.cp_con, b.bandwidth, a.feetype, ' ||
                'decode(a.charge_type, ''RENT'', a.settle_notaxfee, 0) rent_income, ' ||
                'b.start_rent_rate, ' ||
                'decode(b.start_rent_rate, 0, 0, decode(a.charge_type, ''RENT'', decode(a.in_object, b.start_prov, a.settle_notaxfee, 0), 0)) start_rent_fee, ' ||
                'b.end_rent_rate, ' ||
                'decode(b.end_rent_rate, 0, 0, decode(a.charge_type, ''RENT'', decode(a.in_object, b.end_prov, a.settle_notaxfee, 0), 0)) end_rent_fee, ' ||
								'decode(a.charge_type, ''SETUP'', a.settle_notaxfee, 0) setup_income, ' ||
                'b.start_setup_rate, ' ||
                'decode(b.start_setup_rate, 0, 0, decode(a.charge_type, ''SETUP'', decode(a.in_object, b.start_prov, a.settle_notaxfee, 0), 0)) start_setup_fee, ' ||
                'b.end_setup_rate, ' ||
                'decode(b.end_setup_rate, 0, 0, decode(a.charge_type, ''SETUP'', decode(a.in_object, b.end_prov, a.settle_notaxfee, 0), 0)) end_setup_fee, ' ||
								'decode(a.charge_type, ''SERV_RENT'', a.settle_notaxfee, 0) serv_rent_income, ' ||
                'b.start_serv_rent_rate, ' ||
                'decode(b.start_serv_rent_rate, 0, 0, decode(a.charge_type, ''SERV_RENT'', decode(a.in_object, b.start_prov, a.settle_notaxfee, 0), 0)) start_serv_rent_fee, ' ||
                'b.end_serv_rent_rate, ' ||
                'decode(b.end_serv_rent_rate, 0, 0, decode(a.charge_type, ''SERV_RENT'', decode(a.in_object, b.end_prov, a.settle_notaxfee, 0), 0)) end_serv_rent_fee, ' ||
								'decode(a.charge_type, ''SERV_ONCE'', a.settle_notaxfee, 0) serv_once_income, ' ||
                'b.start_serv_once_rate, ' ||
                'decode(b.start_serv_once_rate, 0, 0, decode(a.charge_type, ''SERV_ONCE'', decode(a.in_object, b.start_prov, a.settle_notaxfee, 0), 0)) start_serv_once_fee, ' ||
                'b.end_serv_once_rate, ' ||
                'decode(b.end_serv_once_rate, 0, 0, decode(a.charge_type, ''SERV_ONCE'', decode(a.in_object, b.end_prov, a.settle_notaxfee, 0), 0)) end_serv_once_fee ' ||
           'from rpt_inter_supp a left join stl_baseinfo_srv6 b ' ||
             'on a.product_order_id = b.soid';
SELECT @inter_sql;
PREPARE STMT FROM @inter_sql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

RPT_SQL := VER_TABLE || '表' || RPT_SETTLEMONTH || '报表中间层 -双跨专线补充报表（SRV6）数据生成结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

commit;

PROC_OUT:='Y';
        outReturn := 0;

SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
    END;
END;
//
delimiter ;