/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层 -双跨专线补充报表（互联网专线）数据生成
**/
use stludr;
DROP PROCEDURE IF EXISTS stludr.`RPT_OTHER_STL_SUPP_INTERNET`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE stludr."RPT_OTHER_STL_SUPP_INTERNET"(
    RPT_SETTLEMONTH IN VARCHAR2,
    FLAG_VERSION IN CHAR,
    PROC_OUT OUT VARCHAR2,
    szSysErr OUT VARCHAR2(1000),
    nReturn OUT NUMBER(4)
)
AS
    ----双跨专线补充报表（互联网专线）
    v_proc_name varchar2(100) := 'RPT_OTHER_STL_SUPP_INTERNET';

    STL_MONTH       VARCHAR2(6);
    STL_NUMBER      NUMBER(18);/*SQL返回数*/
    RPT_RUNSITE VARCHAR2(32); --运行定位标识
    RPT_SQL     VARCHAR2(4000); --拼SQL的字符串变量
    RPT_TABLE   VARCHAR2(64); --拼表名的字符串变量

    G_VERSION RVL_CONF_VERSION.VERSION%TYPE; --版本号变量
    G_RESNUM  RVL_CONF_VERSION.VERSIONNUM%TYPE; --版本序号
    ERRCODE   VARCHAR2(32);
    ERRMSG    VARCHAR2(2048);
    VER_TABLE VARCHAR2(64);
    inter_sql   varchar2(4096);
    outReturn  int;

BEGIN

    PROC_OUT := '';
    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        PROC_OUT := 'N';
        szSysErr := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || PROC_OUT ) AS error_msg ;
    END;

    BEGIN
        if ( length(RPT_SETTLEMONTH) < 6 )  then
            SELECT 'RPT_SETTLEMONTH length less than 6. RPT_SETTLEMONTH=[' || RPT_SETTLEMONTH || ']';
            outReturn := -1;
            ROLLBACK;
            RETURN;
        end if;

        SELECT 'RPT_SETTLEMONTH=' ||  RPT_SETTLEMONTH;


        VER_TABLE   := 'RPT_INTER_SUPP';
        RPT_RUNSITE := '0';
        RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '双跨专线补充报表（互联网专线）数据生成开始时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
        SELECT RPT_SQL;
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        --互联网专线
        RPT_RUNSITE := '2';
        update stl_baseinfo_internet set end_func_rate = 100, end_ip_rate = 100, end_setup_rate = 100, end_sla_rate = 100
         where start_prov = end_prov;

        update stl_baseinfo_internet set start_func_rate = 100 - end_func_rate, start_ip_rate = 100 - end_ip_rate,
               start_setup_rate = 100 - end_setup_rate, start_sla_rate = 100 - end_sla_rate;

        update stl_baseinfo_internet set start_func_rate = null, end_func_rate = null, start_ip_rate = null, end_ip_rate = null,
               start_setup_rate = null, end_setup_rate = null
         where start_prov = '030' or end_prov = '030';

        update stl_baseinfo_internet a set customername = (select first_name from stlusers.stl_customer c
         where customer_code = a.customernumber and RPT_SETTLEMONTH BETWEEN DATE_FORMAT(effective_date, '%Y%m') AND DATE_FORMAT(expiry_date, '%Y%m'));

        SET @RPT_SQL := 'truncate table rpt_inter_supp';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        SET @inter_sql := 'insert into rpt_inter_supp(order_mode, offer_code, product_code, offer_order_id, product_order_id, charge_type, charge_category, ' ||
           'feetype, out_object, in_object, settle_notaxfee) ' ||
        'select order_mode, offer_code, product_code, offer_order_id, product_order_id,  ' ||
               'decode(charge_code, ''02'', ''FUNC'', ''22'', ''IP'', ''08'', ''SETUP'', ''52'', ''FUNC'', ''72'', ''IP'', ''58'', ''SETUP'', ' ||
                                   '''1103'', ''FUNC'', ''1105'', ''IP'', ''1104'', ''SETUP'', ''5103'', ''FUNC'', ''5105'', ''IP'', ''5104'', ''SETUP'', ' ||
                                   '''1124'', ''FUNC'', ''1125'', ''SETUP'', ''5124'', ''FUNC'', ''5125'', ''SETUP'', ''1304'', ''IP'', ''5304'', ''IP'', ' ||
                                   '''1461'', ''FUNC'', ''1462'', ''SETUP'', ''5461'', ''FUNC'', ''5462'', ''SETUP'') charge_type, ' ||
               'decode(charge_code, ''02'', ''PROV'', ''22'', ''PROV'', ''08'', ''PROV'', ''52'', ''PROV'', ''72'', ''PROV'', ''58'', ''PROV'', ' ||
                                   '''1103'', ''PROV'', ''1105'', ''PROV'', ''1104'', ''PROV'', ''5103'', ''DOMESTIC'', ''5105'', ''DOMESTIC'', ''5104'', ''DOMESTIC'', ' ||
                                   '''1124'', ''DOMESTIC'', ''1125'', ''DOMESTIC'', ''5124'', ''DOMESTIC'', ''5125'', ''DOMESTIC'', ''1304'', ''DOMESTIC'', ''5304'', ''DOMESTIC'', ' ||
                                   '''1461'', ''OVERSEAS'', ''1462'', ''OVERSEAS'', ''5461'', ''OVERSEAS'', ''5462'', ''OVERSEAS'') charge_category, ' ||
               'feetype, out_object, in_object, sum(settle_notaxfee) ' ||
          'from ur_recv_' || RPT_SETTLEMONTH || '_t ' ||
         'where product_code in (''111208'') and settle_notaxfee <> 0 ' ||
         'group by order_mode, offer_code, product_code, offer_order_id, product_order_id, charge_code, feetype, out_object, in_object';
        SELECT @inter_sql;
        PREPARE STMT FROM @inter_sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @RPT_SQL := 'delete from rpt_supp_internet where settlemonth = ''' || RPT_SETTLEMONTH || '''';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @inter_sql := 'insert into rpt_supp_internet ' ||
         'select null,' || RPT_SETTLEMONTH || ' settlemonth, a.product_order_id soid, b.customername, b.pospecname || ''_'' || b.sospecname po_soname, b.start_prov_nm start_company, b.start_prov, ' ||
                'b.start_city, b.manager_nm, b.manager_con, b.end_prov_nm end_prov_country, b.end_prov, b.end_city, b.end_distr, b.address, b.cp_nm, b.cp_con, b.bandwidth, a.feetype, ' ||
                'decode(a.charge_type, ''FUNC'', a.settle_notaxfee, 0) func_income, ' ||
                'b.start_func_rate, ' ||
                'decode(b.start_func_rate, 0, 0, decode(a.charge_type, ''FUNC'', decode(a.charge_category, ''PROV'', decode(b.start_prov, a.in_object, a.settle_notaxfee, 0), ''DOMESTIC'', decode(a.in_object, b.start_prov, a.settle_notaxfee, 0), ''OVERSEAS'', decode(a.in_object, b.start_prov, a.settle_notaxfee, 0), 0), 0)) start_func_fee, ' ||
                'b.end_func_rate, ' ||
                'decode(b.end_func_rate, 0, 0, decode(a.charge_type, ''FUNC'', decode(a.charge_category, ''PROV'', decode(a.in_object, b.end_prov, a.settle_notaxfee, 0), ''DOMESTIC'', decode(a.in_object, b.end_prov, a.settle_notaxfee, 0), ''OVERSEAS'', decode(a.in_object, b.end_prov, a.settle_notaxfee, 0), 0), 0)) end_func_fee, ' ||
                'decode(a.charge_type, ''IP'', a.settle_notaxfee, 0) ip_income, ' ||
                'b.start_ip_rate, ' ||
                'decode(b.start_ip_rate, 0, 0, decode(a.charge_type, ''IP'', decode(a.charge_category, ''PROV'', decode(a.in_object, b.start_prov, a.settle_notaxfee, 0), ''DOMESTIC'', decode(a.in_object, b.start_prov, a.settle_notaxfee, 0), ''OVERSEAS'', decode(a.in_object, b.start_prov, a.settle_notaxfee, 0), 0), 0)) start_ip_fee, ' ||
                'b.end_ip_rate, ' ||
                'decode(b.end_ip_rate, 0, 0, decode(a.charge_type, ''IP'', decode(a.charge_category, ''PROV'', decode(a.in_object, b.end_prov, a.settle_notaxfee, 0), ''DOMESTIC'', decode(a.in_object, b.end_prov, a.settle_notaxfee, 0), ''OVERSEAS'', decode(a.in_object, b.end_prov, a.settle_notaxfee, 0), 0), 0)) end_ip_fee, ' ||
                'decode(a.charge_type, ''SETUP'', a.settle_notaxfee, 0) setup_income, ' ||
                'b.start_setup_rate, ' ||
                'decode(b.start_setup_rate, 0, 0, decode(a.charge_type, ''SETUP'', decode(a.charge_category, ''PROV'', decode(a.in_object, b.start_prov, a.settle_notaxfee, 0), ''DOMESTIC'', decode(a.in_object, b.start_prov, a.settle_notaxfee, 0), ''OVERSEAS'', decode(a.in_object, b.start_prov, a.settle_notaxfee, 0), 0), 0)) start_setup_fee, ' ||
                'b.end_setup_rate, ' ||
                'decode(b.end_setup_rate, 0, 0, decode(a.charge_type, ''SETUP'', decode(a.charge_category, ''PROV'', decode(a.in_object, b.end_prov, a.settle_notaxfee, 0), ''DOMESTIC'', decode(a.in_object, b.end_prov, a.settle_notaxfee, 0), ''OVERSEAS'', decode(a.in_object, b.end_prov, a.settle_notaxfee, 0), 0), 0)) end_setup_fee, ' ||
                '0 sla_income, ' ||
                'null start_sla_rate, ' ||
                '0 start_sla_fee, ' ||
                'null end_sla_rate, ' ||
                '0 end_sla_fee ' ||
           'from rpt_inter_supp a left join stl_baseinfo_internet b ' ||
             'on a.product_order_id = b.soid';
        SELECT @inter_sql;
        PREPARE STMT FROM @inter_sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        RPT_SQL := VER_TABLE || '表' || RPT_SETTLEMONTH || '双跨专线补充报表（互联网专线）数据生成结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
        commit;

        PROC_OUT:='Y';
        outReturn := 0;

        SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
    END;
END ;;
DELIMITER ;