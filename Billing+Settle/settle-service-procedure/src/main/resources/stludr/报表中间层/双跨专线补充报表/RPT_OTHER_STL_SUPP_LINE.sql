/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层 -双跨专线补充报表（数据专线）数据生成
**/
use stludr;
DROP PROCEDURE IF EXISTS stludr.`RPT_OTHER_STL_SUPP_LINE`;
DELIMITER ;;
create or replace DEFINER="stludr"@"10.%" PROCEDURE stludr."RPT_OTHER_STL_SUPP_LINE"(
    RPT_SETTLEMONTH IN VARCHAR2,
    FLAG_VERSION IN CHAR,
    PROC_OUT OUT VARCHAR2,
    szSysErr OUT VARCHAR2(1000),
    nReturn OUT NUMBER(4)
)
AS
    -- 双跨专线补充报表（专线）
    v_proc_name varchar2(100) := 'RPT_OTHER_STL_SUPP_LINE';
    G_VERSION RVL_CONF_VERSION.VERSION%TYPE; --版本号变量
    G_RESNUM  RVL_CONF_VERSION.VERSIONNUM%TYPE; --版本序号
    ERRCODE   VARCHAR2(32);
    ERRMSG    VARCHAR2(2048);
    VER_TABLE VARCHAR2(64);
    inter_sql   varchar2(4096);
    RPT_RUNSITE VARCHAR2(32); --运行定位标识
    RPT_SQL     VARCHAR2(4000); --拼SQL的字符串变量
    RPT_TABLE   VARCHAR2(64); --拼表名的字符串变量

    outReturn  int;

BEGIN

    PROC_OUT := '';
    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        PROC_OUT := 'N';
        szSysErr := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || PROC_OUT ) AS error_msg ;
    END;

    BEGIN
        if ( length(RPT_SETTLEMONTH) < 6 )  then
            SELECT 'RPT_SETTLEMONTH length less than 6. RPT_SETTLEMONTH=[' || RPT_SETTLEMONTH || ']';
            outReturn := -1;
            ROLLBACK;
            RETURN;
        end if;

        SELECT 'RPT_SETTLEMONTH=' ||  RPT_SETTLEMONTH;

        VER_TABLE   := 'RPT_INTER_SUPP';
        RPT_RUNSITE := '0';
        RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表开始时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
        SELECT RPT_SQL;
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        --专线类
        RPT_RUNSITE := '3';
        update stl_baseinfo_line set start_func_rate = 100 - a_func_rate - z_func_rate,
                                     start_setup_rate = 100 - a_setup_rate - z_setup_rate;

        update stl_baseinfo_line set a_func_rate = a_func_rate + start_func_rate, a_setup_rate = a_setup_rate + start_setup_rate,
               start_func_rate = 0, start_setup_rate = 0
         where start_prov = a_prov;

        update stl_baseinfo_line set z_func_rate = z_func_rate + start_func_rate, z_setup_rate = z_setup_rate + start_setup_rate,
               start_func_rate = 0, start_setup_rate = 0
         where start_prov = z_prov;

        update stl_baseinfo_line set a_func_rate = z_func_rate + a_func_rate, a_setup_rate = z_setup_rate + a_setup_rate,
               z_func_rate = 0, z_setup_rate = 0
         where a_prov = z_prov;

        update stl_baseinfo_line set start_func_rate = null, start_setup_rate = null
         where start_prov = '030' or a_prov = '030' or z_prov = '030';
		RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || 'stl_baseinfo_line：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        update stl_baseinfo_line a set customername = (select DISTINCT first_name from stlusers.stl_customer c
         where customer_code = a.customernumber and  RPT_SETTLEMONTH BETWEEN DATE_FORMAT(effective_date, '%Y%m') AND DATE_FORMAT(expiry_date, '%Y%m'));


        SET @RPT_SQL := 'truncate table rpt_inter_supp';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        SET @inter_sql := 'insert into rpt_inter_supp(order_mode, offer_code, product_code, offer_order_id, product_order_id, charge_type, charge_category, ' ||
           'feetype, out_object, in_object, settle_notaxfee) ' ||
        'select order_mode, offer_code, product_code, offer_order_id, product_order_id,  ' ||
               'decode(charge_code, ''02'', ''FUNC'', ''08'', ''SETUP'', ''52'', ''FUNC'', ''58'', ''SETUP'', ' ||
                                   '''1113'', ''FUNC'', ''1115'', ''SETUP'', ''5113'', ''FUNC'', ''5115'', ''SETUP'', ' ||
                                   '''1124'', ''FUNC'', ''1125'', ''SETUP'', ''5124'', ''FUNC'', ''5125'', ''SETUP'', ' ||
                                   '''1456'', ''FUNC'', ''1458'', ''SETUP'', ''5456'', ''FUNC'', ''5458'', ''SETUP'', ' ||
                                   '''1461'', ''FUNC'', ''1462'', ''SETUP'', ''5461'', ''FUNC'', ''5462'', ''SETUP'', ' ||
                                   '''1114'', ''FUNC'', ''1116'', ''SETUP'', ''5114'', ''FUNC'', ''5116'', ''SETUP'', ' ||
                                   '''1457'', ''FUNC'', ''1459'', ''SETUP'', ''5457'', ''FUNC'', ''5459'', ''SETUP'') charge_type, ' ||
               'decode(charge_code, ''02'', ''PROV'', ''08'', ''PROV'', ''52'', ''PROV'', ''58'', ''PROV'', ' ||
                                   '''1113'', ''DOMESTIC'', ''1115'', ''DOMESTIC'', ''5113'', ''DOMESTIC'', ''5115'', ''DOMESTIC'', ' ||
                                   '''1124'', ''DOMESTIC'', ''1125'', ''DOMESTIC'', ''5124'', ''DOMESTIC'', ''5125'', ''DOMESTIC'', ' ||
                                   '''1456'', ''DOMESTIC'', ''1458'', ''DOMESTIC'', ''5456'', ''DOMESTIC'', ''5458'', ''DOMESTIC'', ' ||
                                   '''1461'', ''OVERSEAS'', ''1462'', ''OVERSEAS'', ''5461'', ''OVERSEAS'', ''5462'', ''OVERSEAS'', ' ||
                                   '''1114'', ''OVERSEAS'', ''1116'', ''OVERSEAS'', ''5114'', ''OVERSEAS'', ''5116'', ''OVERSEAS'', ' ||
                                   '''1457'', ''OVERSEAS'', ''1459'', ''OVERSEAS'', ''5457'', ''OVERSEAS'', ''5459'', ''OVERSEAS'') charge_category, ' ||
              'feetype, out_object, in_object, sum(settle_notaxfee) ' ||
         'from ur_recv_' || RPT_SETTLEMONTH || '_t ' ||
        'where product_code in (''111241'', ''111207'', ''111206'', ''5001401'') and settle_notaxfee <> 0 ' ||
        'group by order_mode, offer_code, product_code, offer_order_id, product_order_id, charge_code, feetype, out_object, in_object';

        SELECT @inter_sql;
        PREPARE STMT FROM @inter_sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @RPT_SQL := 'delete from rpt_supp_line where settlemonth = ''' || RPT_SETTLEMONTH || '''';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        SET @inter_sql := 'insert into rpt_supp_line ' ||
         'select null,' || RPT_SETTLEMONTH || ' settlemonth, a.product_order_id soid, b.customername, b.sospecname soname, b.start_prov_nm start_company, b.start_prov, ' ||
                'b.start_city, b.manager_nm, b.manager_con, b.a_prov_nm a_prov_country, b.a_prov, b.a_city, b.a_distr, b.a_address, b.a_cp_nm, b.a_cp_con, b.z_prov_nm z_prov_country, b.z_prov, b.z_city, b.z_distr, b.z_address, b.z_cp_nm, b.z_cp_con,b.bandwidth, a.feetype, ' ||
                'decode(a.charge_type, ''FUNC'', a.settle_notaxfee, 0) func_income, ' ||
                'b.start_func_rate, ' ||
                'decode(b.start_func_rate, 0, 0, decode(a.charge_type, ''FUNC'', decode(a.charge_category, ''PROV'', decode(b.start_prov, a.in_object, a.settle_notaxfee, 0), ''DOMESTIC'', decode(a.in_object, b.start_prov, a.settle_notaxfee, 0), ''OVERSEAS'', decode(a.in_object, b.start_prov, a.settle_notaxfee, 0), 0), 0)) start_func_fee, ' ||
                'b.a_func_rate, ' ||
                'decode(b.a_func_rate, 0, 0, decode(a.charge_type, ''FUNC'', decode(a.charge_category, ''PROV'', decode(a.in_object, b.a_prov, a.settle_notaxfee, 0), ''DOMESTIC'', decode(a.in_object, b.a_prov, a.settle_notaxfee, 0), ''OVERSEAS'', decode(a.in_object, b.a_prov, a.settle_notaxfee, 0), 0), 0)) a_func_fee, ' ||
                'b.z_func_rate, ' ||
                'decode(b.z_func_rate, 0, 0, decode(a.charge_type, ''FUNC'', decode(a.charge_category, ''PROV'', decode(a.in_object, b.z_prov, a.settle_notaxfee, 0), ''DOMESTIC'', decode(a.in_object, b.z_prov, a.settle_notaxfee, 0), ''OVERSEAS'', decode(a.in_object, b.z_prov, a.settle_notaxfee, 0), 0), 0)) z_func_fee, ' ||
                'decode(a.charge_type, ''SETUP'', a.settle_notaxfee, 0) setup_income, ' ||
                'b.start_setup_rate, ' ||
                'decode(b.start_setup_rate, 0, 0, decode(a.charge_type, ''SETUP'', decode(a.charge_category, ''PROV'', decode(a.in_object, b.start_prov, a.settle_notaxfee, 0), ''DOMESTIC'', decode(a.in_object, b.start_prov, a.settle_notaxfee, 0), ''OVERSEAS'', decode(a.in_object, b.start_prov, a.settle_notaxfee, 0), 0), 0)) start_setup_fee, ' ||
                'b.a_setup_rate, ' ||
                'decode(b.a_setup_rate, 0, 0, decode(a.charge_type, ''SETUP'', decode(a.charge_category, ''PROV'', decode(a.in_object, b.a_prov, a.settle_notaxfee, 0), ''DOMESTIC'', decode(a.in_object, b.a_prov, a.settle_notaxfee, 0), ''OVERSEAS'', decode(a.in_object, b.a_prov, a.settle_notaxfee, 0), 0), 0)) a_setup_fee, ' ||
                'b.z_setup_rate, ' ||
                'decode(b.z_setup_rate, 0, 0, decode(a.charge_type, ''SETUP'', decode(a.charge_category, ''PROV'', decode(a.in_object, b.z_prov, a.settle_notaxfee, 0), ''DOMESTIC'', decode(a.in_object, b.z_prov, a.settle_notaxfee, 0), ''OVERSEAS'', decode(a.in_object, b.z_prov, a.settle_notaxfee, 0), 0), 0)) z_setup_fee ' ||
           'from rpt_inter_supp a left join stl_baseinfo_line b ' ||
             'on a.product_order_id = b.soid';
        SELECT @inter_sql;
        PREPARE STMT FROM @inter_sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        RPT_SQL := VER_TABLE || '表' || RPT_SETTLEMONTH || '报表中间层 -双跨专线补充报表（数据专线）数据生成结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        commit;

        PROC_OUT:='Y';
        outReturn := 0;

        SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
    END;
END;;
DELIMITER ;