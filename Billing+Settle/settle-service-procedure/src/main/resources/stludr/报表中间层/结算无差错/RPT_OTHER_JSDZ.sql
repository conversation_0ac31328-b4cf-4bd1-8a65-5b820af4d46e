/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层 -结算无差错-月对账告警文件数据生成
**/
DROP PROCEDURE IF EXISTS stludr.`RPT_OTHER_JSDZ`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "RPT_OTHER_JSDZ"(
    RPT_SETTLEMONTH IN VARCHAR2,
    FLAG_VERSION IN CHAR,
    PROC_OUT OUT VARCHAR2,
    szSysErr OUT VARCHAR2(1000),
    nReturn OUT NUMBER(4)
)
AS

    v_proc_name       VARCHAR2(30) := 'RPT_OTHER_JSDZ';
    iv_Sql_Insert    VARCHAR2(3072);
    iv_Sql_Insert1   VARCHAR2(1024);
    iv_Sql_Insert2   VARCHAR2(2048);
    iv_Sql_Insert3   VARCHAR2(2048);
    iv_Sql_Update    VARCHAR2(1024);
    vSql             VARCHAR2(3072);
    RPT_SQL          VARCHAR2(3072);
    outReturn        NUMBER;

    G_VERSION RVL_CONF_VERSION.VERSION%TYPE; --版本号变量
    G_RESNUM  RVL_CONF_VERSION.VERSIONNUM%TYPE; --版本序号
    ERRCODE   VARCHAR2(32);
    ERRMSG    VARCHAR2(2048);
    VER_TABLE VARCHAR2(64);
    RPT_RUNSITE VARCHAR2(64);
    inter_sql   varchar2(2048);
BEGIN

    PROC_OUT := '';
    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        PROC_OUT := 'N';
        szSysErr := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || PROC_OUT ) AS error_msg ;
    END;

    BEGIN
        if ( length(RPT_SETTLEMONTH) < 6 )  then
            SELECT 'inMonth length less than 6. RPT_SETTLEMONTH=[' || RPT_SETTLEMONTH || ']' ;
            PROC_OUT := 'N';
            outReturn := -1;
            ROLLBACK;
            RETURN;
        end if;
        VER_TABLE   := 'JSDZ_JSJKYJ';
    RPT_RUNSITE := '0';
    RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表开始时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
        SELECT 'RPT_SETTLEMONTH=' ||  RPT_SETTLEMONTH;

        SET @vSql := 'delete from JSDZ_JSJKYJ where settlemonth = ' || RPT_SETTLEMONTH ;
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
RPT_SQL :='清空数据';
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        ----M02001 车务通 入
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02001'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
          (select ''240'' object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_cpp_' || RPT_SETTLEMONTH || '_t t
           where t.product_code in (''110901'', ''110903'') AND T.INDEX_ID = 1 AND T.DEST_SOURCE = ''3'') data, stl_province_cd prov
           where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
RPT_SQL :='M02001 车务通 入';
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
        ----M02002 车务通 出
        set @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02002'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
          (select out_object object, trim(to_char(sum(settle_notaxfee/ NVL((
	    		SELECT
	    			T1.PRODUCT_DIGIT
	    		FROM
	    			STLUDR.STL_CONFIG_DIGIT T1
	    		WHERE
	    			T1.POSPEC_NUMBER = t.OFFER_CODE
	    			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_cpp_' || RPT_SETTLEMONTH || '_t t
         where t.product_code in (''110901'', ''110903'') AND T.INDEX_ID = 1 AND T.DEST_SOURCE = ''3''
         group by out_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
RPT_SQL :='M02002 车务通 出';
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
        ----M02003 3-0% 应入
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02003'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
        (select in_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_recv_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode in (3, 4) and T.DEST_SOURCE = 0 or t.order_mode = 5 and t.dest_source = 1)
           and t.tax_rate = 0 group by in_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
RPT_SQL :='M02003 3-0% 应入';
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
        ----M02004 3-6% 应入
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02004'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
        (select in_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_recv_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode in (3, 4) and T.DEST_SOURCE = 0 or t.order_mode = 5 and t.dest_source = 1)
           and t.tax_rate = 6 group by in_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
RPT_SQL :='M02004 3-6% 应入';
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
        ----M02005 3-9% 应入
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02005'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
        (select in_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_recv_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode in (3, 4) and T.DEST_SOURCE = 0 or t.order_mode = 5 and t.dest_source = 1)
           and t.tax_rate = 9 group by in_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02006 3-13% 应入
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02006'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
        (select in_object object, trim(to_char(sum(settle_notaxfee/ NVL((
			SELECT
				T1.PRODUCT_DIGIT
			FROM
				STLUDR.STL_CONFIG_DIGIT T1
			WHERE
				T1.POSPEC_NUMBER = t.OFFER_CODE
				AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_recv_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode in (3, 4) and T.DEST_SOURCE = 0 or t.order_mode = 5 and t.dest_source = 1)
           and t.tax_rate = 13 group by in_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        ----M02007 3-0% 应出
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02007'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
        (select out_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_recv_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode in (3, 4) and T.DEST_SOURCE = 0 or t.order_mode = 5 and t.dest_source = 1)
           and t.tax_rate = 0 group by out_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02008 3-6% 应出
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02008'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
        (select out_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_recv_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode in (3, 4) and T.DEST_SOURCE = 0 or t.order_mode = 5 and t.dest_source = 1)
           and t.tax_rate = 6 group by out_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02009 3-9% 应出
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02009'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
        (select out_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_recv_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode in (3, 4) and T.DEST_SOURCE = 0 or t.order_mode = 5 and t.dest_source = 1)
           and t.tax_rate = 9 group by out_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02010 3-13% 应出
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02010'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
        (select out_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_recv_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode in (3, 4) and T.DEST_SOURCE = 0 or t.order_mode = 5 and t.dest_source = 1)
           and t.tax_rate = 13 group by out_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02011 3-0% 实入
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02011'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
        (select in_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode in (3, 4) and T.DEST_SOURCE = 0 or t.order_mode = 5 and t.dest_source = 1)
           and t.tax_rate = 0 group by in_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02012 3-6% 实入
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02012'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
        (select in_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode in (3, 4) and T.DEST_SOURCE = 0 or t.order_mode = 5 and t.dest_source = 1)
           and t.tax_rate = 6 group by in_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02013 3-9% 实入
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02013'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
        (select in_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode in (3, 4) and T.DEST_SOURCE = 0 or t.order_mode = 5 and t.dest_source = 1)
           and t.tax_rate = 9 group by in_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02014 3-10% 实入
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02014'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
        (select in_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode in (3, 4) and T.DEST_SOURCE = 0 or t.order_mode = 5 and t.dest_source = 1)
           and t.tax_rate = 10 group by in_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02015 3-11% 实入
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02015'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
        (select in_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode in (3, 4) and T.DEST_SOURCE = 0 or t.order_mode = 5 and t.dest_source = 1)
           and t.tax_rate = 11 group by in_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02016 3-13% 实入
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02016'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
        (select in_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        			SELECT
        				T1.PRODUCT_DIGIT
        			FROM
        				STLUDR.STL_CONFIG_DIGIT T1
        			WHERE
        				T1.POSPEC_NUMBER = t.OFFER_CODE
        				AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
        where (t.order_mode in (3, 4) and T.DEST_SOURCE = 0 or t.order_mode = 5 and t.dest_source = 1)
          and t.tax_rate = 13 group by in_object) data, stl_province_cd prov
        where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ---M02017 3-16% 实入
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02017'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select in_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        			SELECT
        				T1.PRODUCT_DIGIT
        			FROM
        				STLUDR.STL_CONFIG_DIGIT T1
        			WHERE
        				T1.POSPEC_NUMBER = t.OFFER_CODE
        				AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
        where (t.order_mode in (3, 4) and T.DEST_SOURCE = 0 or t.order_mode = 5 and t.dest_source = 1)
          and t.tax_rate = 16 group by in_object) data, stl_province_cd prov
        where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ---M02018 3-17% 实入
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02018'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select in_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        			SELECT
        				T1.PRODUCT_DIGIT
        			FROM
        				STLUDR.STL_CONFIG_DIGIT T1
        			WHERE
        				T1.POSPEC_NUMBER = t.OFFER_CODE
        				AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
        where (t.order_mode in (3, 4) and T.DEST_SOURCE = 0 or t.order_mode = 5 and t.dest_source = 1)
          and t.tax_rate = 17 group by in_object) data, stl_province_cd prov
        where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ---M02019 3-0% 实出
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02019'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select out_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        			SELECT
        				T1.PRODUCT_DIGIT
        			FROM
        				STLUDR.STL_CONFIG_DIGIT T1
        			WHERE
        				T1.POSPEC_NUMBER = t.OFFER_CODE
        				AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
        where (t.order_mode in (3, 4) and T.DEST_SOURCE = 0 or t.order_mode = 5 and t.dest_source = 1)
          and t.tax_rate = 0 group by out_object) data, stl_province_cd prov
        where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ---M02020 3-6% 实出
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02020'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select out_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        			SELECT
        				T1.PRODUCT_DIGIT
        			FROM
        				STLUDR.STL_CONFIG_DIGIT T1
        			WHERE
        				T1.POSPEC_NUMBER = t.OFFER_CODE
        				AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
        where (t.order_mode in (3, 4) and T.DEST_SOURCE = 0 or t.order_mode = 5 and t.dest_source = 1)
          and t.tax_rate = 6 group by out_object) data, stl_province_cd prov
        where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ---M02021 3-9% 实出
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02021'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select out_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        			SELECT
        				T1.PRODUCT_DIGIT
        			FROM
        				STLUDR.STL_CONFIG_DIGIT T1
        			WHERE
        				T1.POSPEC_NUMBER = t.OFFER_CODE
        				AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
        where (t.order_mode in (3, 4) and T.DEST_SOURCE = 0 or t.order_mode = 5 and t.dest_source = 1)
          and t.tax_rate = 9 group by out_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02022 3-10% 实出
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02022'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select out_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode in (3, 4) and T.DEST_SOURCE = 0 or t.order_mode = 5 and t.dest_source = 1)
           and t.tax_rate = 10 group by out_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02023 3-11% 实出
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02023'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select out_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode in (3, 4) and T.DEST_SOURCE = 0 or t.order_mode = 5 and t.dest_source = 1)
           and t.tax_rate = 11 group by out_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02024 3-13% 实出
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02024'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select out_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode in (3, 4) and T.DEST_SOURCE = 0 or t.order_mode = 5 and t.dest_source = 1)
           and t.tax_rate = 13 group by out_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02025 3-16% 实出
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02025'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select out_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode in (3, 4) and T.DEST_SOURCE = 0 or t.order_mode = 5 and t.dest_source = 1)
           and t.tax_rate = 16 group by out_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02026 3-17% 实出
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02026'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select out_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode in (3, 4) and T.DEST_SOURCE = 0 or t.order_mode = 5 and t.dest_source = 1)
           and t.tax_rate = 17 group by out_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02027 1-0% 应入
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02027'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select in_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_recv_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode = 1 and T.DEST_SOURCE = 0)
           and t.tax_rate = 0 group by in_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
RPT_SQL :='M02027 1-0% 应入';
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
        ----M02028 1-6% 应入
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02028'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select in_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_recv_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode = 1 and T.DEST_SOURCE = 0)
           and t.tax_rate = 6 group by in_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02029 1-9% 应入
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02029'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select in_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_recv_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode = 1 and T.DEST_SOURCE = 0)
           and t.tax_rate = 9 group by in_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02030 1-13% 应入
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02030'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select in_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_recv_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode = 1 and T.DEST_SOURCE = 0)
           and t.tax_rate = 13 group by in_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02031 1-0% 应出
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02031'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select out_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_recv_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode = 1 and T.DEST_SOURCE = 0)
           and t.tax_rate = 0 group by out_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02032 1-6% 应出
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02032'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select out_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_recv_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode = 1 and T.DEST_SOURCE = 0)
           and t.tax_rate = 6 group by out_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
RPT_SQL :='M02032 1-6% 应出';
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
        ----M02033 1-9% 应出
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02033'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select out_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_recv_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode = 1 and T.DEST_SOURCE = 0)
           and t.tax_rate = 9 group by out_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02034 1-13% 应出
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02034'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select out_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_recv_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode = 1 and T.DEST_SOURCE = 0)
           and t.tax_rate = 13 group by out_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02035 1-0% 实入
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02035'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select in_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode = 1 and T.DEST_SOURCE = 0)
           and t.tax_rate = 0 group by in_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02036 1-6% 实入
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02036'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select in_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode = 1 and T.DEST_SOURCE = 0)
           and t.tax_rate = 6 group by in_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02037 1-9% 实入
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02037'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select in_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode = 1 and T.DEST_SOURCE = 0)
           and t.tax_rate = 9 group by in_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02038 1-10% 实入
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02038'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select in_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode = 1 and T.DEST_SOURCE = 0)
           and t.tax_rate = 10 group by in_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02039 1-11% 实入
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02039'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select in_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode = 1 and T.DEST_SOURCE = 0)
           and t.tax_rate = 11 group by in_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02040 1-13% 实入
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02040'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select in_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode = 1 and T.DEST_SOURCE = 0)
           and t.tax_rate = 13 group by in_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
RPT_SQL :='M02040 1-13% 实入';
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
        ----M02041 1-16% 实入
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02041'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select in_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode = 1 and T.DEST_SOURCE = 0)
           and t.tax_rate = 16 group by in_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02042 1-17% 实入
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02042'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select in_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode = 1 and T.DEST_SOURCE = 0)
           and t.tax_rate = 17 group by in_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02043 1-0% 实出
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02043'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select out_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode = 1 and T.DEST_SOURCE = 0)
           and t.tax_rate = 0 group by out_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02044 1-6% 实出
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02044'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select out_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode = 1 and T.DEST_SOURCE = 0)
           and t.tax_rate = 6 group by out_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02045 1-9% 实出
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02045'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select out_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode = 1 and T.DEST_SOURCE = 0)
           and t.tax_rate = 9 group by out_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02046 1-10% 实出
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02046'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select out_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode = 1 and T.DEST_SOURCE = 0)
           and t.tax_rate = 10 group by out_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02047 1-11% 实出
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02047'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select out_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode = 1 and T.DEST_SOURCE = 0)
           and t.tax_rate = 11 group by out_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02048 1-13% 实出
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02048'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select out_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode = 1 and T.DEST_SOURCE = 0)
           and t.tax_rate = 13 group by out_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02049 1-16% 实出
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02049'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select out_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode = 1 and T.DEST_SOURCE = 0)
           and t.tax_rate = 16 group by out_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----M02050 1-17% 实出
        SET @RPT_SQL := 'insert into JSDZ_JSJKYJ select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''M02050'' idx_code, prov.prov_cd, nvl(data.idx_value, ''0.00'') from
         ( select out_object object, trim(to_char(sum(settle_notaxfee/ NVL((
        		SELECT
        			T1.PRODUCT_DIGIT
        		FROM
        			STLUDR.STL_CONFIG_DIGIT T1
        		WHERE
        			T1.POSPEC_NUMBER = t.OFFER_CODE
        			AND T1.PRODUCT_NUMBER = t.PRODUCT_CODE ),1)) / 100, ''9999999999990.99'')) idx_value From ur_paid_' || RPT_SETTLEMONTH || '_t t
         where (t.order_mode = 1 and T.DEST_SOURCE = 0)
           and t.tax_rate = 17 group by out_object) data, stl_province_cd prov
         where data.object(+) = prov.prov_cd and prov.prov_type in (0, 1)';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        commit;
RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
        PROC_OUT := 'Y';
        outReturn := 0;

        SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
    END;
END ;;
DELIMITER ;