DELIMITER ;;
CREATE or replace DEFINER="stludr"@"10.%" PROCEDURE "RPT_OTHER_STL_P2C_NMG"(
    RPT_SETTLEMONTH IN VARCHAR2,
    FLAG_VERSION IN CHAR,
    PROC_OUT OUT VARCHAR2,
    szSysErr OUT VARCHAR2(1000),
    nReturn OUT NUMBER(4)
)
AS
    v_proc_name       VARCHAR2(30) := 'RPT_OTHER_STL_P2C_NMG';
    G_VERSION RVL_CONF_VERSION.VERSION%TYPE; --版本号变量
    G_RESNUM  RVL_CONF_VERSION.VERSIONNUM%TYPE; --版本序号
    ERRCODE   VARCHAR2(32);
    ERRMSG    VARCHAR2(2048);
    VER_TABLE VARCHAR2(64);

    RPT_RUNSITE VARCHAR2(32); --运行定位标识
    RPT_SQL     VARCHAR2(4000); --拼SQL的字符串变量
    RPT_TABLE   VARCHAR2(64); --拼表名的字符串变量
    outReturn  int;

BEGIN

    PROC_OUT := '';
    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        PROC_OUT := 'N';
        szSysErr := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || PROC_OUT ) AS error_msg ;
    END;

    BEGIN
        if ( length(RPT_SETTLEMONTH) < 6 )  then
            SELECT 'RPT_SETTLEMONTH length less than 6. RPT_SETTLEMONTH=[' || RPT_SETTLEMONTH || ']';
            PROC_OUT := 'N';
            outReturn := -1;
            ROLLBACK;
            RETURN;
        end if;

        SELECT 'RPT_SETTLEMONTH=' ||  RPT_SETTLEMONTH;

        VER_TABLE   := 'rpt_p2c_nmg';
        RPT_RUNSITE := '0';
        RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表开始时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);


        RPT_RUNSITE := '1';
        SET @RPT_SQL := 'delete from rpt_p2c_nmg where settlemonth = ''' || RPT_SETTLEMONTH || '''';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);



        RPT_RUNSITE := '2';
        SET @RPT_SQL := 'insert into rpt_p2c_nmg(settlemonth,settle_out_type, outprov_code, inprov_code,taxrate, settlement_amount, fee_flag, RATE_PLAN_GH_CODE) ' ||
                        'select ''' || RPT_SETTLEMONTH || ''',b.settlement_party_out_type, b.settlement_party_out, ' ||
                        'b.settlement_party_in, a.tax_rate, sum(b.settlement_amount), a.fee_flag,a.RATE_PLAN_GH_CODE ' ||
                        'from sync_interface_mc_' || RPT_SETTLEMONTH || ' a, sync_interface_mc_p2c_nmg_' || RPT_SETTLEMONTH || ' b ' ||
                        'where a.id = b.parent_id and a.file_name = b.file_name ' ||
                        'and a.status = 0 and b.status = 0 ' ||
                        'group by b.settlement_party_out_type,b.settlement_party_out, b.settlement_party_in, a.tax_rate, a.fee_flag, a.RATE_PLAN_GH_CODE ' ;

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, @RPT_SQL);


        --         RPT_RUNSITE := '3';
--         RPT_SQL     := '更新表中省公司的中文名称和往来段... ...';
--         UPDATE rpt_p2c_nmg T
--            SET T.OUTPROV_NAME = (SELECT T1.PROV_NM FROM STL_PROVINCE_CD T1 WHERE T1.PROV_CD = T.OUTPROV_CODE),
--                T.INPROV_NAME = (SELECT T2.PROV_NM FROM STL_PROVINCE_CD T2 WHERE T2.PROV_CD = T.INPROV_CODE),
--                T.PROV_WL = (SELECT T3.PROV_WL FROM STL_PROVINCE_CD T3 WHERE T3.PROV_CD = T.OUTPROV_CODE)
--          WHERE T.SETTLEMONTH = RPT_SETTLEMONTH;
--
--         call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
--
--         RPT_RUNSITE := '3';
--         RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表结束时间：' ||TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
--         call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);


        commit;

        PROC_OUT:='Y';
        outReturn := 0;

        SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
    END;
END ;;
DELIMITER ;