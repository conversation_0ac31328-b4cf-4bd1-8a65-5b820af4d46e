/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层 -省专报表数据生成
**/
use stludr;
DELIMITER ;;
CREATE or replace DEFINER="stludr"@"10.%" PROCEDURE stludr."RPT_OTHER_STL_P2C"(
    RPT_SETTLEMONTH IN VARCHAR2,
    FLAG_VERSION IN CHAR,
    PROC_OUT OUT VARCHAR2,
    szSysErr OUT VARCHAR2(1000),
    nReturn OUT NUMBER(4)
)
AS
    -- 省云结算报表中间表
    v_proc_name       VARCHAR2(30) := 'RPT_OTHER_STL_P2C';
     G_VERSION RVL_CONF_VERSION.VERSION%TYPE; --版本号变量
    G_RESNUM  RVL_CONF_VERSION.VERSIONNUM%TYPE; --版本序号
    ERRCODE   VARCHAR2(32);
    ERRMSG    VARCHAR2(2048);
    VER_TABLE VARCHAR2(64);

    RPT_RUNSITE VARCHAR2(32); --运行定位标识
    RPT_SQL     VARCHAR2(4000); --拼SQL的字符串变量
    RPT_TABLE   VARCHAR2(64); --拼表名的字符串变量
    outReturn  int;

BEGIN

    PROC_OUT := '';
    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
PROC_OUT := 'N';
        szSysErr := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || PROC_OUT ) AS error_msg ;
    END;

    BEGIN
        if ( length(RPT_SETTLEMONTH) < 6 )  then
            SELECT 'RPT_SETTLEMONTH length less than 6. RPT_SETTLEMONTH=[' || RPT_SETTLEMONTH || ']';
PROC_OUT := 'N';
            outReturn := -1;
            ROLLBACK;
            RETURN;
        end if;

        SELECT 'RPT_SETTLEMONTH=' ||  RPT_SETTLEMONTH;

        VER_TABLE   := 'RPT_P2C';
        RPT_RUNSITE := '0';
        RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表开始时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        --中间表获取版本号
        --SETTLE_REPORT_VERSION(RPT_SETTLEMONTH, VER_TABLE, G_VERSION, G_RESNUM, FLAG_VERSION);
        RPT_RUNSITE := '1';
        SET @RPT_SQL := 'delete from rpt_p2c where settlemonth = ''' || RPT_SETTLEMONTH || '''';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

                --从二次结算结果表获取数据
        /*SET @RPT_SQL     := 'insert into rpt_p2c(settlemonth, version, offer_code, offer_name, product_code, product_name, product_class, settlement_class, outprov_code, inprov_code, ' ||
                               'taxrate, settlement_amount, fee_flag, sign_entity, order_mode, charge_code) ' ||
                        'select ''' || RPT_SETTLEMONTH || ''',''' || G_VERSION || ''', a.offer_code,null,a.product_code,null, null, ''2'', a.out_object, ' ||
                               'a.in_object, to_char(round(a.tax_rate / 100, 2)), sum(a.settle_notaxfee * 10), decode(a.feetype, ''0'', ''1'', ''1'', ''2''), a.sign_entity, a.order_mode, null ' ||
                          'from ur_eboss_' || RPT_SETTLEMONTH || '_t a ' ||
                        'where a.dest_source = ''5'' ' ||
                           'and (a.offer_code not in(''50055'',''50090'', ''50099'') and (a.offer_code<>''50034'' or a.in_object <> ''HLW'')) '||
                         'group by a.offer_code, a.product_code, a.out_object, a.in_object, a.tax_rate, ' ||
                               'a.feetype, a.sign_entity, a.order_mode ' ||
                         'union all ' ||
            'select ''' || RPT_SETTLEMONTH || ''',''' || G_VERSION || ''', a.offer_code,null,a.product_code,null,decode(substr(b.po_name, 1, 3), ''和对讲'', ''1'', ''云视讯'', ''2'', ''千里眼'', ''3'',''和商务'',''4'') product_class, ''1'' settlement_class,a.out_object, '||
              'a.in_object,to_char(round(a.tax_rate / 100, 2)),sum(a.settle_notaxfee * 10),decode(a.feetype, ''0'', ''1'', ''1'', ''2''),a.sign_entity,a.order_mode, null  '||
              'from ur_eboss_'|| RPT_SETTLEMONTH || '_t a, rvl_dict_config b, stl_province_cd c  '||
              'where a.offer_code=''50055'' and a.dest_source=''5''  '||
              'and a.offer_code=b.offer_code and a.product_code=b.product_code  '||
              'and a.out_object=c.prov_cd and a.charge_code=b.fee_type   '||
              'group by a.offer_code,a.product_code,b.po_name,a.out_object,a.in_object,a.tax_rate, a.feetype,a.sign_entity,a.order_mode   '||
                'union all ' ||
             'select ''' || RPT_SETTLEMONTH || ''',''' || G_VERSION || ''', a.offer_code,null,a.product_code,null, null, ''2'', a.out_object, ' ||
               'a.in_object, to_char(round(a.tax_rate / 100, 2)), sum(a.settle_notaxfee * 10), decode(a.feetype, ''0'', ''1'', ''1'', ''2''), a.sign_entity, a.order_mode, a.charge_code ' ||
               'from ur_eboss_' || RPT_SETTLEMONTH || '_t a ' ||
               'where a.dest_source = ''5'' ' ||
               'and a.offer_code =''50034'' ' ||
               'and a.in_object = ''HLW''  '||
               'group by a.offer_code, a.product_code, a.out_object, a.in_object, a.tax_rate, a.feetype, a.sign_entity, a.order_mode, a.charge_code ';*/
        SET @RPT_SQL     :=   'insert into rpt_p2c(settlemonth, version, offer_code, offer_name, product_code, product_name, product_class, settlement_class, outprov_code, inprov_code, ' ||
                           'taxrate, settlement_amount, fee_flag, sign_entity, order_mode, charge_code, rateplan_id,partner_number,partner_rule_type,adj_month) ' ||
                    'select ''' || RPT_SETTLEMONTH || ''',''' || G_VERSION || ''', a.offer_code,'''',decode(nvl(a.product_order_id, 0), 0, '''', a.product_code),'''', '''', ''2'', a.out_object, ' ||
                           'a.in_object, to_char(round(a.tax_rate / 100, 2)), sum(a.settle_notaxfee * 10), decode(a.feetype, ''0'', ''1'', ''1'', ''2''), a.sign_entity, a.order_mode, a.charge_code, ' ||
                            'decode(length(a.duration), 4, a.duration, '''') rateplan_id,'''','''',adjmonth ' ||
                      'from ur_eboss_' || RPT_SETTLEMONTH || '_t a ' ||
                    'where a.dest_source = ''5'' ' ||
                       'and a.offer_code not in(''50055'',''50090'', ''50099'') '||
                     'group by a.offer_code, a.product_code, a.product_order_id, a.out_object, a.in_object, a.tax_rate, ' ||
                     'a.feetype, a.sign_entity, a.order_mode, a.charge_code, decode(length(a.duration), 4, a.duration, ''''),a.adjmonth ' ||
                     'union all ' ||
        'select ''' || RPT_SETTLEMONTH || ''',''' || G_VERSION || ''', a.offer_code,'''',a.product_code,'''',decode(substr(b.po_name, 1, 3), ''和对讲'', ''1'', ''云视讯'', ''2'', ''千里眼'', ''3'',''和商务'',''4'') product_class, ''1'' settlement_class,a.out_object, '||
          'a.in_object,to_char(round(a.tax_rate / 100, 2)),sum(a.settle_notaxfee * 10),decode(a.feetype, ''0'', ''1'', ''1'', ''2''),a.sign_entity,a.order_mode, '''', '''', '''', '''',a.adjmonth  '||
          'from ur_eboss_'|| RPT_SETTLEMONTH || '_t a, rvl_dict_config b, stl_province_cd c  '||
          'where a.offer_code=''50055'' and a.dest_source=''5''  '||
          'and a.offer_code=b.offer_code and a.product_code=b.product_code  '||
          'and a.out_object=c.prov_cd and a.charge_code=b.fee_type   '||
          'group by a.offer_code,a.product_code,b.po_name,a.out_object,a.in_object,a.tax_rate, a.feetype,a.sign_entity,a.order_mode,a.adjmonth   ';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        RPT_RUNSITE := '3';
        RPT_SQL     := '更新表中产品和商品的中文名称... ...';
        UPDATE RPT_P2C T
           SET T.OFFER_NAME = (SELECT DISTINCT NVL(T1.NAME, '') FROM STL_PRODUCT T1 WHERE T1.PRODUCT_CODE = T.OFFER_CODE AND T1.ACCT_MONTH = RPT_SETTLEMONTH),
               T.PRODUCT_NAME = (SELECT DISTINCT NVL(T1.NAME, '') FROM STL_SERVICE T1 WHERE T1.SERVICE_CODE = T.PRODUCT_CODE AND T1.ACCT_MONTH = RPT_SETTLEMONTH)
         WHERE T.SETTLEMONTH = RPT_SETTLEMONTH;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        RPT_RUNSITE := '4';
        SET @RPT_SQL     := 'insert into rpt_p2c(settlemonth, version, offer_code, offer_name, product_code, product_name, product_class, settlement_class, outprov_code, inprov_code, ' ||
                               'taxrate, settlement_amount, fee_flag, sign_entity, order_mode, charge_code, rateplan_id, gh_code) ' ||
                        'select ''' || RPT_SETTLEMONTH || ''', ''' || G_VERSION || ''', a.po_id, a.po_name, a.product_id, decode(a.one_product_id, ''111601'', ''基础口令产品'', null), a.product_class, a.settlement_class, b.settlement_party_out, ' ||
                               'b.settlement_party_in, a.tax_rate, sum(b.settlement_amount), a.fee_flag, a.contract_main, a.order_mode, null, a.rateplan_id, a.gh_code ' ||
                          'from sync_interface_mc_' || RPT_SETTLEMONTH || ' a, sync_interface_mc_p2c_' || RPT_SETTLEMONTH || ' b ' ||
                         'where a.id = b.id and a.file_name = b.file_name ' ||
                           'and a.status = 0 and b.status = 0 ' ||
                         'group by a.po_id, a.po_name, a.product_id, a.one_product_id, a.product_class, a.settlement_class, b.settlement_party_out, b.settlement_party_in, a.tax_rate, ' ||
                               'a.fee_flag, a.contract_main, a.order_mode, a.rateplan_id, a.gh_code ' ||
                         'union all ' ||
                    	 'select ''' || RPT_SETTLEMONTH || ''' settlemonth, ''' || G_VERSION || ''' version, ''BC2C'', a.product_name, a.product_id, ''云客服'', a.pv_product_class, a.settlement_class, a.settlement_party_out, ' ||
                         'a.settlement_party_in, to_char(a.tax_rate,''FM0.90''), sum(a.settlement_amount), 1 fee_flag , NULL sign_entity, decode(a.prov_code,''000'',''1'',''3''), NULL charge_code, NULL rateplan_id, a.gh_code ' ||
                    	 'from SYNC_INTERFACE_BC2C_PS a ' ||
                    	 'where a.status = 0 and ACCT_MONTH =''' || RPT_SETTLEMONTH  || ''' ' ||
                    	 'group by a.product_name, a.product_id, a.pv_product_class, a.settlement_class, a.settlement_party_out, a.settlement_party_in, a.tax_rate, ' ||
                    	 'a.prov_code, a.gh_code ' ||
                        'union all ' ||
                        'select ''' || RPT_SETTLEMONTH || ''', ''' || G_VERSION || ''', a.product_id, a.product_name, null, null, null, b.settle_class, b.settlement_party_out, ' ||
                               'b.settlement_party_in, a.tax_rate, sum(b.settlement_amount), a.fee_flag, a.main_contract, a.busi_mode, null,a.rateplan_id,null ' ||
                          'from sync_interface_esp_' || RPT_SETTLEMONTH || ' a, sync_interface_esp_p2c_' || RPT_SETTLEMONTH || ' b ' ||
                         'where a.id = b.id and a.file_name = b.file_name ' ||
                           'and a.status = 0 and b.status = 0 ' ||
                         'group by a.product_id, a.product_name, b.settle_class, b.settlement_party_out, b.settlement_party_in, a.tax_rate, ' ||
                               'a.fee_flag, a.main_contract, a.busi_mode, a.rateplan_id ';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, @RPT_SQL);

        --cmiot(车务通)接口表获取数据
        RPT_RUNSITE := '5';
        SET @RPT_SQL     := 'insert into rpt_p2c(settlemonth, version, offer_code, offer_name, product_code, product_name, product_class, settlement_class, outprov_code, inprov_code, '||
                               'taxrate, settlement_amount, fee_flag, sign_entity, order_mode, charge_code, rateplan_id) '||
                'select ''' || RPT_SETTLEMONTH || ''',''' || G_VERSION || ''', product_spec_num, decode(product_spec_num,''100014'',''千里眼个人'', ''100001'', ''车务通前装'', ''100002'', ''车务通后装'',''100007'',''OneNet'',''100008'',''行车卫士'',''100006'',''OneTraffic''), null, null, null, settle_type, settle_out, decode(product_spec_num,''100014'',''ZYWLW'',''100007'',''ZYWLW'',''100008'',''ZYWLW'',''SY'')  settle_in, tax_rate / 100, ' ||
                'sum(settle_amount * 1000), 1, null, null, null, null '||
                'from stl_cmiot_sett '||
                'where product_spec_num in(''100014'',''100001'', ''100002'',''100007'',''100008'',''100006'') '||
                'and settle_month=''' || RPT_SETTLEMONTH || ''' '||
                'group by product_spec_num, settle_type, settle_out, settle_in, tax_rate';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, @RPT_SQL);

        --iot商城接口表获取数据
        RPT_RUNSITE := '6';
        SET @RPT_SQL     := 'insert into rpt_p2c(settlemonth, version, offer_code, offer_name, product_code, product_name, product_class, settlement_class, outprov_code, inprov_code, '||
                               'taxrate, settlement_amount, fee_flag, sign_entity, order_mode, charge_code, rateplan_id) '||
                'select ''' || RPT_SETTLEMONTH || ''',''' || G_VERSION || ''', product_spec_num, decode(product_spec_num, ''100003'', ''DICT和对讲业务'', ''100004'', ''DICT千里眼业务'', ''100009'', ''OneNet增值服务'',''100010'',''OnePark增值服务'',''100011'',''千里眼（任务销售模式）'',''100012'',''OnePark（任务销售模式）'',''100013'',''行车卫士'',''300011'',''千里眼（任务销售模式）''), null, null,  '||
                ' decode(product_spec_num,''100003'',''100003'',''100004'',''100004'',''100005'',''100005'',''100009'',''100009'',''100010'',''100010'',''100011'',''100011'',''100012'',''100012'',''100013'',''100013'',''300011'',''300011''), settle_type, settle_out,    '||
                ' decode(product_spec_num,''100003'', ''ZQ'',''100004'',''ZYWLW'',''100005'',''ZQ'',''100009'',''ZYWLW'',''100010'',''ZYWLW'',''100011'',''ZYWLW'',''100012'',''ZYWLW'',''100013'',''ZYWLW'',''300011'',''ZYWLW'') settle_in, tax_rate / 100, ' ||
                'sum(settle_amount * 1000), 1, null, null, null, null '||
                'from stl_iot_sett '||
                'where product_spec_num in(''100003'', ''100004'', ''100005'',''100009'',''100010'',''100011'',''100012'',''100013'',''300011'') '||
                'and settle_type = ''01''  ' ||
                'and settle_month=''' || RPT_SETTLEMONTH || ''' '||
                'group by product_spec_num, settle_type, settle_out, settle_in, tax_rate ';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, @RPT_SQL);

        -----专线卫士防火墙报表
        RPT_RUNSITE := '7';
        SET @RPT_SQL     := 'insert into rpt_p2c(settlemonth, version, offer_code, offer_name, product_code, product_name, product_class, settlement_class, outprov_code, inprov_code, ' ||
                               'taxrate, settlement_amount, fee_flag, sign_entity, order_mode, charge_code, rateplan_id,partner_number,partner_rule_type,adj_month) ' ||
        	   'select ''' || RPT_SETTLEMONTH || ''',''' || G_VERSION || ''', a.offer_code,null,a.product_code,null, null, ''2'', a.out_object, ' ||
               'a.in_object, to_char(round(a.tax_rate / 100, 2)), sum(a.settle_notaxfee * 10), decode(a.feetype, ''0'', ''1'', ''1'', ''2''), a.sign_entity, a.order_mode, a.charge_code, null, '''', '''', ''''  ' ||
               'from ur_eboss_' || RPT_SETTLEMONTH || '_t a ' ||
               'where a.dest_source = ''5'' ' ||
               'and a.offer_code =''50090'' ' ||
               'and a.in_object = ''ZW''  ' ||
               'group by a.offer_code, a.product_code, a.out_object, a.in_object, a.tax_rate,a.feetype, a.sign_entity, a.order_mode ,a.charge_code ' ||
        ------e企收银
          'union all ' ||
          'select ''' || RPT_SETTLEMONTH || ''',''' || G_VERSION || ''', a.offer_code,null,a.product_code,null, null, ''2'', a.out_object, ' ||
           'a.in_object, to_char(round(a.tax_rate / 100, 2)), sum(a.settle_notaxfee * 10), decode(a.feetype, ''0'', ''1'', ''1'', ''2''), a.sign_entity, a.order_mode, a.charge_code, null, '''', '''',a.adjmonth ' ||
           'from ur_eboss_' || RPT_SETTLEMONTH || '_t a ' ||
           'where a.dest_source = ''5'' ' ||
           'and a.offer_code =''50099'' ' ||
           'group by a.offer_code, a.product_code, a.out_object, a.in_object, a.tax_rate,a.feetype, a.sign_entity, a.order_mode ,a.charge_code, a.adjmonth ';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, @RPT_SQL);

        -- 梧桐大数据Hadoop云平台20240507
        RPT_RUNSITE := '12';
                        SET @RPT_SQL := 'delete from RPT_BIG_DATA where settlemonth = ''' || RPT_SETTLEMONTH || '''';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
        SET @RPT_SQL := 'insert into RPT_BIG_DATA(settlemonth, version, offer_code, offer_name, product_code, product_name, product_class, settlement_class, outprov_code,outprov_name, inprov_code,inprov_name, '||
                                   'taxrate, settlement_amount, fee_flag) '||
                    'select ''' || RPT_SETTLEMONTH || ''',''' || G_VERSION || ''', ''75983'', ''带宽型业务'', '''', '''',  '||
                     ' s.BUS_TYPE, '''', s.PROVINCE_CODE, s.PROVINCE_NAME,    '||
                     ' s.PROVINCE_CODE_IN, s.PROVINCE_NAME_IN, ''0.06'', ' ||
                    'sum(s.DOWN_DATA_TRAFFIC * cfg.SETTLE_PRICE * 100) settlement_amount, 1  '||
                    'from STL_BIG_DATA_SETT s join bigdata_broadband_price_cfg cfg on s.BUS_TYPE = cfg.bus_type '||
                    'where s.settle_month=''' || RPT_SETTLEMONTH || ''' and cfg.enable = ''1'' '||
                    'group by s.BUS_TYPE, s.PROVINCE_CODE, s.PROVINCE_NAME, s.PROVINCE_CODE_IN, s.PROVINCE_NAME_IN';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, @RPT_SQL);

        -- OneS星辰安全业务
        RPT_RUNSITE := '13';
            SET @RPT_SQL := 'delete from stludr.RPT_ONES_P2C where settle_month = ''' || RPT_SETTLEMONTH || '''';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
        SET @RPT_SQL := 'INSERT INTO stludr.RPT_ONES_P2C(SETTLE_MONTH,CUSTOMER_PROV,CUSTOMER_PROV_NAME,CUSTOMER_CODE,CUSTOMER_NAME,OFFER_CODE,OFFER_NAME,PRODUCT_CODE, '||
        'TAX_RATE,NO_TAX_FEE,TAX_FEE,PARTNER_CODE,PARTNER_NAME,CHARGE_CODE,CHARGE_NAME,FEE_FLAG,PRODUCT_ORDER_ID,OUT_OBJECT,OUT_OBJECT_NAME) '||
        'SELECT a.SETTLE_MONTH,a.ORDER_PROV,b.PROV_NM AS CUSTOMER_PROV_NAME,a.CUSTOMER_CODE,c.FIRST_NAME,a.OFFER_CODE, '||
        '''OneS星辰安全'' OFFER_NAME,a.PRODUCT_CODE,a.TAX_RATE,round(sum(to_char(a.SETTLE_NOTAXFEE , ''FM999999999999990.00'')) *10 ) AS SETTLE_NOTAXFEE, '||
        'sum(a.TAX_FEE),''QXMC'',''北京启明星辰信息安全技术有限公司'',a.CHARGE_CODE,r.ITEM_NAME, '||
        'decode(a.feetype, ''0'', ''1'', ''1'', ''2''),a.PRODUCT_ORDER_ID,a.OUT_OBJECT,b1.PROV_NM AS OUT_OBJECT_NAME '||
        'FROM STLUDR.UR_EBOSS_' || RPT_SETTLEMONTH || '_T a LEFT JOIN STLUDR.STL_PROVINCE_CD b ON a.ORDER_PROV = b.PROV_CD '||
        'LEFT JOIN STLUDR.STL_PROVINCE_CD b1 ON a.OUT_OBJECT = b1.PROV_CD  '||
        'LEFT JOIN STLUSERS.STL_RULE_ITEM_T r ON a.charge_code = r.CHARGE_ITEM  '||
        'LEFT JOIN STLUSERS.STL_CUSTOMER c ON a.CUSTOMER_CODE = c.CUSTOMER_CODE '||
        'WHERE a.OFFER_CODE = ''60008'' AND a.rule_id = r.rule_id   '||
        'GROUP BY a.SETTLE_MONTH,a.ORDER_PROV,b.PROV_NM,a.CUSTOMER_CODE,c.FIRST_NAME,a.OFFER_CODE,a.PRODUCT_CODE,a.TAX_RATE, '||
        'a.CHARGE_CODE,r.ITEM_NAME,a.feetype,a.PRODUCT_ORDER_ID,a.OUT_OBJECT,b1.PROV_NM';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, @RPT_SQL);

--          物联网卡平台接口表获取数据
	    RPT_RUNSITE := '8';
	    SET @RPT_SQL   := 'insert into rpt_p2c(settlemonth, version, offer_code, offer_name, product_code, product_name, product_class, settlement_class, outprov_code, inprov_code, '||
                       'taxrate, settlement_amount, fee_flag, sign_entity, order_mode, charge_code, rateplan_id) '||
        'select ''' || RPT_SETTLEMONTH || ''',''' || G_VERSION || ''', BUS_TYPE, decode(BUS_TYPE, ''400001'', ''M2M芯片产品''), '''', '''',  '||
         'decode(BUS_TYPE,''400001'',''400001''), settle_type, settle_out,    '||
         'decode(SETTLE_IN,''12'', ''ZYWLW'') settle_in, tax_rate / 100, ' ||
        'sum(settle_amount * 1000), 1, '''', '''', '''', '''' '||
        'from STL_IOT_CARD_SETT '||
        'where BUS_TYPE in(''400001'') '||
        'and settle_type = ''01'' ' ||
        'and settle_month=''' || RPT_SETTLEMONTH || ''' '||
        'group by BUS_TYPE, settle_type, settle_out, settle_in, tax_rate ';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, @RPT_SQL);

        -- iot商城接口表获取数据20240326
        RPT_RUNSITE := '11';
        SET @RPT_SQL   := 'insert into rpt_p2c(settlemonth, version, offer_code, offer_name, product_code, product_name, product_class, settlement_class, outprov_code, inprov_code, '||
                          'taxrate, settlement_amount, fee_flag, sign_entity, order_mode, charge_code, rateplan_id,partner_number,partner_rule_type) '||
                          'select ''' || RPT_SETTLEMONTH || ''',''' || G_VERSION || ''', product_spec_num, decode(product_spec_num, ''100003'', ''DICT和对讲业务'', ''100004'', ''DICT千里眼业务'',''100005'', ''DICT云视讯业务''), '''', '''',  '||
                          ' product_spec_num, settle_type, settle_out,  '||
                          'decode(PARTNER_NUMBER,''01'', ''DZYJC'',''02'',''DZYWLW'',''03'',''DZYJS'',''04'',''DZD'',''05'',''ZQ'',''06'',''DZW'',''07'',''DCY'',''08'',''DMG'') inprov_code, ' ||
                          'tax_rate / 100, ' ||
                          'sum(settle_amount * 1000), 1, '''', '''', '''', '''', ' ||
                          ' partner_number, PARTNER_RULE_TYPE '||
                          'from stl_iot_sett '||
                          'where product_spec_num in(''100003'',''100004'',''100005'') '||
                          'and settle_type = ''06'' ' ||
                          'and settle_month=''' || RPT_SETTLEMONTH || ''' '||
                          'group by product_spec_num, settle_type, settle_out, settle_in, tax_rate,PARTNER_NUMBER,PARTNER_RULE_TYPE ';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, @RPT_SQL);

        RPT_RUNSITE := '9';
        RPT_SQL     := '更新表中省公司的中文名称和往来段... ...';
        UPDATE RPT_P2C T
           SET T.OUTPROV_NAME = (SELECT T1.PROV_NM FROM STL_PROVINCE_CD T1 WHERE T1.PROV_CD = T.OUTPROV_CODE),
               T.INPROV_NAME = (SELECT T2.PROV_NM FROM STL_PROVINCE_CD T2 WHERE T2.PROV_CD = T.INPROV_CODE),
               T.PROV_WL = (SELECT T3.PROV_WL FROM STL_PROVINCE_CD T3 WHERE T3.PROV_CD = T.OUTPROV_CODE)
         WHERE T.SETTLEMONTH = RPT_SETTLEMONTH;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        ----二级中间表计算
        --STL_LIMIT(RPT_SETTLEMONTH, PROC_OUT);
        --STL_P2C_LIMITED(RPT_SETTLEMONTH, FLAG_VERSION, PROC_OUT);

        RPT_RUNSITE := '8';
        RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表结束时间：' ||TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);


        commit;

        PROC_OUT:='Y';
        outReturn := 0;

        SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
    END;
END ;;
DELIMITER ;
