/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层-非省间结算-非省间结算报表中间表数据清理
**/
DROP PROCEDURE IF EXISTS stludr.`RPT_OTHER_STL_CLEAN`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "RPT_OTHER_STL_CLEAN"(
    RPT_SETTLEMONTH IN VARCHAR2,
    STL_VER IN NUMBER,
    FLAG IN VARCHAR2,
    PROC_OUT OUT VARCHAR2
)
AS
    --清理对应的版本数据
    v_proc_name       VARCHAR2(30) := 'RPT_OTHER_STL_CLEAN';
    iv_Sql_Insert    VARCHAR2(3072);
    iv_Sql_Insert1   VARCHAR2(1024);
    iv_Sql_Insert2   VARCHAR2(2048);
    iv_Sql_Insert3   VARCHAR2(2048);
    iv_Sql_Update    VARCHAR2(1024);
    outReturn       int;
    --RPT_SETTLEMONTH 结算账期
    --STL_VER  版本号码
    --FLAG      1 : 大于等于版本号码的版本的数据全部清理
    --     其他值 : 只清理对应版本的数据
    cursor C2 IS select table_name from RVL_TABLE_ALL T WHERE T.VALID = 'Y' AND T.PKG='OTHER';
    c2_type RVL_TABLE_ALL%rowtype;

BEGIN

    PROC_OUT := '';
    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        PROC_OUT := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || PROC_OUT ) AS error_msg ;
    END;

    BEGIN
        if ( length(RPT_SETTLEMONTH) < 6 )  then
            SELECT 'RPT_SETTLEMONTH length less than 6. RPT_SETTLEMONTH=[' || RPT_SETTLEMONTH || ']' FROM dual;
            outReturn := -1;
            ROLLBACK;
            RETURN;
        end if;

        SELECT 'RPT_SETTLEMONTH=' ||  RPT_SETTLEMONTH FROM dual;


        IF FLAG = '1' THEN
            --清理中间表的数据

            for c2_type in C2 LOOP
                SET @RPT_SQL := 'DELETE FROM '||c2_type.TABLE_NAME||' T WHERE T.VERSION IN (SELECT DISTINCT VERSION FROM RVL_CONF_VERSION T1 WHERE T1.VERSIONNUM >= '||STL_VER||' AND T1.TABLE_NAME = '''||c2_type.TABLE_NAME||''' AND T.SETTLEMONTH = '''||RPT_SETTLEMONTH||''') AND T.SETTLEMONTH = '''||RPT_SETTLEMONTH||'''';
                SELECT @RPT_SQL;
                PREPARE STMT FROM @RPT_SQL;
                EXECUTE STMT;
                DEALLOCATE PREPARE STMT;
            END LOOP;

            --清理版本表里的数据
            DELETE FROM RVL_CONF_VERSION T
             WHERE T.VERSIONNUM >= STL_VER
               AND T.TABLE_NAME IN (SELECT T1.TABLE_NAME FROM RVL_TABLE_ALL T1 WHERE T1.VALID = 'Y' AND T1.PKG='OTHER')
               AND T.SETTLEMONTH = RPT_SETTLEMONTH;
        ELSE
            --清理中间表的数据
            for c2_type in C2 LOOP
                SET @RPT_SQL := 'DELETE FROM '||c2_type.TABLE_NAME||' T WHERE T.VERSION IN (SELECT DISTINCT VERSION FROM RVL_CONF_VERSION T1 WHERE T1.VERSIONNUM = '||STL_VER||' AND T1.TABLE_NAME = '''||c2_type.TABLE_NAME||''' AND T.SETTLEMONTH = '''||RPT_SETTLEMONTH||''') AND T.SETTLEMONTH = '''||RPT_SETTLEMONTH||'''';
                SELECT @RPT_SQL;
                PREPARE STMT FROM @RPT_SQL;
                EXECUTE STMT;
                DEALLOCATE PREPARE STMT;
            END LOOP;


            --清理版本表里的数据
            DELETE FROM RVL_CONF_VERSION T
             WHERE T.VERSIONNUM = STL_VER
               AND T.TABLE_NAME IN (SELECT T1.TABLE_NAME FROM RVL_TABLE_ALL T1 WHERE T1.VALID = 'Y' AND T1.PKG='OTHER')
               AND T.SETTLEMONTH = RPT_SETTLEMONTH;

        END IF;

        commit;

        PROC_OUT := 'Y';
        outReturn := 0;

        SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
    END;
END ;;
DELIMITER ;