/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层 -非省间结算报表中间表总调用
**/
DROP PROCEDURE IF EXISTS stludr.`RPT_OTHER_STL_MAIN`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "RPT_OTHER_STL_MAIN"(
    RPT_SETTLEMONTH IN VARCHAR2,
    FLAG_VERSION IN CHAR,
    PROC_OUT OUT VARCHAR2
)
AS
    -- 流量统付全网业务分客户结算明细表
    v_proc_name       VARCHAR2(30) := 'RPT_OTHER_STL_MAIN';
    G_VERSION RVL_CONF_VERSION.VERSION%TYPE; --版本号变量
    G_RESNUM  RVL_CONF_VERSION.VERSIONNUM%TYPE; --版本序号
    ERRCODE   VARCHAR2(32);
    ERRMSG    VARCHAR2(2048);
    VER_TABLE VARCHAR2(64);
    RPT_RUNSITE VARCHAR2(32); --运行定位标识
    RPT_SQL     VARCHAR2(4000); --拼SQL的字符串变量
    RPT_TABLE   VARCHAR2(64); --拼表名的字符串变量
    outReturn  int;
    PROC_MAIN_OUT VARCHAR2(2000);
BEGIN

    PROC_OUT := '';
    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        PROC_OUT := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || PROC_OUT ) AS error_msg ;
    END;

    BEGIN
        if ( length(RPT_SETTLEMONTH) < 6 )  then
            SELECT 'RPT_SETTLEMONTH length less than 6. RPT_SETTLEMONTH=[' || RPT_SETTLEMONTH || ']';
            outReturn := -1;
            ROLLBACK;
            RETURN;
        end if;

        SELECT 'RPT_SETTLEMONTH=' ||  RPT_SETTLEMONTH;

        PROC_MAIN_OUT := '';
        call RPT_OTHER_STL_GPRS_BL(RPT_SETTLEMONTH, FLAG_VERSION, PROC_OUT);
        PROC_MAIN_OUT:=PROC_MAIN_OUT||'|'||PROC_OUT;
        call RPT_OTHER_STL_RECEIPT_DETAIL(RPT_SETTLEMONTH, FLAG_VERSION, PROC_OUT);
        PROC_MAIN_OUT:=PROC_MAIN_OUT||'|'||PROC_OUT;
        PROC_OUT:=PROC_MAIN_OUT;

        commit;

        outReturn := 0;

        SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
    END;
END ;;
DELIMITER ;