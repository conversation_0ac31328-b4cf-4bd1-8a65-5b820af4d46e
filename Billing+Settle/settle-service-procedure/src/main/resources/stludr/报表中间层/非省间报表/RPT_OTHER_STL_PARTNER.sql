/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层 -合作伙伴报表数据生成
**/
use stludr;
DROP PROCEDURE IF EXISTS stludr.`RPT_OTHER_STL_PARTNER`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE stludr.`RPT_OTHER_STL_PARTNER`(
    RPT_SETTLEMONTH IN VARCHAR2,
    FLAG_VERSION IN CHAR,
    PROC_OUT OUT VARCHAR2,
    szSysErr OUT VARCHAR2(1000),
    nReturn OUT NUMBER(4)
)
AS
    -- 合作伙伴结算报表中间表
    v_proc_name       VARCHAR2(30) := 'RPT_OTHER_STL_PARTNER';
    G_VERSION RVL_CONF_VERSION.VERSION%TYPE; --版本号变量
    G_RESNUM  RVL_CONF_VERSION.VERSIONNUM%TYPE; --版本序号
    ERRCODE   VARCHAR2(32);
    ERRMSG    VARCHAR2(2048);
    VER_TABLE VARCHAR2(64);
    RPT_RUNSITE VARCHAR2(32); --运行定位标识
    RPT_SQL     VARCHAR2(4000); --拼SQL的字符串变量
    RPT_TABLE   VARCHAR2(64); --拼表名的字符串变量
    outReturn  int;
    outSysError VARCHAR2(4000);
BEGIN

    PROC_OUT := '';
    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
PROC_OUT := 'N';
        szSysErr := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || PROC_OUT ) AS error_msg ;
    END;

    BEGIN
        if ( length(RPT_SETTLEMONTH) < 6 )  then
            SELECT 'RPT_SETTLEMONTH length less than 6. RPT_SETTLEMONTH=[' || RPT_SETTLEMONTH || ']';
            outReturn := -1;
            ROLLBACK;
            RETURN;
        end if;

        SELECT 'RPT_SETTLEMONTH=' ||  RPT_SETTLEMONTH;

        VER_TABLE   := 'RPT_PARTNER';
        RPT_RUNSITE := '0';
        RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表开始时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        --中间表获取版本号
        --SETTLE_REPORT_VERSION(RPT_SETTLEMONTH, VER_TABLE, G_VERSION, G_RESNUM, FLAG_VERSION);
        RPT_RUNSITE := '1';

        SET @RPT_SQL := 'delete from rpt_partner where settlemonth = ''' || RPT_SETTLEMONTH || '''';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @RPT_SQL := 'delete from rpt_partner_dict where settlemonth = ''' || RPT_SETTLEMONTH || '''';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @RPT_SQL := 'delete from RPT_PARTNER_ZXWS where SETTLE_MONTH = ''' || RPT_SETTLEMONTH || '''';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
        -------BBOSS出账（A类业务）
        SET @RPT_SQL     := 'insert into rpt_partner(settlemonth, version, order_mode, offer_code, product_code, partner_code, partner_name, report_product_code, report_product_name, rateplan_id, ' ||
                               'rateplan_name, sign_entity, fee_flag, par_settl_amount, tax_rate, par_res_settl_rate, settlement_class) ' ||
                        'select ''' || RPT_SETTLEMONTH || ''', ''' || G_VERSION || ''', a.order_mode, a.offer_code, a.product_code, b.partner_code, b.partner_name, a.product_code, null, ' ||
                               'get_normal_feetype(a.charge_code), null, null, a.feetype + 1, sum(a.settle_notaxfee * 10), a.tax_rate / 100, b.par_res_rate, null ' ||
                        'from ur_eboss_' || RPT_SETTLEMONTH || '_t a, stludr.rvl_bboss_partner b '||
                       'where a.dest_source = ''5'' and b.type = ''A''  ' ||
                         'and (a.product_code = b.product_code and b.column_name = ''product_code'') ' ||
                       'group by a.order_mode, a.offer_code, a.product_code, a.charge_code, a.feetype, a.tax_rate, b.partner_code, b.partner_name, b.par_res_rate';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, @RPT_SQL);

        RPT_RUNSITE := '2';
        RPT_SQL     := '更新表中的REPORT_PRODUCT_NAME... ...';
        UPDATE RPT_PARTNER T
           SET T.REPORT_PRODUCT_NAME = (SELECT DISTINCT NVL(T1.NAME, '') FROM STL_SERVICE T1 WHERE T1.SERVICE_CODE = T.PRODUCT_CODE AND T1.ACCT_MONTH = RPT_SETTLEMONTH)
         WHERE T.SETTLEMONTH = RPT_SETTLEMONTH;
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        -------BBOSS出账（B类业务，i笛云、口袋扫描仪、WPS需要进行成员权益领取判断）
        RPT_RUNSITE := '3';
        SET @RPT_SQL     := 'insert into rpt_partner(settlemonth, version, order_mode, offer_code, product_code, partner_code, partner_name, report_product_code, report_product_name, rateplan_id, ' ||
                               'rateplan_name, sign_entity, fee_flag, par_settl_amount, tax_rate, par_res_settl_rate, settlement_class) ' ||
                        'select ''' || RPT_SETTLEMONTH || ''', ''' || G_VERSION || ''', a.order_mode, a.offer_code, a.product_code, b.partner_code, b.partner_name, a.duration, c.name, ' ||
                               'decode(a.product_code, ''5003810'', get_normal_feetype(a.charge_code), a.charge_code), null, null, a.feetype + 1, sum(round(a.settle_notaxfee * b.par_rate))*10, a.tax_rate / 100, b.par_res_rate, null ' ||
                        'from ur_eboss_' || RPT_SETTLEMONTH || '_t a, stludr.rvl_bboss_partner b, rvl_rateplan_conf c '||
                       'where a.dest_source = ''5'' and b.type = ''B'' ' ||
                         'and a.duration = c.rateplan_id ' ||
                         'and (a.product_code = b.product_code and b.column_name = ''product_code'') ' ||
                         'and a.product_code not in (''5003821'', ''5003822'', ''2021999400048964'',''2022999400013561'',''2022999400013562'',''5003816'',''5003817'') ' ||
                       'group by a.order_mode, a.offer_code, a.product_code, a.charge_code, a.feetype, a.tax_rate, b.partner_code, b.partner_name, a.duration, c.name, b.par_res_rate ' ||
                       'union all ' ||
                        'select ''' || RPT_SETTLEMONTH || ''', ''' || G_VERSION || ''', a.order_mode, a.offer_code, a.product_code, b.partner_code, b.partner_name, a.duration, c.name, ' ||
                               'a.charge_code, null, null, a.feetype + 1, sum(round(a.settle_notaxfee * b.par_rate))*10, a.tax_rate / 100, b.par_res_rate, null ' ||
                        'from ur_eboss_' || RPT_SETTLEMONTH || '_t a, stludr.rvl_bboss_partner b, rvl_rateplan_conf c, stlusers.stl_mem_interests d ' ||
                       'where a.dest_source = ''5'' and b.type = ''B'' ' ||
                         'and a.duration = c.rateplan_id  ' ||
                         'and (a.product_code = b.product_code and b.column_name = ''product_code'') ' ||
                         'and a.product_code in (''5003821'', ''5003822'',''2022999400013561'',''2022999400013562'',''5003816'',''5003817'') ' ||
                         'and a.customer_code = d.customer_num and a.product_order_id = d.prodist_sku_num and a.member_code = d.member_num ' ||
                       'group by a.order_mode, a.offer_code, a.product_code, a.charge_code, a.feetype, a.tax_rate, b.partner_code, b.partner_name, a.duration, c.name, b.par_res_rate ' ||
                       ' union all ' ||
                        'select ''' || RPT_SETTLEMONTH || ''', ''' || G_VERSION || ''', a.order_mode, a.offer_code, a.product_code, b.partner_code, b.partner_name, a.duration, c.name, ' ||
                               'a.charge_code, null, null, a.feetype + 1, sum(round(a.settle_notaxfee * b.par_rate))*10, a.tax_rate / 100, b.par_res_rate, null ' ||
                        'from ur_eboss_' || RPT_SETTLEMONTH || '_t a, stludr.rvl_bboss_partner b, rvl_rateplan_conf c, stlusers.stl_mem_interests d ' ||
                       'where a.dest_source = ''5'' and b.type = ''B'' ' ||
                         'and a.charge_code = b.par_chargecode '||
                         'and a.duration = c.rateplan_id  ' ||
                         'and (a.product_code = b.product_code and b.column_name = ''product_code'') ' ||
                         'and a.product_code = ''2021999400048964'' ' ||
                         'and a.customer_code = d.customer_num and a.product_order_id = d.prodist_sku_num and a.member_code = d.member_num ' ||
                       'group by a.order_mode, a.offer_code, a.product_code, a.charge_code, a.feetype, a.tax_rate, b.partner_code, b.partner_name, a.duration, c.name, b.par_res_rate ';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, @RPT_SQL);
        -- 专线卫视DICT
        RPT_RUNSITE := '9';
            SET @RPT_SQL := 'INSERT INTO RPT_PARTNER_ZXWS(SETTLE_MONTH,CUSTOMER_PROV,CUSTOMER_PROV_NAME,CUSTOMER_CODE,CUSTOMER_NAME,OFFER_CODE,OFFER_NAME,PRODUCT_CODE,PRODUCT_NAME, '||
              'TAX_RATE,NO_TAX_FEE,TAX_FEE,PARTNER_CODE,PARTNER_NAME,CHARGE_CODE,CHARGE_NAME,FEE_FLAG,PRODUCT_ORDER_ID,OUT_OBJECT,OUT_OBJECT_NAME) '||
              'SELECT a.SETTLE_MONTH,a.ORDER_PROV,b.PROV_NM AS CUSTOMER_PROV_NAME,a.CUSTOMER_CODE,c.FIRST_NAME,a.OFFER_CODE, '||
              '''专线卫视DICT'' OFFER_NAME,a.PRODUCT_CODE,''专线卫士DICT集成服务包-优享版专线安全检测服务'',a.TAX_RATE,round(sum(to_char(a.SETTLE_NOTAXFEE , ''FM999999999999990.00'')) *10 ) AS SETTLE_NOTAXFEE, '||
              'sum(a.TAX_FEE),''QXMC'',''北京启明星辰信息安全技术有限公司'',a.CHARGE_CODE,r.ITEM_NAME, '||
              'decode(a.feetype, ''0'', ''1'', ''1'', ''2''),a.PRODUCT_ORDER_ID,a.OUT_OBJECT,b1.PROV_NM AS OUT_OBJECT_NAME '||
              'FROM STLUDR.UR_EBOSS_' || RPT_SETTLEMONTH || '_T a LEFT JOIN STLUDR.STL_PROVINCE_CD b ON a.ORDER_PROV = b.PROV_CD '||
              'LEFT JOIN STLUDR.STL_PROVINCE_CD b1 ON a.OUT_OBJECT = b1.PROV_CD  '||
              'LEFT JOIN STLUSERS.STL_RULE_ITEM_T r ON a.charge_code = r.CHARGE_ITEM  '||
              'LEFT JOIN STLUSERS.STL_CUSTOMER c ON a.CUSTOMER_CODE = c.CUSTOMER_CODE '||
              'WHERE a.OFFER_CODE = ''60009''  AND a.rule_id = r.rule_id   '||
              'GROUP BY a.SETTLE_MONTH,a.ORDER_PROV,b.PROV_NM,a.CUSTOMER_CODE,c.FIRST_NAME,a.OFFER_CODE,a.PRODUCT_CODE,a.TAX_RATE, '||
              'a.CHARGE_CODE,r.ITEM_NAME,a.feetype,a.PRODUCT_ORDER_ID,a.OUT_OBJECT,b1.PROV_NM';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, @RPT_SQL);

        --DICT
        RPT_RUNSITE := '4';
        SET @RPT_SQL := 'insert into rpt_partner_dict(settlemonth, version, order_mode, offer_code, product_code, partner_name, report_product_name, fee_flag, par_settl_amount, ' ||
                   'tax_rate, settlement_class,  product_type, offer_order_id, product_order_id,inprov_code,outprov_code,settle_type,partner_rule_type,adj_month) ' ||
           --dict 利用product_type字段区分业务
              'select ''' || RPT_SETTLEMONTH || ''', ''' || G_VERSION || ''', a.order_mode,a.offer_code,a.product_code,b.partner_name, decode(a.offer_code,''50055'',substr(b.po_name, 1, length(b.po_name)-4) || ''-'' || b.service_name) report_product_name,  '||
                   'decode(a.feetype, ''0'', ''1'', ''1'', ''2'') fee_flag,a.settle_notaxfee * 10 par_settl_amount,b.tax_rate,''1'' settlement_class,  '||
                   'decode(substr(b.po_name,1,3), ''和对讲'',''1'',''云视讯'',''2'',''千里眼'',''3'',''和商务'',''4'') product_type, offer_order_id, product_order_id ,a.in_object,a.out_object,'''','''',adjmonth '||
                   'from ur_eboss_'|| RPT_SETTLEMONTH || '_t a, rvl_dict_config b   '||
                   'where a.offer_code = ''50055'' and a.product_code <> ''2023999400010056'' '||
                   'and a.offer_code=b.offer_code and a.charge_code=b.fee_type  '||
                    'union all   '||
           -- 千里眼标准产品增值服务、OnePark行业增值服务 、行车卫士标准产品
              'select ''' || RPT_SETTLEMONTH || ''', ''' || G_VERSION || ''', a.order_mode,a.offer_code,a.product_code,b.partner_name, decode(a.offer_code,''50055'',substr(b.po_name, 1, length(b.po_name)-4) || ''-'' || b.service_name, b.po_name) report_product_name,  '||
                   'decode(a.feetype, ''0'', ''1'', ''1'', ''2'') fee_flag,a.settle_notaxfee * 10 par_settl_amount,b.tax_rate,''1'' settlement_class,  '||
                   'decode(substr(b.po_name,1,3), ''和对讲'',''1'',''云视讯'',''2'',''千里眼'',''3'',''和商务'',''4'') product_type, offer_order_id, product_order_id ,a.in_object,a.out_object,'''','''',adjmonth '||
                   'from ur_eboss_'|| RPT_SETTLEMONTH || '_t a, rvl_dict_config b   '||
                   'where (a.product_code in(''2023999400010056'',''2023999400048574'') or a.offer_code in(''50117'',''50119'')) and dest_source=''6''  '||
                   'and a.offer_code=b.offer_code and a.charge_code=b.fee_type  '||
            -- 和商务tv，e企组网，5G消息集成服务、移动办公DICT集成服务包、集团语音DICT集成服务包,onepay、e企赢客、国际短信通用定制DICT服务包
                   'union all ' ||
               'select ''' || RPT_SETTLEMONTH || ''', ''' || G_VERSION || ''', a.order_mode,a.offer_code,a.product_code,b.partner_name, (b.po_name || ''-'' || b.service_name)  report_product_name, ' ||
                   'decode(a.feetype, ''0'', ''1'', ''1'', ''2'') fee_flag,a.settle_notaxfee * 10 par_settl_amount,b.tax_rate,''1'' settlement_class, ' ||
                   ' null, offer_order_id, product_order_id ,a.in_object,a.out_object,'''','''',adjmonth ' ||
                   'from ur_eboss_' || RPT_SETTLEMONTH || '_t a, rvl_dict_config b ' ||
                    'where (a.offer_code in(''50028'',''50110'') or a.product_code in(''2022999400072067'',''2023999400036743'',''2023999400035055'',''2024999480000390'',''2024999480001367'',''2025999480006061'') )  '||
                   'and a.offer_code=b.offer_code and a.charge_code=b.fee_type ' ||

        --IOT商城DICT
--                    'union all ' ||
--                 'select ''' || RPT_SETTLEMONTH || ''', ''' || G_VERSION || ''', ''0'', a.product_spec_num,null,a.partner_name,a.product_name report_product_name, ' ||
--                    '1 fee_flag,a.settle_amount * 1000 par_settl_amount,to_char(a.tax_rate / 100), ''1'' settlement_class, null, 0, 0 ' ||
--                    'from stl_iot_sett a ' ||
--                    'where a.product_spec_num in (''100003'', ''100004'', ''100005'',''100009'',''100010'') and settle_type in (''02'',''03'',''04'',''05'') ' ||
--                    'and a.settle_month = ''' || RPT_SETTLEMONTH || ''' ';

        -- IOT商城DICT 20240326
                        'union all ' ||
                        'select ''' || RPT_SETTLEMONTH || ''', ''' || G_VERSION || ''', ''0'', a.product_spec_num,'''',a.partner_name,a.product_name report_product_name, ' ||
                        '1 fee_flag,a.settle_amount * 1000 par_settl_amount,to_char(a.tax_rate / 100), ''1'' settlement_class, '''', 0, 0 ,'''',settle_out,settle_type,'''','''' ' ||
                        'from stl_iot_sett a ' ||
                        'where a.product_spec_num in (''100009'',''100010'') and settle_type in (''02'',''03'',''04'',''05'') ' ||
                        'and a.settle_month = ''' || RPT_SETTLEMONTH || ''' ' ||
            -- IOT商城DICT 20240326 100003 100004 100005当“合作伙伴编码”=05-政企分公司时，合作伙伴的相关数据展示在政企合作伙伴的报表中。
                        'union all ' ||
                        'select ''' || RPT_SETTLEMONTH || ''', ''' || G_VERSION || ''', ''0'', a.product_spec_num,'''',decode(a.PARTNER_NUMBER,''01'',''中移系统集成有限公司'',''02'',''中移物联网有限公司'',''03'',''中移建设有限公司'',''04'',''中国移动通信集团终端有限公司'',''06'',''卓望信息技术（北京）有限公司'',''14'',''卓望数码技术（深圳）有限公司'',a.partner_name) partner_name,a.product_name report_product_name, ' ||
                        '1 fee_flag,a.settle_amount * 1000 par_settl_amount,to_char(a.tax_rate / 100), ''1'' settlement_class, '''', 0, 0 ,'''',settle_out,settle_type,partner_rule_type,'''' ' ||
                        'from stl_iot_sett a ' ||
                        'where a.product_spec_num in (''100003'', ''100004'', ''100005'') and   settle_type in(''02'',''03'',''04'',''05'',''06'')  ' ||
                        'and a.settle_month = ''' || RPT_SETTLEMONTH || ''' ';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

RPT_SQL     := '插入数据... ...';
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, @RPT_SQL);
         ----更新订购级别的DICT合作伙伴名称
        update  rpt_partner_dict a
            join (select DISTINCT offer_order_id, product_order_id, partner_name
                    from RVL_DICT_ORDER_CONFIG t) b
              on (a.offer_order_id = b.offer_order_id and a.product_order_id = b.product_order_id)
              set  a.partner_name = b.partner_name
            where a.settlemonth =RPT_SETTLEMONTH;
RPT_SQL     := '更新订购级别的DICT合作伙伴名称... ...';
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, @RPT_SQL);

        RPT_RUNSITE := '5';
        RPT_SQL     := '更新表中产品和商品的中文名称... ...';
        UPDATE RPT_PARTNER T
           SET T.OFFER_NAME = (SELECT DISTINCT NVL(T1.NAME, null) FROM STL_PRODUCT T1 WHERE T1.PRODUCT_CODE = T.OFFER_CODE AND T1.ACCT_MONTH = RPT_SETTLEMONTH),
               T.PRODUCT_NAME = (SELECT DISTINCT NVL(T1.NAME, null) FROM STL_SERVICE T1 WHERE T1.SERVICE_CODE = T.PRODUCT_CODE AND T1.ACCT_MONTH = RPT_SETTLEMONTH),
               T.rateplan_name = (SELECT DISTINCT NVL(T1.description, null) FROM stl_charge_item_def T1 WHERE T1.charge_item_ref = T.rateplan_id AND T1.ACCT_MONTH = RPT_SETTLEMONTH)
         WHERE T.SETTLEMONTH = RPT_SETTLEMONTH;

        UPDATE RPT_PARTNER_DICT T
           SET T.OFFER_NAME = (SELECT DISTINCT NVL(T1.NAME, null) FROM STL_PRODUCT T1 WHERE T1.PRODUCT_CODE = T.OFFER_CODE AND T1.ACCT_MONTH = RPT_SETTLEMONTH),
               T.PRODUCT_NAME = (SELECT DISTINCT NVL(T1.NAME, null) FROM STL_SERVICE T1 WHERE T1.SERVICE_CODE = T.PRODUCT_CODE AND T1.ACCT_MONTH = RPT_SETTLEMONTH),
               T.rateplan_name = (SELECT DISTINCT NVL(T1.description, null) FROM stl_charge_item_def T1 WHERE T1.charge_item_ref = T.rateplan_id AND T1.ACCT_MONTH = RPT_SETTLEMONTH)
         WHERE T.SETTLEMONTH = RPT_SETTLEMONTH;
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        -------EBOSS出账
        RPT_RUNSITE := '6';
        SET @RPT_SQL     := 'insert into rpt_partner(settlemonth, version, order_mode, offer_code, offer_name, product_code, product_name, partner_code, partner_name, report_product_code, report_product_name, rateplan_id, ' ||
                               'rateplan_name, sign_entity, fee_flag, par_settl_amount, tax_rate, par_res_settl_rate, settlement_class, product_type, bs_type) ' ||
                         'select ''' || RPT_SETTLEMONTH || ''', ''' || G_VERSION || ''', a.order_mode, a.po_id, a.po_name, a.product_id, decode(a.one_product_id, ''111601'', ''基础口令产品'', ''''), a.partner_code, a.partner_name, a.product_id, a.product_name, a.rateplan_id, ' ||
                               'a.rateplan_name, a.contract_main, a.fee_flag, sum(a.par_settl_amount), a.tax_rate, a.par_res_settl_rate, a.settlement_class, a.product_type, a.bs_type ' ||
                          'from sync_interface_mc_' || RPT_SETTLEMONTH || ' a ' ||
                         'where a.status = 0 and pay_tag = 1 ' ||
                         'group by a.order_mode, a.po_id, a.po_name, a.product_id, a.one_product_id, a.partner_code, a.partner_name, a.product_id, a.product_name, a.rateplan_id, a.rateplan_name, ' ||
                               'a.contract_main, a.fee_flag, a.tax_rate, a.par_res_settl_rate, a.settlement_class, a.product_type, a.bs_type ' ||
                        'union all ' ||
                    	'select ''' || RPT_SETTLEMONTH || ''', ''' || G_VERSION || ''', decode(a.PROV_CODE,''000'' ,''1'',''3'') order_mode, a.PRODUCT_ID, ' ||
                    	'a.PRODUCT_NAME, a.product_id, PRODUCT_NAME, a.partner_code, a.partner_name, a.product_id, a.product_name, NULL rateplan_id, ' ||
                    	' NULL rateplan_name, NULL contract_main, 1 fee_flag, sum(a.par_settl_amount), to_char(a.tax_rate,''FM0.90''), a.PAR_RESSETTL_RATE, NULL settlement_class, ' ||
                    	'''BC2C'' product_type ,NULL  ' ||
                    	' from STLUDR.SYNC_INTERFACE_BC2C_PART a where a.status = 0 and a.ACCT_MONTH = ' || RPT_SETTLEMONTH ||
                    	' group by a.product_id, a.PRODUCT_NAME, a.partner_code, a.partner_name, a.product_id, a.product_name, a.tax_rate,
                              a.PROV_CODE, a.PAR_SETTLE_RATE, a.PAR_RESSETTL_RATE ' ||
                        'union all ' ||
                        'select ''' || RPT_SETTLEMONTH || ''', ''' || G_VERSION || ''', a.busi_mode, a.product_id, a.product_name, NULL, NULL,  b.partner_code, b.partner_name, a.product_id, a.product_name, a.rateplan_id, ' ||
                               'a.rateplan_name, a.main_contract, a.fee_flag, sum(b.par_settl_amount), a.tax_rate, b.par_res_settl_rate,NULL, NULL, NULL  ' ||
                        'from sync_interface_esp_' || RPT_SETTLEMONTH || ' a, sync_interface_esp_part_' || RPT_SETTLEMONTH || ' b ' ||
                        'where a.status = 0 and b.status = 0 and pay_tag = 0 ' ||
                        'group by a.busi_mode, a.product_id, a.product_name, b.partner_code, b.partner_name, a.product_id, a.rateplan_id, a.rateplan_name, ' ||
                               'a.main_contract, a.fee_flag, a.tax_rate, b.par_res_settl_rate ';

        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, @RPT_SQL);

        -- 星火党建
        RPT_RUNSITE := '7';

        RPT_RUNSITE := '8';
        RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表结束时间：' ||TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        commit;

        PROC_OUT:='Y';
        outReturn := 0;
        outSysError := '';

        SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
    END;
END ;;
DELIMITER ;