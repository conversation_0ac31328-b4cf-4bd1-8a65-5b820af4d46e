/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层 -受理模式1应收二次结算中间表计算
**/
DROP PROCEDURE IF EXISTS stludr.`STL_BLZQ`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "STL_BLZQ"(RPT_SETTLEMONTH IN VARCHAR2,
                                      FLAG_VERSION    IN CHAR,
                                      PROC_OUT        OUT VARCHAR2,
                                      szSysErr OUT VARCHAR2(1000),
                                      nReturn OUT NUMBER(4)   )
AS
    G_VERSION RVL_CONF_VERSION.VERSION%TYPE; -- 版本号变量
    G_RESNUM  RVL_CONF_VERSION.VERSIONNUM%TYPE; -- 版本序号
    ERRCODE   VARCHAR2(32);
    ERRMSG    VARCHAR2(2048);
    VER_TABLE VARCHAR2(64);
    RPT_TABLE VARCHAR2(64);
    RPT_RUNSITE VARCHAR2(64);
    RPT_SQL VARCHAR2(4096);
    v_proc_name  VARCHAR2(30) := 'STL_BLZQ';
BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
PROC_OUT := 'N';
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
        ROLLBACK;

        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;

BEGIN
  VER_TABLE   := 'RPT_CMCC_BLZQ';
  RPT_RUNSITE := '0';
  RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表开始时间：' ||
                 TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
  -- 中间表获取版本号
  call SETTLE_REPORT_VERSION(RPT_SETTLEMONTH,VER_TABLE,G_VERSION,G_RESNUM,FLAG_VERSION,szSysErr,nReturn);
  -- 结果表处理数据
  RPT_RUNSITE := '1';
  RPT_TABLE   := 'UR_EBOSS_' || RPT_SETTLEMONTH || '_T';
 /* RPT_SQL     := 'INSERT INTO RPT_CMCC_BLZQ(SETTLEMONTH, VERSION, INPROV_CODE, ECPROV_CODE, OUTPROV_CODE, ACCTMONTH, ' ||
                 'SIGN, PRODUCT_CODE, PRODUCT_NAME, CUSTOMERNUMBER, ACCOUNT_NUMBER, ' ||
                 'RECEIPT_NAME, BAT, CHARGE_CODE, SETTLE_SUBJ, TAXRATE, ZQNOTAXFEE, ZQTAXFEE,POSPEC_CODE,SOSPEC_CODE,PRODUCT_ORDER_ID,DEST_SOURCE,TAX_RATE_ORI)' ||
                 ' SELECT ''' || RPT_SETTLEMONTH || ''', ''' || G_VERSION ||
                 ''', T.IN_OBJECT, T.CUSTOMER_PROV, T.OUT_OBJECT, T.ORG_MONTH, ' ||
                 'T.SIGN_ENTITY, DECODE(T.PRODUCT_ORDER_ID, NULL, T.OFFER_CODE, T.PRODUCT_CODE), DECODE(T.PRODUCT_ORDER_ID, NULL, GET_PRODUCT_NAME(T.OFFER_CODE, T.PRODUCT_CODE, ''' ||
                 RPT_SETTLEMONTH ||
                 ''', ''1''), GET_PRODUCT_NAME(T.OFFER_CODE, T.PRODUCT_CODE, ''' ||
                 RPT_SETTLEMONTH ||
                 ''', ''3'')), T.CUSTOMER_CODE, GET_ACCOUNTNUMBER(T.ACCOUNT_ID), ' ||
                 'GET_RECEIPT_NAME(T.ACCOUNT_ID),  T.PHASE, T.CHARGE_CODE, T1.SETTLE_SUBJ, T.TAX_RATE, SUM(T.SETTLE_NOTAXFEE), SUM(T.SETTLE_TAXFEE), T.OFFER_CODE, T.PRODUCT_CODE,T.PRODUCT_ORDER_ID,T.DEST_SOURCE,T.TAX_RATE_ORI ' ||
                 ' FROM ' || RPT_TABLE || ' T, STL_PRODUCT_SUBJ T1  ' ||
                 ' WHERE T.OFFER_CODE = T1.PRODUCT_CODE(+) AND T.DEST_SOURCE = ''0'' AND T.ORDER_MODE = 1 AND T.SOURCE_ID = 3 ' ||
                 ' GROUP BY T.IN_OBJECT, T.CUSTOMER_PROV, T.CUSTOMER_CODE, T.OUT_OBJECT, T.ORG_MONTH, T.SIGN_ENTITY, T.PRODUCT_ORDER_ID, T.OFFER_CODE, T.PRODUCT_CODE, T.ACCOUNT_ID, T.PHASE,T.CHARGE_CODE, T1.SETTLE_SUBJ, T.TAX_RATE,T.DEST_SOURCE,T.TAX_RATE_ORI';
  */
  /*添加249 战略客户过滤*/
   set @RPT_SQL     := 'INSERT INTO RPT_CMCC_BLZQ(SETTLEMONTH, VERSION, INPROV_CODE, ECPROV_CODE, OUTPROV_CODE, ACCTMONTH, ' ||
                 'SIGN, PRODUCT_CODE, PRODUCT_NAME, CUSTOMERNUMBER, ACCOUNT_NUMBER, ' ||
                 'RECEIPT_NAME, BAT, CHARGE_CODE, SETTLE_SUBJ, TAXRATE, ZQNOTAXFEE, ZQTAXFEE,POSPEC_CODE,SOSPEC_CODE,PRODUCT_ORDER_ID,DEST_SOURCE,TAX_RATE_ORI,feetype)' ||
                 ' SELECT ''' || RPT_SETTLEMONTH || ''', ''' || G_VERSION ||
                 ''', T.IN_OBJECT, T.CUSTOMER_PROV, T.OUT_OBJECT, T.ORG_MONTH, ' ||
                 'T.SIGN_ENTITY, DECODE(T.PRODUCT_ORDER_ID, NULL, T.OFFER_CODE, T.PRODUCT_CODE), DECODE(T.PRODUCT_ORDER_ID, NULL, GET_PRODUCT_NAME(T.OFFER_CODE, T.PRODUCT_CODE, ''' ||
                 RPT_SETTLEMONTH ||
                 ''', ''1''), GET_PRODUCT_NAME(T.OFFER_CODE, T.PRODUCT_CODE, ''' ||
                 RPT_SETTLEMONTH ||
                 ''', ''3'')), T.CUSTOMER_CODE, GET_ACCOUNTNUMBER(T.ACCOUNT_ID), ' ||
                 'GET_RECEIPT_NAME(T.ACCOUNT_ID),  T.PHASE, T.CHARGE_CODE, T1.SETTLE_SUBJ, T.TAX_RATE, SUM(T.SETTLE_NOTAXFEE), SUM(T.SETTLE_TAXFEE), T.OFFER_CODE, T.PRODUCT_CODE,T.PRODUCT_ORDER_ID,T.DEST_SOURCE,T.TAX_RATE_ORI,t.feetype ' ||
                 ' FROM ' || RPT_TABLE || ' T, stlusers.STL_PRODUCT_SUBJ T1,(SELECT A.* FROM stlusers.STL_STRATEGIC_CU_CONFIG A'||
                 '   WHERE A.ISNULL = 1) T2   '||
                 ' WHERE T.OFFER_CODE = T1.PRODUCT_CODE(+) AND T2.CUCODE = T.CUSTOMER_CODE AND T.DEST_SOURCE = ''0'' AND T.ORDER_MODE = 1 AND T.SOURCE_ID = 3 and t.out_object <> ''030'' and t.in_object <> ''030''' ||
                 ' GROUP BY T.IN_OBJECT, T.CUSTOMER_PROV, T.CUSTOMER_CODE, T.OUT_OBJECT, T.ORG_MONTH, T.SIGN_ENTITY, T.PRODUCT_ORDER_ID, T.OFFER_CODE, T.PRODUCT_CODE, T.ACCOUNT_ID, T.PHASE,T.CHARGE_CODE, T1.SETTLE_SUBJ, T.TAX_RATE,T.DEST_SOURCE,T.TAX_RATE_ORI,t.feetype';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

  --新添加数据源

  /*
     ******** start

     销售支撑分摊报表
      中间表：RPT_CMCC_BLZQ
      存储过程：STL_BLZQ
      结入省统计
      金额：正
  */
  RPT_RUNSITE := '11';
  RPT_TABLE   := ' UR_EXT_' || RPT_SETTLEMONTH || '_T';
  set @RPT_SQL     :='INSERT INTO RPT_CMCC_BLZQ  (SETTLEMONTH,   VERSION,   INPROV_CODE,   ECPROV_CODE,'||
                '   OUTPROV_CODE,   ACCTMONTH,   PRODUCT_CODE,      CUSTOMERNUMBER,'||
                '   BAT,   CHARGE_CODE,   SETTLE_SUBJ,   TAXRATE,   ZQNOTAXFEE,   ZQTAXFEE,   POSPEC_CODE,'||
                '   SOSPEC_CODE,   DEST_SOURCE,   TAX_RATE_ORI, feetype)   '||
                'SELECT ''' || RPT_SETTLEMONTH || ''','''|| G_VERSION ||''' VERSION,''000'' IN_OBJECT,T.CUSTOMER_PROV,'||
                '       T.IN_OBJECT OUT_OBJECT,T.ORG_MONTH,T.OFFER_CODE,T.CUSTOMER_CODE,T.PHASE,T.CHARGE_CODE,'||
                '       T1.SETTLE_SUBJ,T.TAX_RATE, SUM(T.SETTLE_NOTAXFEE) SETTLE_NOTAXFEE , SUM(T.SETTLE_TAXFEE) '||
                '       SETTLE_TAXFEE,T.OFFER_CODE, T.PRODUCT_CODE,T.DEST_SOURCE,T.TAX_RATE, ''0'' '||
                'FROM ' || RPT_TABLE ||' T,stlusers.STL_PRODUCT_SUBJ T1, '||
                '       (SELECT A.* FROM stlusers.STL_STRATEGIC_CU_CONFIG A '||
                '        WHERE A.ISNULL = 1) T2 '||
                ' WHERE T.OFFER_CODE = T1.PRODUCT_CODE(+) AND T2.CUCODE = T.CUSTOMER_CODE AND T.ORDER_MODE = 1 and t.out_object <> ''030'' and t.in_object <> ''030'''||
                '       AND T.DEST_SOURCE = ''0'' GROUP BY T.CUSTOMER_PROV,T.IN_OBJECT,T.ORG_MONTH,T.OFFER_CODE,T.CUSTOMER_CODE, '||
                '       T.PHASE,T.CHARGE_CODE,T1.SETTLE_SUBJ,T.TAX_RATE,T.OFFER_CODE,T.PRODUCT_CODE,T.STREAM_ID,T.DEST_SOURCE,'||
                '       T.TAX_RATE';
  SELECT @RPT_SQL;
  PREPARE STMT FROM @RPT_SQL;
  EXECUTE STMT;
  DEALLOCATE PREPARE STMT;



  /*
       结出省统计
       金额：负
  */


   RPT_RUNSITE := '12';
  RPT_TABLE   := ' UR_EXT_' || RPT_SETTLEMONTH || '_T';
  set @RPT_SQL     :='INSERT INTO RPT_CMCC_BLZQ  (SETTLEMONTH,   VERSION,   INPROV_CODE,   ECPROV_CODE,'||
                '   OUTPROV_CODE,   ACCTMONTH,   PRODUCT_CODE,      CUSTOMERNUMBER,'||
                '   BAT,   CHARGE_CODE,   SETTLE_SUBJ,   TAXRATE,   ZQNOTAXFEE,   ZQTAXFEE,   POSPEC_CODE,'||
                '   SOSPEC_CODE,   DEST_SOURCE,   TAX_RATE_ORI, feetype)   '||
               'SELECT ''' || RPT_SETTLEMONTH || ''','''|| G_VERSION ||''' VERSION,''000'' IN_OBJECT,T.CUSTOMER_PROV,'||
               '       T.OUT_OBJECT OUT_OBJECT,T.ORG_MONTH,T.OFFER_CODE,T.CUSTOMER_CODE,T.PHASE,T.CHARGE_CODE,'||
               '       T1.SETTLE_SUBJ,T.TAX_RATE,-SUM(T.SETTLE_NOTAXFEE) SETTLE_NOTAXFEE,-SUM(T.SETTLE_TAXFEE) SETTLE_TAXFEE, '||
               '       T.OFFER_CODE,T.PRODUCT_CODE,T.DEST_SOURCE,T.TAX_RATE, ''0'' '||
               '  FROM ' || RPT_TABLE ||' T,stlusers.STL_PRODUCT_SUBJ T1,'||
               '       (SELECT A.* '||
               '          FROM stlusers.STL_STRATEGIC_CU_CONFIG A '||
               '         WHERE A.ISNULL = 1) T2 '||
               ' WHERE T.OFFER_CODE = T1.PRODUCT_CODE(+) AND T2.CUCODE = T.CUSTOMER_CODE  AND T.ORDER_MODE = 1 and t.out_object <> ''030'' and t.in_object <> ''030'''||
               '       AND T.DEST_SOURCE = ''0'' GROUP BY T.CUSTOMER_PROV,T.OUT_OBJECT,T.ORG_MONTH,T.OFFER_CODE, '||
               '       T.CUSTOMER_CODE,T.PHASE,T.CHARGE_CODE,T1.SETTLE_SUBJ,T.TAX_RATE,T.OFFER_CODE,T.PRODUCT_CODE, '||
               '       T.STREAM_ID,T.DEST_SOURCE,T.TAX_RATE ';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


   /*
     ******** end
  */

  RPT_RUNSITE := '2';
  RPT_SQL     := '更新表中省公司的中文名称... ...';
  UPDATE RPT_CMCC_BLZQ T
     SET T.INPROV_NAME =
         (SELECT T1.PROV_NM
            FROM STL_PROVINCE_CD T1
           WHERE T1.PROV_CD = T.INPROV_CODE),
         T.ECPROV_NAME =
         (SELECT T1.PROV_NM
            FROM STL_PROVINCE_CD T1
           WHERE T1.PROV_CD = T.ECPROV_CODE),
         T.OUTPROV_NAME =
         (SELECT T1.PROV_NM
            FROM STL_PROVINCE_CD T1
           WHERE T1.PROV_CD = T.OUTPROV_CODE)
   WHERE T.VERSION = G_VERSION
     AND T.SETTLEMONTH = RPT_SETTLEMONTH;
   call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
  RPT_RUNSITE := '3';
  RPT_SQL     := ' ... ...';
  UPDATE RPT_CMCC_BLZQ T
     SET T.SIGNAME     =
         (SELECT DISTINCT NVL(T1.DICTDESC, '')
            FROM STL_CONF_DICT T1
           WHERE TO_DATE(RPT_SETTLEMONTH, 'yyyyMM') BETWEEN
                 TO_DATE(TO_CHAR(T1.EFFECTIVE_DATE, 'yyyyMM'), 'yyyyMM') AND
                 TO_DATE(TO_CHAR(T1.EXPIRY_DATE, 'yyyyMM'), 'yyyyMM')
             AND T1.DICTVALUE = T.SIGN
             AND T1.ITEM = 'ENTITY'),
         T.CUSTOMERNAME = (SELECT NVL(T1.FIRST_NAME, '') FROM stlusers.STL_CUSTOMER T1 WHERE T.ACCTMONTH BETWEEN DATE_FORMAT(T1.effective_date, '%Y%m') AND DATE_FORMAT(T1.expiry_date, '%Y%m') AND T1.CUSTOMER_CODE = T.CUSTOMERNUMBER)
   WHERE T.VERSION = G_VERSION
     AND T.SETTLEMONTH = RPT_SETTLEMONTH;
   call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
  RPT_RUNSITE := '4';
  RPT_SQL     := '更新表中产品和商品的中文名称... ...';
  UPDATE RPT_CMCC_BLZQ T
     SET T.POSPEC_NAME =
         (SELECT DISTINCT NVL(T1.NAME, '')
            FROM STL_PRODUCT T1
           WHERE T1.PRODUCT_CODE = T.POSPEC_CODE
             AND T1.ACCT_MONTH = RPT_SETTLEMONTH),
         T.SOSPEC_NAME =
         (SELECT DISTINCT NVL(T1.NAME, '')
            FROM STL_SERVICE T1
           WHERE T1.SERVICE_CODE = T.SOSPEC_CODE
             AND T1.ACCT_MONTH = RPT_SETTLEMONTH)
   WHERE T.VERSION = G_VERSION
     AND T.SETTLEMONTH = RPT_SETTLEMONTH;
   call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
  RPT_RUNSITE := '5';
  RPT_SQL     := '更新表费项分类和费项的中文名称（201811后不再更新费项分类）... ...';
  UPDATE RPT_CMCC_BLZQ T
     SET T.CHARGE_NAME =
         (SELECT DISTINCT NVL(T1.DESCRIPTION, '')
            FROM STL_CHARGE_ITEM_DEF T1
           WHERE T1.CHARGE_ITEM_REF = T.CHARGE_CODE
             AND T1.ACCT_MONTH = RPT_SETTLEMONTH)/*,
         T.FEETYPE = CASE
                       WHEN T.CHARGE_CODE > 0 AND T.CHARGE_CODE <= 50 THEN
                        '0'
                       WHEN T.CHARGE_CODE >= 1000 AND T.CHARGE_CODE < 5000 THEN
                        '0'
                       WHEN T.CHARGE_CODE > 50 AND T.CHARGE_CODE <= 100 THEN
                        '1'
                       WHEN T.CHARGE_CODE >= 5000 AND T.CHARGE_CODE < 10000 THEN
                        '1'
                     END*/
   WHERE T.VERSION = G_VERSION
     AND T.SETTLEMONTH = RPT_SETTLEMONTH;
   call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
  RPT_RUNSITE := '6';
  RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表结束时间：' ||
                 TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
  PROC_OUT := 'Y';
  COMMIT;
  szSysErr := 'OK';
  nReturn := 0;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
  SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;

  END;
END ;;
DELIMITER ;