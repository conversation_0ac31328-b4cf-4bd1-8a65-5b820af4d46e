/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层-小数点位数还原-合作伙伴报表中间表小数点位数还原
**/
DROP PROCEDURE IF EXISTS stludr.`STL_DIGIT_RPT_PARTNER`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "STL_DIGIT_RPT_PARTNER"(RPT_SETTLEMONTH IN VARCHAR2,
                                                   BAT IN VARCHAR2,
                                                   PROC_OUT OUT VARCHAR2,
                                                   szSysErr OUT VARCHAR2(1000),
                                                   nReturn OUT NUMBER(4) )
AS
    ERRCODE   VARCHAR2(32);
    ERRMSG    VARCHAR2(2048);
    VER_TABLE VARCHAR2(64);
    RPT_TABLE VARCHAR2(64);
    RPT_RUNSITE VARCHAR2(64);
    RPT_SQL VARCHAR2(4096);
    v_proc_name VARCHAR2(30) := 'STL_DIGIT_RPT_PARTNER';
BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
        ROLLBACK;

        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;
  BEGIN
    VER_TABLE   := 'RPT_PARTNER';
    RPT_RUNSITE := '3';
--   	RPT_SQL   :='';
  	CASE BAT
	WHEN '2' THEN
    	set @RPT_SQL   :='UPDATE RPT_PARTNER T SET T.PAR_SETTL_AMOUNT = T.PAR_SETTL_AMOUNT /NVL((SELECT T1.PRODUCT_DIGIT FROM STLUDR.STL_CONFIG_DIGIT T1 '
    			|| 'WHERE T1.POSPEC_NUMBER = T.OFFER_CODE AND T1.PRODUCT_NUMBER = T.PRODUCT_CODE ),1) WHERE T.SETTLEMONTH = '''|| RPT_SETTLEMONTH ||''' ';
    SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

       ELSE RPT_SQL := NULL;
    END CASE;
    PROC_OUT := 'Y';
    COMMIT;

    szSysErr := 'OK';
    nReturn := 0;

    SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;

  END;
END ;;
DELIMITER ;