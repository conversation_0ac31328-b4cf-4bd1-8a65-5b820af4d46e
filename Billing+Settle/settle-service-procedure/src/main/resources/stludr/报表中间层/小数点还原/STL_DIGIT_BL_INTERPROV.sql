/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层-小数点位数还原-受理模式3应收报表中间表小数点位数还原
**/
DROP PROCEDURE IF EXISTS stludr.`STL_DIGIT_BL_INTERPROV`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "STL_DIGIT_BL_INTERPROV"(RPT_SETTLEMONTH IN VARCHAR2,
                                                    BAT IN VARCHAR2,
                                                    PROC_OUT OUT VARCHAR2,
                                                    szSysErr OUT VARCHAR2(1000),
                                                    nReturn OUT NUMBER(4) )
AS
    ERRCODE   VARCHAR2(32);
    ERRMSG    VARCHAR2(2048);
    VER_TABLE VARCHAR2(64);
    RPT_TABLE VARCHAR2(64);
    RPT_RUNSITE VARCHAR2(64);
    RPT_SQL VARCHAR2(4096);
    VERS VARCHAR2(32);
    v_proc_name VARCHAR2(30) := 'STL_DIGIT_BL_INTERPROV';
BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
        ROLLBACK;

        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;
  BEGIN
    VER_TABLE   := 'RPT_BL_INTERPROV';
    RPT_RUNSITE := '5';

    SELECT NVL(MAX(T1.VERSION),1) FROM STLUDR.RPT_BL_INTERPROV T1 WHERE T1.SETTLEMONTH =  RPT_SETTLEMONTH INTO  VERS ;

    SELECT 'KEY VERSION VALUE =' || VERS;

    set @RPT_SQL   :='UPDATE RPT_BL_INTERPROV T SET T.SETTNOTAXFEE = T.SETTNOTAXFEE /NVL((SELECT T1.PRODUCT_DIGIT FROM STLUDR.STL_CONFIG_DIGIT T1 '
    			|| 'WHERE T1.POSPEC_NUMBER = T.POSPEC_CODE AND T1.PRODUCT_NUMBER = T.PRODUCT_CODE ),1) WHERE T.SETTLEMONTH = '''|| RPT_SETTLEMONTH ||''' '
    			|| 'AND T.VERSION = '''|| VERS ||''' ';
    SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    PROC_OUT := 'Y';
    COMMIT;

    szSysErr := 'OK';
    nReturn := 0;

    SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;

  END;
END ;;
DELIMITER ;