/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层-销售支撑协同报表-销售支撑协同报表（总表）数据生成
**/
DROP PROCEDURE IF EXISTS stludr.`STL_ALL`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "STL_ALL"(RPT_SETTLEMONTH IN VARCHAR2,
                                    FLAG_VERSION IN CHAR,
                                    PROC_OUT OUT VARCHAR2,
                                    szSysErr OUT VARCHAR2(1000),
                                    nReturn OUT NUMBER(4)   )
AS
    G_VERSION RVL_CONF_VERSION.VERSION%TYPE; -- 版本号变量
    G_RESNUM  RVL_CONF_VERSION.VERSIONNUM%TYPE; -- 版本序号
    ERRCODE   VARCHAR2(32);
    ERRMSG    VARCHAR2(2048);
    VER_TABLE VARCHAR2(64);
    RPT_TABLE VARCHAR2(64);
    RPT_RUNSITE VARCHAR2(64);
    RPT_SQL VARCHAR2(4096);
    v_proc_name VARCHAR2(30) := 'STL_ALL';

BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
PROC_OUT := 'N';
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
        ROLLBACK;

        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;
  BEGIN
    VER_TABLE   := 'RPT_ORIG_ALL';
    RPT_RUNSITE := '0';
    RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表开始时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
    -- 中间表获取版本号
    call SETTLE_REPORT_VERSION(RPT_SETTLEMONTH, VER_TABLE, G_VERSION, G_RESNUM, FLAG_VERSION,szSysErr,nReturn);
    -- 结果表处理数据
    RPT_TABLE   := 'UR_RECV_' || RPT_SETTLEMONTH || '_T';
    RPT_RUNSITE := '1';
    RPT_SQL := 'INSERT INTO RPT_ORIG_ALL(SETTLEMONTH,VERSION,CUSTOMERNUMBER,POSPECNUMBER,HOME_PROV,HOME_PROV_NM,ORGFEE,INCOMINGPROV,INCOMINGPROV_NM,SETTLEFEE) ';
    -- 插入跨省数据专线、跨省互联网专线、呼叫中心直联（还原EC归属省总收入、配合省收入）
    set @RPT_SQL := RPT_SQL || 'SELECT '''||RPT_SETTLEMONTH||''','''||G_VERSION||''',D.CUSTOMER_CODE,D.OFFER_CODE, D.CUSTOMER_PROV,P1.PROV_NM,D.ORGFEE,D.IN_OBJECT,P2.PROV_NM,D.SETTLEFEE '
                       || ' FROM (SELECT AA.CUSTOMER_CODE,AA.OFFER_CODE,AA.CUSTOMER_PROV,AA.ORGFEE,BB.IN_OBJECT,SUM(BB.SETTLEFEE) SETTLEFEE '
                       || '        FROM (SELECT T.CUSTOMER_CODE,T.OFFER_CODE,T.CUSTOMER_PROV,SUM(T.ORGFEE) ORGFEE '
                       || '               FROM (SELECT TT.CUSTOMER_CODE,TT.OFFER_CODE,TT.CUSTOMER_PROV,TT.PRODUCT_ORDER_ID,TT.CHARGE_CODE,MAX(TT.CHARGE) ORGFEE'
                       || '                      FROM '||RPT_TABLE||' TT '
                       || '                     WHERE TT.ORDER_MODE = ''1'' AND TT.DEST_SOURCE = ''0'' AND TT.OFFER_CODE IN (''01011301'', ''01011304'', ''1101001'', ''01011306'')'
                       || '                       and tt.out_object <> ''030'' and tt.in_object <> ''030'''
                       || '                     GROUP BY TT.CUSTOMER_CODE,TT.OFFER_CODE,TT.CUSTOMER_PROV,TT.PRODUCT_ORDER_ID,TT.CHARGE_CODE) T'
                       || '               GROUP BY T.CUSTOMER_CODE, T.OFFER_CODE, T.CUSTOMER_PROV) AA'
                       || '        LEFT JOIN (SELECT PT.CUSTOMER_CODE,PT.OFFER_CODE,PT.CUSTOMER_PROV,PT.IN_OBJECT, SUM(PT.SETTLE_NOTAXFEE + PT.SETTLE_TAXFEE) SETTLEFEE'
                       || '                    FROM '||RPT_TABLE||' PT'
                       || '                   WHERE PT.ORDER_MODE = ''1'' AND PT.DEST_SOURCE = ''0'' AND PT.CUSTOMER_PROV <> PT.IN_OBJECT AND PT.OFFER_CODE IN (''01011301'', ''01011304'', ''1101001'', ''01011306'')'
                       || '                     and pt.out_object <> ''030'' and pt.in_object <> ''030'''
                       || '                   GROUP BY PT.CUSTOMER_CODE, PT.OFFER_CODE,PT.CUSTOMER_PROV,PT.IN_OBJECT) BB'
                       || '        ON AA.CUSTOMER_PROV = BB.CUSTOMER_PROV AND AA.CUSTOMER_CODE =BB.CUSTOMER_CODE AND AA.OFFER_CODE = BB.OFFER_CODE'
                       || ' GROUP BY AA.CUSTOMER_CODE,AA.OFFER_CODE,AA.CUSTOMER_PROV,AA.ORGFEE,BB.IN_OBJECT) D,STL_PROVINCE_CD P1,STL_PROVINCE_CD P2 '
                       || 'WHERE D.CUSTOMER_PROV = P1.PROV_CD AND D.IN_OBJECT = P2.PROV_CD(+)';
    SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

    RPT_RUNSITE := '2';
    RPT_SQL := 'INSERT INTO RPT_ORIG_ALL(SETTLEMONTH,VERSION,CUSTOMERNUMBER,POSPECNUMBER,HOME_PROV,HOME_PROV_NM,ORGFEE,INCOMINGPROV,INCOMINGPROV_NM,SETTLEFEE)';
    -- 跨国数据专线（铁通承建）、全网IDC（机房在总部）（从自有产品表还原EC归属省总收入）
    set @RPT_SQL := RPT_SQL || ' SELECT '''||RPT_SETTLEMONTH||''','''||G_VERSION||''',T.CUSTOMER_CODE,T.OFFER_CODE,T.CUSTOMER_PROV,PROV.PROV_NM,SUM(T.ORGFEE),'''','''',NULL'
                       || ' FROM (SELECT TT.CUSTOMER_CODE,TT.OFFER_CODE,TT.CUSTOMER_PROV,TT.PRODUCT_ORDER_ID,TT.CHARGE_CODE,MAX(TT.CHARGE) ORGFEE'
                               || ' FROM '||RPT_TABLE||' TT WHERE TT.ORDER_MODE = ''1'' AND TT.DEST_SOURCE = ''1'' AND TT.OFFER_CODE IN (''01011305'', ''1010403'')'
                               || ' GROUP BY TT.CUSTOMER_CODE,TT.OFFER_CODE,TT.CUSTOMER_PROV,TT.PRODUCT_ORDER_ID,TT.CHARGE_CODE) T,STL_PROVINCE_CD PROV'
                       || ' WHERE T.CUSTOMER_PROV = PROV.PROV_CD GROUP BY T.CUSTOMER_CODE, T.OFFER_CODE, T.CUSTOMER_PROV, PROV.PROV_NM';
    SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

    RPT_RUNSITE := '3';
    RPT_SQL := 'INSERT INTO RPT_ORIG_ALL(SETTLEMONTH,VERSION,CUSTOMERNUMBER,POSPECNUMBER,HOME_PROV,HOME_PROV_NM,ORGFEE,INCOMINGPROV,INCOMINGPROV_NM,SETTLEFEE)';
    -- 全网IDC（机房在省）、移动400、统一充值（还原EC归属省总收入）
    set @RPT_SQL := RPT_SQL || ' SELECT '''||RPT_SETTLEMONTH||''','''||G_VERSION||''',T.CUSTOMER_CODE,T.OFFER_CODE,T.CUSTOMER_PROV,PROV.PROV_NM,SUM(T.ORGFEE) ORGFEE,'''','''',NULL'
                       || '  FROM (  SELECT TT.CUSTOMER_CODE,TT.OFFER_CODE,TT.CUSTOMER_PROV,TT.PRODUCT_ORDER_ID,TT.CHARGE_CODE,MAX(TT.CHARGE) ORGFEE'
                                || '  FROM '||RPT_TABLE||' TT'
                                || ' WHERE TT.ORDER_MODE = ''1'' AND TT.DEST_SOURCE = ''0'' AND TT.OFFER_CODE IN (''1010403'', ''01114001'', ''010150001'')'
                                || ' GROUP BY TT.CUSTOMER_CODE,TT.OFFER_CODE,TT.CUSTOMER_PROV,TT.PRODUCT_ORDER_ID,TT.CHARGE_CODE) T,STL_PROVINCE_CD PROV'
                       || ' WHERE T.CUSTOMER_PROV = PROV.PROV_CD'
                       || ' GROUP BY T.CUSTOMER_CODE, T.OFFER_CODE, T.CUSTOMER_PROV, PROV.PROV_NM';
    SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

    RPT_RUNSITE := '4';
    RPT_SQL := 'INSERT INTO RPT_ORIG_ALL(SETTLEMONTH,VERSION,CUSTOMERNUMBER,POSPECNUMBER,HOME_PROV,HOME_PROV_NM,ORGFEE,INCOMINGPROV,INCOMINGPROV_NM,SETTLEFEE) ';
    -- 集团客户一点支付(1322)、M2M通道型（还原EC归属省总收入、成员归属省收入）
    set @RPT_SQL := RPT_SQL || 'SELECT '''||RPT_SETTLEMONTH||''','''||G_VERSION||''',D.CUSTOMER_CODE,D.OFFER_CODE,D.CUSTOMER_PROV,P1.PROV_NM,D.ORGFEE,D.IN_OBJECT,P2.PROV_NM,D.SETTLEFEE'
                       || ' FROM (SELECT AA.CUSTOMER_CODE,AA.OFFER_CODE,AA.CUSTOMER_PROV,AA.ORGFEE,BB.IN_OBJECT,SUM(SETTLEFEE) SETTLEFEE'
                       || '        FROM (SELECT TT.CUSTOMER_CODE,TT.OFFER_CODE,TT.CUSTOMER_PROV,SUM(TT.CHARGE) ORGFEE'
                       || '               FROM '||RPT_TABLE||' TT'
                       || '              WHERE TT.ORDER_MODE = ''1'' AND TT.DEST_SOURCE = ''0'' AND TT.OFFER_CODE IN (''*********'', ''*********'')'
                       || '              GROUP BY TT.CUSTOMER_CODE, TT.OFFER_CODE, TT.CUSTOMER_PROV) AA'
                       || '       LEFT JOIN (SELECT PT.CUSTOMER_CODE,PT.OFFER_CODE,PT.CUSTOMER_PROV,PT.IN_OBJECT,SUM(PT.SETTLE_NOTAXFEE + PT.SETTLE_TAXFEE) SETTLEFEE'
                       || '                   FROM '||RPT_TABLE||' PT'
                       || '                  WHERE PT.ORDER_MODE = ''1'' AND PT.DEST_SOURCE = ''0'' AND PT.CUSTOMER_PROV <> PT.IN_OBJECT AND PT.OFFER_CODE IN (''*********'', ''*********'')'
                       || '                  GROUP BY PT.CUSTOMER_CODE,PT.OFFER_CODE,PT.CUSTOMER_PROV,PT.IN_OBJECT) BB '
                       || '       ON AA.CUSTOMER_PROV = BB.CUSTOMER_PROV AND AA.CUSTOMER_CODE = BB.CUSTOMER_CODE AND AA.OFFER_CODE = BB.OFFER_CODE'
                       || '       GROUP BY AA.CUSTOMER_CODE,AA.OFFER_CODE,AA.CUSTOMER_PROV,AA.ORGFEE,BB.IN_OBJECT) D,STL_PROVINCE_CD P1,STL_PROVINCE_CD P2 '
                       || 'WHERE D.CUSTOMER_PROV = P1.PROV_CD AND D.IN_OBJECT = P2.PROV_CD(+)';
    SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

    RPT_RUNSITE := '5';
    RPT_SQL := 'INSERT INTO RPT_ORIG_ALL(SETTLEMONTH,VERSION,CUSTOMERNUMBER,POSPECNUMBER,HOME_PROV,HOME_PROV_NM,ORGFEE,INCOMINGPROV,INCOMINGPROV_NM,SETTLEFEE) ';
    -- 流量统付（特别处理，按orgbid分组）
    set @RPT_SQL := RPT_SQL || 'SELECT '''||RPT_SETTLEMONTH||''','''||G_VERSION||''',T.CUSTOMER_CODE,T.OFFER_CODE,T.CUSTOMER_PROV,PROV.PROV_NM,SUM(ORGFEE),'''','''',NULL'
                       || ' FROM (SELECT TT.STREAM_ID,TT.CUSTOMER_CODE,TT.OFFER_CODE,TT.CUSTOMER_PROV,MAX(TT.CHARGE) ORGFEE'
                       || '        FROM '||RPT_TABLE||' TT'
                       || '       WHERE TT.DEST_SOURCE = ''0'' AND TT.PRODUCT_CODE IN (SELECT PRODUCT_CODE FROM STL_CONFIG_REPORT WHERE TYPE_NM = ''GPRSDOM'') AND TT.ORDER_MODE = ''1'''
                       || '       GROUP BY TT.STREAM_ID,TT.CUSTOMER_CODE,TT.OFFER_CODE,TT.CUSTOMER_PROV) T,STL_PROVINCE_CD PROV '
                       || 'WHERE T.CUSTOMER_PROV = PROV.PROV_CD AND T.ORGFEE <> 0 '
                       || 'GROUP BY T.CUSTOMER_CODE, T.OFFER_CODE, T.CUSTOMER_PROV, PROV.PROV_NM';
    SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

    RPT_RUNSITE := '6';
    RPT_SQL := 'INSERT INTO RPT_ORIG_ALL(SETTLEMONTH,VERSION,CUSTOMERNUMBER,POSPECNUMBER,HOME_PROV,HOME_PROV_NM,ORGFEE,INCOMINGPROV,INCOMINGPROV_NM,SETTLEFEE) ';
    -- 集团短彩信
    set @RPT_SQL := RPT_SQL  || ' SELECT '''||RPT_SETTLEMONTH||''','''||G_VERSION||''',T.CUSTOMER_CODE,T.OFFER_CODE,T.CUSTOMER_PROV,PROV.PROV_NM,ORGFEE,'''','''',NULL   '
                        || '   FROM (SELECT TT.CUSTOMER_CODE,TT.OFFER_CODE,TT.CUSTOMER_PROV,SUM(TT.CHARGE) ORGFEE    '
                        || '           FROM (SELECT T.STREAM_ID,T.CUSTOMER_CODE,T.OFFER_CODE,T.CUSTOMER_PROV,MAX(T.CHARGE) AS CHARGE   '
                        || '                   FROM '||RPT_TABLE||' T    '
                        || '                  WHERE T.DEST_SOURCE = ''0'' AND T.OFFER_CODE IN (''010101001'',''010101003'',''010101004'',''010101005'',''010101009'',''010105003'',''010105004'',''010111001'') AND T.ORDER_MODE = ''1'''
                        || '                  GROUP BY T.STREAM_ID,T.CUSTOMER_CODE,T.OFFER_CODE,T.CUSTOMER_PROV) TT   '
                        || '          GROUP BY TT.CUSTOMER_CODE, TT.OFFER_CODE, TT.CUSTOMER_PROV) T,  '
                        || '        STL_PROVINCE_CD PROV     '
                        || '  WHERE T.CUSTOMER_PROV = PROV.PROV_CD AND T.ORGFEE <> 0   ';
    SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

    RPT_RUNSITE := '7';
    RPT_SQL := 'INSERT INTO RPT_ORIG_ALL(SETTLEMONTH,VERSION,CUSTOMERNUMBER,POSPECNUMBER,HOME_PROV,HOME_PROV_NM,ORGFEE,INCOMINGPROV,INCOMINGPROV_NM,SETTLEFEE) ';
    -- 云MAS
    set @RPT_SQL := RPT_SQL || 'SELECT '''||RPT_SETTLEMONTH||''','''||G_VERSION||''',D.CUSTOMER_CODE,D.OFFER_CODE, D.CUSTOMER_PROV,P1.PROV_NM,D.ORGFEE,D.IN_OBJECT,P2.PROV_NM,D.SETTLEFEE '
                       || ' FROM (SELECT AA.CUSTOMER_CODE,AA.OFFER_CODE,AA.CUSTOMER_PROV,AA.ORGFEE,BB.IN_OBJECT,SUM(BB.SETTLEFEE) SETTLEFEE '
                       || '        FROM (SELECT T.CUSTOMER_CODE,T.OFFER_CODE,T.CUSTOMER_PROV,SUM(T.ORGFEE) ORGFEE '
                       || '               FROM (SELECT TT.CUSTOMER_CODE,TT.OFFER_CODE,TT.CUSTOMER_PROV,DECODE(TT.PRODUCT_ORDER_ID,NULL,TT.OFFER_ORDER_ID,TT.PRODUCT_ORDER_ID),TT.CHARGE_CODE,MAX(TT.CHARGE) ORGFEE'
                       || '                      FROM '||RPT_TABLE||' TT '
                       || '                     WHERE TT.ORDER_MODE = ''1'' AND TT.DEST_SOURCE IN (''0'', ''1'') AND TT.OFFER_CODE IN (''*********'', ''*********'', ''*********'',''*********'', ''*********'', ''*********'',''*********'')'
                       || '                     GROUP BY TT.CUSTOMER_CODE,TT.OFFER_CODE,TT.CUSTOMER_PROV,DECODE(TT.PRODUCT_ORDER_ID,NULL,TT.OFFER_ORDER_ID,TT.PRODUCT_ORDER_ID),TT.CHARGE_CODE) T'
                       || '               GROUP BY T.CUSTOMER_CODE, T.OFFER_CODE, T.CUSTOMER_PROV) AA'
                       || '        LEFT JOIN (SELECT PT.CUSTOMER_CODE,PT.OFFER_CODE,PT.CUSTOMER_PROV,PT.IN_OBJECT, SUM(PT.SETTLE_NOTAXFEE + PT.SETTLE_TAXFEE) SETTLEFEE'
                       || '                    FROM '||RPT_TABLE||' PT'
                       || '                   WHERE PT.ORDER_MODE = ''1'' AND PT.DEST_SOURCE IN (''0'', ''1'') AND PT.CUSTOMER_PROV <> PT.IN_OBJECT AND PT.OFFER_CODE IN (''*********'', ''*********'', ''*********'',''*********'', ''*********'', ''*********'',''*********'')'
                       || '                   GROUP BY PT.CUSTOMER_CODE, PT.OFFER_CODE,PT.CUSTOMER_PROV,PT.IN_OBJECT) BB'
                       || '        ON AA.CUSTOMER_PROV = BB.CUSTOMER_PROV AND AA.CUSTOMER_CODE =BB.CUSTOMER_CODE AND AA.OFFER_CODE = BB.OFFER_CODE'
                       || ' GROUP BY AA.CUSTOMER_CODE,AA.OFFER_CODE,AA.CUSTOMER_PROV,AA.ORGFEE,BB.IN_OBJECT) D,STL_PROVINCE_CD P1,STL_PROVINCE_CD P2 '
                       || 'WHERE D.CUSTOMER_PROV = P1.PROV_CD AND D.IN_OBJECT = P2.PROV_CD(+)';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

    RPT_RUNSITE := '8';
    RPT_SQL := '更新数据中的客户名称以及商品名称... ...';
    UPDATE RPT_ORIG_ALL T
           SET T.CUSTOMERNAME = (SELECT NVL(T1.FIRST_NAME, '') FROM stlusers.STL_CUSTOMER T1 WHERE RPT_SETTLEMONTH BETWEEN DATE_FORMAT(T1.effective_date, '%Y%m') AND DATE_FORMAT(T1.expiry_date, '%Y%m') AND T1.CUSTOMER_CODE = T.CUSTOMERNUMBER),
               T.POSPECNAME   = (SELECT DISTINCT NVL(T1.NAME, '') FROM STL_PRODUCT T1 WHERE T1.PRODUCT_CODE = T.POSPECNUMBER AND T1.ACCT_MONTH = RPT_SETTLEMONTH)
    WHERE T.VERSION=G_VERSION AND T.SETTLEMONTH=RPT_SETTLEMONTH;

    RPT_RUNSITE := '9';
    RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
    PROC_OUT := 'Y';
    COMMIT;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
    szSysErr := 'OK';
    nReturn := 0;

    SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;


  END;
END ;;
DELIMITER ;