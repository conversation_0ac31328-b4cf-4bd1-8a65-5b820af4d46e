/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层 -销售支撑协同报表（呼叫中心直联）数据生成
**/
DROP PROCEDURE IF EXISTS stludr.`STL_CALL_CENTER`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "STL_CALL_CENTER"(RPT_SETTLEMONTH IN VARCHAR2,
                                            FLAG_VERSION IN CHAR,
                                            PROC_OUT OUT VARCHAR2,
                                            szSysErr OUT VARCHAR2(1000),
                                            nReturn OUT NUMBER(4)   )
AS
    G_VERSION RVL_CONF_VERSION.VERSION%TYPE; -- 版本号变量
    G_RESNUM  RVL_CONF_VERSION.VERSIONNUM%TYPE; -- 版本序号
    ERRCODE   VARCHAR2(32);
    ERRMSG    VARCHAR2(2048);
    VER_TABLE VARCHAR2(64);
    RPT_TABLE VARCHAR2(64);
    RPT_RUNSITE VARCHAR2(64);
    RPT_SQL VARCHAR2(4096);
    v_proc_name VARCHAR2(30) := 'STL_CALL_CENTER';

    --呼叫中心直连
    PRODUCT_CODE_CALL_CENTER  VARCHAR2(32) := '1101001';

BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        PROC_OUT := 'N';
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
        ROLLBACK;

        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;
  BEGIN
    VER_TABLE   := 'RPT_ORIG_CALL_CENTER';
    RPT_RUNSITE := '0';
    RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表开始时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
    -- 中间表获取版本号
    CALL SETTLE_REPORT_VERSION(RPT_SETTLEMONTH, VER_TABLE, G_VERSION, G_RESNUM, FLAG_VERSION,szSysErr,nReturn);
    -- 结果表处理数据
    RPT_TABLE   := 'UR_RECV_' || RPT_SETTLEMONTH || '_T';
    RPT_RUNSITE := '1';
    RPT_SQL     := 'INSERT INTO RPT_ORIG_CALL_CENTER(SETTLEMONTH,VERSION,OMDATE,BUSNAME,CUSTOMERNAME,CUSTOMERNUMBER,ORID,FLAG,CUSATTRPROV,SSNUM,BUSACCPROV,BAMNAME) ';
    set @RPT_SQL     := RPT_SQL || 'SELECT '''||RPT_SETTLEMONTH||''','''||G_VERSION||''',TO_CHAR(T3.START_TIME, ''yyyyMMdd''),T2.BUSNAME,T2.CUSTOMERNAME,T2.CUSTOMERNUMBER,T1.PRODUCT_ORDER_ID,T2.FLAG,T2.CUSATTRPROV,T2.SSNUM,T2.BUSACCPROV,T2.BAMNAME '
                           || ' FROM (SELECT TT.PRODUCT_ORDER_ID, TT.OFFER_ORDER_ID FROM '||RPT_TABLE||' TT'
                           || '       WHERE (TT.CHARGE_CODE > 0 AND TT.CHARGE_CODE <= 50 OR TT.CHARGE_CODE >= 1000 AND TT.CHARGE_CODE <= 4999) AND TT.ORDER_MODE = ''1'' AND TT.DEST_SOURCE = ''0'' AND TT.OFFER_CODE = '''||PRODUCT_CODE_CALL_CENTER||''' and tt.out_object <> ''030'' and tt.in_object <> ''030'') T1 '
                           || 'LEFT JOIN (SELECT * FROM stlusers.STL_ORIG_CALL_CENTER WHERE SETTLEMONTH = '''||RPT_SETTLEMONTH||''' AND PARTID = SUBSTR('''||RPT_SETTLEMONTH||''', 5)) T2 ON T1.PRODUCT_ORDER_ID = T2.ORID '
                           || 'LEFT JOIN (SELECT PROD_ORDER_ID,ORDER_ID,MIN(EFFECTIVE_DATE) AS START_TIME FROM stlusers.STL_SERV_BIZ_CODE GROUP BY PROD_ORDER_ID, ORDER_ID) T3 ON T1.PRODUCT_ORDER_ID = T3.ORDER_ID AND T1.OFFER_ORDER_ID = T3.PROD_ORDER_ID '
                           || 'GROUP BY T3.START_TIME,T2.BUSNAME,T2.CUSTOMERNAME,T2.CUSTOMERNUMBER,T1.PRODUCT_ORDER_ID,T2.FLAG,T2.CUSATTRPROV,T2.SSNUM,T2.BUSACCPROV,T2.BAMNAME';
    SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

    RPT_RUNSITE := '2';
    RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
    PROC_OUT := 'Y';
    COMMIT;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
    szSysErr := 'OK';
    nReturn := 0;

    SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;


  END;
END ;;
DELIMITER ;