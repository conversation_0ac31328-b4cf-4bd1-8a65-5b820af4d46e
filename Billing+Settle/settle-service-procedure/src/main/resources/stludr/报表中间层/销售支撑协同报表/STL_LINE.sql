/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层 -销售支撑协同报表-（跨省互联网专线）数据生成
**/
DROP PROCEDURE IF EXISTS stludr.`STL_LINE`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "STL_LINE"(RPT_SETTLEMONTH IN VARCHAR2,
                                        FLAG_VERSION IN CHAR,
                                        PROC_OUT OUT VARCHAR2,
                                        szSysErr OUT VARCHAR2(1000),
                                        nReturn OUT NUMBER(4)   )
AS
    G_VERSION RVL_CONF_VERSION.VERSION%TYPE; -- 版本号变量
    G_RESNUM  RVL_CONF_VERSION.VERSIONNUM%TYPE; -- 版本序号
    ERRCODE   VARCHAR2(32);
    ERRMSG    VARCHAR2(2048);
    VER_TABLE VARCHAR2(64);
    RPT_TABLE VARCHAR2(64);
    RPT_RUNSITE VARCHAR2(64);
    RPT_SQL VARCHAR2(4096);
    v_proc_name VARCHAR2(30) := 'STL_LINE';

    -- network_fee       constant varchar2(2) := '01'; --通讯费
    FEATURES_FEE       VARCHAR2(2) := '02'; --功能费

    INSTALL_DEBUG_FEE  VARCHAR2(2) := '08'; --安装调测费

    PRODUCT_CODE_LINE         VARCHAR2(32) := '01011301'; --数据专线
    PRODUCT_CODE_LINE2        VARCHAR2(32) := '01011306'; --数据专线
BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        PROC_OUT := 'N';
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
        ROLLBACK;

        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;
  BEGIN
    VER_TABLE   := 'RPT_ORIG_LINE';
    RPT_RUNSITE := '0';
    RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表开始时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
    -- 中间表获取版本号
    call SETTLE_REPORT_VERSION(RPT_SETTLEMONTH, VER_TABLE, G_VERSION, G_RESNUM, FLAG_VERSION,szSysErr,nReturn);
    -- 结果表处理数据
    RPT_TABLE   := 'UR_RECV_' || RPT_SETTLEMONTH || '_T';
    RPT_RUNSITE := '1';
    RPT_SQL := 'INSERT INTO RPT_ORIG_LINE(SETTLEMONTH,VERSION,OMDATE,BUSNAME,CUSTOMERNAME,CUSTOMERNUMBER,ORID,FLAG,CUSATTRPROV,WIRECODE,INDEFEE,INDEFEEA,INDEFEEZ,MONFEAFEE,MONFEAFEEA,MONFEAFEEZ,COMPA,PROVA,AREAA,CMNAMEA,COMPZ,PROVZ,AREAZ,CMNAMEZ) ';
    set @RPT_SQL := RPT_SQL  || 'SELECT '''||RPT_SETTLEMONTH||''','''||G_VERSION||''',T.OMDATE,T.BUSNAME,T.CUSTOMERNAME,T.CUSTOMERNUMBER,T.ORID,T.FLAG,T.CUSATTRPROV,T.WIRECODE,SUM(T.INDEFEE),SUM(T.INDEFEEA),SUM(INDEFEEZ), SUM(T.MONFEAFEE),SUM(T.MONFEAFEEA),SUM(T.MONFEAFEEZ),T.COMPA,T.PROVA,T.AREAA,T.CMNAMEA,T.COMPZ,T.PROVZ,T.AREAZ,T.CMNAMEZ                               '
                        || ' FROM (SELECT TO_CHAR(T3.START_TIME, ''yyyyMMdd'') AS OMDATE,T2.BUSNAME,T2.CUSTOMERNAME,T2.CUSTOMERNUMBER,T1.PRODUCT_ORDER_ID AS ORID,T2.FLAG,T2.CUSATTRPROV,T2.WIRECODE,                                                                                                                               '
                        || '              DECODE(T1.CHARGE_CODE,'''||INSTALL_DEBUG_FEE||''',(T1.SETTLE_NOTAXFEE + T1.SETTLE_TAXFEE),0) AS INDEFEE,                                                                                                                                                                                          '
                        || '              CASE WHEN T1.CHARGE_CODE = '''||INSTALL_DEBUG_FEE||''' AND T1.IN_OBJECT = T2.COMPA THEN (T1.SETTLE_NOTAXFEE + T1.SETTLE_TAXFEE) ELSE 0 END AS INDEFEEA,                                                                                                                                           '
                        || '              CASE WHEN T1.CHARGE_CODE = '''||INSTALL_DEBUG_FEE||''' AND T1.IN_OBJECT = T2.COMPZ AND T2.COMPA <> T2.COMPZ THEN (T1.SETTLE_NOTAXFEE + T1.SETTLE_TAXFEE) ELSE 0 END AS INDEFEEZ,                                                                                                                  '
                        || '              DECODE(T1.CHARGE_CODE,'''||FEATURES_FEE||''',(T1.SETTLE_NOTAXFEE + T1.SETTLE_TAXFEE),0) AS MONFEAFEE,                                                                                                                                                                                             '
                        || '              CASE WHEN T1.CHARGE_CODE = '''||FEATURES_FEE||''' AND T1.IN_OBJECT = T2.COMPA THEN (T1.SETTLE_NOTAXFEE + T1.SETTLE_TAXFEE) ELSE 0 END AS MONFEAFEEA,                                                                                                                                              '
                        || '              CASE WHEN T1.CHARGE_CODE = '''||FEATURES_FEE||''' AND T1.IN_OBJECT = T2.COMPZ AND T2.COMPA <> T2.COMPZ THEN (T1.SETTLE_NOTAXFEE + T1.SETTLE_TAXFEE) ELSE 0 END AS MONFEAFEEZ,                                                                                                                         '
                        || '              T2.COMPA,T2.PROVA,T2.AREAA,T2.CMNAMEA,T2.COMPZ,T2.PROVZ,T2.AREAZ,T2.CMNAMEZ                                                                                                                                                                                                             '
                        || '        FROM (SELECT * FROM '||RPT_TABLE||' TT WHERE TT.CHARGE_CODE IN ('''||FEATURES_FEE||''', '''||INSTALL_DEBUG_FEE||''')  AND TT.ORDER_MODE = ''1'' AND TT.DEST_SOURCE = ''0'' AND TT.OFFER_CODE IN ( '''||PRODUCT_CODE_LINE||''','''||PRODUCT_CODE_LINE2||''') and tt.out_object <> ''030'' and tt.in_object <> ''030'') T1                                                                                                 '
                        || '        LEFT JOIN (SELECT * FROM stlusers.STL_ORIG_LINE WHERE SETTLEMONTH = '''||RPT_SETTLEMONTH||''' AND PARTID = SUBSTR('''||RPT_SETTLEMONTH||''', 5)) T2 ON T1.PRODUCT_ORDER_ID =T2.ORID                                                                                                                                        '
                        || '        LEFT JOIN (SELECT PROD_ORDER_ID,ORDER_ID,MIN(EFFECTIVE_DATE) AS START_TIME FROM stlusers.STL_SERV_BIZ_CODE GROUP BY PROD_ORDER_ID, ORDER_ID) T3 ON T1.PRODUCT_ORDER_ID = T3.ORDER_ID AND T1.OFFER_ORDER_ID = T3.PROD_ORDER_ID) T                                                                       '
|| 'GROUP BY T.OMDATE,T.BUSNAME,T.CUSTOMERNAME,T.CUSTOMERNUMBER,T.ORID,T.FLAG,T.CUSATTRPROV,T.WIRECODE,T.COMPA,T.PROVA,T.AREAA,T.CMNAMEA,T.COMPZ,T.PROVZ,T.AREAZ,T.CMNAMEZ ';
    SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

    RPT_RUNSITE := '2';
    RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
    PROC_OUT := 'Y';
    COMMIT;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
    szSysErr := 'OK';
    nReturn := 0;

    SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;

  END;
END ;;
DELIMITER ;