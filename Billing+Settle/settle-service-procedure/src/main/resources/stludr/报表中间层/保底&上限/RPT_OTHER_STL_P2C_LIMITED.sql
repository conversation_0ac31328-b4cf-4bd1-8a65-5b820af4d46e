/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层-保底/上限-省专报表保底/上限金额调整
**/
use stludr;
DROP PROCEDURE IF EXISTS stludr.`RPT_OTHER_STL_P2C_LIMITED`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE stludr.RPT_OTHER_STL_P2C_LIMITED(
    RPT_SETTLEMONTH IN VARCHAR2,
    FLAG_VERSION IN CHAR,
    PROC_OUT OUT VARCHAR2
)
AS
    -- 省云结算报表二级中间表
    v_proc_name       VARCHAR2(30) := 'RPT_OTHER_STL_P2C_LIMITED';
    G_VERSION RVL_CONF_VERSION.VERSION%TYPE; --版本号变量
    G_RESNUM  RVL_CONF_VERSION.VERSIONNUM%TYPE; --版本序号
    ERRCODE   VARCHAR2(32);
    ERRMSG    VARCHAR2(2048);
    VER_TABLE VARCHAR2(64);

    RPT_RUNSITE VARCHAR2(32); --运行定位标识
    RPT_SQL     VARCHAR2(4000); --拼SQL的字符串变量
    RPT_TABLE   VARCHAR2(64); --拼表名的字符串变量
    outReturn   int;
    iv_Rule_ID  number(8);
    iv_Rep_Num  varchar2(20);
    iv_Col_Name varchar2(128);
    iv_Priority number(4);
    iv_Previous   number(18); -- 前N个月的结算金额总和

    CURSOR CEILING_LIST IS
    select t.RULE_ID, t.REP_NUM, t.COL_NAME, t.PRIORITY FROM rvl_limit_column t, 
       (select distinct rule_id, eff_month, exp_month from rvl_limit_fee where RPT_SETTLEMONTH between eff_month and exp_month) s
     where mod(t.RULE_ID, 2) = 1 and t.rule_id = s.rule_id and t.rep_num <> 'D313-5'
     order by RULE_ID, PRIORITY;

    ---- 80% 保底改造 特殊处理  10月  11月 也要进行保底计算
    CURSOR FLOOR_LIST IS
    select t.RULE_ID, t.REP_NUM, t.COL_NAME, t.PRIORITY FROM rvl_limit_column t,
       (select distinct rule_id, eff_month, exp_month from rvl_limit_fee) s
    where mod(t.RULE_ID, 2) = 0  AND RPT_SETTLEMONTH between s.eff_month and s.exp_month  and (RPT_SETTLEMONTH = s.exp_month  or  SUBSTR(RPT_SETTLEMONTH,-2)= '10'
        OR SUBSTR(RPT_SETTLEMONTH,-2)= '11') and t.rule_id = s.rule_id
      and t.rep_num <> 'D313-5'
    order by RULE_ID;

BEGIN

    PROC_OUT := '';
    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        PROC_OUT := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || PROC_OUT ) AS error_msg ;
    END;

    BEGIN
        if ( length(RPT_SETTLEMONTH) < 6 )  then
            SELECT 'RPT_SETTLEMONTH length less than 6. RPT_SETTLEMONTH=[' || RPT_SETTLEMONTH || ']';
            outReturn := -1;
            ROLLBACK;
            RETURN;
        end if;

        SELECT 'RPT_SETTLEMONTH=' ||  RPT_SETTLEMONTH;


        VER_TABLE   := 'STL_P2C_USAGE';
        RPT_RUNSITE := '0';
        RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '省专二级中间表计算开始时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        --删除当月数据
        delete from rpt_p2c_limited where settlemonth = RPT_SETTLEMONTH;

        RPT_SQL := '写入二级中间表（费项类型和省代码补全）...';
        RPT_RUNSITE := '1';
	
	-- 普通业务
        insert into rpt_p2c_limited(settlemonth, rep_num, col_name, taxrate, fee_flag, outprov_name, prov_cd, prov_wl, settle_fee, temp_fee, ceiling_rule, floor_rule, priority_c, priority_f)
        select distinct settlemonth, rep_num, col_name, taxrate, fee_flag, prov_nm, prov_cd, prov_wl, settle_fee, temp_fee, sum(ceiling_rule), sum(floor_rule), max(priority_c),
               max(priority_f)
          from(select RPT_SETTLEMONTH settlemonth, a.REP_NUM, a.COL_NAME, '6' taxrate, c.FEE_FLAG, b.PROV_NM, b.PROV_CD, '30' || b.PROV_WL prov_wl, 0 settle_fee, 0 temp_fee,
               decode(d.limit_type, 'C', d.rule_id, null) ceiling_rule, decode(d.limit_type, 'F', d.rule_id, null) floor_rule, decode(d.limit_type, 'C', a.priority) priority_c,
               decode(d.limit_type, 'F', a.priority) priority_f
          from rvl_limit_column a,
               stl_province_cd b,
               (select 1 fee_flag from dual union select 2 from dual) c,
               (select distinct rule_id, limit_type from rvl_limit_fee where RPT_SETTLEMONTH between eff_month and exp_month) d
         where b.PROV_TYPE = '0'
           and a.rule_id = d.rule_id and a.rep_num <> 'D313-5')
        group by settlemonth, rep_num, col_name, taxrate, fee_flag, prov_nm, prov_cd, prov_wl, settle_fee;

	-- 移动云自有I+P
				insert into rpt_p2c_limited(settlemonth, rep_num, col_name, taxrate, fee_flag, outprov_name, prov_cd, prov_wl, settle_fee, temp_fee, ceiling_rule, floor_rule, priority_c, priority_f)
		select t1.* from
        (select distinct settlemonth, rep_num, col_name, taxrate, fee_flag, prov_nm, prov_cd, prov_wl, settle_fee, temp_fee, sum(ceiling_rule), sum(floor_rule), max(priority_c),
               max(priority_f)
          from(select RPT_SETTLEMONTH settlemonth, a.REP_NUM, a.COL_NAME, '6' taxrate, c.FEE_FLAG, b.PROV_NM, b.PROV_CD, '30' || b.PROV_WL prov_wl, 0 settle_fee, 0 temp_fee,
               decode(d.limit_type, 'C', d.rule_id, null) ceiling_rule, decode(d.limit_type, 'F', d.rule_id, null) floor_rule, decode(d.limit_type, 'C', a.priority) priority_c,
               decode(d.limit_type, 'F', a.priority) priority_f
          from rvl_limit_column a,
               stl_province_cd b,
               (select 1 fee_flag from dual union select 2 from dual) c,
               (select distinct rule_id, limit_type from rvl_limit_fee where RPT_SETTLEMONTH between eff_month and exp_month) d
         where b.PROV_TYPE = '0'
           and a.rule_id = d.rule_id and a.rep_num = 'D313-5')
        group by settlemonth, rep_num, col_name, taxrate, fee_flag, prov_nm, prov_cd, prov_wl, settle_fee) t1;

        RPT_SQL := '更新二级中间表的实际结算金额...';
        RPT_RUNSITE := '2';
         merge into rpt_p2c_limited a
         using (select RPT_SETTLEMONTH settlemonth, c.rep_num, c.col_name, a.taxrate * 100 taxrate, a.fee_flag, a.outprov_name, a.outprov_code prov_cd, a.prov_wl,
                       sum(a.settlement_amount) settle_fee
                  from rpt_p2c a, rvl_p2c_bus_config c
                 where a.settlemonth = RPT_SETTLEMONTH

                   and RPT_SETTLEMONTH between c.start_month and c.end_month
                   and c.REPORT_SOURCE = '2'

                   and a.inprov_code = c.company_code
                   and (c.type = 'O' and c.remark is null and a.offer_code = c.offer_code
                     or c.type = 'O' and c.remark = '3' and a.offer_code = c.offer_code and a.settlement_class = c.remark --D313-7 9210018, 9213004两商品只取settlement_class=3的
                     or c.type = 'OP' and a.offer_code = c.offer_code and a.product_code = c.product_code
                     or c.type = 'OPF' and a.offer_code = c.offer_code and a.product_code = c.product_code and a.charge_code = c.charge_item
                     or c.type = 'OPFR' and a.offer_code = c.offer_code and a.product_code = c.product_code and a.charge_code = c.charge_item and a.rateplan_id = c.rateplan_id
                     or c.type = 'OF' and a.offer_code = c.offer_code and a.charge_code = c.charge_item)
                 group by c.rep_num, c.col_name, a.taxrate * 100, a.fee_flag, a.outprov_name, a.outprov_code, a.prov_wl) t
            on (a.rep_num = t.rep_num and a.col_name = t.col_name and a.taxrate = t.taxrate and a.fee_flag = t.fee_flag and a.prov_cd = t.prov_cd)
          when matched then
                update set a.settle_fee = t.settle_fee where a.settlemonth = RPT_SETTLEMONTH;

        ----D313-2 和教育
        merge into rpt_p2c_limited a
            using (select RPT_SETTLEMONTH settlemonth, 'D313-2' rep_num, '和教育' col_name, a.taxrate * 100 taxrate, a.fee_flag, a.outprov_name, a.outprov_code prov_cd, a.prov_wl,
                          sum(a.settlement_amount) settle_fee
                     from rpt_p2c a
                    where a.settlemonth = RPT_SETTLEMONTH
                      and a.inprov_code = 'CY'
                      and (a.settlement_class = '1' and a.offer_code not in (select offer_code from rvl_p2c_bus_config where rep_num = 'D313-2' and offer_code is not null)
                          or a.offer_code = '50049')
                    group by a.taxrate * 100, a.fee_flag, a.outprov_name, a.outprov_code, a.prov_wl) t
               on (a.rep_num = t.rep_num and a.col_name = t.col_name and a.taxrate = t.taxrate and a.fee_flag = t.fee_flag and a.prov_cd = t.prov_cd)
             when matched then
                   update set a.settle_fee = t.settle_fee where a.settlemonth = RPT_SETTLEMONTH;

        ----D313-1 移动云SAAS   202406 梦璇的需求 D313-1  不在计算保底上下限


        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);


----D313-5 I+P+专属云
select '----------------------------------D313-5 I+P+专属云';
merge into rpt_p2c_limited a
    using (select rpt_settlemonth settlemonth, 'D313-5' rep_num, '直营' col_name, '6' taxrate, prov_cd, sum(a_fee + c_fee) settle_fee
           from stl_pvs
           where acct_month = rpt_settlemonth and joint_flag = '0' and inner_customer_flag = '0' and ydn_flag = '0'
           group by prov_cd
    ) t
    on (a.rep_num = t.rep_num and a.col_name = t.col_name and a.taxrate = t.taxrate and a.fee_flag = '1' and a.prov_cd = t.prov_cd)
    when matched then
        update set a.settle_Fee = t.settle_fee where a.settlemonth = rpt_settlemonth and rep_num = 'D313-5';

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);


        RPT_SQL := '报表展示金额计算，超出上限...';
        RPT_RUNSITE := '3';

        OPEN CEILING_LIST;
        LOOP
            FETCH CEILING_LIST INTO iv_Rule_ID, iv_Rep_Num, iv_Col_Name, iv_Priority;
            EXIT WHEN CEILING_LIST%NOTFOUND;
             ------  为了下省明细特殊给定标识，确认是超过上限的逻辑
            merge into rpt_p2c_limited a
                using (select RULE_ID, PROV_CODE, GAP_FROM_CEILING,SPECIAL_ACCU_FEE,SPECIAL_TAG
                       from rvl_limit_usage
                       where settle_month = RPT_SETTLEMONTH
                         and ceiling_reached = '1'
                         and LIMIT_TYPE = 'C') t
                on (a.CEILING_RULE = t.RULE_ID and a.prov_cd = t.PROV_CODE)
                when matched then
                    update set a.type = '10'
                where settlemonth = RPT_SETTLEMONTH and a.PROV_CD = t.PROV_CODE and a.CEILING_RULE = iv_Rule_ID;

        ----调账部分
        if (iv_Priority = 1) then
            ------第一顺位计算的，先获取差额
            merge into rpt_p2c_limited a
            using (select RULE_ID, PROV_CODE, GAP_FROM_CEILING,SPECIAL_ACCU_FEE,SPECIAL_TAG 
                      from rvl_limit_usage
                    where settle_month = RPT_SETTLEMONTH
                      and ceiling_reached = '1'
                      and LIMIT_TYPE = 'C') t
                on (a.CEILING_RULE = t.RULE_ID and a.prov_cd = t.PROV_CODE)
              when matched then
                    update set a.report_fee =
                        case when SPECIAL_TAG > 1 then 0
                        else decode(sign(a.settle_fee - t.gap_from_ceiling - coalesce(t.SPECIAL_ACCU_FEE, 0)), -1, 0, a.settle_fee - t.gap_from_ceiling - coalesce(t.SPECIAL_ACCU_FEE, 0))
                    end,
                        /*a.TEMP_FEE = a.SETTLE_FEE - decode(sign(a.settle_fee - (t.gap_from_ceiling - a.TEMP_FEE)), -1, 0, a.settle_fee - (t.gap_from_ceiling - a.TEMP_FEE))*/
                    a.TEMP_FEE =
                        case when SPECIAL_TAG > 1 then t.GAP_FROM_CEILING - a.SETTLE_FEE
                        else t.GAP_FROM_CEILING + coalesce(t.SPECIAL_ACCU_FEE, 0) - (a.SETTLE_FEE - decode(sign(a.settle_fee - t.gap_from_ceiling - coalesce(t.SPECIAL_ACCU_FEE, 0)), -1, 0, a.settle_fee - t.gap_from_ceiling - coalesce(t.SPECIAL_ACCU_FEE, 0)))
                    end
                    where fee_flag = 2 and settlemonth = RPT_SETTLEMONTH and a.PROV_CD = t.PROV_CODE
                      and a.CEILING_RULE = iv_Rule_ID and /*a.REP_NUM = iv_Rep_Num and a.COL_NAME = iv_Col_Name and*/ a.PRIORITY_C = iv_Priority;
        else
            ------非第一顺位计算的，temp_fee（剩余扣减额度）已更新，直接使用
            update rpt_p2c_limited a
                set a.report_fee = decode(sign(a.settle_fee - a.TEMP_FEE), -1, 0, a.settle_fee - a.TEMP_FEE),
                    a.TEMP_FEE = a.TEMP_FEE - (a.SETTLE_FEE - decode(sign(a.settle_fee - a.TEMP_FEE), -1, 0, a.settle_fee - a.TEMP_FEE))
              where a.FEE_FLAG = 2 and settlemonth = RPT_SETTLEMONTH
                and a.CEILING_RULE = iv_Rule_ID and /*a.REP_NUM = iv_Rep_Num and a.COL_NAME = iv_Col_Name and*/ a.PRIORITY_C = iv_Priority;
        end if;

          ------更新对应的非调账数据的temp_fee（剩余扣减额度）
            merge into rpt_p2c_limited a
            using (select ceiling_rule, priority_c, prov_cd, temp_fee
                      from rpt_p2c_limited
                    where settlemonth = RPT_SETTLEMONTH and fee_flag = 2 and temp_fee is not null) t
                on (a.priority_c = t.priority_c and a.prov_cd = t.prov_cd and a.ceiling_rule = t.ceiling_rule)
              when matched then
                    update set a.temp_fee = t.temp_fee
                    where fee_flag = 1 and settlemonth = RPT_SETTLEMONTH and a.PRIORITY_C = iv_Priority and a.ceiling_rule = iv_Rule_ID;

        ----非调账部分
          update rpt_p2c_limited a
              set a.report_fee = decode(sign(a.settle_fee - a.TEMP_FEE), -1, 0, a.settle_fee - a.TEMP_FEE),
                  a.TEMP_FEE = a.TEMP_FEE - (a.SETTLE_FEE - decode(sign(a.settle_fee - a.TEMP_FEE), -1, 0, a.settle_fee - a.TEMP_FEE))
            where a.FEE_FLAG = 1 and settlemonth = RPT_SETTLEMONTH
              and a.CEILING_RULE = iv_Rule_ID and /*a.REP_NUM = iv_Rep_Num and a.COL_NAME = iv_Col_Name and*/ a.PRIORITY_C = iv_Priority;
              commit;

        ------更新下个优先级的调账数据的temp_fee（剩余扣减额度）
            merge into rpt_p2c_limited a
            using (select ceiling_rule, priority_c, prov_cd, temp_fee
                      from rpt_p2c_limited
                    where settlemonth = RPT_SETTLEMONTH and fee_flag = 1 and temp_fee is not null) t
                on (a.priority_c = t.priority_c + 1 and a.prov_cd = t.prov_cd and a.ceiling_rule = t.ceiling_rule)
              when matched then
                    update set a.temp_fee = t.temp_fee where fee_flag = 2 and settlemonth = RPT_SETTLEMONTH and a.PRIORITY_C = iv_Priority + 1;

        END LOOP;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

--         10 ，11 ，12 月的数据
        OPEN FLOOR_LIST;
        LOOP
            FETCH FLOOR_LIST INTO iv_Rule_ID, iv_Rep_Num, iv_Col_Name, iv_Priority;
            EXIT WHEN FLOOR_LIST%NOTFOUND;

        ----如果为最后一月，未达保底的

        ------非调账
        RPT_SQL := '报表展示金额计算，未达保底，非调账...';
        RPT_RUNSITE := '5';

        merge into rpt_p2c_limited a
            using (select RULE_ID, PROV_CODE, GAP_TIL_FLOOR
                      from rvl_limit_usage
                    where settle_month = RPT_SETTLEMONTH
                      and FLOOR_UNREACHED = '1'
                      and LIMIT_TYPE = 'F') t
                on (a.FLOOR_RULE = t.RULE_ID and a.prov_cd = t.PROV_CODE)
              when matched then
                    update set a.report_fee = a.SETTLE_FEE + t.GAP_TIL_FLOOR
                    where fee_flag = 1 and settlemonth = RPT_SETTLEMONTH and a.PROV_CD = t.PROV_CODE
                      and a.FLOOR_RULE = iv_Rule_ID and a.PRIORITY_F = 1;

        ---- 第10 11 12 特殊处理
        merge into rpt_p2c_limited a
            using (select RULE_ID, PROV_CODE, GAP_TIL_FLOOR, SPECIAL_TAG
                   from rvl_limit_usage
                   where settle_month = RPT_SETTLEMONTH
                     and  (SUBSTR(rpt_settlemonth,-2)= '10' or SUBSTR(rpt_settlemonth,-2)= '11' or SUBSTR(rpt_settlemonth,-2)= '12')
                     and LIMIT_TYPE = 'F' and RULE_ID != '18') t
            on (a.FLOOR_RULE = t.RULE_ID and a.prov_cd = t.PROV_CODE)
            when matched then
                update set a.report_fee = a.SETTLE_FEE + t.GAP_TIL_FLOOR
            where fee_flag = 1 and settlemonth = RPT_SETTLEMONTH and a.PROV_CD = t.PROV_CODE
                        and a.FLOOR_RULE = iv_Rule_ID and a.PRIORITY_F = 1 and SPECIAL_TAG = 0;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        END LOOP;


        ----I+P+专属云
				
	-- 计算前N个月的结算报表总额
delete from rvl_pvs_previous where settlemonth = RPT_SETTLEMONTH;
select '----------------------------------计算前N个月的结算报表总额、接口表总额';
insert into rvl_pvs_previous
select null,RPT_SETTLEMONTH, rep_num, col_name, taxrate, fee_flag, outprov_name, prov_cd, prov_wl, sum(report_fee), substr(RPT_SETTLEMONTH, 0, 4) || '01',
       to_char(add_months(to_date(RPT_SETTLEMONTH, 'yyyymm'), -1), 'yyyymm'), 0
from rpt_p2c_limited
where rep_num = 'D313-5' and fee_flag = '1'
  and settlemonth >= substr(RPT_SETTLEMONTH, 0, 4) || '01' and settlemonth < RPT_SETTLEMONTH
group by rep_num, col_name, taxrate, fee_flag, outprov_name, prov_cd, prov_wl;

--计算前N个月的接口表总金额
merge into rvl_pvs_previous a
    using (select PROV_CD, sum(a_fee + c_fee) accu_fee
           from stl_pvs b
           where acct_month >= substr(RPT_SETTLEMONTH, 1, 4) || '01' and acct_month < RPT_SETTLEMONTH
             and joint_flag = '0' and inner_customer_flag = '0' and ydn_flag = '0'
           group by prov_cd) t
    on (a.PROV_CD = t.PROV_CD)
    when matched then
        update set a.sum_pvs_fee = t.accu_fee
    where a.SETTLEMONTH = RPT_SETTLEMONTH
				  and rep_num = 'D313-5' and taxrate = '6';

-- 1月，未达至当月累计保底的
if substr(RPT_SETTLEMONTH, 5, 2) = '01' then
select '----------------------------------1月，未达至当月累计保底的';
merge into rpt_p2c_limited a
    using (select a.rule_id, a.prov_code, a.limit_fee report_fee, a.limit_fee temp_fee
           From rvl_limit_fee a, rvl_limit_biz b, rvl_limit_usage c
           where b.type = 'I+P' and a.limit_type = 'F' and RPT_SETTLEMONTH between a.eff_month and a.exp_month
             and a.rule_id = c.rule_id and RPT_SETTLEMONTH = c.settle_month and c.floor_unreached = '1' and a.prov_code = c.prov_code
             and a.rule_id = b.rule_id) t
    on (a.floor_rule = t.rule_id and a.prov_cd = t.prov_code)
    when matched then
        update set a.report_fee = t.report_fee, a.temp_fee = t.temp_fee
    where a.settlemonth = RPT_SETTLEMONTH and a.fee_flag = 1 and rep_num = 'D313-5';

-- 1月，已超年上限的
select '----------------------------------1月，已超年上限的';
merge into rpt_p2c_limited a
    using (select a.rule_id, a.prov_code, a.limit_fee report_fee, a.limit_fee temp_fee
           From rvl_limit_fee a, rvl_limit_biz b, rvl_limit_usage c
           where b.type = 'I+P' and a.limit_type = 'C' and RPT_SETTLEMONTH between a.eff_month and a.exp_month
             and a.rule_id = c.rule_id and RPT_SETTLEMONTH = c.settle_month and c.ceiling_reached = '1' and a.prov_code = c.prov_code
             and a.rule_id = b.rule_id) t
    on (a.ceiling_rule = t.rule_id and a.prov_cd = t.prov_code)
    when matched then
        update set a.report_fee = t.report_fee, a.temp_fee = t.temp_fee
    where a.settlemonth = RPT_SETTLEMONTH and a.fee_flag = 1 and rep_num = 'D313-5';

-- 1月，已达保底未超上限的
select '----------------------------------1月，已达保底未超上限的';
update rpt_p2c_limited
set report_fee = settle_fee
where report_fee is null
  and settlemonth = RPT_SETTLEMONTH and rep_num = 'D313-5' and fee_flag = 1;

else
  -- 2~12月，未达至当月累计保底的
select '----------------------------------2~12月，未达至当月累计保底的';
merge into rpt_p2c_limited a
    using (select a.rule_id, a.prov_code, a.limit_fee - d.sum_report_fee report_fee, a.limit_fee temp_fee
           From rvl_limit_fee a, rvl_limit_biz b, rvl_limit_usage c, rvl_pvs_previous d
           where b.type = 'I+P' and a.limit_type = 'F' and RPT_SETTLEMONTH between a.eff_month and a.exp_month
             and a.rule_id = c.rule_id and RPT_SETTLEMONTH = c.settle_month and c.floor_unreached = '1' and a.prov_code = c.prov_code
             and a.rule_id = b.rule_id
             and a.prov_code = d.prov_cd and d.settlemonth = RPT_SETTLEMONTH and d.rep_num = 'D313-5') t
    on (a.floor_rule = t.rule_id and a.prov_cd = t.prov_code)
    when matched then
        update set a.report_fee = t.report_fee, a.temp_fee = t.temp_fee
    where a.settlemonth = RPT_SETTLEMONTH and a.fee_flag = 1 and rep_num = 'D313-5';

-- 2~12月，已超年上限的
select '----------------------------------2~12月，已超年上限的';
merge into rpt_p2c_limited a
    using (select a.rule_id, a.prov_code, a.limit_fee - d.sum_report_fee report_fee, a.limit_fee temp_fee
           From rvl_limit_fee a, rvl_limit_biz b, rvl_limit_usage c, rvl_pvs_previous d
           where b.type = 'I+P' and a.limit_type = 'C' and RPT_SETTLEMONTH between a.eff_month and a.exp_month
             and a.rule_id = c.rule_id and RPT_SETTLEMONTH = c.settle_month and c.ceiling_reached = '1' and a.prov_code = c.prov_code
             and a.rule_id = b.rule_id
             and a.prov_code = d.prov_cd and d.settlemonth = RPT_SETTLEMONTH and d.rep_num = 'D313-5') t
    on (a.ceiling_rule = t.rule_id and a.prov_cd = t.prov_code)
    when matched then
        update set a.report_fee = t.report_fee, a.temp_fee = t.temp_fee
    where a.settlemonth = RPT_SETTLEMONTH and a.fee_flag = 1 and rep_num = 'D313-5';

-- 2~12月，已达保底未超上限的
select '----------------------------------2~12月，已达保底未超上限的';
merge into rpt_p2c_limited a
    using (select d.prov_cd, nvl(d.sum_report_fee, 0) sum_report_fee, nvl(d.sum_pvs_fee, 0) sum_pvs_fee from rvl_pvs_previous d
           where d.settlemonth = RPT_SETTLEMONTH and d.rep_num = 'D313-5') t
    on (a.prov_cd = t.prov_cd)
    when matched then
        update set a.report_fee = t.sum_pvs_fee + a.settle_fee - t.sum_report_fee
    where a.settlemonth = RPT_SETTLEMONTH and a.fee_flag = 1 and a.rep_num = 'D313-5' and a.report_fee is null;
end if;



        RPT_SQL := '报表展示金额计算，未设置保底/上限的...';
        RPT_RUNSITE := '6';
        update rpt_p2c_limited
           set report_fee = settle_fee
         where report_fee is null
           and settlemonth = RPT_SETTLEMONTH;
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        commit;

        PROC_OUT:='Y';
        outReturn := 0;

        SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
    END;
END ;;
DELIMITER ;
