/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层-保底/上限-移动云IaaS、PaaS和专属云金额占比计算
**/
use stludr;
DELIMITER ;;
create or replace DEFINER="stludr"@"10.%" PROCEDURE stludr."STL_PVS"(
    RPT_SETTLEMONTH IN VARCHAR2,
    FLAG_VERSION IN CHAR,
    PROC_OUT OUT VARCHAR2,
    szSysErr OUT VARCHAR2(1000),
    nReturn OUT NUMBER(4)
)
As
  outReturn   NUMBER(4);
  szSysErr    VARCHAR2(2048);
  VER_TABLE   VARCHAR2(64) := 'stl_pvs';
  RPT_RUNSITE VARCHAR2(5);
  RPT_SQL     VARCHAR2(200);
  v_proc_name    VARCHAR2(30) := 'STL_PVS';
BEGIN

  outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
    GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        PROC_OUT := 'N';
        szSysErr := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;

BEGIN
  SET @RPT_RUNSITE := '0';
  SET @RPT_SQL     := RPT_SETTLEMONTH || '账期移动云自用I+P及专属云报表金额计算，开始时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');

call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

-- 专属云31省结算金额计算

SET @RPT_SQL     := '1 专属云31省结算金额计算....';

        SET @vSql := 'delete from stl_dec_fee where acct_month =''' || RPT_SETTLEMONTH || '''';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
-- 31省类型，非最大占比的省（非广东），按比例计算
insert into stl_dec_fee
select null,a.acct_month, b.prov_cd, a.ys_guideprice * 1000 total_fee, round(a.ys_guideprice * b.rate / 100, 2) * 1000 settle_fee, '2' settle_method, a.ys_settletype, ys_ghcode
from (select acct_month, ys_settletype, ys_ghcode, sum(ys_guideprice) ys_guideprice
      from ys_information_supplement
      where acct_month = RPT_SETTLEMONTH and ys_productcategories in ('3', '4') and ys_settlemethod = '2' and ys_status = '1'
      group by acct_month, ys_settletype, ys_ghcode) a, rvl_dec_rate b
where b.rate_order <> 1 and RPT_SETTLEMONTH between b.start_month and b.end_month;

-- 31省类型，最大占比的省（广东），用总金额减去其它省金额计算
insert into stl_dec_fee
select null,t.acct_month, s.prov_cd, t.total_fee, t.total_fee - t.settle_fee settle_fee, '2' settle_method, joint_flag, gh_code
from (select a.acct_month, joint_flag, gh_code, total_fee, sum(settle_fee) settle_fee
      from stl_dec_fee a
      where a.acct_month = RPT_SETTLEMONTH and settle_method = '2'
      group by acct_month, joint_flag, gh_code, total_fee) t, rvl_dec_rate s
where s.rate_order = 1 and RPT_SETTLEMONTH between s.start_month and s.end_month;

-- 单省类型
insert into stl_dec_fee
select null,a.acct_month, a.ys_province, a.ys_guideprice * 1000, a.ys_guideprice * 1000, ys_settlemethod, ys_settletype, ys_ghcode
from ys_information_supplement a
where a.acct_month = RPT_SETTLEMONTH and a.ys_productcategories in ('3', '4') and a.ys_settlemethod = '1' and ys_status = '1';

commit;

-- 专属云接口、4.30接口（非ToC、ToC）数据汇总
SET @RPT_RUNSITE := '2';
SET @RPT_SQL     := '专属云接口、4.30接口（非ToC、ToC）数据汇总，非云能内部签约客户...';
SELECT @RPT_RUNSITE || ''  '' ||@RPT_SQL;

SET @vSql := 'delete from stl_pvs where acct_month =''' || RPT_SETTLEMONTH || '''';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

SET @vSql  := 'insert into stl_pvs(acct_month, prov_cd, joint_flag, inner_customer_flag, ydn_flag, rate_plan_gh_code, a_fee, c_fee) ' ||
								 'select acct_month, prov_cd, joint_flag, inner_customer_flag, ydn_flag, rate_plan_gh_code, sum(a_fee) a_fee, sum(c_fee) c_fee ' ||
  'from (select acct_month, prov_code prov_cd, joint_flag, inner_customer_flag, ' ||
                             'decode(on_product_code, ''10005'', ''1'', decode(product_id, ''9202095'', ''1'', ''0'')) ydn_flag, rate_plan_gh_code, settle_fee a_fee, 0 c_fee ' ||
                    'from (select ''' || RPT_SETTLEMONTH || ''' acct_month, prov_code, joint_flag, inner_customer_flag, on_product_code, product_id, ' ||
                                             'rate_plan_gh_code, sum(pv_settle_value) settle_fee ' ||
                                    'from sync_interface_pvs_' || RPT_SETTLEMONTH || ' ' ||
                                 'where status = ''0'' and yn_inner_customer_flag = ''0'' and fee_flag = ''1'' ' ||
                                 'group by prov_code, joint_flag, inner_customer_flag, on_product_code, product_id, rate_plan_gh_code ' ||
																 'union all ' ||
													'select ''' || RPT_SETTLEMONTH || ''' acct_month, prov_code, joint_flag, inner_customer_flag, on_product_code, product_id, ' ||
                                             'rate_plan_gh_code, sum(pv_settle_value) settle_fee ' ||
                                    'from sync_interface_pvs_' || RPT_SETTLEMONTH || ' ' ||
                                 'where status = ''0'' and yn_inner_customer_flag = ''0'' and fee_flag = ''2'' and inner_customer_flag = ''0'' ' ||
																   'and original_bill_month >= ''202501'' ' ||
                                 'group by prov_code, joint_flag, inner_customer_flag, on_product_code, product_id, rate_plan_gh_code ' ||
													       'union all ' ||
													'select ''' || RPT_SETTLEMONTH || ''' acct_month, prov_code, joint_flag, inner_customer_flag, on_product_code, product_id, ' ||
                                             'rate_plan_gh_code, sum(pv_settle_value) settle_fee ' ||
                                    'from sync_interface_pvs_' || RPT_SETTLEMONTH || ' ' ||
                                 'where status = ''0'' and yn_inner_customer_flag = ''0'' and fee_flag = ''2'' and inner_customer_flag = ''1'' ' ||
																   'and original_bill_month >= ''202401'' ' ||
                                 'group by prov_code, joint_flag, inner_customer_flag, on_product_code, product_id, rate_plan_gh_code) ' ||
                 'union all ' ||
                'select acct_month, prov_code prov_cd, ''0'' joint_flag, ''0'' inner_customer_flag, ' ||
                             'decode(bz_type, ''N4301'', ''1'', ''N4302'', ''1'', ''0'') ydn_flag, rate_plan_gh_code, settle_fee a_fee, 0 c_fee ' ||
                    'from (select ''' || RPT_SETTLEMONTH || ''' acct_month, prov_code, bz_type, ' ||
                                             'rate_plan_gh_code, sum(pv_settle_value) settle_fee ' ||
                                    'from sync_interface_pvs_toc_' || RPT_SETTLEMONTH || ' ' ||
                                 'where status = ''0'' ' ||
                                 'group by prov_code, bz_type, rate_plan_gh_code) ' ||
                 'union all ' ||
                'select acct_month, prov_cd, decode(joint_flag, ''1'', ''0'', ''2'', ''1'', ''3'', ''0'', ''4'', ''1'') joint_flag, ' ||
                             'decode(joint_flag, ''1'', ''0'', ''2'', ''0'', ''3'', ''1'', ''4'', ''1'') inner_customer_flag, ' ||
                             '''0'' ydn_flag, gh_code rate_plan_gh_code, 0 a_fee, settle_fee c_fee ' ||
                    'from (select acct_month, prov_cd, joint_flag, ' ||
                                             'gh_code, sum(settle_fee) settle_fee ' ||
                                    'from stl_dec_fee ' ||
                                 'where acct_month = ''' || RPT_SETTLEMONTH || ''' and joint_flag in (''1'', ''2'', ''3'', ''4'') ' ||
                                 'group by acct_month, prov_cd, joint_flag, gh_code)) ' ||
'group by acct_month, prov_cd, joint_flag, inner_customer_flag, ydn_flag, rate_plan_gh_code';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


SET @RPT_RUNSITE := '3';
SET @RPT_SQL     := '专属云接口、4.30接口（非ToC、ToC）数据汇总，云能内部签约客户...';
SELECT @RPT_RUNSITE || ''  '' ||@RPT_SQL;

SET @vSql := 'delete from stl_pvs_z002 where acct_month =''' || RPT_SETTLEMONTH || '''';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

SET @vSql  := 'insert into stl_pvs_z002(acct_month, prov_wl, rate_plan_gh_code, on_product_code, if_support_partner_settle, settlement_class, a_fee, c_fee) ' ||
								 'select acct_month, prov_wl, rate_plan_gh_code, on_product_code, if_support_partner_settle, settlement_class, sum(a_fee) a_fee, sum(c_fee) c_fee ' ||
  'from (select acct_month, settlement_party_out_name prov_wl, rate_plan_gh_code, on_product_code, if_support_partner_settle, settlement_class, ' ||
        'settle_fee a_fee, 0 c_fee ' ||
                    'from (select ''' || RPT_SETTLEMONTH || ''' acct_month, on_product_code, if_support_partner_settle, settlement_class, settlement_party_out_name, ' ||
                                             'rate_plan_gh_code, sum(pv_settle_value) settle_fee ' ||
                                    'from sync_interface_pvs_' || RPT_SETTLEMONTH || ' ' ||
                                 'where status = ''0'' and yn_inner_customer_flag = ''1'' and fee_flag in (''1'', ''2'') ' ||
                                 'group by on_product_code, if_support_partner_settle, settlement_class, settlement_party_out_name, rate_plan_gh_code) ' ||
         'union all ' ||
                'select acct_month, b.prov_wl, gh_code rate_plan_gh_code, null on_product_code, null if_support_partner_settle, ' ||
									     'null settlement_class, 0 a_fee, settle_fee c_fee ' ||
                    'from (select acct_month, prov_cd, joint_flag, ' ||
                                             'gh_code, sum(settle_fee) settle_fee ' ||
                                    'from stl_dec_fee ' ||
                                 'where acct_month = ''' || RPT_SETTLEMONTH || ''' and joint_flag = ''5'' ' ||
                                 'group by acct_month, prov_cd, joint_flag, gh_code) a ' ||
																  'left join stl_wl_cd b on a.prov_cd = b.prov_cd) ' ||
'group by acct_month, prov_wl, rate_plan_gh_code, on_product_code, if_support_partner_settle, settlement_class';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

commit;
RPT_SQL := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期移动云自用I+P及专属云报表金额计算结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
    RPT_RUNSITE := '99';
    PROC_OUT := 'Y';
    nReturn := 0;
    szSysErr := 'OK';
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);
SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
end;
END;;
DELIMITER ;
