/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层-保底/上限-移动云合作伙伴报表超上限金额扣减
**/
use stludr
DELIMITER ;;
CREATE or replace DEFINER="stludr"@"10.%" PROCEDURE stludr."STL_PARTNER_LIMITED"(  RPT_SETTLEMONTH IN VARCHAR2,
                                G_VERSION IN VARCHAR2,
                                nReturn   OUT NUMBER,
                                szSysErr  OUT VARCHAR2)
AS
    vSql           VARCHAR2(15000);
    outReturn      int;
    v_proc_name    VARCHAR2(30) := 'PROC_CDN_VAS';

    RPT_RUNSITE VARCHAR2(32); --运行定位标识
    RPT_SQL     VARCHAR2(4000); --拼SQL的字符串变量
    RPT_TABLE   VARCHAR2(64); --拼表名的字符串变量
BEGIN

    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        szSysErr := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;

    BEGIN

    RPT_TABLE :=v_proc_name ;
    RPT_RUNSITE :='0' ;
    RPT_SQL :='start_time:'|| TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');

    call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, RPT_TABLE, RPT_SQL);

    SET @vSql := 'delete from RPT_PARTNER_LIMITED where settlemonth = ''' || RPT_SETTLEMONTH || '''';
    SELECT @vSql;
    PREPARE STMT FROM @vSql;
    EXECUTE STMT;
    DEALLOCATE PREPARE STMT;


        outReturn  := 0;
        szSysErr := 'OK';
    END;

    BEGIN

        SET @vSql  :=  'INSERT INTO RPT_PARTNER_LIMITED ( ' ||
                            '       SETTLEMONTH, VERSION, RATEPLAN_ID, PAR_SETTL_AMOUNT_LIMITED, PAR_SETTL_AMOUNT, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, PARTNER_CODE, PARTNER_NAME,  ' ||
                            '       REPORT_PRODUCT_CODE, REPORT_PRODUCT_NAME,  RATEPLAN_NAME,    ' ||
                            '       SIGN_ENTITY, FEE_FLAG,  TAX_RATE, PAR_RES_SETTL_RATE, SETTLEMENT_CLASS,PRODUCT_TYPE,OFFER_NAME,PRODUCT_NAME)      ' ||
                            'SELECT   ''' || RPT_SETTLEMONTH || ''' SETTLEMONTH, ''' || G_VERSION || ''' VERSION, '||
                            '    RATEPLAN_ID,              ' ||
                            '       PAR_SETTL_AMOUNT AS PAR_SETTL_AMOUNT_LIMITED,    ' ||
                            '       CASE   ' ||
                            '           WHEN TOTAL_SUM > 0  THEN    ' ||
                            '               0     ' ||
                            '           WHEN TOTAL_SUM < 0 AND TOTAL_SUM > PAR_SETTL_AMOUNT  THEN   ' ||
                            '               TOTAL_SUM * -1    ' ||
                            '           WHEN TOTAL_SUM < 0  THEN  '||
                            '               PAR_SETTL_AMOUNT * -1   '||
                            '       END AS PAR_SETTL_AMOUNT,   ' ||
                            '       ORDER_MODE, OFFER_CODE, PRODUCT_CODE, PARTNER_CODE,      ' ||
                            '       PARTNER_NAME, REPORT_PRODUCT_CODE,  REPORT_PRODUCT_NAME, RATEPLAN_NAME, SIGN_ENTITY, FEE_FLAG,   ' ||
                            '       TAX_RATE, PAR_RES_SETTL_RATE, SETTLEMENT_CLASS,PRODUCT_TYPE,OFFER_NAME,PRODUCT_NAME    ' ||
                            'FROM (           ' ||
                            '   SELECT     ' ||
                            '       AA.RATEPLAN_ID,   ' ||
                            '       AA.PAR_SETTL_AMOUNT,    ' ||
                            '       AA.NUM,    ' ||
                            '       SUM(AA.PAR_SETTL_AMOUNT) OVER ( ORDER BY AA.NUM, PAR_RES_SETTL_RATE ) AS TOTAL_SUM,   ' ||
                            '       ORDER_MODE, OFFER_CODE, PRODUCT_CODE, PARTNER_CODE,      ' ||
                            '       PARTNER_NAME, REPORT_PRODUCT_CODE,  REPORT_PRODUCT_NAME, RATEPLAN_NAME, SIGN_ENTITY, FEE_FLAG,   ' ||
                            '       TAX_RATE, PAR_RES_SETTL_RATE, SETTLEMENT_CLASS,PRODUCT_TYPE,OFFER_NAME,PRODUCT_NAME    ' ||
                            '   FROM(     ' ||
                            '       SELECT ''10'' RATEPLAN_ID ,SUM( SETTLE_FEE ) - SUM( REPORT_FEE ) PAR_SETTL_AMOUNT, ''10'' NUM,     ' ||
                            '                    '''' ORDER_MODE, '''' OFFER_CODE, '''' PRODUCT_CODE, ''''PARTNER_CODE,     ' ||
                            '                    '''' PARTNER_NAME, '''' REPORT_PRODUCT_CODE,   '''' REPORT_PRODUCT_NAME, '''' RATEPLAN_NAME, '''' SIGN_ENTITY, ''''FEE_FLAG,     ' ||
                            '                    '''' TAX_RATE, '''' PAR_RES_SETTL_RATE, '''' SETTLEMENT_CLASS, '''' PRODUCT_TYPE, '''' OFFER_NAME, '''' PRODUCT_NAME      ' ||
                            '    FROM RPT_P2C_LIMITED WHERE REP_NUM = ''D313-1''  AND SETTLEMONTH = ''' || RPT_SETTLEMONTH || ''''||
                            '       UNION ALL    ' ||
                            '       SELECT PAR1.RATEPLAN_ID, PAR1.PAR_SETTL_AMOUNT, FT1.NUM, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, PARTNER_CODE,     ' ||
                            '                    PARTNER_NAME, REPORT_PRODUCT_CODE, REPORT_PRODUCT_NAME, RATEPLAN_NAME, SIGN_ENTITY, FEE_FLAG,    ' ||
                            '                    TAX_RATE, PAR_RES_SETTL_RATE, SETTLEMENT_CLASS, PRODUCT_TYPE,OFFER_NAME,PRODUCT_NAME    ' ||
                            '       FROM     ' ||
                            '           (SELECT     ' ||
                            '               RATEPLAN_ID,     ' ||
                            '               SUM( PAR_SETTL_AMOUNT ) * -1 PAR_SETTL_AMOUNT,     ' ||
                            '               MIN(ORDER_MODE) AS ORDER_MODE,     ' ||
                            '               MIN(OFFER_CODE) AS OFFER_CODE,      ' ||
                            '               MIN(PRODUCT_CODE) AS PRODUCT_CODE,     ' ||
                            '               MIN(PARTNER_CODE) AS PARTNER_CODE,      ' ||
                            '               MIN(PARTNER_NAME) AS PARTNER_NAME,      ' ||
                            '               MIN(REPORT_PRODUCT_CODE) AS REPORT_PRODUCT_CODE,    ' ||
                            '               MIN(REPORT_PRODUCT_NAME) AS REPORT_PRODUCT_NAME,      ' ||
                            '               MIN(RATEPLAN_NAME) AS RATEPLAN_NAME,     ' ||
                            '               MIN(SIGN_ENTITY) AS SIGN_ENTITY,    ' ||
                            '               ''2'' FEE_FLAG,    ' ||
                            '               MIN(TAX_RATE)  AS TAX_RATE,    ' ||
                            '               PAR_RES_SETTL_RATE,    ' ||
                            '               MIN(SETTLEMENT_CLASS)  AS SETTLEMENT_CLASS,     ' ||
                            '        MIN(PRODUCT_TYPE) PRODUCT_TYPE,    ' ||
                            '               MIN(OFFER_NAME) OFFER_NAME,     ' ||
                            '               MIN(PRODUCT_NAME)    PRODUCT_NAME      ' ||
                            '           FROM    ' ||
                            '               RPT_PARTNER    ' ||
                            '           WHERE       ' ||
                            '               SETTLEMONTH = ''' || RPT_SETTLEMONTH || ''''||
                            '               AND FEE_FLAG != 1     ' ||
                            '           GROUP BY RATEPLAN_ID, PAR_RES_SETTL_RATE) PAR1     ' ||
                            '       JOIN     ' ||
                            '           (SELECT ''*********'' RATEPLAN_ID, ''11'' NUM FROM DUAL     ' ||
                            '            UNION     ' ||
                            '            SELECT ''111800793'', ''12'' FROM DUAL     ' ||
                            '            UNION     ' ||
                            '            SELECT ''111800707'', ''13'' FROM DUAL     ' ||
                            '            UNION     ' ||
                            '            SELECT ''111800700'', ''14'' FROM DUAL    ' ||
                            '            UNION    ' ||
                            '            SELECT ''111800284'' RATEPLAN_ID, ''15'' FROM DUAL     ' ||
                            '            UNION     ' ||
                            '            SELECT ''111800285'', ''16'' FROM DUAL    ' ||
                            '            UNION     ' ||
                            '            SELECT ''111800286'', ''17'' FROM DUAL     ' ||
                            '            UNION     ' ||
                            '            SELECT ''111800283'', ''18'' FROM DUAL  ' ||
                            '            UNION    ' ||
                            '            SELECT ''111800706'' RATEPLAN_ID, ''19'' FROM DUAL    ' ||
                            '            UNION    ' ||
                            '            SELECT ''111800679'', ''20'' FROM DUAL   ' ||
                            '            UNION    ' ||
                            '            SELECT ''111800705'', ''21'' FROM DUAL    ' ||
                            '            UNION    ' ||
                            '            SELECT ''111800682'', ''22'' FROM DUAL    ' ||
                            '            UNION    ' ||
                            '           SELECT ''111800708'' RATEPLAN_ID, ''23'' FROM DUAL ) FT1     ' ||
                            '       ON PAR1.RATEPLAN_ID = FT1.RATEPLAN_ID    ' ||
                            '       UNION ALL     ' ||
                            '       SELECT PAR.RATEPLAN_ID, PAR.PAR_SETTL_AMOUNT, FT.NUM, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, PARTNER_CODE,     ' ||
                            '                    PARTNER_NAME, REPORT_PRODUCT_CODE, REPORT_PRODUCT_NAME, RATEPLAN_NAME, SIGN_ENTITY, FEE_FLAG,   ' ||
                            '                    TAX_RATE, PAR_RES_SETTL_RATE, SETTLEMENT_CLASS, PRODUCT_TYPE,OFFER_NAME,PRODUCT_NAME    ' ||
                            '       FROM    ' ||
                            '           (SELECT    ' ||
                            '               RATEPLAN_ID,    ' ||
                            '               SUM( PAR_SETTL_AMOUNT ) * -1 PAR_SETTL_AMOUNT,    ' ||
                            '               MIN(ORDER_MODE) AS ORDER_MODE,    ' ||
                            '               MIN(OFFER_CODE) AS OFFER_CODE,     ' ||
                            '               MIN(PRODUCT_CODE) AS PRODUCT_CODE,    ' ||
                            '               MIN(PARTNER_CODE) AS PARTNER_CODE,     ' ||
                            '               MIN(PARTNER_NAME) AS PARTNER_NAME,     ' ||
                            '               MIN(REPORT_PRODUCT_CODE) AS REPORT_PRODUCT_CODE,     ' ||
                            '               MIN(REPORT_PRODUCT_NAME) AS REPORT_PRODUCT_NAME,     ' ||
                            '               MIN(RATEPLAN_NAME) AS RATEPLAN_NAME,    ' ||
                            '               MIN(SIGN_ENTITY) AS SIGN_ENTITY,     ' ||
                            '               ''1'' FEE_FLAG,    ' ||
                            '               MIN(TAX_RATE)  AS TAX_RATE,     ' ||
                            '               PAR_RES_SETTL_RATE,    ' ||
                            '               MIN(SETTLEMENT_CLASS)  AS SETTLEMENT_CLASS,     ' ||
                            '          MIN(PRODUCT_TYPE) PRODUCT_TYPE,     ' ||
                            '               MIN(OFFER_NAME) OFFER_NAME,     ' ||
                            '               MIN(PRODUCT_NAME)    PRODUCT_NAME         ' ||
                            '           FROM     ' ||
                            '               RPT_PARTNER    ' ||
                            '           WHERE       ' ||
                            '               SETTLEMONTH = ''' || RPT_SETTLEMONTH || ''''||
                            '               AND FEE_FLAG = 1     ' ||
                            '           GROUP BY RATEPLAN_ID, PAR_RES_SETTL_RATE) PAR     ' ||
                            '       JOIN    ' ||
                            '           (SELECT  ''*********'' RATEPLAN_ID, ''31'' NUM FROM DUAL    ' ||
                            '               UNION    ' ||
                            '               SELECT ''111800793'', ''32'' FROM DUAL    ' ||
                            '               UNION     ' ||
                            '               SELECT ''111800707'', ''33'' FROM DUAL     ' ||
                            '               UNION     ' ||
                            '               SELECT ''111800700'', ''34'' FROM DUAL     ' ||
                            '               UNION     ' ||
                            '               SELECT ''111800284'' RATEPLAN_ID, ''35'' FROM DUAL   ' ||
                            '               UNION   ' ||
                            '               SELECT ''111800285'', ''36'' FROM DUAL  ' ||
                            '               UNION   ' ||
                            '               SELECT ''111800286'', ''37'' FROM DUAL   ' ||
                            '               UNION   ' ||
                            '               SELECT ''111800283'', ''38'' FROM DUAL  ' ||
                            '               UNION  ' ||
                            '               SELECT ''111800706'' RATEPLAN_ID, ''39'' FROM DUAL   ' ||
                            '               UNION   ' ||
                            '               SELECT ''111800679'', ''40'' FROM DUAL  ' ||
                            '               UNION   ' ||
                            '               SELECT ''111800705'', ''41'' FROM DUAL   ' ||
                            '               UNION   ' ||
                            '               SELECT ''111800682'', ''42'' FROM DUAL   ' ||
                            '               UNION  ' ||
                            '               SELECT ''111800708'' RATEPLAN_ID, ''43'' FROM DUAL ) FT   ' ||
                            '       ON PAR.RATEPLAN_ID = FT.RATEPLAN_ID  ' ||
                            '   ) AA  ' ||
                            ') WHERE RATEPLAN_ID != 10  ';


        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        RPT_RUNSITE:='1';
        RPT_SQL := 'end_time:' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, RPT_TABLE, RPT_SQL);

        outReturn  := 0;
        szSysErr := 'OK';

    END;

    SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;

END ;;
DELIMITER ;