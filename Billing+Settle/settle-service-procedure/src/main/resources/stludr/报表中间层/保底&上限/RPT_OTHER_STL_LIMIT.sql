/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：报表中间层 -保底/上限-保底/上限差额计算
**/
use stludr;
DROP PROCEDURE IF EXISTS stludr.`RPT_OTHER_STL_LIMIT`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "RPT_OTHER_STL_LIMIT"(
    RPT_SETTLEMONTH IN VARCHAR2,
    PROC_OUT OUT VARCHAR2
)
AS
    ---- 当年累计结算金额和保底/上限差额计算
    v_proc_name       VARCHAR2(30) := 'RPT_OTHER_STL_LIMIT';
    G_VERSION RVL_CONF_VERSION.VERSION%TYPE; --版本号变量
    G_RESNUM  RVL_CONF_VERSION.VERSIONNUM%TYPE; --版本序号
    ERRCODE   VARCHAR2(32);
    ERRMSG    VARCHAR2(2048);
    VER_TABLE VARCHAR2(64);

    RPT_RUNSITE VARCHAR2(32); --运行定位标识
    RPT_SQL     VARCHAR2(4000); --拼SQL的字符串变量
    RPT_TABLE   VARCHAR2(64); --拼表名的字符串变量
    outReturn  int;
    v_acc_val number(16);
BEGIN

    PROC_OUT := '';
    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        PROC_OUT := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || PROC_OUT ) AS error_msg ;
    END;

    BEGIN
        if ( length(RPT_SETTLEMONTH) < 6 )  then
            SELECT 'RPT_SETTLEMONTH length less than 6. RPT_SETTLEMONTH=[' || RPT_SETTLEMONTH || ']';
            outReturn := -1;
            ROLLBACK;
            RETURN;
        end if;

        SELECT 'RPT_SETTLEMONTH=' ||  RPT_SETTLEMONTH;

        --VER_TABLE   := 'STL_P2C_USAGE';
        VER_TABLE   := 'RVL_LIMIT_USAGE';
        RPT_RUNSITE := '0';
        RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期保底/上限差额计算开始时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        --删除当月数据
        --delete from stl_p2c_usage where settlemonth = RPT_SETTLEMONTH;
        delete from rvl_limit_usage where settle_month = RPT_SETTLEMONTH;

        RPT_SQL := '一阶段，写入基础数据...';
        RPT_RUNSITE := '1';

        insert into rvl_limit_usage(RULE_ID, LIMIT_TYPE, SETTLE_MONTH, EFF_MONTH, EXP_MONTH, TAX_RATE, PROV_CODE, MONTH_FEE, ACCU_FEE, NON_ADJ_FEE, ADJ_FEE)
        select a.RULE_ID, a.LIMIT_TYPE, rpt_settlemonth, a.EFF_MONTH, a.EXP_MONTH, '6', a.PROV_CODE, 0, 0, 0, 0
          from rvl_limit_fee a
        where rpt_settlemonth between a.EFF_MONTH and a.EXP_MONTH;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        RPT_SQL := '二阶段，更新当月结算额...';
        RPT_RUNSITE := '2';

        merge into rvl_limit_usage a
        using (select t2.RULE_ID, t1.OUTPROV_CODE, sum(t1.SETTLEMENT_AMOUNT) month_fee,
                      round(sum(decode(t1.FEE_FLAG, 1, t1.SETTLEMENT_AMOUNT, 0))) non_adj_fee, round(sum(decode(t1.FEE_FLAG, 2, t1.SETTLEMENT_AMOUNT, 0))) adj_fee
                  from rpt_p2c t1, rvl_limit_biz t2
                where t1.SETTLEMONTH = rpt_settlemonth
                  and (t2.TYPE = 'O' and t1.OFFER_CODE = t2.OFFER_CODE
                    or t2.TYPE = 'OP' and t1.OFFER_CODE = t2.OFFER_CODE and t1.PRODUCT_CODE = t2.PRODUCT_CODE
                    or t2.TYPE = 'OPR' and t1.OFFER_CODE = t2.OFFER_CODE and t1.PRODUCT_CODE = t2.PRODUCT_CODE and t1.rateplan_id = t2.rateplan_id and t1.offer_code <> '1010402'
                    or t2.TYPE = 'OPR' and t1.OFFER_CODE = t2.OFFER_CODE and t1.PRODUCT_CODE = t2.PRODUCT_CODE and t1.rateplan_id = t2.rateplan_id and t1.offer_code = '1010402' and t1.settlement_class = 1 and t1.outprov_code <> '000'
                    or t2.TYPE = 'OPF' and t1.OFFER_CODE = t2.OFFER_CODE and t1.PRODUCT_CODE = t2.PRODUCT_CODE and t1.CHARGE_CODE = t2.CHARGE_CODE
                    or t2.TYPE = 'OPFR' and t1.OFFER_CODE = t2.OFFER_CODE and t1.PRODUCT_CODE = t2.PRODUCT_CODE and t1.CHARGE_CODE = t2.CHARGE_CODE and t1.rateplan_id = t2.rateplan_id
                    or t2.TYPE = 'OF' and t1.OFFER_CODE = t2.OFFER_CODE and t1.CHARGE_CODE = t2.CHARGE_CODE)
                group by t2.RULE_ID, t1.OUTPROV_CODE) b
            on (a.RULE_ID = b.RULE_ID and a.PROV_CODE = b.OUTPROV_CODE)
          when matched then
                update set a.MONTH_FEE = b.MONTH_FEE, a.NON_ADJ_FEE = b.NON_ADJ_FEE, a.ADJ_FEE = b.ADJ_FEE
                where a.SETTLE_MONTH = rpt_settlemonth;

        merge into rvl_limit_usage a
            using (select t2.rule_id, t1.prov_cd, sum(t1.a_fee + t1.c_fee) month_fee
                   from stl_pvs t1, rvl_limit_biz t2
                   where t1.acct_month = rpt_settlemonth
                     and t1.joint_flag = '0' and t1.inner_customer_flag = '0' and t1.ydn_flag = '0'
                     and t2.type = 'I+P'
                   group by t2.rule_id, t1.prov_cd) b
            on (a.rule_id = b.rule_id and a.prov_code = b.prov_cd)
            when matched then
                update set a.month_fee = b.month_fee
            where a.settle_month = rpt_settlemonth;

        call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        RPT_SQL := '三阶段，更新累计结算额...';
        RPT_RUNSITE := '3';

	-- 普通业务
        merge into rvl_limit_usage a
        using (select RULE_ID, LIMIT_TYPE, PROV_CODE, nvl(accu_fee, 0) accu_fee, CEILING_REACHED, GAP_FROM_CEILING,SPECIAL_ACCU_FEE,SPECIAL_TAG
                  from rvl_limit_usage b
                where settle_month = decode(rpt_settlemonth, b.EFF_MONTH, rpt_settlemonth,
                      to_char(add_months(to_date(rpt_settlemonth, 'yyyymm'), -1), 'yyyymm'))
                  and rpt_settlemonth between b.eff_month and b.exp_month) t
            on (a.RULE_ID = t.RULE_ID and a.LIMIT_TYPE = t.LIMIT_TYPE and a.PROV_CODE = t.PROV_CODE)
          when matched then
                update set a.accu_fee = nvl(t.accu_fee + a.month_fee - decode(t.ceiling_reached, '1', t.gap_from_ceiling, 0), 0),
                    a.SPECIAL_ACCU_FEE = t.SPECIAL_ACCU_FEE,
                    a.SPECIAL_TAG = t.SPECIAL_TAG
       where a.SETTLE_MONTH = rpt_settlemonth
				  and a.rule_id not in (select distinct rule_id from rvl_limit_biz where type = 'I+P');
				
	-- 移动云自有I+P
merge into rvl_limit_usage a
    using (select PROV_CD, sum(a_fee + c_fee) accu_fee
           from stl_pvs b
           where acct_month >= substr(rpt_settlemonth, 1, 4) || '01' and acct_month <= rpt_settlemonth
             and joint_flag = '0' and inner_customer_flag = '0' and ydn_flag = '0'
           group by prov_cd) t
    on (a.PROV_CODE = t.PROV_CD)
    when matched then
        update set a.accu_fee = t.accu_fee
    where a.SETTLE_MONTH = rpt_settlemonth
				  and a.rule_id in (select distinct rule_id from rvl_limit_biz where type = 'I+P');

call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

        RPT_SQL := '四阶段，更新与保底/上限的距离...';
        RPT_RUNSITE := '4';

        merge into rvl_limit_usage a
        using (select b.RULE_ID, b.LIMIT_TYPE, b.PROV_CODE, b.LIMIT_FEE
                  from rvl_limit_fee b
                where rpt_settlemonth between b.EFF_MONTH and b.EXP_MONTH) t
            on (a.RULE_ID = t.RULE_ID and a.LIMIT_TYPE = t.LIMIT_TYPE and a.PROV_CODE = t.PROV_CODE)
          when matched then
                update set a.GAP_FROM_CEILING = a.ACCU_FEE - t.LIMIT_FEE,
                          a.CEILING_REACHED = decode(sign(a.ACCU_FEE - t.LIMIT_FEE), 1, 1, 0)
                where a.SETTLE_MONTH = rpt_settlemonth
                  and a.LIMIT_TYPE = 'C' and a.ceiling_reached is null;
-- -- 80% 保底改造 上限特殊处理 11月 12月，GAP_FROM_CEILING 去掉10月补足得80%得金额
-- merge into rvl_limit_usage a
--     using (select b.RULE_ID, b.LIMIT_TYPE, b.PROV_CODE, b.LIMIT_FEE
--            from rvl_limit_fee b
--            where rpt_settlemonth between b.EFF_MONTH and b.EXP_MONTH
--              AND (SUBSTR(rpt_settlemonth,-2) = '11' or SUBSTR(rpt_settlemonth,-2) = '12') AND b.RULE_ID != 17) t
--     on (a.RULE_ID = t.RULE_ID and a.LIMIT_TYPE = t.LIMIT_TYPE and a.PROV_CODE = t.PROV_CODE)
--     when matched then
--         update set a.GAP_FROM_CEILING = a.ACCU_FEE - t.LIMIT_FEE + a.SPECIAL_ACCU_FEE,
--             a.CEILING_REACHED = decode(sign(a.ACCU_FEE - t.LIMIT_FEE), 1, 1, 0)
--     where a.SETTLE_MONTH = rpt_settlemonth
--                and a.LIMIT_TYPE = 'C';
--     -- 80% 保底改造 上限特殊处理 12月，GAP_FROM_CEILING 去掉10月补足得80%得金额
--     merge into rvl_limit_usage a
--         using (select b.RULE_ID, b.LIMIT_TYPE, b.PROV_CODE, b.LIMIT_FEE
--                from rvl_limit_fee b
--                where rpt_settlemonth between b.EFF_MONTH and b.EXP_MONTH
--                  AND SUBSTR(rpt_settlemonth,-2)= '12' AND b.RULE_ID != 17) t
--         on (a.RULE_ID = t.RULE_ID and a.LIMIT_TYPE = t.LIMIT_TYPE and a.PROV_CODE = t.PROV_CODE)
--         when matched then
--             update set a.GAP_FROM_CEILING =
--                     CASE WHEN SPECIAL_TAG ='1' THEN month_fee
--                          WHEN ACCU_FEE - MONTH_FEE <=  t.LIMIT_FEE AND ACCU_FEE - MONTH_FEE > (LIMIT_FEE * 0.8) THEN a.ACCU_FEE - t.LIMIT_FEE
--                          WHEN ACCU_FEE - MONTH_FEE > t.LIMIT_FEE THEN month_fee
--                          ELSE a.ACCU_FEE - t.LIMIT_FEE + a.SPECIAL_ACCU_FEE
--                         END ,
--                 a.CEILING_REACHED = decode(sign(a.ACCU_FEE - t.LIMIT_FEE), 1, 1, 0)
--         where a.SETTLE_MONTH = rpt_settlemonth
--                    and a.LIMIT_TYPE = 'C' ;
--     -- 80% 保底改造 特殊处理
--     merge into rvl_limit_usage a
--         using (select b.RULE_ID, b.LIMIT_TYPE, b.PROV_CODE, b.LIMIT_FEE
--                from rvl_limit_fee b
--                where rpt_settlemonth between b.EFF_MONTH and b.EXP_MONTH
--                  AND (SUBSTR(rpt_settlemonth,-2)= '11' OR SUBSTR(rpt_settlemonth,-2)= '12') AND b.RULE_ID != 17) t
--         on (a.RULE_ID = t.RULE_ID and a.LIMIT_TYPE = t.LIMIT_TYPE and a.PROV_CODE = t.PROV_CODE)
--         when matched then
--             update set a.SPECIAL_ACCU_FEE =
--                     CASE WHEN SUBSTR(rpt_settlemonth,-2)= '12' THEN NULL
--                          WHEN month_fee > SPECIAL_ACCU_FEE THEN 0
--                          ELSE SPECIAL_ACCU_FEE - month_fee
--                         END
--         where a.SETTLE_MONTH = rpt_settlemonth  AND a.SPECIAL_ACCU_FEE IS NOT null
--                    and a.LIMIT_TYPE = 'C' ;

----保底
        merge into rvl_limit_usage a
        using (select b.RULE_ID, b.LIMIT_TYPE, b.PROV_CODE, b.LIMIT_FEE
                  from rvl_limit_fee b
                where rpt_settlemonth between b.EFF_MONTH and b.EXP_MONTH) t
            on (a.RULE_ID = t.RULE_ID and a.LIMIT_TYPE = t.LIMIT_TYPE and a.PROV_CODE = t.PROV_CODE)
          when matched then
                update set a.GAP_TIL_FLOOR = t.LIMIT_FEE - a.ACCU_FEE,
                          a.FLOOR_UNREACHED = decode(sign(t.LIMIT_FEE - a.ACCU_FEE), 1, 1, 0)
                where a.SETTLE_MONTH = rpt_settlemonth
                  and a.SETTLE_MONTH = a.EXP_MONTH
                  and a.LIMIT_TYPE = 'F' and a.floor_unreached is null;

---- 80% 保底改造 特殊处理 10月按保底80%处理
merge into rvl_limit_usage a
    using (select b.RULE_ID, b.LIMIT_TYPE, b.PROV_CODE, b.LIMIT_FEE
           from rvl_limit_fee b
           where SUBSTR(rpt_settlemonth,-2)= '10' and rpt_settlemonth between b.EFF_MONTH and b.EXP_MONTH) t
    on (a.RULE_ID = t.RULE_ID and a.LIMIT_TYPE = t.LIMIT_TYPE and a.PROV_CODE = t.PROV_CODE and a.RULE_ID != '18')
    when matched then
        update SET a.GAP_TIL_FLOOR = ROUND(t.LIMIT_FEE * 0.8) - a.ACCU_FEE,
            a.SPECIAL_ACCU_FEE = ROUND(t.LIMIT_FEE * 0.8) - a.ACCU_FEE,
            a.SPECIAL_TAG = 0
    where a.SETTLE_MONTH = rpt_settlemonth
          and a.LIMIT_TYPE = 'F'
          and ROUND(t.LIMIT_FEE * 0.8) > a.ACCU_FEE;

---- 80% 保底改造 特殊处理 10月已经超过80%的 SPECIAL_ACCU_FEE 置为 0
merge into rvl_limit_usage a
    using (select b.RULE_ID, b.LIMIT_TYPE, b.PROV_CODE, b.LIMIT_FEE
           from rvl_limit_fee b
           where SUBSTR(rpt_settlemonth,-2)= '10' and rpt_settlemonth between b.EFF_MONTH and b.EXP_MONTH) t
    on (a.RULE_ID = t.RULE_ID and a.LIMIT_TYPE = t.LIMIT_TYPE and a.PROV_CODE = t.PROV_CODE and a.RULE_ID != '18')
    when matched then
        update SET a.SPECIAL_ACCU_FEE = 0
    where a.SETTLE_MONTH = rpt_settlemonth
          and a.LIMIT_TYPE = 'F'
          and ROUND(t.LIMIT_FEE * 0.8) <= a.ACCU_FEE;


---- 80% 保底改造 特殊处理 11月
---- 如果累计金额《 80%  报表 0所以 -month_fee
---- 如果》80%，扣减掉80%  与实际累计金额得差值
merge into rvl_limit_usage a
    using (select b.RULE_ID, b.LIMIT_TYPE, b.PROV_CODE, b.LIMIT_FEE
           from rvl_limit_fee b
           where SUBSTR(rpt_settlemonth,-2)= '11' and rpt_settlemonth between b.EFF_MONTH and b.EXP_MONTH) t
    on (a.RULE_ID = t.RULE_ID and a.LIMIT_TYPE = t.LIMIT_TYPE and a.PROV_CODE = t.PROV_CODE and a.RULE_ID != '18')
    when matched then
        update SET a.GAP_TIL_FLOOR =
                CASE WHEN a.month_fee <= SPECIAL_ACCU_FEE THEN -month_fee
                     WHEN a.month_fee > SPECIAL_ACCU_FEE   THEN - SPECIAL_ACCU_FEE --11月超过80%
                     ELSE t.LIMIT_FEE - a.ACCU_FEE
                    END,
            a.SPECIAL_ACCU_FEE =
                    CASE WHEN a.month_fee <= SPECIAL_ACCU_FEE THEN SPECIAL_ACCU_FEE - a.month_fee
                         WHEN a.month_fee > SPECIAL_ACCU_FEE  THEN 0
                        END
    where a.SETTLE_MONTH = rpt_settlemonth and a.LIMIT_TYPE = 'F' and a.SPECIAL_TAG is not null;
---- 80% 保底改造 特殊处理 12月
merge into rvl_limit_usage a
    using (select b.RULE_ID, b.LIMIT_TYPE, b.PROV_CODE, b.LIMIT_FEE
           from rvl_limit_fee b
           where SUBSTR(rpt_settlemonth,-2)= '12' and rpt_settlemonth between b.EFF_MONTH and b.EXP_MONTH) t
    on (a.RULE_ID = t.RULE_ID and a.LIMIT_TYPE = t.LIMIT_TYPE and a.PROV_CODE = t.PROV_CODE and a.RULE_ID != '18')
    when matched then
        update SET a.GAP_TIL_FLOOR =
                CASE WHEN a.month_fee < SPECIAL_ACCU_FEE THEN t.LIMIT_FEE - a.ACCU_FEE - SPECIAL_ACCU_FEE  -- 12月未到80%
                     WHEN a.month_fee >= SPECIAL_ACCU_FEE AND accu_fee < limit_fee and SPECIAL_ACCU_FEE > 0  THEN t.LIMIT_FEE - a.ACCU_FEE - SPECIAL_ACCU_FEE
                     WHEN a.month_fee >= SPECIAL_ACCU_FEE AND accu_fee < limit_fee and SPECIAL_ACCU_FEE = 0  THEN t.LIMIT_FEE - a.ACCU_FEE
                     WHEN a.month_fee >= SPECIAL_ACCU_FEE AND accu_fee > limit_fee and SPECIAL_ACCU_FEE = 0  THEN 0
                     WHEN a.month_fee >= SPECIAL_ACCU_FEE AND accu_fee > limit_fee and SPECIAL_ACCU_FEE > 0  THEN -SPECIAL_ACCU_FEE
                     ELSE t.LIMIT_FEE - a.ACCU_FEE
                    END,
            a.SPECIAL_ACCU_FEE = 0
    where a.SETTLE_MONTH = rpt_settlemonth
          and a.LIMIT_TYPE = 'F' and a.SPECIAL_TAG is not null;

-- ---- 80% 保底改造 特殊处理10,11月  超过上限走上限逻辑没超过 走保底得逻辑
merge into rvl_limit_usage a
    using (select b.RULE_ID, b.LIMIT_TYPE, b.PROV_CODE, b.LIMIT_FEE
           from rvl_limit_fee b
           where (SUBSTR(rpt_settlemonth,-2)= '10' or SUBSTR(rpt_settlemonth,-2)= '11') and rpt_settlemonth between b.EFF_MONTH and b.EXP_MONTH) t
    on (a.RULE_ID - 1 = t.RULE_ID and a.LIMIT_TYPE != t.LIMIT_TYPE and a.PROV_CODE = t.PROV_CODE and a.RULE_ID != '18' )
    when matched then
        update set a.SPECIAL_TAG =
                CASE WHEN accu_fee >= t.LIMIT_FEE THEN  a.SPECIAL_TAG + 1
                     ELSE 0
                    END
    where a.SETTLE_MONTH = rpt_settlemonth
              and a.LIMIT_TYPE = 'F' and a.SPECIAL_TAG is not null;
----  12月处理
merge into rvl_limit_usage a
    using (select b.RULE_ID, b.LIMIT_TYPE, b.PROV_CODE, b.LIMIT_FEE
           from rvl_limit_fee b
           where  SUBSTR(rpt_settlemonth,-2)= '12' and rpt_settlemonth between b.EFF_MONTH and b.EXP_MONTH) t
    on (a.RULE_ID - 1 = t.RULE_ID and a.LIMIT_TYPE != t.LIMIT_TYPE and a.PROV_CODE = t.PROV_CODE and a.RULE_ID != '18' )
    when matched then
        update set a.SPECIAL_TAG =
                CASE WHEN accu_fee - month_fee >= t.LIMIT_FEE THEN 2
                     WHEN accu_fee - month_fee < t.LIMIT_FEE and accu_fee > t.LIMIT_FEE THEN 1
                     ELSE 0
                    END
    where a.SETTLE_MONTH = rpt_settlemonth
              and a.LIMIT_TYPE = 'F' and a.SPECIAL_TAG is not null;

---- 80% 保底改造 特殊处理 对应上限得SPECIAL_ACCU_FEE SPECIAL_TAG 同步更新
merge into rvl_limit_usage a
    using (select b.RULE_ID,b.PROV_CODE,b.SPECIAL_ACCU_FEE,b.LIMIT_TYPE,b.SPECIAL_TAG from rvl_limit_usage b
           where SUBSTR(rpt_settlemonth,-2)= '10'  and b.settle_month = rpt_settlemonth and b.SPECIAL_TAG is not null) t
    on (a.RULE_ID + 1 = t.RULE_ID and a.LIMIT_TYPE != t.LIMIT_TYPE and a.PROV_CODE = t.PROV_CODE and a.RULE_ID != '17'  )
    when matched then
        update set a.SPECIAL_ACCU_FEE = t.SPECIAL_ACCU_FEE,a.SPECIAL_TAG = t.SPECIAL_TAG
    where a.SETTLE_MONTH = rpt_settlemonth
      and a.LIMIT_TYPE = 'C';

merge into rvl_limit_usage a
    using (select b.RULE_ID,b.PROV_CODE,b.SPECIAL_ACCU_FEE,b.LIMIT_TYPE,b.SPECIAL_TAG from rvl_limit_usage b
           where SUBSTR(rpt_settlemonth,-2)= '11' and b.settle_month = rpt_settlemonth and b.SPECIAL_TAG is not null) t
    on (a.RULE_ID + 1 = t.RULE_ID and a.LIMIT_TYPE != t.LIMIT_TYPE and a.PROV_CODE = t.PROV_CODE and a.RULE_ID != '17' )
    when matched then
        update set a.SPECIAL_ACCU_FEE =
                case when a.MONTH_FEE > a.SPECIAL_ACCU_FEE and t.SPECIAL_TAG = 2 then 0
                     when a.MONTH_FEE > a.SPECIAL_ACCU_FEE and t.SPECIAL_TAG = 1 then a.SPECIAL_ACCU_FEE
                     when a.MONTH_FEE > a.SPECIAL_ACCU_FEE and t.SPECIAL_TAG = 0 then 0
                     when a.MONTH_FEE <= a.SPECIAL_ACCU_FEE and t.SPECIAL_TAG = 0 then t.SPECIAL_ACCU_FEE
                     else t.SPECIAL_ACCU_FEE
                    end,
            a.SPECIAL_TAG = t.SPECIAL_TAG
    where a.SETTLE_MONTH = rpt_settlemonth
      and a.LIMIT_TYPE = 'C' ;

merge into rvl_limit_usage a
    using (select b.RULE_ID,b.PROV_CODE,b.SPECIAL_ACCU_FEE,b.LIMIT_TYPE,b.SPECIAL_TAG from rvl_limit_usage b
           where SUBSTR(rpt_settlemonth,-2)= '12' and b.settle_month = rpt_settlemonth and b.SPECIAL_TAG is not null) t
    on (a.RULE_ID + 1 = t.RULE_ID and a.LIMIT_TYPE != t.LIMIT_TYPE and a.PROV_CODE = t.PROV_CODE and a.RULE_ID != '17' )
    when matched then
        update set a.SPECIAL_ACCU_FEE =
                case when  t.SPECIAL_TAG = 2 then 0
                     when t.SPECIAL_TAG = 1 then a.SPECIAL_ACCU_FEE
                     else a.SPECIAL_ACCU_FEE - a.MONTH_FEE
                    end,
            a.SPECIAL_TAG = t.SPECIAL_TAG
    where a.SETTLE_MONTH = rpt_settlemonth
      and a.LIMIT_TYPE = 'C';

call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);

commit;

PROC_OUT:='Y';
        outReturn := 0;

SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
    END;
END ;;
DELIMITER ;
