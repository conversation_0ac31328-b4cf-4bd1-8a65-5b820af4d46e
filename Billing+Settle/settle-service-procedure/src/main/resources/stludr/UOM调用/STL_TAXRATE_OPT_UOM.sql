/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：出账流程 -接口表税率及状态更新/税率稽核总调用
**/
DROP PROCEDURE IF EXISTS stludr.`STL_TAXRATE_OPT_UOM`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "STL_TAXRATE_OPT_UOM"(
    inMonth          IN   VARCHAR2,
    inBatch          IN   VARCHAR2,
    flag_version     IN   VARCHAR2,
    reserve1         IN   VARCHAR2,
    reserve2         IN   VARCHAR2,
    proc_out         OUT  VARCHAR2,
    outSysError      OUT  VARCHAR2,
    outReturn        OUT  NUMBER,
    outBL            OUT  VARCHAR2,
    outAR            OUT  VARCHAR2
)
AS

    v_proc_name       VARCHAR2(30) := 'STL_TAXRATE_OPT_UOM';
    vSql varchar2(9999);


BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        outSysError := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg ;
    END;

    BEGIN

        outSysError := '';
        outReturn := 0;

        if ( length(inMonth) < 6 )  then
            SELECT 'inMonth length less than 6. inMonth=[' || inMonth || ']';
            ROLLBACK;
            outReturn := -1;
            RETURN;
        end if;

        SELECT 'inMonth=' ||  inMonth FROM dual;

        call STL_UPD_TAXRATE(inMonth,inBatch, outSysError, outReturn);
        IF ( outReturn != 0) THEN
            SELECT 'call STL_UPD_TAXRATE error, return_code=' || outReturn;
            ROLLBACK;
            outReturn := -1;
            RETURN;
        END IF;
        commit;

        call STL_CHK_TAXRATE(inMonth, outSysError, outReturn, outBL, outAR);
        IF ( outReturn != 0) THEN
            SELECT 'call STL_CHK_TAXRATE error, return_code=' || outReturn;
            ROLLBACK;
            outReturn := -1;
            RETURN;
        END IF;
        commit;

        outSysError := 'OK';
        outReturn := 0;

        SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || to_number(outReturn);
    END;

END ;;
DELIMITER ;