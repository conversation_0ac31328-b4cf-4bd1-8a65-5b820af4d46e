/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：出账流程 -报表中间表金额小数点还原
**/
use stludr;
DROP PROCEDURE IF EXISTS stludr.`DIGIT_RESTORE_MAIN_UOM`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE stludr.`DIGIT_RESTORE_MAIN_UOM`(inMonth in varchar2,
                                        inBatch IN VARCHAR2,
                                        flag_version IN VARCHAR2,
                                        reserve1 IN VARCHAR2,
                                        reserve2 IN VARCHAR2,
                                        proc_out OUT VARCHAR2,
                                        outSysError OUT VARCHAR2(1000),
                                        outReturn OUT NUMBER(4),
                                        outBL OUT VARCHAR2,
                                        outAR OUT VARCHAR2)
AS
	-- inMonth 月份
	-- inBatch 批次
	-- flag_version 是否保留上个版本标识  为脚本的调用 统一增加 具体看存储过程有没有实际应用
    v_proc_name  VARCHAR2(50) := 'DIGIT_RESTORE_MAIN_UOM';
BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        outSysError := substr(@p2, 1, 1000);
        outReturn  := -1;
        outBL := -1;
        outAR := -1;
        ROLLBACK;

        select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg ;
    END;

    BEGIN

      call STL_INFO_LOG(inMonth, '0', v_proc_name, '扩位还原开始'||TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

      call STL_DIGIT_CMCC_AR(inMonth,inBatch,proc_out,outSysError,outReturn);

  -- 1+2
      call STL_DIGIT_CMCC_BL(inMonth,inBatch,proc_out,outSysError,outReturn);

      call STL_DIGIT_RPT_P2C(inMonth,inBatch,proc_out,outSysError,outReturn);

      call STL_DIGIT_RPT_PARTNER(inMonth,inBatch,proc_out,outSysError,outReturn);

      call STL_DIGIT_AR_INTERPROV(inMonth,inBatch,proc_out,outSysError,outReturn);

  -- 1+2
      call STL_DIGIT_BL_INTERPROV(inMonth,inBatch,proc_out,outSysError,outReturn);

      call STL_INFO_LOG(inMonth, '0', v_proc_name, '扩位还原结束'||TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));
      COMMIT;
    proc_out :='0';
    outSysError := 'OK';
    outReturn := 0;
    outBL := 0;
    outAR := 0;

      SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
    END;
END ;;
DELIMITER ;