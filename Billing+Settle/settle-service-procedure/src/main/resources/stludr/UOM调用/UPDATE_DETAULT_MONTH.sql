/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：出账流程 -更新界面报表展示默认账期
**/
DROP PROCEDURE IF EXISTS stludr.`UPDATE_DETAULT_MONTH`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "UPDATE_DETAULT_MONTH"(F_MONTH  IN VARCHAR2,
                                                  SETTMODE IN VARCHAR2,
												  PROC_OUT OUT VARCHAR2,
                                          		  szSysErr OUT VARCHAR2(1000),
                                        		  nReturn OUT NUMBER(4))
AS
  --F_MONTH 账期
  --SETTMODE 1-应收 2-实收
  U_NUM NUMBER;
  P_SQL  VARCHAR2(4000);
  v_proc_name       VARCHAR2(50) := 'UPDATE_DETAULT_MONTH';

BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
        ROLLBACK;

        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;
    BEGIN
		IF (SETTMODE = '1') THEN
		DELETE FROM RVL_DETAULT_MONTH T
		WHERE T.T_SETTMODE = 'BL'
		AND T.T_MONTH = F_MONTH;

		UPDATE RVL_DETAULT_MONTH T
		SET T.T_NEW = 'N'
		WHERE T.T_SETTMODE = 'BL'
		AND T.T_NEW = 'Y';

		INSERT INTO RVL_DETAULT_MONTH
		(T_MONTH, T_SETTMODE, T_NEW)
		VALUES
			(F_MONTH, 'BL', 'Y');

		ELSIF (SETTMODE = '2') THEN
		DELETE FROM RVL_DETAULT_MONTH T
		WHERE T.T_SETTMODE = 'AR'
		AND T.T_MONTH = F_MONTH;

		UPDATE RVL_DETAULT_MONTH T
		SET T.T_NEW = 'N'
		WHERE T.T_SETTMODE = 'AR'
		AND T.T_NEW = 'Y';

		INSERT INTO RVL_DETAULT_MONTH
		(T_MONTH, T_SETTMODE, T_NEW)
		VALUES
			(F_MONTH, 'AR', 'Y');
		ELSE
			PROC_OUT := 'N(ERRMSG:输入应实收标识错误!)';
			RETURN;
		END IF;
		COMMIT;
		PROC_OUT := 'Y';

  nReturn := 0;
  szSysErr := 'OK';

  commit;

  SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;

  END;
END ;;
DELIMITER ;