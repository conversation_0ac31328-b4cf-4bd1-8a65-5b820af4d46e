/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：出账流程 -二批省专报表保底上下限计算 (执行完小数点扩位还原后 执行)
**/
use stludr;
DELIMITER ;;
CREATE or replace DEFINER="stludr"@"10.%" PROCEDURE `stludr`.RPT_LIMIT_CALC_UOM(
                                        inMonth in varchar2,
                                        inBatch IN VARCHAR2,
                                        flag_version IN VARCHAR2,
                                        reserve1 IN VARCHAR2,
                                        reserve2 IN VARCHAR2,
                                        proc_out OUT VARCHAR2,
                                        outSysError OUT VARCHAR2(1000),
                                        outReturn OUT NUMBER(4),
                                        outBL OUT VARCHAR2,
                                        outAR OUT VARCHAR2)
AS
	-- inMonth 月份
	-- inBatch 批次
	-- flag_version 是否保留上个版本标识  为脚本的调用 统一增加 具体看存储过程有没有实际应用
    v_proc_name  VARCHAR2(50) := 'RPT_LIMIT_CALC_UOM';
    RPT_RUNSITE VARCHAR2(64);
    RPT_SQL VARCHAR2(4096);
BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        outSysError := substr(@p2, 1, 1000);
        outReturn  := -1;
        outBL := -1;
        outAR := -1;
        ROLLBACK;

        select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg ;
    END;
    BEGIN
     RPT_RUNSITE := '1';
     RPT_SQL     := inBatch || '-执行开始时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
      select 'procedure ' || v_proc_name || ' start ' || inBatch ;

        call STL_INFO_LOG(inMonth, RPT_RUNSITE, v_proc_name, RPT_SQL);

      if inBatch = '2' then
		call RPT_OTHER_STL_LIMIT(inMonth, PROC_OUT);
		call RPT_OTHER_STL_P2C_LIMITED(inMonth, FLAG_VERSION, PROC_OUT);

        call STL_PARTNER_LIMITED(inMonth, FLAG_VERSION,  outReturn, outSysError);
        select 'success';
      else
        select 'inBatch is not 2 skip';
	  end if;

	    RPT_RUNSITE := '2';
        RPT_SQL     := inBatch || '-执行完成时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
        call STL_INFO_LOG(inMonth, RPT_RUNSITE, v_proc_name, RPT_SQL);
    proc_out :='0';
    outSysError := 'OK';
    outReturn := 0;
    outBL := 0;
    outAR := 0;

      SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
    END;
END ;;
DELIMITER ;