/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：出账流程-数据源金额小数点扩位
**/
DROP PROCEDURE IF EXISTS stludr.`DIGIT_EXPAND_MAIN_UOM`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "DIGIT_EXPAND_MAIN_UOM"(inMonth in varchar2,
                                        inBatch IN VARCHAR2,
                                        flag_version IN VARCHAR2,
                                        reserve1 IN VARCHAR2,
                                        reserve2 IN VARCHAR2,
                                        proc_out OUT VARCHAR2,
                                        outSysError OUT VARCHAR2(1000),
                                        outReturn OUT NUMBER(4),
                                        outBL OUT VARCHAR2,
                                        outAR OUT VARCHAR2 )
AS
	-- inMonth 月份
	-- inBatch 批次
	-- flag_version 是否保留上个版本标识  为脚本的调用 统一增加 具体看存储过程有没有实际应用
    v_proc_name  VARCHAR2(50) := 'DIGIT_EXPAND_MAIN_UOM';
BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        outSysError := substr(@p2, 1, 1000);
        outReturn  := -1;
        outBL := -1;
        outAR := -1;
        ROLLBACK;

        select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg ;
        call STLUDR.STL_ERROR_LOG(inMonth,outReturn,@p1,'',v_proc_name,outSysError);
    END;
    BEGIN

      call STL_DIGIT_SYNC_INTERFACE_BL_T(inMonth, inBatch, PROC_OUT,outSysError,outReturn);

	  call STL_DIGIT_SYNC_INTERFACE_AR_T(inMonth, inBatch, PROC_OUT,outSysError,outReturn);

	  call STL_DIGIT_SYNC_INTERFACE_SW_T(inMonth, inBatch, PROC_OUT,outSysError,outReturn);

      SELECT 'SUCCESS';
      COMMIT;

      proc_out :='0';
      outSysError := 'OK';
      outReturn := 0;
      outBL := 0;
      outAR := 0;

      SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
      call STL_INFO_LOG(inMonth, '', v_proc_name, outSysError);
    END;
END ;;
DELIMITER ;