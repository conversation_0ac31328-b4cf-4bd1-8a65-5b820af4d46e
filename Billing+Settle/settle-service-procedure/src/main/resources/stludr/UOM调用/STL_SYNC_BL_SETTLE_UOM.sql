/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：出账流程 -话单数据汇总总调用
**/
DROP PROCEDURE IF EXISTS stludr.`STL_SYNC_BL_SETTLE_UOM`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "STL_SYNC_BL_SETTLE_UOM"(
    inMonth          IN   VARCHAR2,
    inBatch          IN   VARCHAR2,
    flag_version     IN   VARCHAR2,
    reserve1         IN   VARCHAR2,
    reserve2         IN   VARCHAR2,
    proc_out         OUT  VARCHAR2,
    outSysError      OUT  VARCHAR2,
    outReturn        OUT  NUMBER,
    outBL            OUT  VARCHAR2,
    outAR            OUT  VARCHAR2

)
AS

    v_proc_name       VARCHAR2(30) := 'STL_SYNC_BL_SETTLE_UOM';
    P_ERRCODE   VARCHAR2(32);
    P_ERRMSG    VARCHAR2(2048);

BEGIN

    outSysError := '';
    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
    GET DIAGNOSTICS CONDITION 1 P_ERRCODE = RETURNED_SQLSTATE, P_ERRMSG = MESSAGE_TEXT;
        set outSysError = substr(P_ERRMSG, 1, 1000);
	    set outReturn  = -1;
ROLLBACK;
select ('exception: ' || outReturn || '|' || '|' || outSysError ) AS error_msg ;
call STLUDR.STL_ERROR_LOG(inMonth,'-1',outSysError,'',v_proc_name,'');

END;


BEGIN
call STL_INFO_LOG(inMonth, '', v_proc_name,  'Begin call STL_SYNC_BL_SETTLE_OTHER');
call STL_SYNC_BL_SETTLE_OTHER(inMonth,inBatch,flag_version,reserve1,reserve2,proc_out,@outSysError,@outReturn,outBL,outAR);
IF ( @outReturn != 0) THEN
    SELECT 'call STL_SYNC_BL_SETTLE_OTHER error, return_code=' || @outReturn FROM dual;
    ROLLBACK;
    RETURN;
END IF;
commit;

call STL_INFO_LOG(inMonth, '', v_proc_name,  'Begin call STL_SYNC_BL_SETTLE_MAS');
call STL_SYNC_BL_SETTLE_MAS(inMonth,inBatch,flag_version,reserve1,reserve2,proc_out,@outSysError,@outReturn,outBL,outAR);
IF ( @outReturn != 0) THEN
SELECT 'call STL_SYNC_BL_SETTLE_MAS error, return_code=' || @outReturn FROM dual;
ROLLBACK;
RETURN;
END IF;
commit;

call STL_INFO_LOG(inMonth, '', v_proc_name,  'Begin call STL_SYNC_BL_SETTLE_NEWRCS');
call STL_SYNC_BL_SETTLE_NEWRCS(inMonth,inBatch,flag_version,reserve1,reserve2,proc_out,@outSysError,@outReturn,outBL,outAR);
IF ( @outReturn != 0) THEN
SELECT 'call STL_SYNC_BL_SETTLE_NEWRCS error, return_code=' || @outReturn FROM dual;
ROLLBACK;
RETURN;
END IF;
commit;


call STL_INFO_LOG(inMonth, '', v_proc_name,  'Begin call STL_SYNC_BL_SETTLE_MERGE');
call STL_SYNC_BL_SETTLE_MERGE(inMonth,inBatch,flag_version,reserve1,reserve2,proc_out,@outSysError,@outReturn,outBL,outAR);
IF ( @outReturn != 0) THEN
SELECT 'call STL_SYNC_BL_SETTLE_MERGE error, return_code=' || @outReturn FROM dual;
ROLLBACK;
RETURN;
END IF;
commit;
outSysError := 'OK';
        outReturn := 0;

SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
call STL_INFO_LOG(inMonth, '', v_proc_name,  outSysError);
END;
END ;;
DELIMITER ;