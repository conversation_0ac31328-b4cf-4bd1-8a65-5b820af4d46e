/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：下发结算文件 -下发给省公司的db2f任务生成
 * FORMAT_ID+DESCRIPTION
 * 43  BBOSS 1批下发-V101省间直接结算应收账单，用于支付省列支出的应收应结出数据
 * 42  BBOSS 1批下发-V101省间直接结算应收账单，用于非支付省列收入的应收结入数据
 * 24  BBOSS 1批下发-用于主办省的CP应收账单
 * 18  BBOSS 1批下发-用于主办省的应收账单
 * 33  BBOSS 1批下发-用于主办省的应收账单（新格式）
 * 16  BBOSS 1批下发-用于配合省列收入的应收账单
 * 31  BBOSS 1批下发-用于配合省列收入的应收账单（新格式）
 * 22  BBOSS 1批下发-用于配合省列结出到CP基地省的应收账单
**/
use stludr;
delimiter ;;
CREATE OR REPLACE DEFINER="stludr"@"10.%" PROCEDURE stludr."SEND2PROV_FIRST"(
    inMonth          IN   VARCHAR2,

    outSysError OUT VARCHAR2(1000),
    outReturn OUT NUMBER(4)
  )
AS

    iv_bill_16   varchar2(8000);
    iv_bill_18   varchar2(8000);
    iv_bill_22   varchar2(8000);
    iv_bill_24   varchar2(8000);
    iv_bill_31   varchar2(8000);
    iv_bill_33   varchar2(8000);
    iv_bill_37   varchar2(8000);

    vSql      varchar2(10240);
    v_proc_name         VARCHAR2(30) := 'SEND2PROV_FIRST';
 	P_ERRCODE   VARCHAR2(32);
    P_ERRMSG    VARCHAR2(2048);
BEGIN
    outSysError := '';
    outReturn := 0;

DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
    GET DIAGNOSTICS CONDITION 1 P_ERRCODE = RETURNED_SQLSTATE, P_ERRMSG = MESSAGE_TEXT;
    set outSysError = substr(P_ERRMSG, 1, 1000);
    set outReturn  = -1;
    ROLLBACK;
    select ('exception: ' || outReturn || '|' || '|' || outSysError ) AS error_msg ;
    call STLUDR.STL_ERROR_LOG(inMonth,P_ERRCODE,outSysError,'',v_proc_name,'');
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = outSysError;
END;

BEGIN
      -- 1批下发-用于配合省列收入的应收账单
      iv_bill_16 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_16  ' ||
            'SELECT /*+ no_index(a idx_om_ds) */ null, a.IN_OBJECT in_object, 0 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, ((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1 '||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE '||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE '||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 1 ' ||
              'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'') ' ||
          -- 20240527,idc协同营销
                    'AND (A.offer_code not in(''50051'',''60015'')  OR(A.offer_code in(''50051'',''60015'')  AND A.settle_mode = ''1'')) '||
            'UNION ALL ' ||
            'SELECT /*+ hash_join(t2,t1) join_order(t2,a,t1) no_index(a idx_om_ds) */ null, a.IN_OBJECT, 0 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov,  ' ||
                   'a.CHARGE_CODE, sum((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1 '||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE '||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE '||
               'join stl_config_send_svc t2 on a.product_code=t2.product_code '||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 1 ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'') ' ||
             'GROUP BY a.IN_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PRODUCT_ORDER_ID, ' ||
                      'a.CHARGE_CODE, a.TAX_RATE, adjmonth, feetype ' ||
            'ORDER BY in_object, customer_code';

      -- 1批下发-用于主办省的应收账单
      iv_bill_18 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_18  ' ||
            'SELECT /*+ no_index(a idx_om_ds) */ null, a.OUT_OBJECT out_object, 0 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code,  ((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
              'left join stl_config_digit t1 '||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE '||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE '||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 1 AND a.DEST_SOURCE in (''0'', ''1'') ' ||
               'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
          -- 20240527,idc协同营销
              'AND (A.offer_code not in(''50051'',''60015'')  OR(A.offer_code in(''50051'',''60015'')  AND A.settle_mode = ''1'')) '||
               'UNION ALL ' ||
              'SELECT  null, a.OUT_OBJECT out_object, 0 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, ((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1 '||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE '||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE '||
             'WHERE a.ORDER_MODE = ''5'' ' ||
               'AND a.PHASE = 1 AND a.DEST_SOURCE = ''1'' ' ||
               'AND a.OFFER_CODE IN (''*********'', ''*********'') ' ||
            'UNION ALL ' ||
            'SELECT /*+ hash_join(t2,t1) join_order(t2,a,t1) no_index(a idx_om_ds) */ null, a.OUT_OBJECT, 0 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov,  ' ||
                   'a.CHARGE_CODE, SUM((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1 '||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE '||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE '||
               'join stl_config_send_svc t2 on a.product_code=t2.product_code '||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 1 ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'') ' ||
             'GROUP BY a.OUT_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PRODUCT_ORDER_ID, ' ||
                      'a.CHARGE_CODE, a.TAX_RATE, adjmonth, feetype ' ||
            'ORDER BY out_object, customer_code';

      -- 1批下发-用于配合省列结出到CP基地省的应收账单
      iv_bill_22 := 'insert into stludr.bill_22  ' ||
                  'SELECT null, a.IN_OBJECT in_object, 2 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, (a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE) * 10 settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, a.CHARGE * 10 charge, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'b.VALUE prodattr1, '''' prodattr2, adjmonth, feetype ' ||
              'FROM UR_CPR_' || inMonth || '_T a left join stlusers.STL_PRODUCTCHARACTER b on a.PRODUCT_ORDER_ID = b.PRODUCTID ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''3'') ' ||
               'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
               'AND a.DEST_SOURCE = ''7'' ' ||
            'UNION ALL ' ||
            'SELECT /*+ hash_join(t2)*/ null, a.IN_OBJECT, 2 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov,  ' ||
                   'a.CHARGE_CODE, SUM((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE) * 10) settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, SUM(a.CHARGE * 10) orgfee, to_char(SYSDATE, ''yyyymmdd'') dd, b.VALUE prodattr1, '''' prodattr2, adjmonth, feetype ' ||
              'FROM UR_CPR_' || inMonth || '_T a left join stlusers.STL_PRODUCTCHARACTER b on a.PRODUCT_ORDER_ID = b.PRODUCTID ' ||
              'join stl_config_send_svc t2 on a.product_code=t2.product_code '||
             'WHERE a.ORDER_MODE IN (''1'', ''3'') ' ||
               'AND a.DEST_SOURCE = ''7'' ' ||
             'GROUP BY a.IN_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PRODUCT_ORDER_ID, ' ||
                      'a.CHARGE_CODE, a.TAX_RATE, b.VALUE, adjmonth, feetype ' ||

            ------移动云下发给EBOSS的运营支撑费数据
             'union all ' ||
             'select null, a.in_object, 2 settmode, a.customer_code, a.offer_order_id, a.org_month, ' ||
              ''''' paymonth, a.product_order_id, a.member_code, '''' member_prov, ' ||
              'a.charge_code, (a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE) * 10 settlefee, '''' cdr_chargecode, ' ||
              ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
              ''''' settle_taxfee, '''' settle_notaxfee, a.CHARGE * 10 charge, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
              ''''' prodattr1, '''' prodattr2, adjmonth, feetype ' ||
            'from ur_eboss_' || inMonth || '_t a ' ||
           'where a.order_mode in (''1'', ''3'') ' ||
             'and a.dest_source = ''4'' ' ||
             'and a.offer_code = ''1010402'' ' ||
             'and a.in_object = ''000'' ' ||
            'ORDER BY in_object, customer_code';

      -- 1批下发-用于主办省的CP应收账单
      iv_bill_24 := 'insert into stludr.bill_24  ' ||
                  'SELECT null, a.OUT_OBJECT out_object, 0 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, (a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE) * 10 settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_CPR_' || inMonth || '_T a ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''3'') ' ||
              'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
               'AND a.DEST_SOURCE = ''7'' ' ||
            'UNION ALL ' ||
            'SELECT /*+ hash_join(t2)*/ null, a.OUT_OBJECT, 0 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov,  ' ||
                   'a.CHARGE_CODE, SUM((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE) * 10) settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_CPR_' || inMonth || '_T a ' ||
                'join stl_config_send_svc t2 on a.product_code=t2.product_code '||
             'WHERE a.ORDER_MODE IN (''1'', ''3'') ' ||
               'AND a.DEST_SOURCE = ''7'' ' ||
             'GROUP BY a.OUT_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PRODUCT_ORDER_ID, ' ||
                      'a.CHARGE_CODE, a.TAX_RATE, adjmonth, feetype ' ||
            'ORDER BY out_object, customer_code';

      -- 1批下发-用于配合省列收入的应收账单（新格式）
      iv_bill_31 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_31 ' ||
            'SELECT /*+ no_index(a idx_om_ds) */ null, a.IN_OBJECT in_object, 0 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, ((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'a.out_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1  ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 1 ' ||
                'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'') ' ||
          -- 20240527,idc协同营销
                    'AND (A.offer_code not in(''50051'',''60015'')  OR(A.offer_code in(''50051'',''60015'')  AND A.settle_mode = ''1'')) '||
            'UNION ALL ' ||
            'SELECT /*+ hash_join(t2,t1) join_order(t2,a,t1) no_index(a idx_om_ds) */ null, a.IN_OBJECT, 0 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov,  ' ||
                   'a.CHARGE_CODE, SUM((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'a.out_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1  ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
              'join stl_config_send_svc t2 on a.product_code=t2.product_code ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 1 ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'') ' ||
             'GROUP BY a.IN_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PRODUCT_ORDER_ID, ' ||
                      'a.CHARGE_CODE, a.TAX_RATE, a.out_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
            'ORDER BY in_object, customer_code';

     -- 1批下发-用于主办省的应收账单（新格式）
      iv_bill_33 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_33  ' ||
            'SELECT /*+ no_index(a idx_om_ds) */ null, a.OUT_OBJECT out_object, 0 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, ((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'a.in_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
                'left join stl_config_digit t1  ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 1 AND a.DEST_SOURCE in (''0'', ''1'') ' ||
               'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
          -- 20240527,idc协同营销
                    'AND (A.offer_code not in(''50051'',''60015'')  OR(A.offer_code in(''50051'',''60015'')  AND A.settle_mode = ''1'')) '||
               'UNION ALL '||
               'SELECT null, a.OUT_OBJECT out_object, 0 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, ((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'a.in_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
                'left join stl_config_digit t1  ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
             'WHERE a.ORDER_MODE = ''5'' ' ||
               'AND a.PHASE = 1 AND a.DEST_SOURCE = ''1'' ' ||
               'AND a.OFFER_CODE IN (''*********'', ''*********'') ' ||
            'UNION ALL ' ||
            'SELECT  /*+ hash_join(t2,t1) join_order(t2,a,t1) no_index(a idx_om_ds) */ null, a.OUT_OBJECT, 0 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov,  ' ||
                   'a.CHARGE_CODE, SUM((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'a.in_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1  ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
              'join stl_config_send_svc t2 on a.product_code=t2.product_code ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 1 ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'') ' ||
             'GROUP BY a.OUT_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PRODUCT_ORDER_ID, ' ||
                      'a.CHARGE_CODE, a.TAX_RATE, a.in_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
            'ORDER BY out_object, customer_code';



      -- 20240725 V101无差错1批，V101省间直接结算应收账单
      iv_bill_37 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_37  ' ||
                    'SELECT /*+ no_index(a idx_om_ds) */ null, a.IN_OBJECT in_object, a.OUT_OBJECT out_object,0 settmode, a.CUSTOMER_CODE customer_code,offer_code,product_code,order_mode, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                    ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, '''' member_code, '''' member_prov,  ' ||
                    'a.CHARGE_CODE charge_code, sum(((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10))  settlefee, '''' cdr_chargecode, ' ||
                    ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                    ''''' settle_taxfee, '''' settle_notaxfee, settle_month dd, adjmonth, feetype ' ||
                    'FROM UR_RECV_' || inMonth || '_T a ' ||
                    'left join stl_config_digit t1 '||
                    'on t1.POSPEC_NUMBER = a.OFFER_CODE '||
                    'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE '||
                    'WHERE a.ORDER_MODE = ''3'' AND a.PHASE = ''1'' AND a.DEST_SOURCE =''0''  ' ||
                    'AND A.offer_code in(''50051'',''60015'') AND A.settle_mode = ''2'' ' ||
                    'GROUP BY a.IN_OBJECT,a.OUT_OBJECT, a.OFFER_CODE,a.product_code,a.CUSTOMER_CODE,a.ORDER_MODE, a.OFFER_ORDER_ID,a.ORG_MONTH, a.PRODUCT_ORDER_ID, a.CHARGE_CODE, a.TAX_RATE, adjmonth, feetype ,a.settle_month';

--插入前清理数据。
truncate table stludr.bill_16;
truncate table stludr.bill_18;
truncate table stludr.bill_22;
truncate table stludr.bill_24;
truncate table stludr.bill_31;
truncate table stludr.bill_33;
truncate table stludr.bill_37;

BEGIN

set @vSql := iv_bill_16;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_18;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_22;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_24;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_31;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_33;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_37;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
outSysError := 'OK';

COMMIT;
END;


INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select in_object, settmode, customer_code, to_char(offer_order_id), org_month, paymonth, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, adjmonth, feetype from stludr.bill_16', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 16, NULL, NULL, 5000, 'BBOSS 1批下发-用于配合省列收入的应收账单', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select out_object, settmode, customer_code, to_char(offer_order_id), org_month, paymonth, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, adjmonth, feetype from stludr.bill_18', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 18, NULL, NULL, 5000, 'BBOSS 1批下发-用于主办省的应收账单', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select in_object, settmode, customer_code, to_char(offer_order_id), org_month, paymonth, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, charge, dd, prodattr1, prodattr2, adjmonth, feetype from stludr.bill_22', '$SETTLE_HOME/$SETTLE_APPID/data/cp/BILLLIST', 22, NULL, NULL, 5000, 'BBOSS 1批下发-用于配合省列结出到CP基地省的应收账单', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select out_object, settmode, customer_code, to_char(offer_order_id), org_month, paymonth, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, adjmonth, feetype from stludr.bill_24', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 24, NULL, NULL, 5000, 'BBOSS 1批下发-用于主办省的CP应收账单', 'C', inMonth, 'EBILL', SYSDATE);

INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select in_object, settmode, customer_code, to_char(offer_order_id), org_month, paymonth, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, out_object, order_mode, offer_code, product_code, adjmonth, feetype from stludr.bill_31', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 31, NULL, NULL, 5000, 'BBOSS 1批下发-用于配合省列收入的应收账单（新格式）', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select out_object, settmode, customer_code, to_char(offer_order_id), org_month, paymonth, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, in_object, order_mode, offer_code, product_code, adjmonth, feetype from stludr.bill_33', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 33, NULL, NULL, 5000, 'BBOSS 1批下发-用于主办省的应收账单（新格式）', 'C', inMonth, 'EBILL', SYSDATE);

INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select IN_OBJECT,out_object, settmode, customer_code,offer_code,product_code,order_mode, to_char(offer_order_id), org_month, paymonth, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, adjmonth, feetype from stludr.bill_37', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 42, NULL, NULL, 5000, 'BBOSS 1批下发-V101省间直接结算应收账单，用于非支付省列收入的应收结入数据', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select IN_OBJECT,out_object, settmode, customer_code,offer_code,product_code,order_mode, to_char(offer_order_id), org_month, paymonth, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, adjmonth, feetype from stludr.bill_37', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 43, NULL, NULL, 5000, 'BBOSS 1批下发-V101省间直接结算应收账单，用于支付省列支出的应收应结出数据', 'C', inMonth, 'EBILL', SYSDATE);


COMMIT;

outSysError := 'DB2F出口文件下省一批数据已写入file_db2f_task表。';



SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
END;

END;;
delimiter ;

