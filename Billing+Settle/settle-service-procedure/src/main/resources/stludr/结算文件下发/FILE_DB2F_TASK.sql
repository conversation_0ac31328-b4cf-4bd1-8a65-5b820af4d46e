/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：下发结算文件 -下发给EBOSS的db2f任务生成
**/
DROP PROCEDURE IF EXISTS stludr.`FILE_DB2F_TASK`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "FILE_DB2F_TASK"(
                                      inMonth          IN   VARCHAR2,
                                      outSysError    OUT VARCHAR2(1000),
                                      outReturn     OUT NUMBER(4)
)
AS

    vSql      varchar2(10240);
    v_proc_name         VARCHAR2(30) := 'FILE_DB2F_TASK';

BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        outSysError := substr(@p2, 1, 1000);
                outReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg ;
    END;

    BEGIN

    set @vSql := 'truncate table stludr.bill_26';
    SELECT @vSql;
    PREPARE STMT FROM @vSql;
    EXECUTE STMT;
    DEALLOCATE PREPARE STMT;

    set @vSql := 'truncate table stludr.bill_27';
    SELECT @vSql;
    PREPARE STMT FROM @vSql;
    EXECUTE STMT;
    DEALLOCATE PREPARE STMT;

    set @vSql := 'truncate table stludr.bill_28';
    SELECT @vSql;
    PREPARE STMT FROM @vSql;
    EXECUTE STMT;
    DEALLOCATE PREPARE STMT;

    /*insert into stludr.bill_26 select acct_month, customer_code, product_code, prod_inst_id, service_code, svc_inst_id, charge_item_ref,
        accu_occurence, accu_duration, accu_volume from bill_26@DL_BOSS_BILLING;

    insert into stludr.bill_27 select customername, accountnumber, accountname, acct_month, productid, msisdn, prov_nm, fixed_fee,
        comm_fee, internet_fee, sms_fee, added_fee, collect_fee, other_fee, discount_fee from bill_27@DL_BOSS_BILLING;

    insert into stludr.bill_28 select customer_code, account_number, account_name, svc_inst_id, member_number, charge_item,
        description, prov_name, city_name, collect_type, collect_limit, mem_status, acct_month from stludr.bill_28;
    */
    INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEQ_FILE_DB2F_TASK_ID.NEXTVAL, 'Ebosssend', 'tst1', 'stludr', 'select acct_month, customer_code, product_code, prod_inst_id, service_code, svc_inst_id, charge_item_ref, accu_occurence, accu_duration, accu_volume from stludr.bill_26', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 26, NULL, NULL, 5000, 'BBOSS下发政企-业务用量', 'C', inMonth, 'SVC_VOL', SYSDATE);
    INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEQ_FILE_DB2F_TASK_ID.NEXTVAL, 'Ebosssend', 'tst1', 'stludr', 'select customername, accountnumber, accountname, acct_month, productid, msisdn, prov_nm, fixed_fee, comm_fee, internet_fee, sms_fee, added_fee, collect_fee, other_fee, discount_fee from stludr.bill_27', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 27, NULL, NULL, 5000, 'BBOSS下发政企-成员清单数据', 'C', inMonth, 'MEM_DTL', SYSDATE);
    INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEQ_FILE_DB2F_TASK_ID.NEXTVAL, 'Ebosssend', 'tst1', 'stludr', 'select customer_code, account_number, account_name, svc_inst_id, member_number, charge_item, description, prov_name, city_name, collect_type, collect_limit, mem_status, acct_month from stludr.bill_28', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 28, NULL, NULL, 5000, 'BBOSS下发政企-代付业务数据', 'C', inMonth, 'OTH_PAY', SYSDATE);


    outReturn := 0;
    outSysError := 'OK';

    commit;

    SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;

    END;

END ;;
DELIMITER ;