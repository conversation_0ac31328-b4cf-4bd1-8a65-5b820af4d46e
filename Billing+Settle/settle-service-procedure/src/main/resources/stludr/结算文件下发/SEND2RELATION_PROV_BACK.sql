/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：下发结算文件 -下发给省公司的db2f任务生成
**/
delimiter ;;
CREATE OR REPLACE DEFINER="stludr"@"10.%" PROCEDURE "SEND2RELATION_PROV"(
    inMonth          IN   VARCHAR2,
    inBatch IN VARCHAR2,
    flag_version IN VARCHAR2,
    reserve1 IN VARCHAR2,
    reserve2 IN VARCHAR2,
    proc_out OUT VARCHAR2,
    outSysError OUT VARCHAR2(1000),
    outReturn OUT NUMBER(4),
    outBL OUT VARCHAR2,
    outAR OUT VARCHAR2
  )
AS
    iv_bill_12   varchar2(8000);
    iv_bill_13   varchar2(8000);
    iv_bill_16   varchar2(8000);
    iv_bill_17   varchar2(8000);
    iv_bill_18   varchar2(8000);
    iv_bill_21   varchar2(8000);
    iv_bill_22   varchar2(8000);
    iv_bill_23   varchar2(8000);
    iv_bill_24   varchar2(8000);
    iv_bill_25   varchar2(8000);
    iv_bill_29   varchar2(8000);
    iv_bill_30   varchar2(8000);
    iv_bill_31   varchar2(8000);
    iv_bill_32   varchar2(8000);
    iv_bill_33   varchar2(8000);
    iv_bill_34   varchar2(8000);
    iv_bill_35   varchar2(8000);
    iv_bill_36_x1   varchar2(12000);
    iv_bill_36_x2   varchar2(12000);
    iv_bill_36_x3   varchar2(12000);
    iv_bill_36_x4   varchar2(12000);
    iv_bill_36_x5   varchar2(12000);
    iv_bill_36_x6   varchar2(12000);
    iv_bill_36_x7   varchar2(12000);
    iv_bill_36   varchar2(12000);
    iv_bill_37   varchar2(8000);
    iv_bill_38   varchar2(8000);
    iv_bill_39   varchar2(8000);
    vSql      varchar2(10240);
    v_proc_name         VARCHAR2(30) := 'SEND2RELATION_PROV';

BEGIN
    outSysError := '';
    outReturn := 0;



BEGIN


      iv_bill_12 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_12  ' ||
            'SELECT /*+ no_index(a idx_om_ds) */ a.IN_OBJECT in_object, 0 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, ((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10)  settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
              'left join stl_config_digit t1 '||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE '||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE '||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 2 ' ||
               'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'') ' ||
          -- 20240527,idc协同营销
                    'AND (A.offer_code <> ''50051'' OR(A.offer_code = ''50051'' AND A.settle_mode = ''1'')) '||
            'UNION ALL ' ||
            'SELECT /*+ hash_join(t2,t1) join_order(t2,a,t1) no_index(a idx_om_ds) */  a.IN_OBJECT, 0 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov, ' ||
                   'a.CHARGE_CODE, SUM((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1 '||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE '||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE '||
               'join stl_config_send_svc t2 on a.product_code=t2.product_code '||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 2 ' ||
               'AND a.PRODUCT_CODE IN (SELECT PRODUCT_CODE FROM STL_CONFIG_SEND_SVC) ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'') ' ||
             'GROUP BY a.IN_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PRODUCT_ORDER_ID, ' ||
                      'a.CHARGE_CODE, a.TAX_RATE, adjmonth, feetype ' ||
            'ORDER BY in_object, customer_code';



      iv_bill_13 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_13  ' ||
            'SELECT /*+ no_index(a idx_om_ds) */ a.IN_OBJECT in_object, 1 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   'PAID_MONTH paid_month, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, ((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_PAID_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1 '||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE '||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE '||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
              'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'', ''6'') ' ||
          -- 20240527,idc协同营销
                'AND (A.offer_code <> ''50051'' OR(A.offer_code = ''50051'' AND A.settle_mode = ''1'')) '||
            'UNION ALL ' ||
            'SELECT /*+ hash_join(t2,t1) join_order(t2,a,t1) no_index(a idx_om_ds)*/ a.IN_OBJECT, 1 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   'PAID_MONTH, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov, ' ||
                   'a.CHARGE_CODE,  sum((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_PAID_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1 '||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE '||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE '||
               'join stl_config_send_svc t2 on a.product_code=t2.product_code '||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'', ''6'') ' ||
             'GROUP BY a.IN_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PAID_MONTH, ' ||
                      'a.PRODUCT_ORDER_ID, a.CHARGE_CODE, a.TAX_RATE, adjmonth, feetype ' ||
            'ORDER BY in_object, customer_code';



      iv_bill_16 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_16  ' ||
            'SELECT /*+ no_index(a idx_om_ds) */ a.IN_OBJECT in_object, 0 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, ((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1 '||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE '||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE '||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 1 ' ||
              'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'') ' ||
          -- 20240527,idc协同营销
                    'AND (A.offer_code <> ''50051'' OR(A.offer_code = ''50051'' AND A.settle_mode = ''1'')) '||
            'UNION ALL ' ||
            'SELECT /*+ hash_join(t2,t1) join_order(t2,a,t1) no_index(a idx_om_ds) */ a.IN_OBJECT, 0 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov,  ' ||
                   'a.CHARGE_CODE, sum((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1 '||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE '||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE '||
               'join stl_config_send_svc t2 on a.product_code=t2.product_code '||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 1 ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'') ' ||
             'GROUP BY a.IN_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PRODUCT_ORDER_ID, ' ||
                      'a.CHARGE_CODE, a.TAX_RATE, adjmonth, feetype ' ||
            'ORDER BY in_object, customer_code';



      iv_bill_17 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_17  ' ||
            'SELECT  /*+ no_index(a idx_om_ds) */ a.OUT_OBJECT out_object, 0 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, ((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1 '||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE '||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE '||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 2 AND a.DEST_SOURCE in (''0'', ''1'') ' ||
              'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
          -- 20240527,idc协同营销
                'AND (A.offer_code <> ''50051'' OR(A.offer_code = ''50051'' AND A.settle_mode = ''1'')) '||
               'UNION ALL ' ||
              'SELECT  a.OUT_OBJECT out_object, 0 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, ((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1 '||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE '||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE '||
             'WHERE a.ORDER_MODE = ''5'' ' ||
               'AND a.PHASE = 2 AND a.DEST_SOURCE = ''1'' ' ||
               'AND a.OFFER_CODE IN (''*********'', ''*********'') ' ||
            'UNION ALL ' ||
            'SELECT /*+ hash_join(t2,t1) join_order(t2,a,t1) no_index(a idx_om_ds) */ a.OUT_OBJECT, 0 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov,  ' ||
                   'a.CHARGE_CODE,  sum((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1 '||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE '||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE '||
               'join stl_config_send_svc t2 on a.product_code=t2.product_code '||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 2 ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'') ' ||
             'GROUP BY a.OUT_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PRODUCT_ORDER_ID, ' ||
                      'a.CHARGE_CODE, a.TAX_RATE, adjmonth, feetype ' ||
            'ORDER BY out_object, customer_code';



      iv_bill_18 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_18  ' ||
            'SELECT /*+ no_index(a idx_om_ds) */ a.OUT_OBJECT out_object, 0 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code,  ((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
              'left join stl_config_digit t1 '||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE '||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE '||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 1 AND a.DEST_SOURCE in (''0'', ''1'') ' ||
               'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
          -- 20240527,idc协同营销
              'AND (A.offer_code <> ''50051'' OR(A.offer_code = ''50051'' AND A.settle_mode = ''1'')) '||
               'UNION ALL ' ||
              'SELECT  a.OUT_OBJECT out_object, 0 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, ((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1 '||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE '||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE '||
             'WHERE a.ORDER_MODE = ''5'' ' ||
               'AND a.PHASE = 1 AND a.DEST_SOURCE = ''1'' ' ||
               'AND a.OFFER_CODE IN (''*********'', ''*********'') ' ||
            'UNION ALL ' ||
            'SELECT /*+ hash_join(t2,t1) join_order(t2,a,t1) no_index(a idx_om_ds) */ a.OUT_OBJECT, 0 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov,  ' ||
                   'a.CHARGE_CODE, SUM((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1 '||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE '||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE '||
               'join stl_config_send_svc t2 on a.product_code=t2.product_code '||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 1 ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'') ' ||
             'GROUP BY a.OUT_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PRODUCT_ORDER_ID, ' ||
                      'a.CHARGE_CODE, a.TAX_RATE, adjmonth, feetype ' ||
            'ORDER BY out_object, customer_code';



      iv_bill_21 := 'insert into  stludr.bill_21  ' ||
            'SELECT a.OUT_OBJECT out_object, 1 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   'a.PAID_MONTH paid_month, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, (a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE) * 10 settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_CPP_' || inMonth || '_T a ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''3'') ' ||
               'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
               'AND a.DEST_SOURCE = ''7'' ' ||
            'UNION ALL ' ||
            'SELECT a.OUT_OBJECT, 1 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   'a.PAID_MONTH, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov,  ' ||
                   'a.CHARGE_CODE, SUM((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE) * 10) settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_CPP_' || inMonth || '_T a ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''3'') ' ||
               'AND a.PRODUCT_CODE IN (SELECT PRODUCT_CODE FROM STL_CONFIG_SEND_SVC) ' ||
               'AND a.DEST_SOURCE = ''7'' ' ||
             'GROUP BY a.OUT_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PAID_MONTH,  ' ||
                      'a.PRODUCT_ORDER_ID, a.CHARGE_CODE, a.TAX_RATE, adjmonth, feetype ' ||
            'ORDER BY out_object, customer_code';



      iv_bill_22 := 'insert into stludr.bill_22  ' ||
                  'SELECT a.IN_OBJECT in_object, 2 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, (a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE) * 10 settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, a.CHARGE * 10 charge, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'b.VALUE prodattr1, '''' prodattr2, adjmonth, feetype ' ||
              'FROM UR_CPR_' || inMonth || '_T a left join stlusers.STL_PRODUCTCHARACTER b on a.PRODUCT_ORDER_ID = b.PRODUCTID ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''3'') ' ||
               'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
               'AND a.DEST_SOURCE = ''7'' ' ||
            'UNION ALL ' ||
            'SELECT /*+ hash_join(t2)*/ a.IN_OBJECT, 2 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov,  ' ||
                   'a.CHARGE_CODE, SUM((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE) * 10) settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, SUM(a.CHARGE * 10) orgfee, to_char(SYSDATE, ''yyyymmdd'') dd, b.VALUE prodattr1, '''' prodattr2, adjmonth, feetype ' ||
              'FROM UR_CPR_' || inMonth || '_T a left join stlusers.STL_PRODUCTCHARACTER b on a.PRODUCT_ORDER_ID = b.PRODUCTID ' ||
              'join stl_config_send_svc t2 on a.product_code=t2.product_code '||
             'WHERE a.ORDER_MODE IN (''1'', ''3'') ' ||
               'AND a.DEST_SOURCE = ''7'' ' ||
             'GROUP BY a.IN_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PRODUCT_ORDER_ID, ' ||
                      'a.CHARGE_CODE, a.TAX_RATE, b.VALUE, adjmonth, feetype ' ||

            ------移动云下发给EBOSS的运营支撑费数据
             'union all ' ||
             'select a.in_object, 2 settmode, a.customer_code, a.offer_order_id, a.org_month, ' ||
              ''''' paymonth, a.product_order_id, a.member_code, '''' member_prov, ' ||
              'a.charge_code, (a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE) * 10 settlefee, '''' cdr_chargecode, ' ||
              ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
              ''''' settle_taxfee, '''' settle_notaxfee, a.CHARGE * 10 charge, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
              ''''' prodattr1, '''' prodattr2, adjmonth, feetype ' ||
            'from ur_eboss_' || inMonth || '_t a ' ||
           'where a.order_mode in (''1'', ''3'') ' ||
             'and a.dest_source = ''4'' ' ||
             'and a.offer_code = ''1010402'' ' ||
             'and a.in_object = ''000'' ' ||
            'ORDER BY in_object, customer_code';



      iv_bill_23 := 'insert into stludr.bill_23  ' ||
                  'SELECT a.IN_OBJECT in_object, 3 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   'a.PAID_MONTH paid_month, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, (a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE) * 10 settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, a.CHARGE * 10 charge, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'b.VALUE prodattr1, '''' prodattr2, adjmonth, feetype ' ||
              'FROM UR_CPP_' || inMonth || '_T a left join stlusers.STL_PRODUCTCHARACTER b on a.PRODUCT_ORDER_ID = b.PRODUCTID ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''3'') ' ||
               'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
               'AND a.DEST_SOURCE = ''7'' ' ||
            'UNION ALL ' ||
            'SELECT a.IN_OBJECT, 3 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   'a.PAID_MONTH, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov,  ' ||
                   'a.CHARGE_CODE, SUM((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE) * 10) settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, SUM(a.CHARGE * 10) orgfee, to_char(SYSDATE, ''yyyymmdd'') dd, b.VALUE prodattr1, '''' prodattr2, adjmonth, feetype ' ||
              'FROM UR_CPP_' || inMonth || '_T a left join stlusers.STL_PRODUCTCHARACTER b  on a.PRODUCT_ORDER_ID = b.PRODUCTID ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''3'') ' ||
               'AND a.PRODUCT_CODE IN (SELECT PRODUCT_CODE FROM STL_CONFIG_SEND_SVC) ' ||
               'AND a.DEST_SOURCE = ''7'' ' ||
             'GROUP BY a.IN_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PAID_MONTH, ' ||
                      'a.PRODUCT_ORDER_ID, a.CHARGE_CODE, a.TAX_RATE, b.VALUE, adjmonth, feetype ' ||
            'ORDER BY in_object, customer_code ';



      iv_bill_24 := 'insert into stludr.bill_24  ' ||
                  'SELECT a.OUT_OBJECT out_object, 0 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, (a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE) * 10 settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_CPR_' || inMonth || '_T a ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''3'') ' ||
              'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
               'AND a.DEST_SOURCE = ''7'' ' ||
            'UNION ALL ' ||
            'SELECT /*+ hash_join(t2)*/ a.OUT_OBJECT, 0 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov,  ' ||
                   'a.CHARGE_CODE, SUM((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE) * 10) settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_CPR_' || inMonth || '_T a ' ||
                'join stl_config_send_svc t2 on a.product_code=t2.product_code '||
             'WHERE a.ORDER_MODE IN (''1'', ''3'') ' ||
               'AND a.DEST_SOURCE = ''7'' ' ||
             'GROUP BY a.OUT_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PRODUCT_ORDER_ID, ' ||
                      'a.CHARGE_CODE, a.TAX_RATE, adjmonth, feetype ' ||
            'ORDER BY out_object, customer_code';



      iv_bill_25 := 'insert into stludr.bill_25 ' ||
                  'SELECT /*+ no_index(a idx_om_ds) */ a.OUT_OBJECT out_object, 1 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   'a.PAID_MONTH paid_month, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code,((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_PAID_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1 ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'', ''6'') ' ||
          -- 20240527,idc协同营销
                    'AND (A.offer_code <> ''50051'' OR(A.offer_code = ''50051'' AND A.settle_mode = ''1'')) '||
            'UNION ALL ' ||
            'SELECT /*+ hash_join(t2,t1) join_order(t2,a,t1) no_index(a idx_om_ds)*/ a.OUT_OBJECT, 1 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   'a.PAID_MONTH, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov,  ' ||
                   'a.CHARGE_CODE, sum((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_PAID_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1  ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
              'join stl_config_send_svc t2 on a.product_code=t2.product_code ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'', ''6'') ' ||
             'GROUP BY a.OUT_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PAID_MONTH, ' ||
                      'a.PRODUCT_ORDER_ID, a.CHARGE_CODE, a.TAX_RATE, adjmonth, feetype ' ||
            'ORDER BY out_object, customer_code';



      iv_bill_29 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_29  ' ||
            'SELECT  /*+ no_index(a idx_om_ds) */ a.IN_OBJECT in_object, 0 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, ((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'a.out_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
              'left join stl_config_digit t1 ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 2 ' ||
              'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'') ' ||
          -- 20240527,idc协同营销
                    'AND (A.offer_code <> ''50051'' OR(A.offer_code = ''50051'' AND A.settle_mode = ''1'')) '||
            'UNION ALL ' ||
            'SELECT /*+ hash_join(t2,t1) join_order(t2,a,t1) no_index(a idx_om_ds) */ a.IN_OBJECT, 0 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov, ' ||
                   'a.CHARGE_CODE, SUM((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'a.out_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
              'left join stl_config_digit t1  ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
              'join stl_config_send_svc t2 on a.product_code=t2.product_code ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 2 ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'') ' ||
             'GROUP BY a.IN_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PRODUCT_ORDER_ID, ' ||
                      'a.CHARGE_CODE, a.TAX_RATE, a.out_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
            'ORDER BY in_object, customer_code';



      iv_bill_30 := 'insert into stludr.bill_30  ' ||
            'SELECT /*+ no_index(a idx_om_ds) */ a.IN_OBJECT in_object, 1 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   'PAID_MONTH paid_month, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code,  ((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'a.out_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
              'FROM UR_PAID_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1  ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
             'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'', ''6'') ' ||
          -- 20240527,idc协同营销
                    'AND (A.offer_code <> ''50051'' OR(A.offer_code = ''50051'' AND A.settle_mode = ''1'')) '||
            'UNION ALL ' ||
            'SELECT /*+ hash_join(t2,t1) join_order(t2,a,t1) no_index(a idx_om_ds)*/ a.IN_OBJECT, 1 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   'PAID_MONTH, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov, ' ||
                   'a.CHARGE_CODE, SUM((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'a.out_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
              'FROM UR_PAID_' || inMonth || '_T a ' ||
                'left join stl_config_digit t1  ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
              'join stl_config_send_svc t2 on a.product_code=t2.product_code ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'', ''6'') ' ||
             'GROUP BY a.IN_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PAID_MONTH, ' ||
                      'a.PRODUCT_ORDER_ID, a.CHARGE_CODE, a.TAX_RATE, a.out_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
            'ORDER BY in_object, customer_code';



      iv_bill_31 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_31 ' ||
            'SELECT /*+ no_index(a idx_om_ds) */ a.IN_OBJECT in_object, 0 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, ((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'a.out_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1  ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 1 ' ||
                'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'') ' ||
          -- 20240527,idc协同营销
                    'AND (A.offer_code <> ''50051'' OR(A.offer_code = ''50051'' AND A.settle_mode = ''1'')) '||
            'UNION ALL ' ||
            'SELECT /*+ hash_join(t2,t1) join_order(t2,a,t1) no_index(a idx_om_ds) */ a.IN_OBJECT, 0 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov,  ' ||
                   'a.CHARGE_CODE, SUM((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'a.out_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1  ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
              'join stl_config_send_svc t2 on a.product_code=t2.product_code ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 1 ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'') ' ||
             'GROUP BY a.IN_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PRODUCT_ORDER_ID, ' ||
                      'a.CHARGE_CODE, a.TAX_RATE, a.out_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
            'ORDER BY in_object, customer_code';



      iv_bill_32 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_32 ' ||
            'SELECT /*+ no_index(a idx_om_ds) */ a.OUT_OBJECT out_object, 0 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, ((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'a.in_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
                'left join stl_config_digit t1  ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 2 AND a.DEST_SOURCE in (''0'', ''1'') ' ||
              'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
          -- 20240527,idc协同营销
              'AND (A.offer_code <> ''50051'' OR(A.offer_code = ''50051'' AND A.settle_mode = ''1'')) '||
              'UNION ALL '||
               'SELECT  a.OUT_OBJECT out_object, 0 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, ((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'a.in_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
                'left join stl_config_digit t1  ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
             'WHERE  a.ORDER_MODE = ''5'' ' ||
               'AND a.PHASE = 2 AND a.DEST_SOURCE = ''1'' ' ||
               'AND a.OFFER_CODE IN (''*********'', ''*********'') ' ||
            'UNION ALL ' ||
            'SELECT /*+ hash_join(t2,t1) join_order(t2,a,t1) no_index(a idx_om_ds) */ a.OUT_OBJECT, 0 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov,  ' ||
                   'a.CHARGE_CODE, SUM((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'a.in_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1  ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
              'join stl_config_send_svc t2 on a.product_code=t2.product_code ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 2 ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'') ' ||
             'GROUP BY a.OUT_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PRODUCT_ORDER_ID, ' ||
                      'a.CHARGE_CODE, a.TAX_RATE, a.in_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
            'ORDER BY out_object, customer_code';



      iv_bill_33 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_33  ' ||
            'SELECT /*+ no_index(a idx_om_ds) */ a.OUT_OBJECT out_object, 0 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, ((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'a.in_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
                'left join stl_config_digit t1  ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 1 AND a.DEST_SOURCE in (''0'', ''1'') ' ||
               'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
          -- 20240527,idc协同营销
                    'AND (A.offer_code <> ''50051'' OR(A.offer_code = ''50051'' AND A.settle_mode = ''1'')) '||
               'UNION ALL '||
               'SELECT a.OUT_OBJECT out_object, 0 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, ((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'a.in_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
                'left join stl_config_digit t1  ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
             'WHERE a.ORDER_MODE = ''5'' ' ||
               'AND a.PHASE = 1 AND a.DEST_SOURCE = ''1'' ' ||
               'AND a.OFFER_CODE IN (''*********'', ''*********'') ' ||
            'UNION ALL ' ||
            'SELECT  /*+ hash_join(t2,t1) join_order(t2,a,t1) no_index(a idx_om_ds) */ a.OUT_OBJECT, 0 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov,  ' ||
                   'a.CHARGE_CODE, SUM((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'a.in_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1  ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
              'join stl_config_send_svc t2 on a.product_code=t2.product_code ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 1 ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'') ' ||
             'GROUP BY a.OUT_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PRODUCT_ORDER_ID, ' ||
                      'a.CHARGE_CODE, a.TAX_RATE, a.in_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
            'ORDER BY out_object, customer_code';



      iv_bill_34 := 'insert into stludr.bill_34  ' ||
                  'SELECT /*+ no_index(a idx_om_ds) */ a.OUT_OBJECT out_object, 1 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   'a.PAID_MONTH paid_month, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, ((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'a.in_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
              'FROM UR_PAID_' || inMonth || '_T a ' ||
                'left join stl_config_digit t1  ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
              'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'', ''6'') ' ||
          -- 20240527,idc协同营销
                    'AND (A.offer_code <> ''50051'' OR(A.offer_code = ''50051'' AND A.settle_mode = ''1'')) '||
            'UNION ALL ' ||
            'SELECT /*+ hash_join(t2,t1) join_order(t2,a,t1) no_index(a idx_om_ds)*/ a.OUT_OBJECT, 1 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   'a.PAID_MONTH, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov,  ' ||
                   'a.CHARGE_CODE, SUM((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'a.in_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
              'FROM UR_PAID_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1  ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
              'join stl_config_send_svc t2 on a.product_code=t2.product_code ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'', ''6'') ' ||
             'GROUP BY a.OUT_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PAID_MONTH, ' ||
                      'a.PRODUCT_ORDER_ID, a.CHARGE_CODE, a.TAX_RATE, a.in_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
            'ORDER BY out_object, customer_code';

     --参照生产视图进行调整，rownum rn 改为 row_number() over(partition by x.prov_code ) rn， 去掉tab那一层
       iv_bill_35 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_35  ' ||
      'SELECT x.*,y.taxrate, y.minbalanceamount, y.maxbalanceamount, y.settlementclass,y.productclass, ''' || inMonth || ''' settlemonth ' ||
              'from  (select tab.*,row_number() over(partition by prov_code order by rownum) rn from' ||
              '(select a.prov_code, decode(a.pay_tag, ''1'', ''0'', ''2'', ''1'') pay_tag, a.customer_province_number, a.ec_id, a.customer_name, a.fee_seq, a.product_subs_id, substr(a.issue_time,1,8) issue_time, substr(a.expire_time,1,8) expire_time, '||
              'a.product_class, a.settlement_class, a.product_type, a.product_name, a.product_id, a.prod_charge_code, a.rateplan_id, a.rateplan_name,  '||
              'a.partner_name, a.partner_code, a.member_nums, a.billing_term,  a.fee_val, a.settle_fee, a.tax_rate, a.standard_sale_price, a.settle_price,  '||
              'a.par_settle_rate, a.par_res_settl_rate, a.settlement_type, a.par_settl_amount, a.fee_flag, a.busi_type, a.contract_main, a.city_code, a.creator_name, a.if_free_resource,  '||
              'b.settlement_rate, b.settlement_type P2Csettlement_type, b.settlement_amount, a.original_bill_month, a.discount_Type, a.settle_disvalue,  b.Prd_Settle_Disvalue ,  '||
          ' '''' SjProductName,'''' SjProductCode,'''' SalesBaseDiscount,'''' PVsettleRate, 0 PVsettleValue,'''' PVProductClass  '||
              'from stludr.sync_interface_mc_' || inMonth || ' a,  stludr.sync_interface_mc_p2c_' || inMonth || '  b  '||
              'where a.id  = b.id and a.file_name = b.file_name  '||
              'and a.status = 0 and b.status = 0 ' ||
              ' UNION ALL '||
              ' select b.prov_code, ''0'' pay_tag, NULL customer_province_number, NULL ec_id, NULL customer_name,NULL fee_seq, '||
              ' b.product_subs_id, substr(b.issue_time, 1, 8)  issue_time,substr(b.expire_time, 1, 8) expire_time,b.pv_product_class product_class, '||
              ' b.settlement_class,NULL product_type,b.product_name,b.product_id,NULL prod_charge_code,NULL rateplan_id,NULL rateplan_name, '||
              ' a.partner_name,a.partner_code,NULL member_nums,b.billing_term,b.fee_val,NULL settle_fee,to_char(b.tax_rate,''FM0.90''),NULL standard_sale_price, '||
              ' NULL settle_price,a.par_settle_rate,a.par_ressettl_rate,a.settlement_type,a.par_settl_amount,1 fee_flag, ''移动云'' busi_type,NULL contract_main, '||
              ' NULL city_code,NULL creator_name,NULL if_free_resource,b.settlement_rate,b.settlement_type P2Csettlement_type, b.settlement_amount, '||
              ' NULL original_bill_month,NULL discount_Type,NULL settle_disvalue,NULL Prd_Settle_Disvalue,NULL SjProductName,NULL SjProductCode,NULL SalesBaseDiscount, '||
              ' NULL PVsettleRate,0 PVsettleValue,NULL PVProductClass  '||
              ' from stludr.SYNC_INTERFACE_BC2C_PS b left join stludr.SYNC_INTERFACE_BC2C_PART a on a.product_subs_id = b.product_subs_id  and b.prov_code = a.prov_code '||
              ' where  b.ACCT_MONTH =' || inMonth || '' ||
              ' and b.status = 0 ' ||
              ' UNION ALL  ' ||
          'SELECT a.prov_code,''0'' pay_tag,a.customer_province_number,a.ec_id,a.customer_name,a.fee_seq,a.product_subs_id,substr(a.issue_time, 1, 8) issue_time,  ' ||
      'substr(a.expire_time, 1, 8) expire_time,a.product_class,''6'' settlement_class,a.product_type,a.product_name,a.product_id,a.prod_charge_code,a.rateplan_id,  ' ||
      'a.rateplan_name,'''' partner_name,'''' partner_code,'''' member_nums,a.billing_term,a.fee_val,a.settle_fee settle_fee,a.tax_rate,a.standard_sale_price,  ' ||
      'a.settle_price,'''' par_settle_rate,'''' par_res_settl_rate,'''' settlement_type,0 par_settl_amount,a.fee_flag fee_flag,a.busi_type,a.contract_main,  ' ||
      ' a.city_code,a.creator_name,'''' if_free_resource,'''' settlement_rate,'''' P2Csettlement_type,a.PV_SETTLE_VALUE settlement_amount,a.original_bill_month,'''' DISCOUNT_TYPE,  ' ||
          ''''' SETTLE_DISVALUE,'''' Prd_Settle_Disvalue,a.SJ_PRODUCT_NAME SjProductName,a.SJ_PRODUCT_CODE SjProductCode,a.SALES_BASE_DISCOUNT SalesBaseDiscount,  ' ||
      'a.PV_SETTLE_RATE PVsettleRate,a.PV_SETTLE_VALUE PVsettleValue,a.PV_PRODUCT_CLASS PVProductClass FROM sync_interface_pvs_' || inMonth || ' a WHERE a.status = 0  ' ||
          ' UNION ALL  ' ||
      ' SELECT a.prov_code,''0'' pay_tag,'''' customer_province_number,'''' ec_id,'''' customer_name,'''' fee_seq,a.product_subs_id,substr(a.issue_time, 1, 8) issue_time,  ' ||
      'substr(a.expire_time, 1, 8) expire_time,'''' product_class,''6'' settlement_class,'''' product_type,a.product_name,a.product_id,'''' prod_charge_code,  ' ||
      ''''' rateplan_id,'''' rateplan_name,'''' partner_name,'''' partner_code,'''' member_nums,a.billing_term,a.fee_val,0 settle_fee,a.tax_rate,'''' standard_sale_price,  ' ||
      ''''' settle_price,'''' par_settle_rate,'''' par_res_settl_rate,'''' settlement_type,0 par_settl_amount, 1 fee_flag,''移动云'' busi_type,'''' contract_main,'''' city_code,  ' ||
      ' '''' creator_name,'''' if_free_resource,'''' settlement_rate,'''' P2Csettlement_type,a.PV_SETTLE_VALUE settlement_amount,'''' original_bill_month,'''' DISCOUNT_TYPE,'''' SETTLE_DISVALUE,  ' ||
          ' '''' Prd_Settle_Disvalue,'''' SjProductName,'''' SjProductCode,'''' SalesBaseDiscount,a.PV_SETTLE_RATE PVsettleRate,a.PV_SETTLE_VALUE PVsettleValue,  ' ||
          'a.PV_PRODUCT_CLASS PVProductClass FROM sync_interface_pvs_toc_' || inMonth || ' a WHERE a.status = 0 ) tab) x  ' ||
              ' left join   ' ||
              ' (select l.prov_cd, decode(l.col_name, ''移动云SAAS'', ''1'', ''6'') settlementclass,  decode(l.col_name, ''移动云SAAS'', ''1'', ''PaaS'', ''2'', ''IaaS'', ''3'')  productclass,   ' ||
              ' to_char(l.taxrate / 100, ''FM0.00'') taxrate,    ' ||
              ' sum(case when (l.report_fee - l.settle_fee)>0 then l.report_fee - l.settle_fee else 0 end) minbalanceamount,    ' ||
              ' sum(case when (l.report_fee - l.settle_fee)<0 then l.report_fee - l.settle_fee else 0 end) maxbalanceamount   ' ||
              ' from rpt_p2c_limited l   ' ||
              ' where l.settlemonth = ''' || inMonth || '''   ' ||
              ' and l.rep_num = ''D313-5'' and l.col_name <> ''专属云'' ' ||
              ' and l.settle_fee - l.report_fee <> 0 group by l.prov_cd, l.col_name, l.taxrate) y   ' ||
              ' on x.prov_code = y.prov_cd  ';






          --bill_36视图拆分成两部分，x 合成部分 使用临时表 bill_36_x
          --bill_36_x 和y那部分关联插入表bill_36
          --【BBOSS】 通用   原语句条件中or 拼接的部分 拆分成多个子查询进行union all
        iv_bill_36_x1 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_36_x
    select out_object provcode, ''0'' paytag, settle_month billingterm, settle_type settlementclass, company_code settlementpartyin,
           col_code productclass, data_source datasourceid, '''' feeseq, customer_code customernumber, to_char(offer_order_id) productofferingid,
           offer_code pospecnumber, to_char(product_order_id) productsubsid, product_code productspecnumber, charge_code prodchargecode,
           to_char(tax_rate / 100, ''FM0.00'') taxrate, FEETYPE + 1  as feeflag, decode(sign_entity, ''0'', '''') contractmain,
           sum(settle_notaxfee / nvl(product_digit, 1)) * 10  p2csettlementamount,
           decode(feetype, ''1'', adjmonth, '''') originalbillmonth,settlement_mode settlement_mode
      from (
           select t.out_object, t.settle_month,a.settle_type,  a.company_code,a.col_code, a.data_source,
               t.customer_code, t.offer_order_id, t.offer_code,  t.product_order_id,  t.product_code,
               t.charge_code,t.tax_rate,t.FEETYPE, t.sign_entity, t.settle_notaxfee, d.product_digit,
               t.adjmonth,  a.settlement_mode
           from ur_eboss_' || inMonth || '_t t
            join rvl_p2c_bus_config a
          left join stl_config_digit d  on d.pospec_number = t.offer_code  and d.product_number = t.product_code
         where a.col_code is not null
        and t.settle_month between a.start_month and a.end_month and t.dest_source = ''5''
       and t.in_object = a.company_code
       and a.type = ''O'' and t.offer_code = a.offer_code and (t.offer_code <> ''*********'' or t.product_code is null or t.product_code = '''')
       and (t.product_code not in (''2024999480001367'',''2024999480000390'',''2022999400072067'',''2022999400040702'',''2022999400074099'',''2023999400036743'',''5005501'') or t.product_code = '''' or t.product_code is null)
       union all
        select t.out_object, t.settle_month,a.settle_type,  a.company_code,a.col_code, a.data_source,
               t.customer_code, t.offer_order_id, t.offer_code,  t.product_order_id,  t.product_code,
               t.charge_code,t.tax_rate,t.FEETYPE, t.sign_entity, t.settle_notaxfee, d.product_digit,
               t.adjmonth,  a.settlement_mode
           from ur_eboss_' || inMonth || '_t t
            join rvl_p2c_bus_config a
          left join stl_config_digit d  on d.pospec_number = t.offer_code  and d.product_number = t.product_code
         where a.col_code is not null
        and t.settle_month between a.start_month and a.end_month and t.dest_source = ''5''
       and t.in_object = a.company_code
       and a.type = ''OP'' and t.offer_code = a.offer_code and t.product_code = a.product_code
       and (t.product_code not in (''2024999480001367'',''2024999480000390'',''2022999400072067'',''2022999400040702'',''2022999400074099'',''2023999400036743'',''5005501'') or t.product_code = '''' or t.product_code is null)
        union all
        select t.out_object, t.settle_month,a.settle_type,  a.company_code,a.col_code, a.data_source,
               t.customer_code, t.offer_order_id, t.offer_code,  t.product_order_id,  t.product_code,
               t.charge_code,t.tax_rate,t.FEETYPE, t.sign_entity, t.settle_notaxfee, d.product_digit,
               t.adjmonth,  a.settlement_mode
           from ur_eboss_' || inMonth || '_t t
            join rvl_p2c_bus_config a
          left join stl_config_digit d  on d.pospec_number = t.offer_code  and d.product_number = t.product_code
         where a.col_code is not null
        and t.settle_month between a.start_month and a.end_month and t.dest_source = ''5''
       and t.in_object = a.company_code
       and a.type = ''OPF'' and t.offer_code = a.offer_code and t.product_code = a.product_code and t.charge_code = a.charge_item
       and (t.product_code not in (''2024999480001367'',''2024999480000390'',''2022999400072067'',''2022999400040702'',''2022999400074099'',''2023999400036743'',''5005501'') or t.product_code = '''' or t.product_code is null)
 union all
        select t.out_object, t.settle_month,a.settle_type,  a.company_code,a.col_code, a.data_source,
               t.customer_code, t.offer_order_id, t.offer_code,  t.product_order_id,  t.product_code,
               t.charge_code,t.tax_rate,t.FEETYPE, t.sign_entity, t.settle_notaxfee, d.product_digit,
               t.adjmonth,  a.settlement_mode
           from ur_eboss_' || inMonth || '_t t
            join rvl_p2c_bus_config a
          left join stl_config_digit d  on d.pospec_number = t.offer_code  and d.product_number = t.product_code
         where a.col_code is not null
        and t.settle_month between a.start_month and a.end_month and t.dest_source = ''5''
       and t.in_object = a.company_code
       and  a.type = ''OPFR'' and t.offer_code = a.offer_code and t.product_code = a.product_code and t.charge_code = a.charge_item and t.duration = a.rateplan_id
       and (t.product_code not in (''2024999480001367'',''2024999480000390'',''2022999400072067'',''2022999400040702'',''2022999400074099'',''2023999400036743'',''5005501'') or t.product_code = '''' or t.product_code is null)
 union all
        select t.out_object, t.settle_month,a.settle_type,  a.company_code,a.col_code, a.data_source,
               t.customer_code, t.offer_order_id, t.offer_code,  t.product_order_id,  t.product_code,
               t.charge_code,t.tax_rate,t.FEETYPE, t.sign_entity, t.settle_notaxfee, d.product_digit,
               t.adjmonth,  a.settlement_mode
           from ur_eboss_' || inMonth || '_t t
            join rvl_p2c_bus_config a
          left join stl_config_digit d  on d.pospec_number = t.offer_code  and d.product_number = t.product_code
         where a.col_code is not null
        and t.settle_month between a.start_month and a.end_month and t.dest_source = ''5''
       and t.in_object = a.company_code
       and a.type = ''OF'' and t.offer_code = a.offer_code and t.charge_code = a.charge_item
       and (t.product_code not in (''2024999480001367'',''2024999480000390'',''2022999400072067'',''2022999400040702'',''2022999400074099'',''2023999400036743'',''5005501'') or t.product_code = '''' or t.product_code is null)
 union all
        select t.out_object, t.settle_month,a.settle_type,  a.company_code,a.col_code, a.data_source,
               t.customer_code, t.offer_order_id, t.offer_code,  t.product_order_id,  t.product_code,
               t.charge_code,t.tax_rate,t.FEETYPE, t.sign_entity, t.settle_notaxfee, d.product_digit,
               t.adjmonth,  a.settlement_mode
           from ur_eboss_' || inMonth || '_t t
            join rvl_p2c_bus_config a
          left join stl_config_digit d  on d.pospec_number = t.offer_code  and d.product_number = t.product_code
         where a.col_code is not null
        and t.settle_month between a.start_month and a.end_month and t.dest_source = ''5''
       and t.in_object = a.company_code
       and a.type = ''P'' and t.product_code = a.product_code
       and (t.product_code not in (''2024999480001367'',''2024999480000390'',''2022999400072067'',''2022999400040702'',''2022999400074099'',''2023999400036743'',''5005501'') or t.product_code = '''' or t.product_code is null)
      )
     group by out_object, settle_month, settle_type, company_code,
              col_code, data_source, customer_code, offer_order_id,
              offer_code, product_order_id, product_code, charge_code,
              tax_rate, feetype, sign_entity, adjmonth, settlement_mode ';

        --20240326  下发省公司的省专结算明细数据中，按照新D313政企条件标准产品DICT服务包结算单的业务数据下发
        --20240425 202404账期开始不再包含给政企的外部合作伙伴数据
     iv_bill_36_x2 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_36_x
                      select t.out_object provcode, ''0'' paytag, t.settle_month billingterm, ''1'' settlementclass,''ZQ'' settlementpartyin,
                      a.col_code productclass, a.data_source datasourceid, '''' feeseq, t.customer_code customernumber, to_char(t.offer_order_id) productofferingid,
                      t.offer_code pospecnumber, to_char(t.product_order_id) productsubsid, t.product_code productspecnumber, t.charge_code prodchargecode,
                      to_char(t.tax_rate / 100, ''FM0.00'') taxrate, t.FEETYPE + 1 as feeflag, decode(t.sign_entity, ''0'', '''') contractmain,
                      sum(t.settle_notaxfee /nvl(d.product_digit,1)) * 10 p2csettlementamount,
                      decode(t.feetype, ''1'', t.adjmonth, '''') originalbillmonth,''3'' settlement_mode
                      from ur_eboss_' || inMonth || '_t t
                        join rvl_p2c_bus_config a
                     left join stl_config_digit d  on d.pospec_number = t.offer_code  and d.product_number = t.product_code
                      where col_code is not null and t.settle_month between a.start_month and a.end_month and t.dest_source = ''5'' AND a.rep_num=''D313''
                      and t.product_code = a.product_code
                      and  a.type = ''OP'' and t.offer_code = a.offer_code and t.product_code = a.product_code and t.product_code in (''2024999480001367'',''2024999480000390'',''2022999400072067'',''2022999400040702'',''2022999400074099'',''2023999400036743'',''2023999400035055'')
                      and t.in_object!=''ZQ''
                      group by t.out_object, t.settle_month,
                      a.col_code, a.data_source, t.customer_code, t.offer_order_id,
                      t.offer_code, t.product_order_id, t.product_code, t.charge_code,
                      t.tax_rate, t.feetype, t.sign_entity, t.adjmonth, a.settlement_mode ';

             --【BBOSS】结给政企分公司的DICT业务（无配置）
             -- 20240425 202404账期开始不再包含给政企的外部合作伙伴数据
           iv_bill_36_x3 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_36_x
                      select t.out_object provcode, ''0'' paytag, t.settle_month billingterm, ''1'' settlementclass, ''ZQ'' settlementpartyin,
                      decode(substr(b.po_name, 1, 3), ''和对讲'', ''5'', ''云视讯'', ''6'', ''千里眼'', ''7'', ''和商务'', ''8'') productclass, ''01'' datasourceid, '''' feeseq,
                      t.customer_code customernumber, to_char(t.offer_order_id) productofferingid,
                      t.offer_code pospecnumber, to_char(t.product_order_id) productsubsid, t.product_code productspecnumber, t.charge_code prodchargecode,
                      to_char(t.tax_rate / 100, ''FM0.00'') taxrate, t.FEETYPE + 1 as feeflag, decode(t.sign_entity, ''0'', '''') contractmain,
                      sum(t.settle_notaxfee /nvl(d.product_digit,1)) * 10 p2csettlementamount,
                      decode(t.feetype, ''1'', t.adjmonth, '''') originalbillmonth, ''3'' settlement_mode
                      from ur_eboss_' || inMonth || '_t t
                       join rvl_dict_config b on t.offer_code = b.offer_code and t.product_code = b.product_code
                       and t.charge_code = b.fee_type  and t.tax_rate = b.tax_rate * 100
		                   left join stl_config_digit d  on  d.pospec_number = t.offer_code and d.product_number = t.product_code
                      where t.product_code = ''5005501'' and t.dest_source = ''5''
                      and t.in_object!=''ZQ''
                      group by t.out_object, t.settle_month,
                      b.po_name, t.customer_code, t.offer_order_id,
                      t.offer_code, t.product_order_id, t.product_code, t.charge_code,
                      t.tax_rate, t.feetype, t.sign_entity, t.adjmonth ';

     -- 【BBOSS】中移凌云，9个需要参与保底/上限调整的费项以外的费项（需求无法给出确定费项，因此采用排除法，无配置）
     -- 20240725上线  中移凌云所有费项都下省
      iv_bill_36_x4 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_36_x
              select t.out_object provcode, ''0'' paytag, t.settle_month billingterm, ''1'' settlementclass, t.in_object settlementpartyin,
           ''19'' productclass, ''01'' datasourceid, '''' feeseq, t.customer_code customernumber, to_char(t.offer_order_id) productofferingid,
           t.offer_code pospecnumber, to_char(t.product_order_id) productsubsid, t.product_code productspecnumber, t.charge_code prodchargecode,
           to_char(t.tax_rate / 100, ''FM0.00'') taxrate, t.FEETYPE + 1  as feeflag, decode(t.sign_entity, ''0'', '''') contractmain,
           sum(t.settle_notaxfee /nvl(d.product_digit,1)) * 10  p2csettlementamount,
           decode(t.feetype, ''1'', t.adjmonth, '''') originalbillmonth,''3'' settlement_mode
      from ur_eboss_' || inMonth || '_t t
       left join stl_config_digit d  on  d.pospec_number = t.offer_code and d.product_number = t.product_code
      where t.offer_code = ''50087'' and t.dest_source = ''5''  and t.product_code = ''2022999400021419''
       group by t.out_object, t.settle_month, t.in_object, t.customer_code, t.offer_order_id,
             t.offer_code, t.product_order_id, t.product_code, t.charge_code,
              t.tax_rate, t.feetype, t.sign_entity, t.adjmonth';


     -- 【EBOSS】通用
     --对or部分优化处理，抽取公共部分(a.type = ''O'' and t1.product_id = a.offer_code)提到外面进行and处理
     iv_bill_36_x5 := 'insert into stludr.bill_36_x '||
    'select t1.prov_code, ''0'' paytag, ''' || inMonth || ''' billingterm, a.settle_type settlementclass, a.company_code settlementpartyin, a.col_code productclass, ' ||
           'a.data_source datasourceid, '''' feeseq, t1.eboss_customer_number customernumber, t1.subs_id productofferingid, t1.product_id pospecnumber, ' ||
           ''''' productsubsid, '''' productspecnumber, t1.charge_code prodchargecode, to_char(to_number(t1.tax_rate), ''FM0.00'') taxrate, to_char(t1.fee_flag) feeflag, t1.main_contract contractmain, ' ||
           'sum(t2.settlement_amount) p2csettlementamount, decode(t1.fee_flag, ''2'', t1.original_bill_month) originalbillmonth,a.settlement_mode settlement_mode ' ||
      'from sync_interface_esp_' || inMonth || ' t1, sync_interface_esp_p2c_' || inMonth || ' t2, (select * from rvl_p2c_bus_config ' ||
                                                                             'where col_code is not null) a ' ||
     'where t1.id = t2.id and t1.file_name = t2.file_name and t1.status = ''0'' and t2.status = ''0'' ' ||
       'and ''' || inMonth || ''' between a.start_month and a.end_month ' ||
       'and t2.settlement_party_in = a.company_code and (t2.settlement_party_in <> ''CY'' or decode(t2.settle_class, ''1'', ''1'', ''2'', ''5'') = a.settle_type) ' ||
       'and a.type = ''O'' and t1.product_id = a.offer_code  ' ||
         'and (a.remark is null or a.remark = ''3'' and t2.settle_class = ''3'' ' ||
         'or a.remark = ''not 3'' and (t2.settle_class <> ''3'' or (t2.settle_class is null or t2.settle_class =''''))) ' ||
     'group by t1.prov_code, a.settle_type, a.company_code, a.col_code, ' ||
              'a.data_source, t1.eboss_customer_number, t1.subs_id, t1.product_id, ' ||
              't1.charge_code, t1.tax_rate, t1.fee_flag, t1.main_contract, ' ||
              't1.original_bill_month, a.settlement_mode ';

      -- 【BBOSS】5G终端分成
      iv_bill_36_x6 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_36_x
      select t.out_object provcode, ''0'' paytag, t.settle_month billingterm, ''5'' settlementclass, ''ZYJC'' settlementpartyin,
            ''4'' productclass, ''01'' datasourceid, '''' feeseq, t.customer_code customernumber, to_char(t.offer_order_id) productofferingid,
           t.offer_code pospecnumber, to_char(t.product_order_id) productsubsid, t.product_code productspecnumber, t.charge_code prodchargecode,
           to_char(t.tax_rate / 100, ''FM0.00'') taxrate, t.FEETYPE + 1   as feeflag, decode(t.sign_entity, ''0'', '''') contractmain,
            sum(t.settle_notaxfee /nvl(d.product_digit,1)) * 10 p2csettlementamount,
           decode(t.feetype, ''1'', t.adjmonth, '''') originalbillmonth ,''3'' settlement_mode
      from ur_eboss_' || inMonth || '_t t
       join (select distinct terminal_vendor from stludr.maapmma_vendor_conf) b on t.in_object = b.terminal_vendor
		    left join stl_config_digit d  on  d.pospec_number = t.offer_code and d.product_number = t.product_code
       where t.in_object = b.terminal_vendor and t.dest_source = ''5'' and t.product_code = ''5003401'' and t.in_object <> ''HLW''
     group by t.out_object, t.settle_month, t.in_object, t.customer_code, t.offer_order_id,
     t.offer_code, t.product_order_id, t.product_code, t.charge_code, t.tax_rate, t.feetype, t.sign_entity, t.adjmonth ';



     -- 【EBOSS】EBOSS和教育产品，无配置（EBOSS上传的数据中，凡结给成研但我方表中无配置的，均认为是和教育）
     iv_bill_36_x7 := 'insert into stludr.bill_36_x '||
    'select t1.prov_code, ''0'' paytag, ''' || inMonth || ''' billingterm, decode(t2.settle_class, ''2'', ''5'', t2.settle_class) settlementclass, ''CY'' settlementpartyin, ''9'' productclass, ' ||
           '''02'' datasourceid, '''' feeseq, t1.eboss_customer_number customernumber, t1.subs_id productofferingid, t1.product_id pospecnumber, ' ||
           ''''' productsubsid, '''' productspecnumber, t1.charge_code prodchargecode, to_char(to_number(t1.tax_rate), ''FM0.00'') taxrate, to_char(t1.fee_flag) feeflag, t1.main_contract contractmain, ' ||
           'sum(t2.settlement_amount) p2csettlementamount, decode(t1.fee_flag, ''2'', t1.original_bill_month) originalbillmonth, ''3'' settlement_mode  ' ||
      'from sync_interface_esp_' || inMonth || ' t1, sync_interface_esp_p2c_' || inMonth || ' t2 ' ||
     'where t1.id = t2.id and t1.file_name = t2.file_name and t1.status = ''0'' and t2.status = ''0'' ' ||
       'and t2.settlement_party_in = ''CY'' and t2.settle_class in (''1'', ''2'') ' ||
       'and t1.product_id not in (select offer_code from rvl_p2c_bus_config where rep_num = ''D313-2'' and offer_code is not null ' ||
            'and ''' || inMonth || ''' between start_month and end_month) ' ||
       'and t1.product_id not in (select offer_code from rvl_p2c_bus_config where rep_num = ''D313-2'' and data_source = ''02'' ' ||
            'and ''' || inMonth || ''' between start_month and end_month) ' ||
     'group by t1.prov_code, t2.settle_class, t1.eboss_customer_number, t1.subs_id, t1.product_id, ' ||
              't1.charge_code, t1.tax_rate, t1.fee_flag, t1.main_contract, ' ||
              't1.original_bill_month ';

    --原来的round(sum) 组合进行了拆分，插入bill_36_x表时只是sum，没有round计算，往表bill_36插入时做了round处理， 因为调试时的集群版本不支持round函数下推了。
     iv_bill_36 := 'insert into stludr.bill_36
       select /*+ hash_join(y) */ x.PROVCODE, x.PAYTAG,x.BILLINGTERM,x.SETTLEMENTCLASS, x.SETTLEMENTPARTYIN, x.PRODUCTCLASS,
       x.DATASOURCEID, x.FEESEQ,x.CUSTOMERNUMBER, x.PRODUCTOFFERINGID,x.POSPECNUMBER, x.PRODUCTSUBSID,
       x.PRODUCTSPECNUMBER,x.PRODCHARGECODE, x.TAXRATE, x.FEEFLAG, x.CONTRACTMAIN,
       round(x.P2CSETTLEMENTAMOUNT,0), x.ORIGINALBILLMONTH, x.SETTLEMENT_MODE,
       y.taxrate taxrate_lim, y.minbalanceamount, y.maxbalanceamount, y.adjustamount,
       row_number() over(partition by x.provcode, x.paytag, x.billingterm, x.settlementclass, x.settlementpartyin,x.productclass, x.settlement_mode ) rn, ''' || inMonth || ''' settlemonth ' ||
      'from stludr.bill_36_x x '||
       'left join ' ||
 -- 需要进行保底/上限调整的业务 （decode(sign) 改为case when 语句，因为xplan尚不支持sign函数）
    '(select l.prov_cd, a.settle_type settlementclass, a.company_code settlementpartyin, a.col_code productclass, ' ||
           'to_char(l.taxrate / 100, ''FM0.00'') taxrate, sum(case when (l.report_fee - l.settle_fee)>0
                               then
                               l.report_fee - l.settle_fee
                               else
                                 0
                                 end) minbalanceamount,
           sum(case when(l.report_fee - l.settle_fee)<0
                               then
                               l.report_fee - l.settle_fee
                               else
                               0
                               end) maxbalanceamount, 0 adjustamount ' ||
      'from rpt_p2c_limited l, (select distinct rep_num, company_code, col_code, col_name, settle_type from rvl_p2c_bus_config ' ||
                                'where col_code is not null and ''' || inMonth || ''' between start_month and end_month) a ' ||
     'where l.settlemonth = ''' || inMonth || ''' ' ||
       'and l.rep_num = a.rep_num and l.col_name = a.col_name ' ||
       'and l.settle_fee - l.report_fee <> 0 group by l.prov_cd, a.settle_type, a.company_code, a.col_code,l.taxrate) y ' ||
       'on x.provcode = y.prov_cd and x.settlementclass = y.settlementclass and x.settlementpartyin = y.settlementpartyin ' ||
      'and x.productclass = y.productclass';

      -- 20240725 V101无差错2批，BBOSS下发给非支付省，用于非支付省列收入的应收结入数据, UuseTag＝2；
      iv_bill_37 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_37  ' ||
                    'SELECT /*+ no_index(a idx_om_ds) */ a.IN_OBJECT in_object, a.OUT_OBJECT out_object,0 settmode, a.CUSTOMER_CODE customer_code,offer_code,product_code,order_mode, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                    ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, '''' member_code, '''' member_prov,  ' ||
                    'a.CHARGE_CODE charge_code, sum(((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10))  settlefee, '''' cdr_chargecode, ' ||
                    ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                    ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymm'') dd, adjmonth, feetype ' ||
                    'FROM UR_RECV_' || inMonth || '_T a ' ||
                    'left join stl_config_digit t1 '||
                    'on t1.POSPEC_NUMBER = a.OFFER_CODE '||
                    'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE '||
                    'WHERE a.ORDER_MODE = ''3'' AND a.PHASE = ''1'' AND a.DEST_SOURCE =''0''  ' ||
                    'AND A.offer_code = ''50051'' AND A.settle_mode = ''2'' ' ||
                    'GROUP BY a.IN_OBJECT,a.OUT_OBJECT, a.OFFER_CODE,a.product_code,a.CUSTOMER_CODE,a.ORDER_MODE, a.OFFER_ORDER_ID,a.ORG_MONTH, a.PRODUCT_ORDER_ID, a.CHARGE_CODE, a.TAX_RATE, adjmonth, feetype ';

      -- 20240725 V101无差错2，BBOSS下发给支付省，用于支付省列支出的应收应结出数据, UuseTag＝7；
      iv_bill_38 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_38  ' ||
                    'SELECT  /*+ no_index(a idx_om_ds) */ a.IN_OBJECT in_object, a.OUT_OBJECT out_object,0 settmode, a.CUSTOMER_CODE customer_code,offer_code,product_code,order_mode, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                    ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, '''' member_code, '''' member_prov,  ' ||
                    'a.CHARGE_CODE charge_code, sum(((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10)) AS settlefee, '''' cdr_chargecode, ' ||
                    ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                    ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymm'') dd, adjmonth, feetype ' ||
                    'FROM UR_RECV_' || inMonth || '_T a ' ||
                    'left join stl_config_digit t1 '||
                    'on t1.POSPEC_NUMBER = a.OFFER_CODE '||
                    'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE '||
                    'WHERE a.ORDER_MODE = ''3'' ' ||
                    'AND a.PHASE = ''2'' AND a.DEST_SOURCE =''0'' AND A.offer_code = ''50051'' AND A.settle_mode = ''2'' ' ||
                    'GROUP BY a.IN_OBJECT,a.OUT_OBJECT, a.OFFER_CODE,a.product_code,a.CUSTOMER_CODE,a.ORDER_MODE, a.OFFER_ORDER_ID,a.ORG_MONTH, a.PRODUCT_ORDER_ID, a.CHARGE_CODE, a.TAX_RATE, adjmonth, feetype ';

      iv_bill_39 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_39 ' ||
     'SELECT INPROV_CODE inprov_code,OUTPROV_CODE outprov_code,SETTLEMONTH settlemonth,PRODUCT_CLASS product_class,  '||
		'to_char(sum(SETTLEMENT_AMOUNT*10),''FM999999999990'')  settlement_amount,  '||
		'to_char(sum(decode(product_class, ''01'', SETTLEMENT_AMOUNT /600000, ''02'', SETTLEMENT_AMOUNT / 600000, ''03'', SETTLEMENT_AMOUNT /600000, ''04'', SETTLEMENT_AMOUNT /600000, ''05'', SETTLEMENT_AMOUNT /600000, ''06'', SETTLEMENT_AMOUNT /600000, ''07'', SETTLEMENT_AMOUNT /320000, ''08'', SETTLEMENT_AMOUNT /444500, ''09'', SETTLEMENT_AMOUNT /288000, ''10'', SETTLEMENT_AMOUNT /444500)), ''FM9999990.0000'' ) bandwidthvalue, '||
		'TAXRATE taxrate '||
		'FROM RPT_BIG_DATA '||
		'WHERE OFFER_CODE = ''75983'' and settlemonth = ''' || inMonth || ''' '||
		'GROUP BY INPROV_CODE,OUTPROV_CODE,SETTLEMONTH,PRODUCT_CLASS,TAXRATE';
--插入前清理数据。
truncate table stludr.bill_12;
truncate table stludr.bill_13;
truncate table stludr.bill_16;
truncate table stludr.bill_17;
truncate table stludr.bill_18;
truncate table stludr.bill_21;
truncate table stludr.bill_22;
truncate table stludr.bill_23;
truncate table stludr.bill_24;
truncate table stludr.bill_25;
truncate table stludr.bill_29;
truncate table stludr.bill_30;
truncate table stludr.bill_31;
truncate table stludr.bill_32;
truncate table stludr.bill_33;
truncate table stludr.bill_34;
truncate table stludr.bill_35;
truncate table stludr.bill_36_x;
truncate table stludr.bill_36;
truncate table stludr.bill_37;
truncate table stludr.bill_38;

truncate table stludr.bill_39;
BEGIN
    set @vSql := iv_bill_12;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_13;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_16;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_17;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_18;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_21;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_22;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_23;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_24;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_25;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_29;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_30;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_31;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_32;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_33;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_34;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_35;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_36_x1;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


set @vSql := iv_bill_36_x2;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


set @vSql := iv_bill_36_x3;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_36_x4;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


set @vSql := iv_bill_36_x5;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


set @vSql := iv_bill_36_x6;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_36_x7;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_36;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_37;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_38;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_39;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
outReturn := 0;
    outSysError := 'OK';

COMMIT;
END;


INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select in_object, settmode, customer_code, to_char(offer_order_id), org_month, paymonth, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, adjmonth, feetype from stludr.bill_12', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 12, NULL, NULL, 5000, 'BBOSS 2批下发-用于配合省列收入的应收账单', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select in_object, settmode, customer_code, to_char(offer_order_id), org_month, paid_month, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, adjmonth, feetype from stludr.bill_13', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 13, NULL, NULL, 5000, 'BBOSS 2批下发-用于配合省销帐的实收账单', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select in_object, settmode, customer_code, to_char(offer_order_id), org_month, paymonth, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, adjmonth, feetype from stludr.bill_16', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 16, NULL, NULL, 5000, 'BBOSS 1批下发-用于配合省列收入的应收账单', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select out_object, settmode, customer_code, to_char(offer_order_id), org_month, paymonth, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, adjmonth, feetype from stludr.bill_17', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 17, NULL, NULL, 5000, 'BBOSS 2批下发-用于主办省的应收账单', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select out_object, settmode, customer_code, to_char(offer_order_id), org_month, paymonth, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, adjmonth, feetype from stludr.bill_18', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 18, NULL, NULL, 5000, 'BBOSS 1批下发-用于主办省的应收账单', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select out_object, settmode, customer_code, to_char(offer_order_id), org_month, paid_month, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, adjmonth, feetype from stludr.bill_21', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 21, NULL, NULL, 5000, 'BBOSS 2批下发-用于主办省的CP实收账单', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select in_object, settmode, customer_code, to_char(offer_order_id), org_month, paymonth, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, charge, dd, prodattr1, prodattr2, adjmonth, feetype from stludr.bill_22', '$SETTLE_HOME/$SETTLE_APPID/data/cp/BILLLIST', 22, NULL, NULL, 5000, 'BBOSS 1批下发-用于配合省列结出到CP基地省的应收账单', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select in_object, settmode, customer_code, to_char(offer_order_id), org_month, paid_month, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, charge, dd, prodattr1, prodattr2, adjmonth, feetype from stludr.bill_23', '$SETTLE_HOME/$SETTLE_APPID/data/cp/BILLLIST', 23, NULL, NULL, 5000, 'BBOSS 2批下发-用于配合省结出到CP基地省的实收账单', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select out_object, settmode, customer_code, to_char(offer_order_id), org_month, paymonth, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, adjmonth, feetype from stludr.bill_24', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 24, NULL, NULL, 5000, 'BBOSS 1批下发-用于主办省的CP应收账单', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select out_object, settmode, customer_code, to_char(offer_order_id), org_month, paid_month, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, adjmonth, feetype from stludr.bill_25', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 25, NULL, NULL, 5000, 'BBOSS 2批下发-用于主办省的实收账单', 'C', inMonth, 'EBILL', SYSDATE);

INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select in_object, settmode, customer_code, to_char(offer_order_id), org_month, paymonth, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, out_object, order_mode, offer_code, product_code, adjmonth, feetype from stludr. bill_29', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 29, NULL, NULL, 5000, 'BBOSS 2批下发-用于配合省列收入的应收账单（新格式）', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select in_object, settmode, customer_code, to_char(offer_order_id), org_month, paid_month, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, out_object, order_mode, offer_code, product_code, adjmonth, feetype from stludr.bill_30', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 30, NULL, NULL, 5000, 'BBOSS 2批下发-用于配合省销帐的实收账单（新格式）', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select in_object, settmode, customer_code, to_char(offer_order_id), org_month, paymonth, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, out_object, order_mode, offer_code, product_code, adjmonth, feetype from stludr.bill_31', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 31, NULL, NULL, 5000, 'BBOSS 1批下发-用于配合省列收入的应收账单（新格式）', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select out_object, settmode, customer_code, to_char(offer_order_id), org_month, paymonth, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, in_object, order_mode, offer_code, product_code, adjmonth, feetype from stludr.bill_32', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 32, NULL, NULL, 5000, 'BBOSS 2批下发-用于主办省的应收账单（新格式）', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select out_object, settmode, customer_code, to_char(offer_order_id), org_month, paymonth, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, in_object, order_mode, offer_code, product_code, adjmonth, feetype from stludr.bill_33', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 33, NULL, NULL, 5000, 'BBOSS 1批下发-用于主办省的应收账单（新格式）', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select out_object, settmode, customer_code, to_char(offer_order_id), org_month, paid_month, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, in_object, order_mode, offer_code, product_code, adjmonth, feetype from stludr.bill_34', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 34, NULL, NULL, 5000, 'BBOSS 2批下发-用于主办省的实收账单（新格式）', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK(TASK_ID,TASK_NAME,DB_NAME,DB_USER,BODY_SQL_TEXT,OUTGOING_DIR,FORMAT_ID,RUN_STYLE,DAY_OR_MONTH,FILE_LEN,DESCRIPTION,TASK_STATUS,ACCT_MONTH,TASK_TYPE,PROCESS_DATE)
VALUES(SEND2RELATION_PROV.NEXTVAL,'Settlesend','tst3','stludr','select prov_code, pay_tag, customer_province_number, ec_id, customer_name, fee_seq, product_subs_id, issue_time, expire_time,
       product_class, settlement_class, product_type, product_name, product_id, prod_charge_code, rateplan_id, rateplan_name,
       partner_name, partner_code, member_nums, billing_term,  fee_val, settle_fee, tax_rate, standard_sale_price, settle_price,
       par_settle_rate, par_res_settl_rate, settlement_type, par_settl_amount, fee_flag, busi_type, contract_main, city_code, creator_name, if_free_resource,
       settlement_rate, P2Csettlement_type, settlement_amount, original_bill_month, discount_Type, settle_disvalue,  Prd_Settle_Disvalue,
       SjProductName, SjProductCode, SalesBaseDiscount, PVsettleRate, PVsettleValue, PVProductClass,TaxRate,MinBalanceAmount,MaxBalanceAmount,SettlementClass,
       ProductClass, rn
      from stludr.bill_35 where rn = 1 and taxrate is not null','$SETTLE_HOME/$SETTLE_APPID/data/mc/BILLLIST',38,NULL,NULL,5000,'BBOSS 移动云省专结算及合作伙伴结算文件下发-0001号文件（保底/上限）','C',inMonth,'EBILL',SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK(TASK_ID,TASK_NAME,DB_NAME,DB_USER,BODY_SQL_TEXT,OUTGOING_DIR,FORMAT_ID,RUN_STYLE,DAY_OR_MONTH,FILE_LEN,DESCRIPTION,TASK_STATUS,ACCT_MONTH,TASK_TYPE,PROCESS_DATE)
VALUES(SEND2RELATION_PROV.NEXTVAL,'Settlesend','tst3','stludr','select distinct prov_code, pay_tag, customer_province_number, ec_id, customer_name, fee_seq, product_subs_id, issue_time, expire_time,
       product_class, settlement_class, product_type, product_name, product_id, prod_charge_code, rateplan_id, rateplan_name,
       partner_name, partner_code, member_nums, billing_term,  fee_val, settle_fee, tax_rate, standard_sale_price, settle_price,
       par_settle_rate, par_res_settl_rate, settlement_type, par_settl_amount, fee_flag, busi_type, contract_main, city_code, creator_name, if_free_resource,
       settlement_rate, P2Csettlement_type, settlement_amount, original_bill_month, discount_Type, settle_disvalue,  Prd_Settle_Disvalue ,
       SjProductName, SjProductCode, SalesBaseDiscount, PVsettleRate, PVsettleValue, PVProductClass, rn
      from stludr.bill_35 where rn <> 1 or taxrate is null','$SETTLE_HOME/$SETTLE_APPID/data/mc/BILLLIST',41,NULL,NULL,5000,'BBOSS 移动云省专结算及合作伙伴结算文件下发-0002~9999号文件（无保底/上限）','C',inMonth,'EBILL',SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select provcode, paytag, billingterm, settlementclass, settlementpartyin, productclass, datasourceid, feeseq, customernumber, productofferingid, pospecnumber, productsubsid, productspecnumber, prodchargecode, taxrate, feeflag, contractmain, p2csettlementamount, originalbillmonth, settlement_mode, taxrate_lim, minbalanceamount, maxbalanceamount, adjustamount, settlemonth from (select * from stludr.bill_36) where rn = 1 and taxrate_lim is not null', '$SETTLE_HOME/$SETTLE_APPID/data/esp/BILLLIST', 39, NULL, NULL, 20000, 'BBOSS/EBOSS行业产品省专结算文件下发-0001号文件（保底/上限）', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select provcode, paytag, billingterm, settlementclass, settlementpartyin, productclass, datasourceid, feeseq, customernumber, productofferingid, pospecnumber, productsubsid, productspecnumber, prodchargecode, taxrate, feeflag, contractmain, p2csettlementamount, originalbillmonth, settlement_mode, '''' as taxrate_lim, minbalanceamount, maxbalanceamount, adjustamount, settlemonth from (select * from stludr.bill_36) where rn <> 1 or taxrate_lim is null', '$SETTLE_HOME/$SETTLE_APPID/data/esp/BILLLIST', 40, NULL, NULL, 5000, 'BBOSS/EBOSS行业产品省专结算文件下发-0002~9999号文件（无保底/上限）', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select IN_OBJECT,out_object, settmode, customer_code,offer_code,product_code,order_mode, to_char(offer_order_id), org_month, paymonth, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, adjmonth, feetype from stludr.bill_37', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 42, NULL, NULL, 5000, 'BBOSS 1批下发-V101省间直接结算应收账单，用于非支付省列收入的应收结入数据', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select IN_OBJECT,out_object, settmode, customer_code,offer_code,product_code,order_mode, to_char(offer_order_id), org_month, paymonth, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, adjmonth, feetype from stludr.bill_37', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 43, NULL, NULL, 5000, 'BBOSS 1批下发-V101省间直接结算应收账单，用于支付省列支出的应收应结出数据', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select IN_OBJECT,out_object, settmode, customer_code,offer_code,product_code,order_mode, to_char(offer_order_id), org_month, paymonth, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, adjmonth, feetype from stludr.bill_38', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 44, NULL, NULL, 5000, 'BBOSS 2批下发-V101省间直接结算应收账单，用于非支付省列收入的应收结入数据', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select IN_OBJECT,out_object, settmode, customer_code,offer_code,product_code,order_mode, to_char(offer_order_id), org_month, paymonth, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, adjmonth, feetype from stludr.bill_38', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 45, NULL, NULL, 5000, 'BBOSS 2批下发-V101省间直接结算应收账单，用于支付省列支出的应收应结出数据', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select inprov_code,outprov_code, settlemonth, product_class,settlement_amount,bandwidthvalue, taxrate from stludr.bill_39', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 46, NULL, NULL, 5000, 'BBOSS 1批下发-S201用于主办省的应收账单，用于非支付省列收入的应收结入数据', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select inprov_code,outprov_code, settlemonth, product_class,settlement_amount,bandwidthvalue, taxrate from stludr.bill_39', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 47, NULL, NULL, 5000, 'BBOSS 1批下发-S201用于主办省的应收账单，用于支付省列支出的应收应结出数据', 'C', inMonth, 'EBILL', SYSDATE);


COMMIT;

outSysError := 'DB2F出口文件数据已写入file_db2f_task表。';



SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
END;

END;;
delimiter ;

