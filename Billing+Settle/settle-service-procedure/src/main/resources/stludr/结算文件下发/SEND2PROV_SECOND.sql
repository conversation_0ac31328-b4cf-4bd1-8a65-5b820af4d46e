/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：下发结算文件 -下发给省公司的db2f任务生成
 * FORMAT_ID+DESCRIPTION
 *45	BBOSS 2批下发-V101省间直接结算应收账单，用于支付省列支出的应收应结出数据
 *44	BBOSS 2批下发-V101省间直接结算应收账单，用于非支付省列收入的应收结入数据
 *21	BBOSS 2批下发-用于主办省的CP实收账单
 *25	BBOSS 2批下发-用于主办省的实收账单
 *34	BBOSS 2批下发-用于主办省的实收账单（新格式）
 *17	BBOSS 2批下发-用于主办省的应收账单
 *32	BBOSS 2批下发-用于主办省的应收账单（新格式）
 *12	BBOSS 2批下发-用于配合省列收入的应收账单
 *29	BBOSS 2批下发-用于配合省列收入的应收账单（新格式）
 *23	BBOSS 2批下发-用于配合省结出到CP基地省的实收账单
 *13	BBOSS 2批下发-用于配合省销帐的实收账单
 *30	BBOSS 2批下发-用于配合省销帐的实收账单（新格式）
**/
use stludr;
delimiter ;;
CREATE OR REPLACE DEFINER="stludr"@"10.%" PROCEDURE stludr."SEND2PROV_SECOND"(
    inMonth          IN   VARCHAR2,

    outSysError OUT VARCHAR2(1000),
    outReturn OUT NUMBER(4)
  )
AS
    iv_bill_12   varchar2(8000);
    iv_bill_13   varchar2(8000);
    iv_bill_17   varchar2(8000);
    iv_bill_21   varchar2(8000);
    iv_bill_23   varchar2(8000);
    iv_bill_25   varchar2(8000);
    iv_bill_29   varchar2(8000);
    iv_bill_30   varchar2(8000);
    iv_bill_32   varchar2(8000);
    iv_bill_34   varchar2(8000);
    iv_bill_38   varchar2(8000);

    vSql      varchar2(10240);
    v_proc_name         VARCHAR2(30) := 'SEND2PROV_SECOND';
 	P_ERRCODE   VARCHAR2(32);
    P_ERRMSG    VARCHAR2(2048);
BEGIN
    outSysError := '';
    outReturn := 0;

DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
    GET DIAGNOSTICS CONDITION 1 P_ERRCODE = RETURNED_SQLSTATE, P_ERRMSG = MESSAGE_TEXT;
    set outSysError = substr(P_ERRMSG, 1, 1000);
    set outReturn  = -1;
    ROLLBACK;
    select ('exception: ' || outReturn || '|' || '|' || outSysError ) AS error_msg ;
    call STLUDR.STL_ERROR_LOG(inMonth,P_ERRCODE,outSysError,'',v_proc_name,'');
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = outSysError;
END;

BEGIN

      -- 2批下发-用于配合省列收入的应收账单
      iv_bill_12 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_12  ' ||
            'SELECT /*+ no_index(a idx_om_ds) */ null, a.IN_OBJECT in_object, 0 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, ((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10)  settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
              'left join stl_config_digit t1 '||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE '||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE '||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 2 ' ||
               'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'') ' ||
          -- 20240527,idc协同营销
                    'AND (A.offer_code not in(''50051'',''60015'')  OR(A.offer_code in(''50051'',''60015'')  AND A.settle_mode = ''1'')) '||
            'UNION ALL ' ||
            'SELECT /*+ hash_join(t2,t1) join_order(t2,a,t1) no_index(a idx_om_ds) */  null, a.IN_OBJECT, 0 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov, ' ||
                   'a.CHARGE_CODE, SUM((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1 '||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE '||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE '||
               'join stl_config_send_svc t2 on a.product_code=t2.product_code '||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 2 ' ||
               'AND a.PRODUCT_CODE IN (SELECT PRODUCT_CODE FROM STL_CONFIG_SEND_SVC) ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'') ' ||
             'GROUP BY a.IN_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PRODUCT_ORDER_ID, ' ||
                      'a.CHARGE_CODE, a.TAX_RATE, adjmonth, feetype ' ||
            'ORDER BY in_object, customer_code';


      -- 2批下发-用于配合省销帐的实收账单
      iv_bill_13 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_13  ' ||
            'SELECT /*+ no_index(a idx_om_ds) */ null, a.IN_OBJECT in_object, 1 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   'PAID_MONTH paid_month, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, ((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_PAID_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1 '||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE '||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE '||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
              'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'', ''6'') ' ||
          -- 20240527,idc协同营销
                'AND (A.offer_code not in(''50051'',''60015'')  OR(A.offer_code in(''50051'',''60015'')  AND A.settle_mode = ''1'')) '||
            'UNION ALL ' ||
            'SELECT /*+ hash_join(t2,t1) join_order(t2,a,t1) no_index(a idx_om_ds)*/ null, a.IN_OBJECT, 1 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   'PAID_MONTH, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov, ' ||
                   'a.CHARGE_CODE,  sum((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_PAID_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1 '||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE '||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE '||
               'join stl_config_send_svc t2 on a.product_code=t2.product_code '||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'', ''6'') ' ||
             'GROUP BY a.IN_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PAID_MONTH, ' ||
                      'a.PRODUCT_ORDER_ID, a.CHARGE_CODE, a.TAX_RATE, adjmonth, feetype ' ||
            'ORDER BY in_object, customer_code';


      -- 2批下发-用于主办省的应收账单
      iv_bill_17 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_17  ' ||
            'SELECT  /*+ no_index(a idx_om_ds) */ null, a.OUT_OBJECT out_object, 0 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, ((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1 '||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE '||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE '||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 2 AND a.DEST_SOURCE in (''0'', ''1'') ' ||
              'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
          -- 20240527,idc协同营销
                'AND (A.offer_code not in(''50051'',''60015'')  OR(A.offer_code in(''50051'',''60015'')  AND A.settle_mode = ''1'')) '||
               'UNION ALL ' ||
              'SELECT  null, a.OUT_OBJECT out_object, 0 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, ((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1 '||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE '||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE '||
             'WHERE a.ORDER_MODE = ''5'' ' ||
               'AND a.PHASE = 2 AND a.DEST_SOURCE = ''1'' ' ||
               'AND a.OFFER_CODE IN (''*********'', ''*********'') ' ||
            'UNION ALL ' ||
            'SELECT /*+ hash_join(t2,t1) join_order(t2,a,t1) no_index(a idx_om_ds) */ null, a.OUT_OBJECT, 0 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov,  ' ||
                   'a.CHARGE_CODE,  sum((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1 '||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE '||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE '||
               'join stl_config_send_svc t2 on a.product_code=t2.product_code '||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 2 ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'') ' ||
             'GROUP BY a.OUT_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PRODUCT_ORDER_ID, ' ||
                      'a.CHARGE_CODE, a.TAX_RATE, adjmonth, feetype ' ||
            'ORDER BY out_object, customer_code';


      -- 2批下发-用于主办省的CP实收账单
      iv_bill_21 := 'insert into  stludr.bill_21  ' ||
            'SELECT null, a.OUT_OBJECT out_object, 1 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   'a.PAID_MONTH paid_month, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, (a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE) * 10 settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_CPP_' || inMonth || '_T a ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''3'') ' ||
               'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
               'AND a.DEST_SOURCE = ''7'' ' ||
            'UNION ALL ' ||
            'SELECT null, a.OUT_OBJECT, 1 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   'a.PAID_MONTH, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov,  ' ||
                   'a.CHARGE_CODE, SUM((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE) * 10) settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_CPP_' || inMonth || '_T a ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''3'') ' ||
               'AND a.PRODUCT_CODE IN (SELECT PRODUCT_CODE FROM STL_CONFIG_SEND_SVC) ' ||
               'AND a.DEST_SOURCE = ''7'' ' ||
             'GROUP BY a.OUT_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PAID_MONTH,  ' ||
                      'a.PRODUCT_ORDER_ID, a.CHARGE_CODE, a.TAX_RATE, adjmonth, feetype ' ||
            'ORDER BY out_object, customer_code';



      -- 2批下发-用于配合省结出到CP基地省的实收账单
      iv_bill_23 := 'insert into stludr.bill_23  ' ||
                  'SELECT null, a.IN_OBJECT in_object, 3 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   'a.PAID_MONTH paid_month, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, (a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE) * 10 settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, a.CHARGE * 10 charge, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'b.VALUE prodattr1, '''' prodattr2, adjmonth, feetype ' ||
              'FROM UR_CPP_' || inMonth || '_T a left join stlusers.STL_PRODUCTCHARACTER b on a.PRODUCT_ORDER_ID = b.PRODUCTID ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''3'') ' ||
               'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
               'AND a.DEST_SOURCE = ''7'' ' ||
            'UNION ALL ' ||
            'SELECT null, a.IN_OBJECT, 3 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   'a.PAID_MONTH, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov,  ' ||
                   'a.CHARGE_CODE, SUM((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE) * 10) settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, SUM(a.CHARGE * 10) orgfee, to_char(SYSDATE, ''yyyymmdd'') dd, b.VALUE prodattr1, '''' prodattr2, adjmonth, feetype ' ||
              'FROM UR_CPP_' || inMonth || '_T a left join stlusers.STL_PRODUCTCHARACTER b  on a.PRODUCT_ORDER_ID = b.PRODUCTID ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''3'') ' ||
               'AND a.PRODUCT_CODE IN (SELECT PRODUCT_CODE FROM STL_CONFIG_SEND_SVC) ' ||
               'AND a.DEST_SOURCE = ''7'' ' ||
             'GROUP BY a.IN_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PAID_MONTH, ' ||
                      'a.PRODUCT_ORDER_ID, a.CHARGE_CODE, a.TAX_RATE, b.VALUE, adjmonth, feetype ' ||
            'ORDER BY in_object, customer_code ';



      -- 2批下发-用于主办省的实收账单
      iv_bill_25 := 'insert into stludr.bill_25 ' ||
                  'SELECT /*+ no_index(a idx_om_ds) */ null, a.OUT_OBJECT out_object, 1 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   'a.PAID_MONTH paid_month, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code,((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_PAID_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1 ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'', ''6'') ' ||
          -- 20240527,idc协同营销
                    'AND (A.offer_code not in(''50051'',''60015'')  OR(A.offer_code in(''50051'',''60015'')  AND A.settle_mode = ''1'')) '||
            'UNION ALL ' ||
            'SELECT /*+ hash_join(t2,t1) join_order(t2,a,t1) no_index(a idx_om_ds)*/ null, a.OUT_OBJECT, 1 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   'a.PAID_MONTH, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov,  ' ||
                   'a.CHARGE_CODE, sum((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, adjmonth, feetype ' ||
              'FROM UR_PAID_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1  ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
              'join stl_config_send_svc t2 on a.product_code=t2.product_code ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'', ''6'') ' ||
             'GROUP BY a.OUT_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PAID_MONTH, ' ||
                      'a.PRODUCT_ORDER_ID, a.CHARGE_CODE, a.TAX_RATE, adjmonth, feetype ' ||
            'ORDER BY out_object, customer_code';


      -- 2批下发-用于配合省列收入的应收账单（新格式）
      iv_bill_29 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_29  ' ||
            'SELECT  /*+ no_index(a idx_om_ds) */ null, a.IN_OBJECT in_object, 0 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, ((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'a.out_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
              'left join stl_config_digit t1 ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 2 ' ||
              'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'') ' ||
          -- 20240527,idc协同营销
                    'AND (A.offer_code not in(''50051'',''60015'')  OR(A.offer_code in(''50051'',''60015'')  AND A.settle_mode = ''1'')) '||
            'UNION ALL ' ||
            'SELECT /*+ hash_join(t2,t1) join_order(t2,a,t1) no_index(a idx_om_ds) */ null, a.IN_OBJECT, 0 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov, ' ||
                   'a.CHARGE_CODE, SUM((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'a.out_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
              'left join stl_config_digit t1  ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
              'join stl_config_send_svc t2 on a.product_code=t2.product_code ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 2 ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'') ' ||
             'GROUP BY a.IN_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PRODUCT_ORDER_ID, ' ||
                      'a.CHARGE_CODE, a.TAX_RATE, a.out_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
            'ORDER BY in_object, customer_code';


      -- 2批下发-用于配合省销帐的实收账单（新格式）
      iv_bill_30 := 'insert into stludr.bill_30  ' ||
            'SELECT /*+ no_index(a idx_om_ds) */ null, a.IN_OBJECT in_object, 1 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   'PAID_MONTH paid_month, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code,  ((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'a.out_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
              'FROM UR_PAID_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1  ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
             'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'', ''6'') ' ||
          -- 20240527,idc协同营销
                    'AND (A.offer_code not in(''50051'',''60015'')  OR(A.offer_code in(''50051'',''60015'')  AND A.settle_mode = ''1'')) '||
            'UNION ALL ' ||
            'SELECT /*+ hash_join(t2,t1) join_order(t2,a,t1) no_index(a idx_om_ds)*/ null, a.IN_OBJECT, 1 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   'PAID_MONTH, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov, ' ||
                   'a.CHARGE_CODE, SUM((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'a.out_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
              'FROM UR_PAID_' || inMonth || '_T a ' ||
                'left join stl_config_digit t1  ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
              'join stl_config_send_svc t2 on a.product_code=t2.product_code ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'', ''6'') ' ||
             'GROUP BY a.IN_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PAID_MONTH, ' ||
                      'a.PRODUCT_ORDER_ID, a.CHARGE_CODE, a.TAX_RATE, a.out_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
            'ORDER BY in_object, customer_code';



      -- 2批下发-用于主办省的应收账单（新格式）
      iv_bill_32 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_32 ' ||
            'SELECT /*+ no_index(a idx_om_ds) */ null, a.OUT_OBJECT out_object, 0 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, ((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'a.in_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
                'left join stl_config_digit t1  ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 2 AND a.DEST_SOURCE in (''0'', ''1'') ' ||
              'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
          -- 20240527,idc协同营销
              'AND (A.offer_code not in(''50051'',''60015'')  OR(A.offer_code in(''50051'',''60015'')  AND A.settle_mode = ''1'')) '||
              'UNION ALL '||
               'SELECT  null, a.OUT_OBJECT out_object, 0 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, ((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'a.in_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
                'left join stl_config_digit t1  ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
             'WHERE  a.ORDER_MODE = ''5'' ' ||
               'AND a.PHASE = 2 AND a.DEST_SOURCE = ''1'' ' ||
               'AND a.OFFER_CODE IN (''*********'', ''*********'') ' ||
            'UNION ALL ' ||
            'SELECT /*+ hash_join(t2,t1) join_order(t2,a,t1) no_index(a idx_om_ds) */ null, a.OUT_OBJECT, 0 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   ''''' paymonth, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov,  ' ||
                   'a.CHARGE_CODE, SUM((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'a.in_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
              'FROM UR_RECV_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1  ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
              'join stl_config_send_svc t2 on a.product_code=t2.product_code ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.PHASE = 2 ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'') ' ||
             'GROUP BY a.OUT_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PRODUCT_ORDER_ID, ' ||
                      'a.CHARGE_CODE, a.TAX_RATE, a.in_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
            'ORDER BY out_object, customer_code';



      -- 2批下发-用于主办省的实收账单（新格式）
      iv_bill_34 := 'insert into stludr.bill_34  ' ||
                  'SELECT /*+ no_index(a idx_om_ds) */ null, a.OUT_OBJECT out_object, 1 settmode, a.CUSTOMER_CODE customer_code, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                   'a.PAID_MONTH paid_month, a.PRODUCT_ORDER_ID product_order_id, a.MEMBER_CODE member_code, '''' member_prov,  ' ||
                   'a.CHARGE_CODE charge_code, ((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'a.in_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
              'FROM UR_PAID_' || inMonth || '_T a ' ||
                'left join stl_config_digit t1  ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
              'and not exists (select 1  from stl_config_send_svc t2 where a.product_code = t2.product_code) ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'', ''6'') ' ||
          -- 20240527,idc协同营销
                    'AND (A.offer_code not in(''50051'',''60015'')  OR(A.offer_code in(''50051'',''60015'')  AND A.settle_mode = ''1'')) '||
            'UNION ALL ' ||
            'SELECT /*+ hash_join(t2,t1) join_order(t2,a,t1) no_index(a idx_om_ds)*/ null, a.OUT_OBJECT, 1 settmode, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, ' ||
                   'a.PAID_MONTH, a.PRODUCT_ORDER_ID, '''' dn, '''' memprov,  ' ||
                   'a.CHARGE_CODE, SUM((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10) AS settlefee, '''' cdr_chargecode, ' ||
                   ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                   ''''' settle_taxfee, '''' settle_notaxfee, to_char(SYSDATE, ''yyyymmdd'') dd, ' ||
                   'a.in_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
              'FROM UR_PAID_' || inMonth || '_T a ' ||
               'left join stl_config_digit t1  ' ||
               'on t1.POSPEC_NUMBER = a.OFFER_CODE ' ||
               'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE ' ||
              'join stl_config_send_svc t2 on a.product_code=t2.product_code ' ||
             'WHERE a.ORDER_MODE IN (''1'', ''2'', ''3'', ''4'') ' ||
               'AND a.DEST_SOURCE in (''0'', ''1'', ''6'') ' ||
             'GROUP BY a.OUT_OBJECT, a.CUSTOMER_CODE, a.OFFER_ORDER_ID, a.ORG_MONTH, a.PAID_MONTH, ' ||
                      'a.PRODUCT_ORDER_ID, a.CHARGE_CODE, a.TAX_RATE, a.in_object, a.order_mode, a.offer_code, a.product_code, adjmonth, feetype ' ||
            'ORDER BY out_object, customer_code';



      -- 20240725 V101无差错2批，V101省间直接结算应收账单
      iv_bill_38 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_38  ' ||
                    'SELECT  /*+ no_index(a idx_om_ds) */ null, a.IN_OBJECT in_object, a.OUT_OBJECT out_object,0 settmode, a.CUSTOMER_CODE customer_code,offer_code,product_code,order_mode, a.OFFER_ORDER_ID offer_order_id, a.ORG_MONTH org_month, ' ||
                    ''''' paymonth, a.PRODUCT_ORDER_ID product_order_id, '''' member_code, '''' member_prov,  ' ||
                    'a.CHARGE_CODE charge_code, sum(((a.SETTLE_TAXFEE + a.SETTLE_NOTAXFEE / nvl(t1.PRODUCT_DIGIT, 1)) * 10)) AS settlefee, '''' cdr_chargecode, ' ||
                    ''''' cdr_chargename, to_char(a.TAX_RATE / 100, ''FM9999990.00'') taxrate, ' ||
                    ''''' settle_taxfee, '''' settle_notaxfee, settle_month dd, adjmonth, feetype ' ||
                    'FROM UR_RECV_' || inMonth || '_T a ' ||
                    'left join stl_config_digit t1 '||
                    'on t1.POSPEC_NUMBER = a.OFFER_CODE '||
                    'and t1.PRODUCT_NUMBER = a.PRODUCT_CODE '||
                    'WHERE a.ORDER_MODE = ''3'' ' ||
                    'AND a.PHASE = ''2'' AND a.DEST_SOURCE =''0'' AND A.offer_code in(''50051'',''60015'')  AND A.settle_mode = ''2'' ' ||
                    'GROUP BY a.IN_OBJECT,a.OUT_OBJECT, a.OFFER_CODE,a.product_code,a.CUSTOMER_CODE,a.ORDER_MODE, a.OFFER_ORDER_ID,a.ORG_MONTH, a.PRODUCT_ORDER_ID, a.CHARGE_CODE, a.TAX_RATE, adjmonth, feetype ,a.settle_month';


--插入前清理数据。
truncate table stludr.bill_12;
truncate table stludr.bill_13;
truncate table stludr.bill_17;
truncate table stludr.bill_21;
truncate table stludr.bill_23;
truncate table stludr.bill_25;
truncate table stludr.bill_29;
truncate table stludr.bill_30;
truncate table stludr.bill_32;
truncate table stludr.bill_34;
truncate table stludr.bill_38;

BEGIN

set @vSql := iv_bill_12;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_13;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_17;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_21;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_23;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_25;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_29;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_30;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_32;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_34;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_38;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
outSysError := 'OK';

COMMIT;
END;


INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select in_object, settmode, customer_code, to_char(offer_order_id), org_month, paymonth, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, adjmonth, feetype from stludr.bill_12', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 12, NULL, NULL, 5000, 'BBOSS 2批下发-用于配合省列收入的应收账单', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select in_object, settmode, customer_code, to_char(offer_order_id), org_month, paid_month, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, adjmonth, feetype from stludr.bill_13', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 13, NULL, NULL, 5000, 'BBOSS 2批下发-用于配合省销帐的实收账单', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select out_object, settmode, customer_code, to_char(offer_order_id), org_month, paymonth, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, adjmonth, feetype from stludr.bill_17', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 17, NULL, NULL, 5000, 'BBOSS 2批下发-用于主办省的应收账单', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select out_object, settmode, customer_code, to_char(offer_order_id), org_month, paid_month, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, adjmonth, feetype from stludr.bill_21', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 21, NULL, NULL, 5000, 'BBOSS 2批下发-用于主办省的CP实收账单', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select in_object, settmode, customer_code, to_char(offer_order_id), org_month, paid_month, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, charge, dd, prodattr1, prodattr2, adjmonth, feetype from stludr.bill_23', '$SETTLE_HOME/$SETTLE_APPID/data/cp/BILLLIST', 23, NULL, NULL, 5000, 'BBOSS 2批下发-用于配合省结出到CP基地省的实收账单', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select out_object, settmode, customer_code, to_char(offer_order_id), org_month, paid_month, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, adjmonth, feetype from stludr.bill_25', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 25, NULL, NULL, 5000, 'BBOSS 2批下发-用于主办省的实收账单', 'C', inMonth, 'EBILL', SYSDATE);

INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select in_object, settmode, customer_code, to_char(offer_order_id), org_month, paymonth, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, out_object, order_mode, offer_code, product_code, adjmonth, feetype from stludr. bill_29', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 29, NULL, NULL, 5000, 'BBOSS 2批下发-用于配合省列收入的应收账单（新格式）', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select in_object, settmode, customer_code, to_char(offer_order_id), org_month, paid_month, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, out_object, order_mode, offer_code, product_code, adjmonth, feetype from stludr.bill_30', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 30, NULL, NULL, 5000, 'BBOSS 2批下发-用于配合省销帐的实收账单（新格式）', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select out_object, settmode, customer_code, to_char(offer_order_id), org_month, paymonth, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, in_object, order_mode, offer_code, product_code, adjmonth, feetype from stludr.bill_32', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 32, NULL, NULL, 5000, 'BBOSS 2批下发-用于主办省的应收账单（新格式）', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select out_object, settmode, customer_code, to_char(offer_order_id), org_month, paid_month, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, in_object, order_mode, offer_code, product_code, adjmonth, feetype from stludr.bill_34', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 34, NULL, NULL, 5000, 'BBOSS 2批下发-用于主办省的实收账单（新格式）', 'C', inMonth, 'EBILL', SYSDATE);

INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select IN_OBJECT,out_object, settmode, customer_code,offer_code,product_code,order_mode, to_char(offer_order_id), org_month, paymonth, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, adjmonth, feetype from stludr.bill_38', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 44, NULL, NULL, 5000, 'BBOSS 2批下发-V101省间直接结算应收账单，用于非支付省列收入的应收结入数据', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select IN_OBJECT,out_object, settmode, customer_code,offer_code,product_code,order_mode, to_char(offer_order_id), org_month, paymonth, to_char(product_order_id), member_code, member_prov, charge_code, settlefee, cdr_chargecode, cdr_chargename, taxrate, settle_taxfee, settle_notaxfee, dd, adjmonth, feetype from stludr.bill_38', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 45, NULL, NULL, 5000, 'BBOSS 2批下发-V101省间直接结算应收账单，用于支付省列支出的应收应结出数据', 'C', inMonth, 'EBILL', SYSDATE);


COMMIT;

outSysError := 'DB2F出口文件下省二批数据已写入file_db2f_task表。';



SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
END;

END;;
delimiter ;

