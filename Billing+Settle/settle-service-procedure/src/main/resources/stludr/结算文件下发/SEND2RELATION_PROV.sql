/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：下发结算文件 -下发给省公司的db2f任务生成
**/
delimiter ;;
CREATE OR REPLACE DEFINER="stludr"@"10.%" PROCEDURE "SEND2RELATION_PROV"(
    inMonth          IN   VARCHAR2,
    inBatch IN VARCHAR2,
    flag_version IN VARCHAR2,
    reserve1 IN VARCHAR2,
    reserve2 IN VARCHAR2,
    proc_out OUT VARCHAR2,
    outSysError OUT VARCHAR2(1000),
    outReturn OUT NUMBER(4),
    outBL OUT VARCHAR2,
    outAR OUT VARCHAR2
  )
AS
    v_proc_name         VARCHAR2(30) := 'SEND2RELATION_PROV';

BEGIN
    outSysError := '';
    outReturn := 0;



BEGIN
CALL SEND2PROV_FIRST(inMonth,@outSysError,@outReturn);

CALL SEND2PROV_SECOND(inMonth,@outSysError,@outReturn);

CALL SEND2PROV_YDY(inMonth,@outSysError,@outReturn);

CALL SEND2PROV_COMPANY(inMonth,@outSysError,@outReturn);

CALL SEND2PROV_S201(inMonth,@outSysError,@outReturn);

END;

outSysError := 'DB2F出口文件数据已写入file_db2f_task表。';



SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;

END;;
delimiter ;

