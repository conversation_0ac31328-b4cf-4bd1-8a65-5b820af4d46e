/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：下发结算文件 -下发给省公司的db2f任务生成
 * FORMAT_ID+DESCRIPTION
 *  39  BBOSS/EBOSS行业产品省专结算文件下发-0001号文件（保底/上限）
 *  40  BBOSS/EBOSS行业产品省专结算文件下发-0002~9999号文件（无保底/上限）
**/
use stludr;
delimiter ;;
CREATE OR REPLACE DEFINER="stludr"@"10.%" PROCEDURE stludr."SEND2PROV_COMPANY"(
    inMonth          IN   VARCHAR2,
    outSysError OUT VARCHAR2(1000),
    outReturn OUT NUMBER(4)
  )
AS

    iv_bill_36_x1   varchar2(12000);
    iv_bill_36_x2   varchar2(12000);
    iv_bill_36_x3   varchar2(12000);
    iv_bill_36_x4   varchar2(12000);
    iv_bill_36_x5   varchar2(12000);
    iv_bill_36_x6   varchar2(12000);
    iv_bill_36_x7   varchar2(12000);
    iv_bill_36_x8   varchar2(12000);
    iv_bill_36   varchar2(12000);

    vSql      varchar2(10240);
    v_proc_name         VARCHAR2(30) := 'SEND2PROV_COMPANY';
 	P_ERRCODE   VARCHAR2(32);
    P_ERRMSG    VARCHAR2(2048);
BEGIN
    outSysError := '';
    outReturn := 0;

DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
    GET DIAGNOSTICS CONDITION 1 P_ERRCODE = RETURNED_SQLSTATE, P_ERRMSG = MESSAGE_TEXT;
    set outSysError = substr(P_ERRMSG, 1, 1000);
    set outReturn  = -1;
    ROLLBACK;
    select ('exception: ' || outReturn || '|' || '|' || outSysError ) AS error_msg ;
    call STLUDR.STL_ERROR_LOG(inMonth,P_ERRCODE,outSysError,'',v_proc_name,'');
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = outSysError;
END;

BEGIN

          --bill_36视图拆分成两部分，x 合成部分 使用临时表 bill_36_x
          --bill_36_x 和y那部分关联插入表bill_36
          --【BBOSS】 通用   原语句条件中or 拼接的部分 拆分成多个子查询进行union all
        iv_bill_36_x1 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_36_x
    select null, out_object provcode, ''0'' paytag, settle_month billingterm, settle_type settlementclass, company_code settlementpartyin,
           col_code productclass, data_source datasourceid, '''' feeseq, customer_code customernumber, to_char(offer_order_id) productofferingid,
           offer_code pospecnumber, to_char(product_order_id) productsubsid, product_code productspecnumber, charge_code prodchargecode,
           to_char(tax_rate / 100, ''FM0.00'') taxrate, FEETYPE + 1  as feeflag, decode(sign_entity, ''0'', '''') contractmain,
           sum(settle_notaxfee / nvl(product_digit, 1)) * 10  p2csettlementamount,
           decode(feetype, ''1'', adjmonth, '''') originalbillmonth,settlement_mode settlement_mode
      from (
           select t.out_object, t.settle_month,a.settle_type,  a.company_code,a.col_code, a.data_source,
               t.customer_code, t.offer_order_id, t.offer_code,  t.product_order_id,  t.product_code,
               t.charge_code,t.tax_rate,t.FEETYPE, t.sign_entity, t.settle_notaxfee, d.product_digit,
               t.adjmonth,  a.settlement_mode
           from ur_eboss_' || inMonth || '_t t
            join rvl_p2c_bus_config a
          left join stl_config_digit d  on d.pospec_number = t.offer_code  and d.product_number = t.product_code
         where a.col_code is not null
        and t.settle_month between a.start_month and a.end_month and t.dest_source = ''5''
       and t.in_object = a.company_code
       and a.type = ''O'' and t.offer_code = a.offer_code and (t.offer_code <> ''*********'' or t.product_code is null or t.product_code = '''')
       and (t.product_code not in (''2024999480001367'',''2024999480000390'',''2022999400072067'',''2022999400040702'',''2022999400074099'',''2023999400036743'',''5005501'') or t.product_code = '''' or t.product_code is null)
       union all
        select t.out_object, t.settle_month,a.settle_type,  a.company_code,a.col_code, a.data_source,
               t.customer_code, t.offer_order_id, t.offer_code,  t.product_order_id,  t.product_code,
               t.charge_code,t.tax_rate,t.FEETYPE, t.sign_entity, t.settle_notaxfee, d.product_digit,
               t.adjmonth,  a.settlement_mode
           from ur_eboss_' || inMonth || '_t t
            join rvl_p2c_bus_config a
          left join stl_config_digit d  on d.pospec_number = t.offer_code  and d.product_number = t.product_code
         where a.col_code is not null
        and t.settle_month between a.start_month and a.end_month and t.dest_source = ''5''
       and t.in_object = a.company_code
       and a.type = ''OP'' and t.offer_code = a.offer_code and t.product_code = a.product_code
       and (t.product_code not in (''2024999480001367'',''2024999480000390'',''2022999400072067'',''2022999400040702'',''2022999400074099'',''2023999400036743'',''5005501'') or t.product_code = '''' or t.product_code is null)
        union all
        select t.out_object, t.settle_month,a.settle_type,  a.company_code,a.col_code, a.data_source,
               t.customer_code, t.offer_order_id, t.offer_code,  t.product_order_id,  t.product_code,
               t.charge_code,t.tax_rate,t.FEETYPE, t.sign_entity, t.settle_notaxfee, d.product_digit,
               t.adjmonth,  a.settlement_mode
           from ur_eboss_' || inMonth || '_t t
            join rvl_p2c_bus_config a
          left join stl_config_digit d  on d.pospec_number = t.offer_code  and d.product_number = t.product_code
         where a.col_code is not null
        and t.settle_month between a.start_month and a.end_month and t.dest_source = ''5''
       and t.in_object = a.company_code
       and a.type = ''OPF'' and t.offer_code = a.offer_code and t.product_code = a.product_code and t.charge_code = a.charge_item
       and (t.product_code not in (''2024999480001367'',''2024999480000390'',''2022999400072067'',''2022999400040702'',''2022999400074099'',''2023999400036743'',''5005501'') or t.product_code = '''' or t.product_code is null)
 union all
        select t.out_object, t.settle_month,a.settle_type,  a.company_code,a.col_code, a.data_source,
               t.customer_code, t.offer_order_id, t.offer_code,  t.product_order_id,  t.product_code,
               t.charge_code,t.tax_rate,t.FEETYPE, t.sign_entity, t.settle_notaxfee, d.product_digit,
               t.adjmonth,  a.settlement_mode
           from ur_eboss_' || inMonth || '_t t
            join rvl_p2c_bus_config a
          left join stl_config_digit d  on d.pospec_number = t.offer_code  and d.product_number = t.product_code
         where a.col_code is not null
        and t.settle_month between a.start_month and a.end_month and t.dest_source = ''5''
       and t.in_object = a.company_code
       and  a.type = ''OPFR'' and t.offer_code = a.offer_code and t.product_code = a.product_code and t.charge_code = a.charge_item and t.duration = a.rateplan_id
       and (t.product_code not in (''2024999480001367'',''2024999480000390'',''2022999400072067'',''2022999400040702'',''2022999400074099'',''2023999400036743'',''5005501'') or t.product_code = '''' or t.product_code is null)
 union all
        select t.out_object, t.settle_month,a.settle_type,  a.company_code,a.col_code, a.data_source,
               t.customer_code, t.offer_order_id, t.offer_code,  t.product_order_id,  t.product_code,
               t.charge_code,t.tax_rate,t.FEETYPE, t.sign_entity, t.settle_notaxfee, d.product_digit,
               t.adjmonth,  a.settlement_mode
           from ur_eboss_' || inMonth || '_t t
            join rvl_p2c_bus_config a
          left join stl_config_digit d  on d.pospec_number = t.offer_code  and d.product_number = t.product_code
         where a.col_code is not null
        and t.settle_month between a.start_month and a.end_month and t.dest_source = ''5''
       and t.in_object = a.company_code
       and a.type = ''OF'' and t.offer_code = a.offer_code and t.charge_code = a.charge_item
       and (t.product_code not in (''2024999480001367'',''2024999480000390'',''2022999400072067'',''2022999400040702'',''2022999400074099'',''2023999400036743'',''5005501'') or t.product_code = '''' or t.product_code is null)
 union all
        select t.out_object, t.settle_month,a.settle_type,  a.company_code,a.col_code, a.data_source,
               t.customer_code, t.offer_order_id, t.offer_code,  t.product_order_id,  t.product_code,
               t.charge_code,t.tax_rate,t.FEETYPE, t.sign_entity, t.settle_notaxfee, d.product_digit,
               t.adjmonth,  a.settlement_mode
           from ur_eboss_' || inMonth || '_t t
            join rvl_p2c_bus_config a
          left join stl_config_digit d  on d.pospec_number = t.offer_code  and d.product_number = t.product_code
         where a.col_code is not null
        and t.settle_month between a.start_month and a.end_month and t.dest_source = ''5''
       and t.in_object = a.company_code
       and a.type = ''P'' and t.product_code = a.product_code
       and (t.product_code not in (''2024999480001367'',''2024999480000390'',''2022999400072067'',''2022999400040702'',''2022999400074099'',''2023999400036743'',''5005501'') or t.product_code = '''' or t.product_code is null)
      )
     group by out_object, settle_month, settle_type, company_code,
              col_code, data_source, customer_code, offer_order_id,
              offer_code, product_order_id, product_code, charge_code,
              tax_rate, feetype, sign_entity, adjmonth, settlement_mode ';

        --20240326  下发省公司的省专结算明细数据中，按照新D313政企条件标准产品DICT服务包结算单的业务数据下发
        --20240425 202404账期开始不再包含给政企的外部合作伙伴数据
            -- D313不再下发
--      iv_bill_36_x2 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_36_x
--                       select t.out_object provcode, ''0'' paytag, t.settle_month billingterm, ''1'' settlementclass,''ZQ'' settlementpartyin,
--                       a.col_code productclass, a.data_source datasourceid, '''' feeseq, t.customer_code customernumber, to_char(t.offer_order_id) productofferingid,
--                       t.offer_code pospecnumber, to_char(t.product_order_id) productsubsid, t.product_code productspecnumber, t.charge_code prodchargecode,
--                       to_char(t.tax_rate / 100, ''FM0.00'') taxrate, t.FEETYPE + 1 as feeflag, decode(t.sign_entity, ''0'', '''') contractmain,
--                       sum(t.settle_notaxfee /nvl(d.product_digit,1)) * 10 p2csettlementamount,
--                       decode(t.feetype, ''1'', t.adjmonth, '''') originalbillmonth,''3'' settlement_mode
--                       from ur_eboss_' || inMonth || '_t t
--                         join rvl_p2c_bus_config a
--                      left join stl_config_digit d  on d.pospec_number = t.offer_code  and d.product_number = t.product_code
--                       where col_code is not null and t.settle_month between a.start_month and a.end_month and t.dest_source = ''5'' AND a.rep_num=''D313''
--                       and t.product_code = a.product_code
--                       and  a.type = ''OP'' and t.offer_code = a.offer_code and t.product_code = a.product_code and t.product_code in (''2024999480001367'',''2024999480000390'',''2022999400072067'',''2022999400040702'',''2022999400074099'',''2023999400036743'',''2023999400035055'')
--                       and t.in_object!=''ZQ''
--                       group by t.out_object, t.settle_month,
--                       a.col_code, a.data_source, t.customer_code, t.offer_order_id,
--                       t.offer_code, t.product_order_id, t.product_code, t.charge_code,
--                       t.tax_rate, t.feetype, t.sign_entity, t.adjmonth, a.settlement_mode ';
--
--              --【BBOSS】结给政企分公司的DICT业务（无配置）
--              -- 20240425 202404账期开始不再包含给政企的外部合作伙伴数据
--            iv_bill_36_x3 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_36_x
--                       select t.out_object provcode, ''0'' paytag, t.settle_month billingterm, ''1'' settlementclass, ''ZQ'' settlementpartyin,
--                       decode(substr(b.po_name, 1, 3), ''和对讲'', ''5'', ''云视讯'', ''6'', ''千里眼'', ''7'', ''和商务'', ''8'') productclass, ''01'' datasourceid, '''' feeseq,
--                       t.customer_code customernumber, to_char(t.offer_order_id) productofferingid,
--                       t.offer_code pospecnumber, to_char(t.product_order_id) productsubsid, t.product_code productspecnumber, t.charge_code prodchargecode,
--                       to_char(t.tax_rate / 100, ''FM0.00'') taxrate, t.FEETYPE + 1 as feeflag, decode(t.sign_entity, ''0'', '''') contractmain,
--                       sum(t.settle_notaxfee /nvl(d.product_digit,1)) * 10 p2csettlementamount,
--                       decode(t.feetype, ''1'', t.adjmonth, '''') originalbillmonth, ''3'' settlement_mode
--                       from ur_eboss_' || inMonth || '_t t
--                        join rvl_dict_config b on t.offer_code = b.offer_code and t.product_code = b.product_code
--                        and t.charge_code = b.fee_type  and t.tax_rate = b.tax_rate * 100
-- 		                   left join stl_config_digit d  on  d.pospec_number = t.offer_code and d.product_number = t.product_code
--                       where t.product_code = ''5005501'' and t.dest_source = ''5''
--                       and t.in_object!=''ZQ''
--                       group by t.out_object, t.settle_month,
--                       b.po_name, t.customer_code, t.offer_order_id,
--                       t.offer_code, t.product_order_id, t.product_code, t.charge_code,
--                       t.tax_rate, t.feetype, t.sign_entity, t.adjmonth ';

     -- 【BBOSS】中移凌云，9个需要参与保底/上限调整的费项以外的费项（需求无法给出确定费项，因此采用排除法，无配置）
     -- 20240725上线  中移凌云所有费项都下省
      iv_bill_36_x4 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_36_x
              select null, t.out_object provcode, ''0'' paytag, t.settle_month billingterm, ''1'' settlementclass, t.in_object settlementpartyin,
           ''19'' productclass, ''01'' datasourceid, '''' feeseq, t.customer_code customernumber, to_char(t.offer_order_id) productofferingid,
           t.offer_code pospecnumber, to_char(t.product_order_id) productsubsid, t.product_code productspecnumber, t.charge_code prodchargecode,
           to_char(t.tax_rate / 100, ''FM0.00'') taxrate, t.FEETYPE + 1  as feeflag, decode(t.sign_entity, ''0'', '''') contractmain,
           sum(t.settle_notaxfee /nvl(d.product_digit,1)) * 10  p2csettlementamount,
           decode(t.feetype, ''1'', t.adjmonth, '''') originalbillmonth,''3'' settlement_mode
      from ur_eboss_' || inMonth || '_t t
       left join stl_config_digit d  on  d.pospec_number = t.offer_code and d.product_number = t.product_code
      where t.offer_code = ''50087'' and t.dest_source = ''5''  and t.product_code = ''2022999400021419''
       group by t.out_object, t.settle_month, t.in_object, t.customer_code, t.offer_order_id,
             t.offer_code, t.product_order_id, t.product_code, t.charge_code,
              t.tax_rate, t.feetype, t.sign_entity, t.adjmonth';


     -- 【EBOSS】通用
     --对or部分优化处理，抽取公共部分(a.type = ''O'' and t1.product_id = a.offer_code)提到外面进行and处理
     iv_bill_36_x5 := 'insert into stludr.bill_36_x '||
    'select null, t2.SETTLEMENT_PARTY_OUT, ''0'' paytag, ''' || inMonth || ''' billingterm, a.settle_type settlementclass, a.company_code settlementpartyin, a.col_code productclass, ' ||
           'a.data_source datasourceid, '''' feeseq, t1.eboss_customer_number customernumber, t1.subs_id productofferingid, t1.product_id pospecnumber, ' ||
           ''''' productsubsid, '''' productspecnumber, t1.charge_code prodchargecode, to_char(to_number(t1.tax_rate), ''FM0.00'') taxrate, to_char(t1.fee_flag) feeflag, t1.main_contract contractmain, ' ||
           'sum(t2.settlement_amount) p2csettlementamount, decode(t1.fee_flag, ''2'', t1.original_bill_month) originalbillmonth,a.settlement_mode settlement_mode ' ||
      'from sync_interface_esp_' || inMonth || ' t1, sync_interface_esp_p2c_' || inMonth || ' t2, (select * from rvl_p2c_bus_config ' ||
                                                                             'where col_code is not null) a ' ||
     'where t1.id = t2.id and t1.file_name = t2.file_name and t1.status = ''0'' and t2.status = ''0'' ' ||
       'and ''' || inMonth || ''' between a.start_month and a.end_month ' ||
       'and t2.settlement_party_in = a.company_code and (t2.settlement_party_in <> ''CY'' or decode(t2.settle_class, ''1'', ''1'', ''2'', ''5'') = a.settle_type) ' ||
       'and a.type = ''O'' and t1.product_id = a.offer_code  ' ||
         'and (a.remark is null or a.remark = ''3'' and t2.settle_class = ''3'' ' ||
         'or a.remark = ''not 3'' and (t2.settle_class <> ''3'' or (t2.settle_class is null or t2.settle_class =''''))) ' ||
     'group by t2.SETTLEMENT_PARTY_OUT, a.settle_type, a.company_code, a.col_code, ' ||
              'a.data_source, t1.eboss_customer_number, t1.subs_id, t1.product_id, ' ||
              't1.charge_code, t1.tax_rate, t1.fee_flag, t1.main_contract, ' ||
              't1.original_bill_month, a.settlement_mode ';

      -- 【BBOSS】5G终端分成
      iv_bill_36_x6 := 'insert /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_36_x
      select null, t.out_object provcode, ''0'' paytag, t.settle_month billingterm, ''5'' settlementclass, ''ZYJC'' settlementpartyin,
            ''4'' productclass, ''01'' datasourceid, '''' feeseq, t.customer_code customernumber, to_char(t.offer_order_id) productofferingid,
           t.offer_code pospecnumber, to_char(t.product_order_id) productsubsid, t.product_code productspecnumber, t.charge_code prodchargecode,
           to_char(t.tax_rate / 100, ''FM0.00'') taxrate, t.FEETYPE + 1   as feeflag, decode(t.sign_entity, ''0'', '''') contractmain,
            sum(t.settle_notaxfee /nvl(d.product_digit,1)) * 10 p2csettlementamount,
           decode(t.feetype, ''1'', t.adjmonth, '''') originalbillmonth ,''3'' settlement_mode
      from ur_eboss_' || inMonth || '_t t
       join (select distinct terminal_vendor from stludr.maapmma_vendor_conf) b on t.in_object = b.terminal_vendor
		    left join stl_config_digit d  on  d.pospec_number = t.offer_code and d.product_number = t.product_code
       where t.in_object = b.terminal_vendor and t.dest_source = ''5'' and t.product_code = ''5003401'' and t.in_object <> ''HLW''
     group by t.out_object, t.settle_month, t.in_object, t.customer_code, t.offer_order_id,
     t.offer_code, t.product_order_id, t.product_code, t.charge_code, t.tax_rate, t.feetype, t.sign_entity, t.adjmonth ';



     -- 【EBOSS】EBOSS和教育产品，无配置（EBOSS上传的数据中，凡结给成研但我方表中无配置的，均认为是和教育）
     iv_bill_36_x7 := 'insert into stludr.bill_36_x '||
    'select null, t2.SETTLEMENT_PARTY_OUT, ''0'' paytag, ''' || inMonth || ''' billingterm, decode(t2.settle_class, ''2'', ''5'', t2.settle_class) settlementclass, ''CY'' settlementpartyin, ''9'' productclass, ' ||
           '''02'' datasourceid, '''' feeseq, t1.eboss_customer_number customernumber, t1.subs_id productofferingid, t1.product_id pospecnumber, ' ||
           ''''' productsubsid, '''' productspecnumber, t1.charge_code prodchargecode, to_char(to_number(t1.tax_rate), ''FM0.00'') taxrate, to_char(t1.fee_flag) feeflag, t1.main_contract contractmain, ' ||
           'sum(t2.settlement_amount) p2csettlementamount, decode(t1.fee_flag, ''2'', t1.original_bill_month) originalbillmonth, ''3'' settlement_mode  ' ||
      'from sync_interface_esp_' || inMonth || ' t1, sync_interface_esp_p2c_' || inMonth || ' t2 ' ||
     'where t1.id = t2.id and t1.file_name = t2.file_name and t1.status = ''0'' and t2.status = ''0'' ' ||
       'and t2.settlement_party_in = ''CY'' and t2.settle_class in (''1'', ''2'') ' ||
       'and t1.product_id not in (select offer_code from rvl_p2c_bus_config where rep_num = ''D313-2'' and offer_code is not null ' ||
            'and ''' || inMonth || ''' between start_month and end_month) ' ||
       'and t1.product_id not in (select offer_code from rvl_p2c_bus_config where rep_num = ''D313-2'' and data_source = ''02'' ' ||
            'and ''' || inMonth || ''' between start_month and end_month) ' ||
     'group by t2.SETTLEMENT_PARTY_OUT, t2.settle_class, t1.eboss_customer_number, t1.subs_id, t1.product_id, ' ||
              't1.charge_code, t1.tax_rate, t1.fee_flag, t1.main_contract, ' ||
              't1.original_bill_month ';

    -- 处理4.6接口进来的和校园
     iv_bill_36_x8 := 'insert into stludr.bill_36_x '||
    'select null, t2.SETTLEMENT_PARTY_OUT, ''0'' paytag, ''' || inMonth || ''' billingterm, decode(t2.settle_class, ''4'', ''1'', ''5'', ''1'', t2.settle_class) settlementclass, ''CY'' settlementpartyin, decode(t2.settle_class, ''4'', ''90'', ''5'', ''89'', t2.settle_class) productclass, ' ||
           '''02'' datasourceid, '''' feeseq, t1.eboss_customer_number customernumber, t1.subs_id productofferingid, t1.product_id pospecnumber, ' ||
           ''''' productsubsid, '''' productspecnumber, t1.charge_code prodchargecode, to_char(to_number(t1.tax_rate), ''FM0.00'') taxrate, to_char(t1.fee_flag) feeflag, t1.main_contract contractmain, ' ||
           'sum(t2.settlement_amount) p2csettlementamount, decode(t1.fee_flag, ''2'', t1.original_bill_month) originalbillmonth, ''3'' settlement_mode  ' ||
      'from sync_interface_esp_' || inMonth || ' t1, sync_interface_esp_p2c_' || inMonth || ' t2 ' ||
     'where t1.id = t2.id and t1.file_name = t2.file_name and t1.status = ''0'' and t2.status = ''0'' ' ||
       'and t2.settlement_party_in = ''CY'' and t2.settle_class in (''4'', ''5'') ' ||
       'and t1.product_id not in (select offer_code from rvl_p2c_bus_config where rep_num = ''D313-2'' and offer_code is not null ' ||
            'and ''' || inMonth || ''' between start_month and end_month) ' ||
       'and t1.product_id not in (select offer_code from rvl_p2c_bus_config where rep_num = ''D313-2'' and data_source = ''02'' ' ||
            'and ''' || inMonth || ''' between start_month and end_month) ' ||
     'group by t2.SETTLEMENT_PARTY_OUT, t2.settle_class, t1.eboss_customer_number, t1.subs_id, t1.product_id, ' ||
              't1.charge_code, t1.tax_rate, t1.fee_flag, t1.main_contract, ' ||
              't1.original_bill_month ';

     --原来的round(sum) 组合进行了拆分，插入bill_36_x表时只是sum，没有round计算，往表bill_36插入时做了round处理， 因为调试时的集群版本不支持round函数下推了。
     iv_bill_36 := 'insert into stludr.bill_36 (PROVCODE, PAYTAG, BILLINGTERM, SETTLEMENTCLASS, SETTLEMENTPARTYIN, PRODUCTCLASS, DATASOURCEID, FEESEQ, CUSTOMERNUMBER,' ||
                ' PRODUCTOFFERINGID, POSPECNUMBER, PRODUCTSUBSID, PRODUCTSPECNUMBER, PRODCHARGECODE, TAXRATE, FEEFLAG, CONTRACTMAIN, P2CSETTLEMENTAMOUNT, ORIGINALBILLMONTH,' ||
                ' SETTLEMENT_MODE, TAXRATE_LIM, MINBALANCEAMOUNT, MAXBALANCEAMOUNT, ADJUSTAMOUNT, RN, SETTLEMONTH)
       		select /*+ hash_join(y) */  COALESCE(x.PROVCODE,y.PROVCODE) as PROVCODE, COALESCE(x.PAYTAG,y.PAYTAG) as PAYTAG, COALESCE(x.BILLINGTERM,y.BILLINGTERM) as BILLINGTERM, COALESCE(x.SETTLEMENTCLASS,y.SETTLEMENTCLASS) as SETTLEMENTCLASS,
		 COALESCE(x.SETTLEMENTPARTYIN,y.SETTLEMENTPARTYIN) as SETTLEMENTPARTYIN, COALESCE(x.PRODUCTCLASS,y.PRODUCTCLASS) as PRODUCTCLASS,
		 x.DATASOURCEID, x.FEESEQ, x.CUSTOMERNUMBER, x.PRODUCTOFFERINGID, x.POSPECNUMBER, x.PRODUCTSUBSID, x.PRODUCTSPECNUMBER, x.PRODCHARGECODE,
		 x.TAXRATE, x.FEEFLAG, x.CONTRACTMAIN, round(x.P2CSETTLEMENTAMOUNT, 0), x.ORIGINALBILLMONTH, COALESCE(x.SETTLEMENT_MODE,y.SETTLEMENT_MODE) SETTLEMENT_MODE,
		       y.taxrate taxrate_lim, y.minbalanceamount, y.maxbalanceamount, y.adjustamount,
       row_number() over(partition by x.provcode, x.paytag, x.billingterm, x.settlementclass, x.settlementpartyin,x.productclass, x.settlement_mode ) rn, ''' || inMonth || ''' settlemonth ' ||
      'from stludr.bill_36_x x  FULL OUTER JOIN '||
 -- 需要进行保底/上限调整的业务 （decode(sign) 改为case when 语句，因为xplan尚不支持sign函数）
    '(select l.prov_cd PROVCODE,''0'' PAYTAG,  '''|| inMonth ||''' BILLINGTERM,  a.settle_type settlementclass, a.company_code settlementpartyin, a.col_code productclass,a.settlement_mode, ' ||
           ' CASE WHEN l.taxrate IS NULL THEN NULL ELSE to_char(l.taxrate / 100, ''FM0.00'') END AS taxrate, sum(case when (l.type is null or (l.type != ''10'' and l.type != ''11'')) and l.report_fee != l.settle_fee
                               then
                               l.report_fee - l.settle_fee
                               else
                                 0
                                 end) minbalanceamount,
           sum(case when  l.type = ''10''
                               then
                               l.report_fee - l.settle_fee
                               else
                               0
                               end) maxbalanceamount ,
            sum(case when  l.type = ''11''
                      then l.report_fee - l.settle_fee
                      else
                       0
                       end) adjustamount ' ||
      'from rpt_p2c_limited l, (select distinct rep_num, company_code, col_code, col_name, settle_type, settlement_mode from rvl_p2c_bus_config ' ||
                                'where settlement_mode = ''1'' and col_code is not null and ''' || inMonth || ''' between start_month and end_month) a ' ||
     'where l.settlemonth = ''' || inMonth || ''' ' ||
       'and l.rep_num = a.rep_num and l.col_name = a.col_name ' ||
       'and l.settle_fee - l.report_fee <> 0 group by l.prov_cd, a.settle_type, a.company_code,a.settlement_mode, a.col_code,l.taxrate) y ' ||
       'on x.provcode = y.PROVCODE and x.settlementclass = y.settlementclass and x.settlement_mode = y.settlement_mode and x.settlementpartyin = y.settlementpartyin ' ||
      'and x.productclass = y.productclass';




--插入前清理数据。
truncate table stludr.bill_36_x;
truncate table stludr.bill_36;

BEGIN

set @vSql := iv_bill_36_x1;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


-- set @vSql := iv_bill_36_x2;
-- SELECT @vSql;
-- PREPARE STMT FROM @vSql;
-- EXECUTE STMT;
-- DEALLOCATE PREPARE STMT;
--
--
-- set @vSql := iv_bill_36_x3;
-- SELECT @vSql;
-- PREPARE STMT FROM @vSql;
-- EXECUTE STMT;
-- DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_36_x4;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


set @vSql := iv_bill_36_x5;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


set @vSql := iv_bill_36_x6;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_36_x7;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_36_x8;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := iv_bill_36;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
outSysError := 'OK';

COMMIT;
END;

INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE)
VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr',
        'select provcode, paytag, billingterm, settlementclass, settlementpartyin, productclass, datasourceid, feeseq, customernumber, productofferingid, pospecnumber, productsubsid, productspecnumber, prodchargecode, taxrate, feeflag, contractmain, p2csettlementamount, originalbillmonth, settlement_mode, taxrate_lim, minbalanceamount, maxbalanceamount, adjustamount, settlemonth from (select * from stludr.bill_36) where  (rn = 1 and taxrate_lim is not null) or taxrate is null',
        '$SETTLE_HOME/$SETTLE_APPID/data/esp/BILLLIST', 39, NULL, NULL, 20000, 'BBOSS/EBOSS行业产品省专结算文件下发-0001号文件（保底/上限）', 'C', inMonth, 'EBILL', SYSDATE);

INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE)
VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr',
        'select provcode, paytag, billingterm, settlementclass, settlementpartyin, productclass, datasourceid, feeseq, customernumber, productofferingid, pospecnumber, productsubsid, productspecnumber, prodchargecode, taxrate, feeflag, contractmain, p2csettlementamount, originalbillmonth, settlement_mode, '''' as taxrate_lim, minbalanceamount, maxbalanceamount, adjustamount, settlemonth from (select * from stludr.bill_36) where (rn <> 1 and taxrate is not null) or taxrate_lim is null',
        '$SETTLE_HOME/$SETTLE_APPID/data/esp/BILLLIST', 40, NULL, NULL, 5000, 'BBOSS/EBOSS行业产品省专结算文件下发-0002~9999号文件（无保底/上限）', 'C', inMonth, 'EBILL', SYSDATE);


COMMIT;

outSysError := 'DB2F出口文件省专数据已写入file_db2f_task表。';



SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
END;

END;;
delimiter ;

