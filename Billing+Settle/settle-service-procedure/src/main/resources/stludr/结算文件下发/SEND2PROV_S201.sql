/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：下发结算文件 -下发给省公司的db2f任务生成
 * FORMAT_ID+DESCRIPTION
 *  47	BBOSS 1批下发-S201用于主办省的应收账单，用于支付省列支出的应收应结出数据
 *  46	BBOSS 1批下发-S201用于主办省的应收账单，用于非支付省列收入的应收结入数据
**/
use stludr;
delimiter ;;
CREATE OR REPLACE DEFINER="stludr"@"10.%" PROCEDURE stludr."SEND2PROV_S201"(
    inMonth          IN   VARCHAR2,

    outSysError OUT VARCHAR2(1000),
    outReturn OUT NUMBER(4)
  )
AS


    v_proc_name         VARCHAR2(30) := 'SEND2PROV_S201';
 	P_ERRCODE   VARCHAR2(32);
    P_ERRMSG    VARCHAR2(2048);
BEGIN
    outSysError := '';
    outReturn := 0;

DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
    GET DIAGNOSTICS CONDITION 1 P_ERRCODE = RETURNED_SQLSTATE, P_ERRMSG = MESSAGE_TEXT;
    set outSysError = substr(P_ERRMSG, 1, 1000);
    set outReturn  = -1;
    ROLLBACK;
    select ('exception: ' || outReturn || '|' || '|' || outSysError ) AS error_msg ;
    call STLUDR.STL_ERROR_LOG(inMonth,P_ERRCODE,outSysError,'',v_proc_name,'');
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = outSysError;
END;


BEGIN

--插入前清理数据。
truncate table stludr.bill_39;


INSERT /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ into stludr.bill_39
SELECT null, r.INPROV_CODE inprov_code,r.OUTPROV_CODE outprov_code,r.SETTLEMONTH settlemonth,r.PRODUCT_CLASS product_class,
	to_char(sum(r.SETTLEMENT_AMOUNT*10),'FM999999999990')  settlement_amount,
	to_char(sum(r.SETTLEMENT_AMOUNT / (cfg.SETTLE_PRICE * 100)), '*********.0000' ) bandwidthvalue,
	r.TAXRATE taxrate
FROM RPT_BIG_DATA r
JOIN bigdata_broadband_price_cfg cfg ON r.PRODUCT_CLASS = cfg.bus_type
WHERE r.OFFER_CODE = '75983' and r.settlemonth = inMonth
AND cfg.enable = '1'
GROUP BY r.INPROV_CODE,r.OUTPROV_CODE,r.SETTLEMONTH,r.PRODUCT_CLASS,r.TAXRATE;



INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select inprov_code,outprov_code, settlemonth, product_class,settlement_amount,bandwidthvalue, taxrate from stludr.bill_39', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 46, NULL, NULL, 5000, 'BBOSS 1批下发-S201用于主办省的应收账单，用于非支付省列收入的应收结入数据', 'C', inMonth, 'EBILL', SYSDATE);
INSERT INTO stlusers.FILE_DB2F_TASK (TASK_ID, TASK_NAME, DB_NAME, DB_USER, BODY_SQL_TEXT, OUTGOING_DIR, FORMAT_ID, RUN_STYLE, DAY_OR_MONTH, FILE_LEN, DESCRIPTION, TASK_STATUS, ACCT_MONTH, TASK_TYPE, PROCESS_DATE) VALUES (SEND2RELATION_PROV.NEXTVAL, 'Settlesend', 'tst3', 'stludr', 'select inprov_code,outprov_code, settlemonth, product_class,settlement_amount,bandwidthvalue, taxrate from stludr.bill_39', '$SETTLE_HOME/$SETTLE_APPID/data/outgoing/BILLLIST', 47, NULL, NULL, 5000, 'BBOSS 1批下发-S201用于主办省的应收账单，用于支付省列支出的应收应结出数据', 'C', inMonth, 'EBILL', SYSDATE);


COMMIT;

outSysError := 'DB2F出口文件S201数据已写入file_db2f_task表。';



SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
END;

END;;
delimiter ;

