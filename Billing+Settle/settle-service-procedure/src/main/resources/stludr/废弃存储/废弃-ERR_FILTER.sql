DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "ERR_FILTER"(inMonth in varchar2,
                                        outResult out varchar2,
                                        outError out varchar2,
                                        szSysErr OUT VARCHAR2(1000),
                                        nReturn OUT NUMBER(4)  )
AS
  iv_Create_Sql varchar2(1000);
  RPT_SQL     VARCHAR2(8000); --拼SQL的字符串变量
  iv_Table_cnt  number(2);
  iv_Insert_Sql varchar2(6100);
  iv_Update_Sql varchar2(1000);
  i number(1);
  iv_Str varchar2(20);
  v_proc_name VARCHAR2(30) := 'err_filter';


BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
        ROLLBACK;

        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;

  BEGIN
  outResult := 0;

  --建立错误结果筛选表
    select count(*) from information_schema.tables where table_name = concat('ERR_RESETTLE_',inMonth)
    and TABLE_SCHEMA = 'STLUDR' and table_type = 'BASE TABLE' into iv_Table_cnt;

    if iv_Table_Cnt = 0 then
      set @RPT_SQL :=      'create table ERR_RESETTLE_' || inMonth || ' ( ERROR_CODE VARCHAR2(100), ERROR_LINE VARCHAR2(100), BIZ_TYPE VARCHAR2(100), DATA_SOURCE VARCHAR2(100), STREAM_ID VARCHAR2(100), EC_CODE VARCHAR2(100), EC_PROV_CODE VARCHAR2(100), OFFER_CODE VARCHAR2(100), PRODUCT_CODE VARCHAR2(100), OFFER_ORDER_ID VARCHAR2(100), PRODUCT_ORDER_ID VARCHAR2(100), ORDER_PROV VARCHAR2(100), ACCOUNT_ID VARCHAR2(100), MEM_NUMBER VARCHAR2(100), MEM_PROV VARCHAR2(100), ORDER_MODE VARCHAR2(100), SIGN_ENTITY VARCHAR2(100), CHARGE_ITEM VARCHAR2(100), CHARGE VARCHAR2(100), AMOUNT_NOTAX VARCHAR2(100), AMOUNT_TAX VARCHAR2(100), TAX_RATE VARCHAR2(100), ORG_MONTH VARCHAR2(100), PAID_MONTH VARCHAR2(100), TICKET_ID VARCHAR2(100), SETTLE_MONTH VARCHAR2(100), OUT_OBJECT VARCHAR2(100), IN_OBJECT VARCHAR2(100), RECORD_ID VARCHAR2(100), SETTLE_NOTAX VARCHAR2(100), SETTLE_TAX VARCHAR2(100), FILE_ID VARCHAR2(100), RULE_ID VARCHAR2(100), DEST_SOURCE VARCHAR2(100), PHASE VARCHAR2(100), START_TIME VARCHAR2(100), RES1 VARCHAR2(100), RES2 VARCHAR2(100), RES3 VARCHAR2(100), ATTACHMENT VARCHAR2(2000), ERR_CODE VARCHAR2(100), ERR_REASON VARCHAR2(1024), RESETTLE_FLAG CHAR(1), FILTER_REASON VARCHAR2(1000) )';
      SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    end if;


  --清空错误结果筛选表
  SET @RPT_SQL  :='truncate table err_resettle_' || inMonth;
      SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;

  end;

  begin
  --将具体字段和报错信息插入err_resettle_yyyymm表
    i := 1;
    while i <= 2 loop
      if i = 1 then iv_Str := 'bill';end if;
      if i = 2 then iv_Str := 'paid';end if;

    set  @iv_Insert_Sql := 'insert into err_resettle_' || inMonth || ' ' ||
                       'select trim(substr(t.r01_raw_udr, 0, instr(t.r01_raw_udr, '','', 1, 1) - 1)) error_code, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 1) + 1, instr(t.r01_raw_udr, '','', 1, 2) - instr(t.r01_raw_udr, '','', 1, 1) - 1)) error_line, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 2) + 1, instr(t.r01_raw_udr, '','', 1, 3) - instr(t.r01_raw_udr, '','', 1, 2) - 1)) biz_type, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 3) + 1, instr(t.r01_raw_udr, '','', 1, 4) - instr(t.r01_raw_udr, '','', 1, 3) - 1)) data_source, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 4) + 1, instr(t.r01_raw_udr, '','', 1, 5) - instr(t.r01_raw_udr, '','', 1, 4) - 1)) Stream_ID, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 5) + 1, instr(t.r01_raw_udr, '','', 1, 6) - instr(t.r01_raw_udr, '','', 1, 5) - 1)) Ec_code, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 6) + 1, instr(t.r01_raw_udr, '','', 1, 7) - instr(t.r01_raw_udr, '','', 1, 6) - 1)) Ec_Prov_Code, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 7) + 1, instr(t.r01_raw_udr, '','', 1, 8) - instr(t.r01_raw_udr, '','', 1, 7) - 1)) offer_code, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 8) + 1, instr(t.r01_raw_udr, '','', 1, 9) - instr(t.r01_raw_udr, '','', 1, 8) - 1)) product_code, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 9) + 1, instr(t.r01_raw_udr, '','', 1, 10) - instr(t.r01_raw_udr, '','', 1, 9) - 1)) offer_order_id, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 10) + 1, instr(t.r01_raw_udr, '','', 1, 11) - instr(t.r01_raw_udr, '','', 1, 10) - 1)) product_order_id, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 11) + 1, instr(t.r01_raw_udr, '','', 1, 12) - instr(t.r01_raw_udr, '','', 1, 11) - 1)) order_prov, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 12) + 1, instr(t.r01_raw_udr, '','', 1, 13) - instr(t.r01_raw_udr, '','', 1, 12) - 1)) account_id, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 13) + 1, instr(t.r01_raw_udr, '','', 1, 14) - instr(t.r01_raw_udr, '','', 1, 13) - 1)) mem_number, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 14) + 1, instr(t.r01_raw_udr, '','', 1, 15) - instr(t.r01_raw_udr, '','', 1, 14) - 1)) mem_prov, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 15) + 1, instr(t.r01_raw_udr, '','', 1, 16) - instr(t.r01_raw_udr, '','', 1, 15) - 1)) order_mode, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 16) + 1, instr(t.r01_raw_udr, '','', 1, 17) - instr(t.r01_raw_udr, '','', 1, 16) - 1)) SIGN_ENTITY, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 17) + 1, instr(t.r01_raw_udr, '','', 1, 18) - instr(t.r01_raw_udr, '','', 1, 17) - 1)) charge_item, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 18) + 1, instr(t.r01_raw_udr, '','', 1, 19) - instr(t.r01_raw_udr, '','', 1, 18) - 1)) charge, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 19) + 1, instr(t.r01_raw_udr, '','', 1, 20) - instr(t.r01_raw_udr, '','', 1, 19) - 1)) AMOUNT_NOTAX, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 20) + 1, instr(t.r01_raw_udr, '','', 1, 21) - instr(t.r01_raw_udr, '','', 1, 20) - 1)) AMOUNT_TAX, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 21) + 1, instr(t.r01_raw_udr, '','', 1, 22) - instr(t.r01_raw_udr, '','', 1, 21) - 1)) TAX_RATE, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 22) + 1, instr(t.r01_raw_udr, '','', 1, 23) - instr(t.r01_raw_udr, '','', 1, 22) - 1)) org_month, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 23) + 1, instr(t.r01_raw_udr, '','', 1, 24) - instr(t.r01_raw_udr, '','', 1, 23) - 1)) paid_month, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 24) + 1, instr(t.r01_raw_udr, '','', 1, 25) - instr(t.r01_raw_udr, '','', 1, 24) - 1)) Ticket_id, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 25) + 1, instr(t.r01_raw_udr, '','', 1, 26) - instr(t.r01_raw_udr, '','', 1, 25) - 1)) settle_month, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 26) + 1, instr(t.r01_raw_udr, '','', 1, 27) - instr(t.r01_raw_udr, '','', 1, 26) - 1)) Out_Object, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 27) + 1, instr(t.r01_raw_udr, '','', 1, 28) - instr(t.r01_raw_udr, '','', 1, 27) - 1)) In_Object, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 28) + 1, instr(t.r01_raw_udr, '','', 1, 29) - instr(t.r01_raw_udr, '','', 1, 28) - 1)) Record_id, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 29) + 1, instr(t.r01_raw_udr, '','', 1, 30) - instr(t.r01_raw_udr, '','', 1, 29) - 1)) Settle_NOTAX, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 30) + 1, instr(t.r01_raw_udr, '','', 1, 31) - instr(t.r01_raw_udr, '','', 1, 30) - 1)) Settle_TAX, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 31) + 1, instr(t.r01_raw_udr, '','', 1, 32) - instr(t.r01_raw_udr, '','', 1, 31) - 1)) File_Id, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 32) + 1, instr(t.r01_raw_udr, '','', 1, 33) - instr(t.r01_raw_udr, '','', 1, 32) - 1)) rule_id, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 33) + 1, instr(t.r01_raw_udr, '','', 1, 34) - instr(t.r01_raw_udr, '','', 1, 33) - 1)) Dest_Source, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 34) + 1, instr(t.r01_raw_udr, '','', 1, 35) - instr(t.r01_raw_udr, '','', 1, 34) - 1)) phase, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 35) + 1, instr(t.r01_raw_udr, '','', 1, 36) - instr(t.r01_raw_udr, '','', 1, 35) - 1)) start_time, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 36) + 1, instr(t.r01_raw_udr, '','', 1, 37) - instr(t.r01_raw_udr, '','', 1, 36) - 1)) res1, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 37) + 1, instr(t.r01_raw_udr, '','', 1, 38) - instr(t.r01_raw_udr, '','', 1, 37) - 1)) res2, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 38) + 1, instr(t.r01_raw_udr, '','', 1, 39) - instr(t.r01_raw_udr, '','', 1, 38) - 1)) res3, ' ||
                       'trim(substr(t.r01_raw_udr, instr(t.r01_raw_udr, '','', 1, 39) + 1)) attachment, ' ||
                       't.a03_err_code err_code, ' ||
                       't.e08_err_reason err_reason, ' ||
                       '''1'' resettle_flag, ' ||
                       ''''' filter_reason ' ||
                       'from err_' || iv_Str || '_' || inMonth || ' t';
    SELECT @iv_Insert_Sql;
        PREPARE STMT FROM @iv_Insert_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
      i := i + 1;
    end loop;
    commit;
    end;


  begin
  --出账金额为0
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''出账金额为0'' ' ||
                     'where resettle_flag = ''1'' ' ||
                     'and amount_notax = ''0''';
    SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;


  --省行业网关云mas（*********）实收不结
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''省行业网关云mas实收不结'' ' ||
                     'where resettle_flag = ''1'' ' ||
                     'and offer_code = ''*********'' ' ||
                     'and biz_type = ''PAID''';
        SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;


  --千里眼（50008）一次性费用不结
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''千里眼一次性费用不结'' ' ||
                     'where resettle_flag = ''1'' ' ||
                     'and offer_code = ''50008'' ' ||
                     'and exists (select charge_item_ref from stlusers.stl_charge_item_onetime a ' ||
                     'where a.charge_item_ref = t.charge_item)';
    SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;


  --企业手机报（4040104）受理模式3实收不结
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''企业手机报受理模式3实收不结'' ' ||
                     'where resettle_flag = ''1'' ' ||
                     'and offer_code = ''4040104'' ' ||
                     'and biz_type = ''PAID'' ' ||
                     'and order_mode = ''3''';
    SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;


  --9003304946产品订购，定向流量统付（无成员）按成员结算
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''9003304946产品订购，定向流量统付（无成员）按成员结算'' ' ||
                     'where resettle_flag = ''1'' ' ||
                     'and product_order_id = ''9003304946''';
    SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;

  --双跨（0102001）受理模式1不结算
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''双跨受理模式1不结'' ' ||
                     'where resettle_flag = ''1'' ' ||
                     'and offer_code = ''0102001'' ' ||
                     'and order_mode = ''1''';
    SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;

    --18 31 32费项 产品级不结
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''双跨18 31 32费项产品级不结'' ' ||
                     'where resettle_flag = ''1'' ' ||
                     'and offer_code = ''0102001'' ' ||
                     'and product_order_id is not null ' ||
                     'and charge_item in (''18'', ''68'', ''31'', ''81'', ''32'', ''82'')';
    SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;

    --一次性费用及1021费项不结
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''双跨一次性费用及1021费项不结'' ' ||
                     'where resettle_flag = ''1'' ' ||
                     'and offer_code = ''0102001'' ' ||
                     'and (charge_item in (''1021'', ''5021'') ' ||
                     'or exists (select charge_item_ref from stlusers.stl_charge_item_onetime a ' ||
                     'where a.charge_item_ref = t.charge_item))';
    SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;


  --通信能力开放（50006）暂无结算
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''通信能力开放暂无结算'' ' ||
                     'where resettle_flag = ''1'' ' ||
                     'and offer_code = ''50006''';
    SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;


  --农信通（010102002） 受理模式3的不结算
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''农信通受理模式3不结'' ' ||
                     'where resettle_flag = ''1'' ' ||
                     'and offer_code = ''010102002'' ' ||
                     'and order_mode in (''3'')';
    SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;

    --应收受理模式5的不结算
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''农信通应收受理模式5不结'' ' ||
                     'where resettle_flag = ''1'' ' ||
                     'and offer_code = ''010102002'' ' ||
                     'and order_mode in (''5'') ' ||
                     'and biz_type = ''BILL''';
    SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;


  --集团短信MAS受理模式3的不结算（010101001）
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''集团短信MAS受理模式3的不结'' ' ||
                     'where resettle_flag = ''1'' ' ||
                     'and offer_code = ''010101001'' ' ||
                     'and order_mode = ''3''';
    SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;


  --企业互联网电视（4040109）不结
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''企业互联网电视不结'' ' ||
                     'where resettle_flag = ''1'' ' ||
                     'and offer_code = ''4040109''';
    SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;


  --数据专线（01011301、01011306）受理模式4不结
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''数据专线受理模式4不结'' ' ||
                     'where resettle_flag = ''1'' ' ||
                     'and offer_code in (''01011301'', ''01011306'') ' ||
                     'and order_mode = ''4''';
    SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;


  --车务通受理模式3的不结（010109002 本地）（010109001 全网）
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''车务通受理模式3的不结'' ' ||
                     'where resettle_flag = ''1'' ' ||
                     'and offer_code in (''010109001'', ''010109002'') ' ||
                     'and order_mode = ''3''';
    SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;


  --400（01114001 移动400）业务受理模式5的不结算
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''400业务受理模式5的不结算'' ' ||
                     'where resettle_flag = ''1'' ' ||
                     'and offer_code = ''01114001'' ' ||
                     'and order_mode = ''5''';
    SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

    commit;


    szSysErr := 'OK';
    nReturn := 0;

    SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;

    END;
  END ;;
DELIMITER ;