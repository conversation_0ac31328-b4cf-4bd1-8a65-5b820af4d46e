DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "SYNC_INTERFACE_BL_GEN_2_02"(
                                        szMonth        IN  VARCHAR2,  -- 开始日期
                                        szSysErr        OUT VARCHAR2,  -- 系统错误文本
                                        nReturn         OUT NUMBER     -- 处理成功标识
)
AS
    ---  计费与结算接口
    --  Sync_Interface_BL_$YYYYMM表
    sz_break               varchar2(10);
    szSql                  VARCHAR2(5000);  -- 动态SQL语法
    vSql                   VARCHAR2(5000);  -- 动态SQL语法
    nRawCnt        NUMBER;
    v_err_msg  VARCHAR2(512);
    v_proc_name         VARCHAR2(50) := 'SYNC_INTERFACE_BL_GEN_2_02';

BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        szSysErr := substr(@p2, 1, 1000);
        szSysErr := 'break:'||sz_break||'error:'||szSysErr;
        nReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;

    BEGIN

        --  将计费侧的接口表数据插入结算测接口表
        sz_break := '321';
        BEGIN
            SET @szSql:= 'insert into stludr.Sync_Interface_BL_'||substr(szMonth, 1, 6)
              ||' select * from Sync_Interface_BL_'||substr(szMonth, 1, 6)||'@DL_BOSS_ACCT  where remark = 2   '
              ||' and customernumber not in ( SELECT customernumber  FROM black_kehu_cus@DL_BOSS_BILLING)';

            SELECT 'sql 321:'||@szSql;
            PREPARE STMT FROM @szSql;
            EXECUTE STMT;
            DEALLOCATE PREPARE STMT;
        END;

        sz_break := '322';
        BEGIN
            SET @szSql:= 'insert into stludr.Sync_Interface_AMOUNT_'||substr(szMonth, 1, 6)
              ||' select * from Sync_Interface_AMOUNT_'||substr(szMonth, 1, 6)||'@DL_BOSS_ACCT   where remark = 2   '
              ||' and customernumber not in ( SELECT customernumber  FROM black_kehu_cus@DL_BOSS_BILLING)';

            SELECT 'sql 322:'||@szSql;
            PREPARE STMT FROM @szSql;
            EXECUTE STMT;
            DEALLOCATE PREPARE STMT;
        END;


        --50077 SD-WAN同步到结算
        sz_break := '324';
        BEGIN
            SET @szSql:= 'insert into stludr.SYNC_INTERFACE_SDWAN_'||substr(szMonth, 1, 6)
             ||' SELECT * FROM SYNC_INTERFACE_SDWAN_'||substr(szMonth, 1, 6)||'@DL_BOSS_ACCT  where remark = 2  '
             ||' and customernumber not in ( SELECT customernumber  FROM black_kehu_cus@DL_BOSS_BILLING)';

            SELECT 'sql 324:'||@szSql;
            PREPARE STMT FROM @szSql;
            EXECUTE STMT;
            DEALLOCATE PREPARE STMT;
        END;

        commit;

        --50055 dict合作伙伴同步到结算
        sz_break := '323';
        BEGIN
            SET @szSql:= 'insert into stludr.RVL_DICT_ORDER_CONFIG '
            ||'SELECT * from  RVL_DICT_ORDER_CONFIG@DL_BOSS_ACCT';
            SELECT 'sql 323:'||@szSql;
            PREPARE STMT FROM @szSql;
            EXECUTE STMT;
            DEALLOCATE PREPARE STMT;
        END;
        commit;

        szSysErr := 'OK';
        nReturn := 0;

        SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;

    END;

END ;;
DELIMITER ;