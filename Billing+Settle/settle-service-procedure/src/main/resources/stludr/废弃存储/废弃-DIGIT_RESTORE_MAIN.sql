DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "DIGIT_RESTORE_MAIN"(RPT_SETTLEMONTH IN VARCHAR2,
                                                BAT IN VARCHAR2,
                                                PROC_OUT OUT VARCHAR2,
                                                szSysErr OUT VARCHAR2(1000),
                                                nReturn OUT NUMBER(4) )
AS
    PROC_MAIN_OUT VARCHAR2(2000);
    v_proc_name  VARCHAR2(50) := 'PKG_RPT_DIGIT_DIGIT_RESTORE_MAIN';
BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
        ROLLBACK;

        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;
    BEGIN
      PROC_MAIN_OUT:='';

      call STL_DIGIT_CMCC_AR(RPT_SETTLEMONTH,BAT,PROC_OUT,szSysErr,nReturn);
      PROC_MAIN_OUT:=PROC_MAIN_OUT||'|'||PROC_OUT;

  -- 1+2
      call STL_DIGIT_CMCC_BL(RPT_SETTLEMONTH,BAT,PROC_OUT,szSysErr,nReturn);
      PROC_MAIN_OUT:=PROC_MAIN_OUT||'|'||PROC_OUT;

      call STL_DIGIT_RPT_P2C(RPT_SETTLEMONTH,BAT,PROC_OUT,szSysErr,nReturn);
      PROC_MAIN_OUT:=PROC_MAIN_OUT||'|'||PROC_OUT;

      call STL_DIGIT_RPT_PARTNER(RPT_SETTLEMONTH,BAT,PROC_OUT,szSysErr,nReturn);
      PROC_MAIN_OUT:=PROC_MAIN_OUT||'|'||PROC_OUT;

      call STL_DIGIT_AR_INTERPROV(RPT_SETTLEMONTH,BAT,PROC_OUT,szSysErr,nReturn);
      PROC_MAIN_OUT:=PROC_MAIN_OUT||'|'||PROC_OUT;

  -- 1+2
      call STL_DIGIT_BL_INTERPROV(RPT_SETTLEMONTH,BAT,PROC_OUT,szSysErr,nReturn);
      PROC_MAIN_OUT:=PROC_MAIN_OUT||'|'||PROC_OUT;

      PROC_OUT:=PROC_MAIN_OUT;
      COMMIT;

      szSysErr := 'OK';
      nReturn := 0;

      SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;
    END;
END ;;
DELIMITER ;