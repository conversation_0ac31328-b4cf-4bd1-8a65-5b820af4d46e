DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "SYNC_INTERFACE_SETTLE_YDBG_5_01"(
                                        szMonth        IN  VARCHAR2,  -- 开始日期
                                        szSysErr        OUT VARCHAR2,  -- 系统错误文本
                                        nReturn         OUT NUMBER     -- 处理成功标识
)
AS
     szSql                  VARCHAR2(2000);  -- 动态SQL语法
     v_err_msg varchar2(2000);
     v_proc_name         VARCHAR2(50) := 'SYNC_INTERFACE_SETTLE_YDBG_5_01';

BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;

    BEGIN

        --  删除结算侧月表中相应时间区间的数据
        --  因为目前禁止跨库写数据，仅允许跨库读数据，而计费与结算之间必须通过dblink进行跨库访问，因此将本存储过程拆分为三个单独的存储过程
        --  第一个存储过程在结算数据库运行，清理结算数据 -- 即本存储过程
        --  第二个存储过程在计费库运行，生成待同步给结算的数据
        --  第三个存储过程在结算库运行，通过dblink将计费库中的数据写入结算数据库

        SET @szSql:= 'delete from stludr.sync_interface_amount_'||substr(szMonth, 1, 6) ||' where pospecnumber =''50038'' and orgmonth='||substr(szMonth, 1, 6);
        SELECT @szSql;
        PREPARE STMT FROM @szSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        commit;

        szSysErr := 'OK';
        nReturn := 0;

        SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;

    END;

END ;;
DELIMITER ;