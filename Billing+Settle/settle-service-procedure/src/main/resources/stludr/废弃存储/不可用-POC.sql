/**
 *存储过程实现功能：报表中间层 -和对讲各省公司上传情况计算   已经无法使用 ，待确认是否使用
**/
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "POC"(P_SETTLEMONTH IN OUT VARCHAR2,
                                        szSysErr OUT VARCHAR2(1000),
                                        nReturn OUT NUMBER(4)  )
AS
  P_SQL VARCHAR2(8000);
  v_proc_name         VARCHAR2(30) := 'POC';
BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
        ROLLBACK;

        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;
    BEGIN

	DROP TABLE stludr.serv_biz_hdj;

	COMMIT;

	set @P_SQL := 'CREATE TABLE  stludr.serv_biz_hdj as  SELECT b.customer_name,' ||
				'a.ec_code,a.order_id,''' || P_SETTLEMONTH || ''' acctmonth from bboss.serv_biz_code@link_db1 a ,' ||
				'bboss.sur_customer@link_db1 b where a.product_code = ''1101011'' and ''' || P_SETTLEMONTH || '''' ||
				'between to_char(a.effective_date,''yyyymm'') and  to_char(a.expiry_date,''yyyymm'')' ||
				'and a.ec_code not in ( select customernumber from esop_crm.black_customer@link_bl2 )' ||
				'and a.ec_code = b.customer_code and sysdate between b.effective_date and b.expiry_date';
    SELECT @P_SQL;
      PREPARE STMT FROM @P_SQL;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;
  COMMIT;

	DROP TABLE stludr.serv_biz_hdj_member;
	COMMIT;

  P_SQL:='';
  set @P_SQL:='CREATE TABLE stludr.serv_biz_hdj_member as ('||
    'SELECT b.customer_name, b.ec_code, a.subscriber_id, a.member_number, a.effective_date  '||
    '  , a.expiry_date, ''' || P_SETTLEMONTH || ''' AS acctmonth       '||
    'FROM ecgroup2.sur_subscriber_member0@link_db1 a, stludr.serv_biz_hdj b     '||
    'WHERE a.subscriber_id = b.order_id       '||
    '  AND ''' || P_SETTLEMONTH || ''' BETWEEN to_char(a.effective_date, ''yyyymm'') AND to_char(a.expiry_date, ''yyyymm'')  '||
    'UNION ALL        '||
    'SELECT b.customer_name, b.ec_code, a.subscriber_id, a.member_number, a.effective_date   '||
    '  , a.expiry_date, ''' || P_SETTLEMONTH || ''' AS acctmonth        '||
    'FROM ecgroup2.sur_subscriber_member1@link_db1 a, stludr.serv_biz_hdj b   '||
    'WHERE a.subscriber_id = b.order_id      '||
    '  AND ''' || P_SETTLEMONTH || ''' BETWEEN to_char(a.effective_date, ''yyyymm'') AND to_char(a.expiry_date, ''yyyymm'')  '||
    'UNION ALL     '||
    'SELECT b.customer_name, b.ec_code, a.subscriber_id, a.member_number, a.effective_date  '||
    '  , a.expiry_date, ''' || P_SETTLEMONTH || ''' AS acctmonth      '||
    'FROM ecgroup2.sur_subscriber_member2@link_db1 a, stludr.serv_biz_hdj b   '||
    'WHERE a.subscriber_id = b.order_id     '||
    '  AND ''' || P_SETTLEMONTH || ''' BETWEEN to_char(a.effective_date, ''yyyymm'') AND to_char(a.expiry_date, ''yyyymm'')  '||
    'UNION ALL   '||
    'SELECT b.customer_name, b.ec_code, a.subscriber_id, a.member_number, a.effective_date    '||
    '  , a.expiry_date, ''' || P_SETTLEMONTH || ''' AS acctmonth    '||
    'FROM ecgroup2.sur_subscriber_member3@link_db1 a, stludr.serv_biz_hdj b  '||
    'WHERE a.subscriber_id = b.order_id     '||
    '  AND ''' || P_SETTLEMONTH || ''' BETWEEN to_char(a.effective_date, ''yyyymm'') AND to_char(a.expiry_date, ''yyyymm'')  '||
    'UNION ALL     '||
    'SELECT b.customer_name, b.ec_code, a.subscriber_id, a.member_number, a.effective_date    '||
    '  , a.expiry_date, ''' || P_SETTLEMONTH || ''' AS acctmonth      '||
    'FROM ecgroup2.sur_subscriber_member4@link_db1 a, stludr.serv_biz_hdj b   '||
    'WHERE a.subscriber_id = b.order_id     '||
    '  AND ''' || P_SETTLEMONTH || ''' BETWEEN to_char(a.effective_date, ''yyyymm'') AND to_char(a.expiry_date, ''yyyymm'')  '||
    'UNION ALL       '||
    'SELECT b.customer_name, b.ec_code, a.subscriber_id, a.member_number, a.effective_date   '||
    '  , a.expiry_date, ''' || P_SETTLEMONTH || ''' AS acctmonth    '||
    'FROM ecgroup2.sur_subscriber_member5@link_db1 a, stludr.serv_biz_hdj b  '||
    'WHERE a.subscriber_id = b.order_id     '||
    '  AND ''' || P_SETTLEMONTH || ''' BETWEEN to_char(a.effective_date, ''yyyymm'') AND to_char(a.expiry_date, ''yyyymm'')  '||
    'UNION ALL     '||
    'SELECT b.customer_name, b.ec_code, a.subscriber_id, a.member_number, a.effective_date '||
    '  , a.expiry_date, ''' || P_SETTLEMONTH || ''' AS acctmonth    '||
    'FROM ecgroup2.sur_subscriber_member6@link_db1 a, stludr.serv_biz_hdj b  '||
    'WHERE a.subscriber_id = b.order_id     '||
    '  AND ''' || P_SETTLEMONTH || ''' BETWEEN to_char(a.effective_date, ''yyyymm'') AND to_char(a.expiry_date, ''yyyymm'')  '||
    'UNION ALL    '||
    'SELECT b.customer_name, b.ec_code, a.subscriber_id, a.member_number, a.effective_date   '||
    '  , a.expiry_date, ''' || P_SETTLEMONTH || ''' AS acctmonth    '||
    'FROM ecgroup2.sur_subscriber_member7@link_db1 a, stludr.serv_biz_hdj b   '||
    'WHERE a.subscriber_id = b.order_id   '||
    '  AND ''' || P_SETTLEMONTH || ''' BETWEEN to_char(a.effective_date, ''yyyymm'') AND to_char(a.expiry_date, ''yyyymm'')  '||
    'UNION ALL      '||
    'SELECT b.customer_name, b.ec_code, a.subscriber_id, a.member_number, a.effective_date   '||
    '  , a.expiry_date, ''' || P_SETTLEMONTH || ''' AS acctmonth    '||
    'FROM ecgroup2.sur_subscriber_member8@link_db1 a, stludr.serv_biz_hdj b  '||
    'WHERE a.subscriber_id = b.order_id   '||
    '  AND ''' || P_SETTLEMONTH || ''' BETWEEN to_char(a.effective_date, ''yyyymm'') AND to_char(a.expiry_date, ''yyyymm'')  '||
    'UNION ALL  '||
    'SELECT b.customer_name, b.ec_code, a.subscriber_id, a.member_number, a.effective_date '||
    '  , a.expiry_date, ''' || P_SETTLEMONTH || ''' AS acctmonth '||
    'FROM ecgroup2.sur_subscriber_member9@link_db1 a, stludr.serv_biz_hdj b'||
    'WHERE a.subscriber_id = b.order_id'||
    '  AND ''' || P_SETTLEMONTH || ''' BETWEEN to_char(a.effective_date, ''yyyymm'') AND to_char(a.expiry_date, ''yyyymm''))';
    SELECT @P_SQL;
      PREPARE STMT FROM @P_SQL;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;
  COMMIT;

  P_SQL:='';
  set @P_SQL:='insert into  stludr.serv_biz_hdj_member ('||
    'SELECT b.customer_name, b.ec_code, a.subscriber_id, a.member_number, a.effective_date   '||
    '  , a.expiry_date, ''' || P_SETTLEMONTH || ''' AS acctmonth           '||
    'FROM ecgroup1.sur_subscriber_member0@link_db1 a, stludr.serv_biz_hdj b     '||
    'WHERE a.subscriber_id = b.order_id      '||
    '  AND ''' || P_SETTLEMONTH || ''' BETWEEN to_char(a.effective_date, ''yyyymm'') AND to_char(a.expiry_date, ''yyyymm'')   '||
    'UNION ALL        '||
    'SELECT b.customer_name, b.ec_code, a.subscriber_id, a.member_number, a.effective_date     '||
    '  , a.expiry_date, ''' || P_SETTLEMONTH || ''' AS acctmonth                      '||
    'FROM ecgroup1.sur_subscriber_member1@link_db1 a, stludr.serv_biz_hdj b   '||
    'WHERE a.subscriber_id = b.order_id           '||
    '  AND ''' || P_SETTLEMONTH || ''' BETWEEN to_char(a.effective_date, ''yyyymm'') AND to_char(a.expiry_date, ''yyyymm'')   '||
    'UNION ALL        '||
    'SELECT b.customer_name, b.ec_code, a.subscriber_id, a.member_number, a.effective_date    '||
    '  , a.expiry_date, ''' || P_SETTLEMONTH || ''' AS acctmonth         '||
    'FROM ecgroup1.sur_subscriber_member2@link_db1 a, stludr.serv_biz_hdj b    '||
    'WHERE a.subscriber_id = b.order_id      '||
    '  AND ''' || P_SETTLEMONTH || ''' BETWEEN to_char(a.effective_date, ''yyyymm'') AND to_char(a.expiry_date, ''yyyymm'')  '||
    'UNION ALL      '||
    'SELECT b.customer_name, b.ec_code, a.subscriber_id, a.member_number, a.effective_date     '||
    '  , a.expiry_date, ''' || P_SETTLEMONTH || ''' AS acctmonth           '||
    'FROM ecgroup1.sur_subscriber_member3@link_db1 a, stludr.serv_biz_hdj b       '||
    'WHERE a.subscriber_id = b.order_id           '||
    '  AND ''' || P_SETTLEMONTH || ''' BETWEEN to_char(a.effective_date, ''yyyymm'') AND to_char(a.expiry_date, ''yyyymm'')  '||
    'UNION ALL           '||
    'SELECT b.customer_name, b.ec_code, a.subscriber_id, a.member_number, a.effective_date       '||
    '  , a.expiry_date, ''' || P_SETTLEMONTH || ''' AS acctmonth          '||
    'FROM ecgroup1.sur_subscriber_member4@link_db1 a, stludr.serv_biz_hdj b     '||
    'WHERE a.subscriber_id = b.order_id              '||
    '  AND ''' || P_SETTLEMONTH || ''' BETWEEN to_char(a.effective_date, ''yyyymm'') AND to_char(a.expiry_date, ''yyyymm'')  '||
    'UNION ALL         '||
    'SELECT b.customer_name, b.ec_code, a.subscriber_id, a.member_number, a.effective_date    '||
    '  , a.expiry_date, ''' || P_SETTLEMONTH || ''' AS acctmonth           '||
    'FROM ecgroup1.sur_subscriber_member5@link_db1 a, stludr.serv_biz_hdj b       '||
    'WHERE a.subscriber_id = b.order_id       '||
    '  AND ''' || P_SETTLEMONTH || ''' BETWEEN to_char(a.effective_date, ''yyyymm'') AND to_char(a.expiry_date, ''yyyymm'')  '||
    'UNION ALL      '||
    'SELECT b.customer_name, b.ec_code, a.subscriber_id, a.member_number, a.effective_date     '||
    '  , a.expiry_date, ''' || P_SETTLEMONTH || ''' AS acctmonth         '||
    'FROM ecgroup1.sur_subscriber_member6@link_db1 a, stludr.serv_biz_hdj b       '||
    'WHERE a.subscriber_id = b.order_id         '||
    '  AND ''' || P_SETTLEMONTH || ''' BETWEEN to_char(a.effective_date, ''yyyymm'') AND to_char(a.expiry_date, ''yyyymm'')  '||
    'UNION ALL                  '||
    'SELECT b.customer_name, b.ec_code, a.subscriber_id, a.member_number, a.effective_date      '||
    '  , a.expiry_date, ''' || P_SETTLEMONTH || ''' AS acctmonth              '||
    'FROM ecgroup1.sur_subscriber_member7@link_db1 a, stludr.serv_biz_hdj b     '||
    'WHERE a.subscriber_id = b.order_id            '||
    '  AND ''' || P_SETTLEMONTH || ''' BETWEEN to_char(a.effective_date, ''yyyymm'') AND to_char(a.expiry_date, ''yyyymm'')  '||
    'UNION ALL     '||
    'SELECT b.customer_name, b.ec_code, a.subscriber_id, a.member_number, a.effective_date                                   '||
    '  , a.expiry_date, ''' || P_SETTLEMONTH || ''' AS acctmonth    '||
    'FROM ecgroup1.sur_subscriber_member8@link_db1 a, stludr.serv_biz_hdj b    '||
    'WHERE a.subscriber_id = b.order_id       '||
    '  AND ''' || P_SETTLEMONTH || ''' BETWEEN to_char(a.effective_date, ''yyyymm'') AND to_char(a.expiry_date, ''yyyymm'')  '||
    'UNION ALL    '||
    'SELECT b.customer_name, b.ec_code, a.subscriber_id, a.member_number, a.effective_date     '||
    '  , a.expiry_date, ''' || P_SETTLEMONTH || ''' AS acctmonth     '||
    'FROM ecgroup1.sur_subscriber_member9@link_db1 a, stludr.serv_biz_hdj b      '||
    'WHERE a.subscriber_id = b.order_id     '||
    '  AND ''' || P_SETTLEMONTH || ''' BETWEEN to_char(a.effective_date, ''yyyymm'') AND to_char(a.expiry_date, ''yyyymm''))';
    SELECT @P_SQL;
      PREPARE STMT FROM @P_SQL;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;
  COMMIT;


DROP TABLE stludr.serv_biz_hdj_result;
COMMIT;

  P_SQL:='';
  set @P_SQL:='CREATE TABLE stludr.serv_biz_hdj_result AS'||
  'SELECT A.CUSTOMER_NAME, A.EC_CODE, D.PROV_NAME, A.SUBSCRIBER_ID, A.MEMBER_NUMBER '||
  '  , A.EFFECTIVE_DATE, A.EXPIRY_DATE, A.ACCTMONTH FROM STLUDR.SERV_BIZ_HDJ_MEMBER A, '||
  '   BBOSS.DOM_LD_AREA_CD_PROV@LINK_DB1 B, BBOSS.IMSI_LD_CD@LINK_DB1 C, BBOSS.PROVINCE_T@LINK_DB1 D '||
  'WHERE SUBSTR(A.MEMBER_NUMBER, 1, 7) = C.MSISDN_AREA_ID '||
  '  AND C.LD_AREA_CD = B.LD_AREA_CD '||
  '  AND B.PROV_CD = D.PROV_CODE GROUP BY A.CUSTOMER_NAME, A.EC_CODE, '||
  '   D.PROV_NAME, A.SUBSCRIBER_ID, A.MEMBER_NUMBER, A.EFFECTIVE_DATE, A.EXPIRY_DATE, A.ACCTMONTH';
    SELECT @P_SQL;
      PREPARE STMT FROM @P_SQL;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;
  COMMIT;

/*和对讲 不要求上传汇总
*/
	set @P_SQL:='insert into stludr.serv_biz_hdj_buyaoqiu'||
           ' select   /*+parallel(a,20)*/ e.customer_name 客户名称,a.ec_code 客户编码,   '||
           ' d.prov_name 归属省,a.productid 产品订购ID,a.msisdn 成员号码, ''否'' 是否上传账单 from import.ur_acctlist_'||P_SETTLEMONTH||'_t@link_db2 a ,'||
           'acct_bss4.customer@link_db2 e,         '||
           ' bboss.dom_ld_area_cd_prov@link_db1  b,  bboss.imsi_ld_cd@link_db1  c, '||
           ' bboss.province_t@link_db1  d       '||
           ' where a.offer_code = ''1101011''  '||
           '  and substr(a.msisdn,1,7) = c.msisdn_area_id and c.ld_area_cd = b.ld_area_cd  '||
           '  and b.prov_cd = d.prov_code    '||
           '  and a.acct_month = '''||P_SETTLEMONTH||''' and a.ec_code = e.customer_number  '||
           '  group by e.customer_name,a.ec_code,d.prov_name,a.productid,a.msisdn   '||
           '  minus  '||
           '  select /*+parallel(a,20)*/ ab.customer_name 客户名称,ab.ec_code 客户编码,  '||
           '  ab.prov_name 归属省,to_char(ab.subscriber_id) 产品订购ID,ab.member_number 成员号码,''否'' 是否上传账单 from stludr.heduijiang_result   ab)';
    SELECT @P_SQL;
      PREPARE STMT FROM @P_SQL;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;
  COMMIT;
/*和对讲 已上传

*/
  P_SQL:='';
  P_SQL:='insert into stludr.serv_biz_hdj_yichuan'||
        '   select   /*+parallel(a,20)*/ e.customer_name 客户名称,a.ec_code 客户编码,  '||
        '   d.prov_name 归属省,a.productid 产品订购ID,a.msisdn 成员号码,''已上传'' 是否上传账单  '||
        ' from import.ur_acctlist_'||P_SETTLEMONTH||'_t@link_db2 a ,    '||
        'acct_bss4.customer@link_db2 e,       '||
        ' bboss.dom_ld_area_cd_prov@link_db1  b,  bboss.imsi_ld_cd@link_db1  c,     '||
        ' bboss.province_t@link_db1  d                       '||
        ' where a.offer_code = ''1101011'''||
        '  and substr(a.msisdn,1,7) = c.msisdn_area_id and c.ld_area_cd = b.ld_area_cd'||
        '  and b.prov_cd = d.prov_code '||
        '  and a.acct_month = '''||P_SETTLEMONTH||''' and a.ec_code = e.customer_number'||
        '  group by e.customer_name,a.ec_code,d.prov_name,a.productid,a.msisdn' ;
    SELECT @P_SQL;
      PREPARE STMT FROM @P_SQL;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;
  COMMIT;
/*和对讲 未上传
*/
  P_SQL := '';
  set @P_SQL := 'insert into stludr.serv_biz_hdj_weichuan(' ||
           '  select /*+parallel(a,20)*/ ab.customer_name 客户名称,ab.ec_code 客户编码, ' ||
           '  ab.prov_name 归属省,to_char(ab.subscriber_id) 产品订购ID,ab.member_number ' ||
           '   成员号码,''否'' 是否上传账单 from stludr.heduijiang_result   ab    ' ||
           ' minus   ' ||
           ' select   /*+parallel(a,20)*/ e.customer_name 客户名称,a.ec_code 客户编码, ' ||
           ' d.prov_name 归属省,a.productid 产品订购ID,a.msisdn 成员号码,''否'' 是否上传账单  ' ||
           '  from import.ur_acctlist_' || P_SETTLEMONTH ||
           '_t@link_db2 a ,  ' || 'acct_bss4.customer@link_db2 e,   ' ||
           ' bboss.dom_ld_area_cd_prov@link_db1  b,  bboss.imsi_ld_cd@link_db1  c,   ' ||
           ' bboss.province_t@link_db1  d   ' ||
           ' where a.offer_code = ''1101011''  ' ||
           '  and substr(a.msisdn,1,7) = c.msisdn_area_id and c.ld_area_cd = b.ld_area_cd  ' ||
           '  and b.prov_cd = d.prov_code   ' || '  and a.acct_month = ''' ||
           P_SETTLEMONTH || ''' and a.ec_code = e.customer_number ' ||
           '  group by e.customer_name,a.ec_code,d.prov_name,a.productid,a.msisdn )';
    SELECT @P_SQL;
      PREPARE STMT FROM @P_SQL;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;
  COMMIT;
/*和对讲 应上传
*/

  P_SQL := '';
  set @P_SQL := 'INSERT INTO STLUDR.SERV_BIZ_HDJ_YING'||
        ' SELECT /*+parallel(a,20)*/ '||
        'AB.ACCTMONTH,'||
        'AB.CUSTOMER_NAME 客户名称,'||
        'AB.EC_CODE 客户编码,'||
        'AB.PROV_NAME 归属省,'||
        'TO_CHAR(AB.SUBSCRIBER_ID) 产品订购ID,'||
        'AB.MEMBER_NUMBER 成员号码 '||
        'FROM STLUDR.SERV_BIZ_HDJ_RESULT AB'||
        ' WHERE AB.ACCTMONTH = '''||P_SETTLEMONTH||'''';
    SELECT @P_SQL;
      PREPARE STMT FROM @P_SQL;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;
  COMMIT;


  nReturn := 0;
  szSysErr := 'OK';

  commit;

  SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;

  END;
END ;;
DELIMITER ;