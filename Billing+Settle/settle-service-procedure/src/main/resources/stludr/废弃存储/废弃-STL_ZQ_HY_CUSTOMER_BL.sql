DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "STL_ZQ_HY_CUSTOMER_BL"(
                                   RPT_SETTLEMONTH IN VARCHAR2,
									FLAG_VERSION IN CHAR,
									PROC_OUT OUT VARCHAR2,
									szSysErr OUT VARCHAR2(1000),
									nReturn OUT NUMBER(4)
)
AS
    v_proc_name varchar2(100) := 'STL_ZQ_HY_CUSTOMER_BL';
    G_VERSION RVL_CONF_VERSION.VERSION%TYPE; --版本号变量
    G_RESNUM  RVL_CONF_VERSION.VERSIONNUM%TYPE; --版本序号
     ERRCODE   VARCHAR2(32);
    ERRMSG    VARCHAR2(2048);
    VER_TABLE VARCHAR2(64);
    inter_sql   varchar2(4096);
    RPT_RUNSITE VARCHAR2(32); --运行定位标识
    RPT_SQL     VARCHAR2(4000); --拼SQL的字符串变量
    RPT_TABLE   VARCHAR2(64); --拼表名的字符串变量

    outReturn  int;
BEGIN

	 PROC_OUT := '';
    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
        ROLLBACK;

        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;
    BEGIN

    VER_TABLE   := 'RPT_ZQ_HY_CUSTOMER_BL';
    RPT_RUNSITE := '0';
    RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表开始时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
    SELECT 'RPT_SQL=' || RPT_SQL;
	-- 中间表获取版本号
    call SETTLE_REPORT_VERSION(RPT_SETTLEMONTH, VER_TABLE, G_VERSION, G_RESNUM, FLAG_VERSION,szSysErr,nReturn);

    --结果表处理数据
    RPT_TABLE   := 'UR_ESP_' || RPT_SETTLEMONTH || '_T';
    RPT_RUNSITE := '1';
    set @RPT_SQL := 'INSERT INTO '||VER_TABLE||' '||
               '   (SETTLEMONTH, VERSION, ECPROV_CODE, INPROV_CODE, OUTPROV_CODE, PRODUCT_CODE, '||
               '   PRODUCT_NAME,CHARGE_CODE, TAXRATE, SETTNOTAXFEE, PRODUCT_ORDER_ID) '||
               'SELECT '''||RPT_SETTLEMONTH||''','''||G_VERSION||''', '||
               '   T.CUSTOMER_PROV, T.IN_OBJECT, T.OUT_OBJECT, '||
               '   DECODE(T.PRODUCT_ORDER_ID, NULL, T.OFFER_CODE, T.PRODUCT_CODE), '||
               '   DECODE(T.PRODUCT_ORDER_ID,NULL, '||
               '          GET_PRODUCT_NAME(T.OFFER_CODE,T.PRODUCT_CODE,'''||RPT_SETTLEMONTH|| ''','' 1 ''), '||
               '          GET_PRODUCT_NAME(T.OFFER_CODE,T.PRODUCT_CODE,'''||RPT_SETTLEMONTH|| ''','' 3 '')), '||
               '   T.CHARGE_CODE, '||
               '   T.TAX_RATE,'||
               '   SUM(T.SETTLE_NOTAXFEE),'||
               '   T.PRODUCT_ORDER_ID '||
               'FROM ' || RPT_TABLE ||' T '||
               '   WHERE T.DEST_SOURCE = ''0'' and t.out_object <> ''030'' and t.in_object <> ''030'''||
               'GROUP BY T.CUSTOMER_PROV,T.OUT_OBJECT,T.IN_OBJECT,T.OFFER_CODE,T.PRODUCT_CODE, '||
               '         T.CHARGE_CODE,T.TAX_RATE,T.PRODUCT_ORDER_ID';
    SELECT @RPT_SQL;
      PREPARE STMT FROM @RPT_SQL;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;

    RPT_RUNSITE := '2';
    RPT_SQL     := '更新表中省公司的中文名称... ...';
    UPDATE RPT_ZQ_HY_CUSTOMER_BL T
       SET T.INPROV_NAME  = (SELECT T1.PROV_NM FROM STL_PROVINCE_CD T1 WHERE T1.PROV_CD = T.INPROV_CODE),
           T.ECPROV_NAME  = (SELECT T1.PROV_NM FROM STL_PROVINCE_CD T1 WHERE T1.PROV_CD = T.ECPROV_CODE),
           T.OUTPROV_NAME = (SELECT T1.PROV_NM FROM STL_PROVINCE_CD T1 WHERE T1.PROV_CD = T.OUTPROV_CODE)
     WHERE T.VERSION = G_VERSION AND T.SETTLEMONTH = RPT_SETTLEMONTH;



	  /*RPT_RUNSITE := '3';
    RPT_SQL     := '更新表中产品和费项的中文名称... ...';
   UPDATE RPT_ZQ_HY_CUSTOMER_BL T
   SET T.PRODUCT_NAME =
       (SELECT DISTINCT NVL(T1.PRODUCTNAME, '')
          FROM STLUSERS.STL_ESP_PRODUCT_T@users_link T1
         WHERE T1.PRODUCTNUMBER = T.PRODUCT_CODE
           and rpt_settlemonth between to_char(t1.eff_date, 'yyyymm') and
               to_char(t1.exp_date, 'yyyymm')),
       T.CHARGE_NAME =
       (SELECT DISTINCT NVL(T2.CHARGENAME3, '')
          FROM STLUSERS.STL_ESP_PRODUCT_T@users_link T2
         WHERE T2.CHARGECODE3 = T.CHARGE_CODE
           and rpt_settlemonth between to_char(t2.eff_date, 'yyyymm') and
               to_char(t2.exp_date, 'yyyymm'))
    WHERE  T.VERSION = G_VERSION AND T.SETTLEMONTH = RPT_SETTLEMONTH;
    STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, VER_TABLE, RPT_SQL);*/



    RPT_RUNSITE := '4';
    RPT_SQL     := VER_TABLE || '表' || RPT_SETTLEMONTH || '账期数据入库报表中间表结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss');
    SELECT 'RPT_SQL=' || RPT_SQL;

    PROC_OUT := 'Y';
    COMMIT;

    szSysErr := 'OK';
    nReturn := 0;

	SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;

    END;
END ;;
DELIMITER ;