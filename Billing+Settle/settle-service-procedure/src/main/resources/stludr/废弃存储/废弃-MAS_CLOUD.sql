/**
 *存储过程实现功能：报表中间层 -云mas各省公司上传情况计算   已经改造放计费 结算这个废弃
**/
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "MAS_CLOUD"(P_SETTLEMONTH IN OUT VARCHAR2,
                                        szSysErr OUT VARCHAR2(1000),
                                        nReturn OUT NUMBER(4))
AS
  P_SQL  VARCHAR2(4000);
  v_proc_name         VARCHAR2(30) := 'MAS_CLOUD';
BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
        ROLLBACK;

        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;
    BEGIN
  /*云mas   应上传账单
  */
  set @P_SQL := 'insert into stludr.serv_yunmas_yingsc  ' ||
           ' SELECT A.EC_CODE       客户编码,       B.CUSTOMERNAME  客户名称,       ' ||
           'D.PROV_NAME     省,       A.PROD_ORDER_ID 商品订购ID,       A.ORDER_ID    ' ||
           '产品订购ID,       C.PRODUCTNAME   产品名称  FROM BBOSS.SERV_BIZ_CODE@LINK_DB1 A,' ||
           'ESOP_CRM.CUSTOMER@LINK_BL2   B,       ESOP_CRM.PRODUCT@LINK_BL2    C,   ' ||
           'BBOSS.PROVINCE_T@LINK_DB1    D WHERE A.PRODUCT_CODE IN (''*********'', ''*********'')' ||
           'AND ''' || P_SETTLEMONTH ||
           ''' BETWEEN TO_CHAR(A.EFFECTIVE_DATE, ''yyyymm'') AND ' ||
           'TO_CHAR(A.EXPIRY_DATE, ''yyyymm'')   AND A.EC_CODE NOT IN     ' ||
           '(SELECT CUSTOMERNUMBER FROM ESOP_CRM.BLACK_CUSTOMER@LINK_BL2)   AND A.EC_CODE =' ||
           ' B.CUSTOMERNUMBER   AND A.ORDER_ID = C.PRODUCTID   AND A.SERVICE_CODE != ''110163''' ||
           'AND B.COMPANYID = D.PROV_CODE';
    SELECT @P_SQL;
      PREPARE STMT FROM @P_SQL;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;
  COMMIT;
  /*云mas  已上传账单
  */
  P_SQL := '';
  set @P_SQL := 'insert into stludr.serv_yunmas_yisc ' ||
           ' SELECT DISTINCT A.EC_CODE 客户编码,B.CUSTOMERNAME 客户名称, D.PROV_NAME 省,' ||
           'A.PRODUCTOFFERINGID 商品订购ID,A.PRODUCTID 产品订购ID,C.PRODUCTNAME 产品名称,' ||
           'SUM(A.FEEVAL) / 1000 账单金额   FROM IMPORT.UR_ACCTLIST_'||
           P_SETTLEMONTH||'_T@LINK_DB2 A, ' ||
           'ESOP_CRM.CUSTOMER@LINK_BL2 B, ESOP_CRM.PRODUCT@LINK_BL2 C,BBOSS.PROVINCE_T@LINK_DB1' ||
           'D  WHERE A.OFFER_CODE IN (''*********'', ''*********'')  AND A.EC_CODE = B.CUSTOMERNUMBER' ||
           'AND A.PRODUCT_CODE != ''110163'' AND A.PRODUCTID = C.PRODUCTID AND A.PROV_CODE = D.PROV_CODE' ||
           ' GROUP BY A.EC_CODE,B.CUSTOMERNAME,D.PROV_NAME,A.PRODUCTOFFERINGID,A.PRODUCTID,C.PRODUCTNAME';
  SELECT @P_SQL;
      PREPARE STMT FROM @P_SQL;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;
      COMMIT;
  /*云mas  应上传而未上传账单
  */
  P_SQL := '';
  set @P_SQL := 'insert into stludr.serv_yunmas_yingddd ' ||
           'SELECT A.EC_CODE 客户编码,B.CUSTOMERNAME  客户名称,D.PROV_NAME 省,A.PROD_ORDER_ID 商品订购ID,' ||
           'A.ORDER_ID 产品订购ID, C.PRODUCTNAME   产品名称  FROM BBOSS.SERV_BIZ_CODE@LINK_DB1 A,' ||
           'ESOP_CRM.CUSTOMER@LINK_BL2   B,ESOP_CRM.PRODUCT@LINK_BL2    C, BBOSS.PROVINCE_T@LINK_DB1    D' ||
           ' WHERE A.PRODUCT_CODE IN (''*********'', ''*********'')   AND A.SERVICE_CODE != ''110163''  ' ||
           ' AND '''|| P_SETTLEMONTH ||
           ''' BETWEEN TO_CHAR(A.EFFECTIVE_DATE, ''yyyymm'') AND' ||
           ' TO_CHAR(A.EXPIRY_DATE, ''yyyymm'')   AND A.EC_CODE NOT IN       (SELECT CUSTOMERNUMBER FROM ' ||
           'ESOP_CRM.BLACK_CUSTOMER@LINK_BL2)   AND A.EC_CODE = B.CUSTOMERNUMBER   AND A.ORDER_ID = C.PRODUCTID' ||
           'AND B.COMPANYID = D.PROV_CODE MINUS SELECT DISTINCT A.EC_CODE  客户编码,B.CUSTOMERNAME      客户名称,' ||
           'D.PROV_NAME  省, A.PRODUCTOFFERINGID 商品订购ID, A.PRODUCTID   产品订购ID,C.PRODUCTNAME ' ||
           '产品名称   FROM IMPORT.UR_ACCTLIST_'||P_SETTLEMONTH||
           '_T@LINK_DB2 A,' ||
           'ESOP_CRM.CUSTOMER@LINK_BL2  B,ESOP_CRM.PRODUCT@LINK_BL2 C, BBOSS.PROVINCE_T@LINK_DB1 D' ||
           ' WHERE A.OFFER_CODE IN (''*********'', ''*********'')   AND A.PRODUCT_CODE != ''110163''' ||
           'AND A.EC_CODE = B.CUSTOMERNUMBER   AND A.PRODUCTID = C.PRODUCTID   AND A.PROV_CODE = D.PROV_CODE';
  SELECT @P_SQL;
      PREPARE STMT FROM @P_SQL;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;
      COMMIT;
  /*云mas  bboss侧不要求而省公司上传的账单
  */
  P_SQL := '';
  set @P_SQL := 'insert into stludr.serv_yunmas_buyaoddd ' ||
           'SELECT DISTINCT A.EC_CODE 客户编码,B.CUSTOMERNAME  客户名称,D.PROV_NAME  省,' ||
           'A.PRODUCTOFFERINGID 商品订购ID,A.PRODUCTID  产品订购ID,C.PRODUCTNAME 产品名称 FROM  '||
           'FROM IMPORT.UR_ACCTLIST_'||P_SETTLEMONTH||'_T@LINK_DB2 A,ESOP_CRM.CUSTOMER@LINK_BL2 B,' ||
           'ESOP_CRM.PRODUCT@LINK_BL2  C, BBOSS.PROVINCE_T@LINK_DB1 D WHERE A.OFFER_CODE IN (''*********'', ''*********'')' ||
           'AND A.PRODUCT_CODE != ''110163'' AND A.EC_CODE = B.CUSTOMERNUMBER AND A.PRODUCTID = C.PRODUCTID' ||
           'AND A.PROV_CODE = D.PROV_CODE MINUS SELECT A.EC_CODE 客户编码, B.CUSTOMERNAME  客户名称,' ||
           'D.PROV_NAME 省, A.PROD_ORDER_ID 商品订购ID,A.ORDER_ID 产品订购ID,C.PRODUCTNAME 产品名称  FROM '||
           'BBOSS.SERV_BIZ_CODE@LINK_DB1 A, ESOP_CRM.CUSTOMER@LINK_BL2   B, ESOP_CRM.PRODUCT@LINK_BL2 C,' ||
           'BBOSS.PROVINCE_T@LINK_DB1 D  WHERE A.PRODUCT_CODE IN (''*********'', ''*********'')' ||
           'AND A.SERVICE_CODE != ''110163''   AND '''||P_SETTLEMONTH||'''BETWEEN TO_CHAR(A.EFFECTIVE_DATE, ''YYYYMM'') AND' ||
           'TO_CHAR(A.EXPIRY_DATE, ''YYYYMM'') AND A.EC_CODE NOT IN (SELECT CUSTOMERNUMBER FROM ESOP_CRM.BLACK_CUSTOMER@LINK_BL2)' ||
           'AND A.EC_CODE = B.CUSTOMERNUMBER   AND A.ORDER_ID = C.PRODUCTID   AND B.COMPANYID = D.PROV_CODE';
  SELECT @P_SQL;
      PREPARE STMT FROM @P_SQL;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;
      COMMIT;


  nReturn := 0;
  szSysErr := 'OK';

  commit;

  SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;

  END;
END ;;
DELIMITER ;