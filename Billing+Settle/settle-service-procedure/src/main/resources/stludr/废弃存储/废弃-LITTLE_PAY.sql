/**
 *存储过程实现功能：报表中间层 -一点支付各省公司上传情况计算  都是计费的表  传蒙确认是不用了~
**/
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "LITTLE_PAY"(P_SETTLEMONTH IN VARCHAR2,
                                        szSysErr OUT VARCHAR2(1000),
                                        nReturn OUT NUMBER(4)  )
AS
  P_SQL VARCHAR2(8000);
  v_proc_name         VARCHAR2(30) := 'LITTLE_PAY';
BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
        ROLLBACK;

        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;
    BEGIN

  /*一点支付
        各省公司上传情况
  */

  -- set @P_SQL := 'delete from boss_billing.ydzf_prov_detail yd where yd.acct_month =''' || P_SETTLEMONTH || '''';
  -- SELECT @P_SQL;
  --     PREPARE STMT FROM @P_SQL;
  --     EXECUTE STMT;
  --     DEALLOCATE PREPARE STMT;
  --     COMMIT;


  P_SQL := '';
  set @P_SQL := ' INSERT INTO boss_billing.YDZF_PROV_DETAIL                                                                                                                                     ' ||
           ' SELECT DISTINCT A.MEMBER_NUMBER AS MEMBER_NUMBER, B.PROV_NAME AS PROV_NAME, B.PROV_CODE AS PROV_CODE, ''' ||
           P_SETTLEMONTH || ''' AS ACCT_MONTH, F.FIRST_NAME               ' ||
           '   , F.EC_CODE, F.PROD_ORDER_ID, F.ORDER_ID, F.PRODUCT_CODE, A.EFFECTIVE_DATE                                                                                            ' ||
           '   , A.EXPIRY_DATE                                                                                                                                                       ' ||
           ' FROM boss_billing.MEMBER_YDZF A, boss_billing.PROVINCE_T B, boss_billing.IMSI_LD_CD C, boss_billing.DOM_LD_AREA_CD_PROV D, boss_billing.ORDER_YDZF F    ' ||
           ' WHERE F.ORDER_ID = A.ORDER_ID                                                                                                                                           ' ||
           '   AND SUBSTR(A.MEMBER_NUMBER, 0, 7) = C.MSISDN_AREA_ID                                                                                                                  ' ||
           '   AND C.LD_AREA_CD = D.LD_AREA_CD                                                                                                                                       ' ||
           '   AND D.PROV_CD = B.PROV_CODE                                                                                                                                           ' ||
           '   AND TO_CHAR(ADD_MONTHS(SYSDATE, -1), ''yyyymm'') BETWEEN TO_CHAR(A.EFFECTIVE_DATE, ''yyyymm'') AND TO_CHAR(A.EXPIRY_DATE, ''yyyymm'')                                 ' ||
           '   AND A.MEMBER_NUMBER IN (SELECT DISTINCT A.MEMBER_NUMBER                                                                                                               ' ||
           '     FROM boss_billing.MEMBER_YDZF A, boss_billing.PROVINCE_T B, boss_billing.IMSI_LD_CD C, boss_billing.DOM_LD_AREA_CD_PROV D, boss_billing.ORDER_YDZF F' ||
           '     WHERE F.ORDER_ID = A.ORDER_ID                                                                                                                                       ' ||
           '       AND SUBSTR(A.MEMBER_NUMBER, 0, 7) = C.MSISDN_AREA_ID                                                                                                              ' ||
           '       AND C.LD_AREA_CD = D.LD_AREA_CD                                                                                                                                   ' ||
           '       AND D.PROV_CD = B.PROV_CODE                                                                                                                                       ' ||
           '       AND TO_CHAR(ADD_MONTHS(SYSDATE, -1), ''yyyymm'') BETWEEN TO_CHAR(A.EFFECTIVE_DATE, ''yyyymm'') AND TO_CHAR(A.EXPIRY_DATE, ''yyyymm'')                             ' ||
           '     MINUS                                                                                                                                                               ' ||
           '     SELECT T.MSISDN                                                                                                                                                     ' ||
           '     FROM boss_billing.UR_ACCTLIST_' || P_SETTLEMONTH || '_T T,                                                                                                          ' ||
           '     boss_billing.PROVINCE_T A, boss_billing.MEMBER_YDZF B, boss_billing.ORDER_YDZF F                                                                     ' ||
           '     WHERE T.OFFER_CODE IN (''010190001'', ''010190002'')                                                                                                                ' ||
           '       AND A.PROV_CODE = T.PROV_CODE                                                                                                                                     ' ||
           '       AND T.PAYTAG = 0                                                                                                                                                  ' ||
           '       AND T.PRODUCTID = B.ORDER_ID                                                                                                                                      ' ||
           '       AND T.MSISDN = B.MEMBER_NUMBER                                                                                                                                    ' ||
           '       AND T.CUSTOMERNUMBER = F.EC_CODE                                                                                                                                  ' ||
           '       AND TO_CHAR(ADD_MONTHS(SYSDATE, -1), ''yyyymm'') BETWEEN TO_CHAR(B.EFFECTIVE_DATE, ''yyyymm'') AND TO_CHAR(B.EXPIRY_DATE, ''yyyymm'')                             ' ||
           '       AND T.POCHARGECODE = ''15'')                                                                                                                                      ' ||
           ' ORDER BY B.PROV_NAME, B.PROV_CODE  ';
      SELECT @P_SQL;
          PREPARE STMT FROM @P_SQL;
          EXECUTE STMT;
          DEALLOCATE PREPARE STMT;
          COMMIT;

      drop table boss_billing.tmp_hyx_ur_acctlist_15sum;

      COMMIT;
      P_SQL := '';
      set @P_SQL := 'CREATE TABLE boss_billing.tmp_hyx_ur_acctlist_15sum  AS' ||
           '  SELECT A.EC_CODE, A.EC_CODE_PROV, A.PROV_CODE, A.PRODUCTOFFERINGID, A.TERM' ||
           '    , A.PRODUCTID, A.MSISDN, A.POCHARGECODE, SUM(A.FEEVAL) AS FEEVAL' ||
           '  FROM boss_billing.UR_ACCTLIST_' || P_SETTLEMONTH || '_T A' ||
           '  WHERE A.OFFER_CODE IN (''010190001'', ''010190002'')' ||
           '  GROUP BY A.EC_CODE, A.EC_CODE_PROV, A.PROV_CODE, A.PRODUCTOFFERINGID,' ||
           '             A.TERM, A.PRODUCTID, A.MSISDN, A.POCHARGECODE';
      SELECT @P_SQL;
          PREPARE STMT FROM @P_SQL;
          EXECUTE STMT;
          DEALLOCATE PREPARE STMT;
          COMMIT;

    /* 全费项情况

    */
    drop table boss_billing.tmp_zy_ydzf;
    COMMIT;

  P_SQL := '';
  set @P_SQL := 'create table boss_billing.tmp_zy_ydzf as                                         ' ||
           '  select b.customer_name,a.productid,a.msisdn,c.prov_name,                  ' ||
           '  max(DECODE(a.pochargecode, ''15'', a.feeval / 1000, 0)) feeval,             ' ||
           '  max(DECODE(a.pochargecode, ''YDZF01'', a.feeval / 1000, 0)) yd1,          ' ||
           '  max(DECODE(a.pochargecode, ''YDZF02'', a.feeval / 1000, 0)) yd2,          ' ||
           '  max(DECODE(a.pochargecode, ''YDZF03'', a.feeval / 1000, 0)) yd3,          ' ||
           '  max(DECODE(a.pochargecode, ''YDZF04'', a.feeval / 1000, 0)) yd4,          ' ||
           '  max(DECODE(a.pochargecode, ''YDZF05'', a.feeval / 1000, 0)) yd5,          ' ||
           '  max(DECODE(a.pochargecode, ''YDZF06'', a.feeval / 1000, 0)) yd6,          ' ||
           '  max(DECODE(a.pochargecode, ''YDZF07'', a.feeval / 1000, 0)) yd7,          ' ||
           '  -max(DECODE(a.pochargecode, ''YDZF08'', a.feeval / 1000, 0)) yd8,         ' ||
           '  f.field_value/1000 limit_feeval                                           ' ||
           '  from boss_billing.tmp_hyx_ur_acctlist_15sum a,boss_billing.customer b,    ' ||
           ' boss_billing.province_t c,                                              ' ||
           '  boss_billing.ORDER_YDZF d,boss_billing.member_ydzf f          ' ||
           '  where a.ec_code = d.ec_code and a.ec_code = b.customer_number and         ' ||
           ' a.prov_code = c.prov_code                                                 ' ||
           '  and f.member_number=a.msisdn and f.order_id=a.productid and a.term        ' ||
           ' =to_char(add_months(sysdate,-1),''yyyymm'')                               ' ||
           '  and to_char(add_months(sysdate,-1),''yyyymm'') between                    ' ||
           ' to_char(f.effective_date,''yyyymm'')                                      ' ||
           ' and to_char(f.expiry_date,''yyyymm'')                                     ' ||
           '  group by b.customer_name,a.productid, a.msisdn, c.prov_name,f.field_value ' ||
           ' order by c.prov_name, a.msisdn desc';
      SELECT @P_SQL;
          PREPARE STMT FROM @P_SQL;
          EXECUTE STMT;
          DEALLOCATE PREPARE STMT;
          COMMIT;

/* 有问题号码

*/
  -- P_SQL := 'delete from boss_billing.ydzf_err_detail a where a.acct_month =''' || P_SETTLEMONTH || '''';
  --     SELECT @P_SQL;
  --         PREPARE STMT FROM @P_SQL;
  --         EXECUTE STMT;
  --         DEALLOCATE PREPARE STMT;
  --         COMMIT;

  P_SQL := '';
  set @P_SQL := 'INSERT INTO boss_billing.YDZF_ERR_DETAIL ' ||
           '       SELECT ''' || P_SETTLEMONTH ||''' AS ACCT_MONTH, A.CUSTOMER_NAME, A.PRODUCTID, A.MSISDN, A.PROV_NAME      ' ||
           '       , A.YD1 AS 套餐及固定费, A.YD2 AS 语音通信费, A.YD3 AS 上网费, A.YD4 AS 短信彩信费, A.YD5 AS 增值业务费 ' ||
           '       , A.YD6 AS 代收业务费, A.YD7 AS 其他费用, A.YD8 AS 优惠及减免, A.YD1 + A.YD2 + A.YD3 + A.YD4 + A.YD5 +  ' ||
           '       A.YD6 + A.YD7 + A.YD8 AS "八费项和", A.LIMIT_FEEVAL AS 支付限额                                        ' ||
           '       , A.FEEVAL AS 本期代付额, A.YD1 + A.YD2 + A.YD3 + A.YD4 + A.YD5 + A.YD6 + A.YD7 + A.YD8 - A.FEEVAL AS  ' ||
           '       "八费项和-本期代付额", A.LIMIT_FEEVAL - A.FEEVAL AS "支付限额-本期代付额"                              ' ||
           '       FROM boss_billing.TMP_ZY_YDZF A                                                                              ' ||
           '       WHERE (A.LIMIT_FEEVAL != 0                                                                             ' ||
           '       OR A.YD1 + A.YD2 + A.YD3 + A.YD4 + A.YD5 + A.YD6 + A.YD7 + A.YD8 - A.FEEVAL != 0)                      ' ||
           '       AND (A.YD1 + A.YD2 + A.YD3 + A.YD4 + A.YD5 + A.YD6 + A.YD7 + A.YD8 - A.FEEVAL != 0                     ' ||
           '       OR A.LIMIT_FEEVAL - A.FEEVAL < 0                                                                       ' ||
           '       OR A.LIMIT_FEEVAL = 0)                                                                                 ' ||
           '       AND (A.LIMIT_FEEVAL = 0                                                                                ' ||
           '       OR A.LIMIT_FEEVAL - A.FEEVAL != 0                                                                      ' ||
           '       OR A.YD1 + A.YD2 + A.YD3 + A.YD4 + A.YD5 + A.YD6 + A.YD7 + A.YD8 - A.FEEVAL < 0)';
  SELECT @P_SQL;
      PREPARE STMT FROM @P_SQL;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;
      COMMIT;

  nReturn := 0;
  szSysErr := 'OK';

  commit;

  SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;

  END;
END ;;
DELIMITER ;
