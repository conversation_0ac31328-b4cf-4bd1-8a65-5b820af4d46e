DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "SYNC_INTERFACE_BL_BAK"(
  ACCT_MONTH IN VARCHAR2,
  V_SYNCSETT_CNT IN NUMBER(6),
  PROC_OUT OUT VARCHAR2,
  PROC_ERRMSG OUT VARCHAR2)
AS
  v_SQL     VARCHAR2(15000);
  ERRCODE   VARCHAR2(32);
  ERRMSG    VARCHAR2(2048);
  nReturn    NUMBER(6);
  szSysErr   VARCHAR2(2048);
  v_proc_name VARCHAR2(30) := 'SYNC_INTERFACE_BL_BAK';


BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
		PROC_OUT := 'N';
        PROC_ERRMSG := 'ERRMSG:' || SUBSTR(SQLERRM, 1, 1024) || ')'||SQLCODE||'create SYNC_INTERFACE_BL_' || ACCT_MONTH || ' faild' ;
        ROLLBACK;

        --日志写表
        select 'exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr  ;
        return;
    END;

  BEGIN
	select count(*) into V_SYNCSETT_CNT from INFORMATION_SCHEMA.TABLES where TABLE_SCHEMA = 'STLUDR' and TABLE_NAME = 'SYNC_INTERFACE_BL_' || ACCT_MONTH || '_1';
	if V_SYNCSETT_CNT > 0 then
        SET @v_SQL := 'drop table STLUDR.SYNC_INTERFACE_BL_' || ACCT_MONTH || '_1';
        SELECT 'SQL1: ' || @v_SQL;
		PREPARE STMT FROM @v_SQL;
		EXECUTE STMT;
		DEALLOCATE PREPARE STMT;
		commit;
   end if;
       SET @v_SQL := 'create table STLUDR.SYNC_INTERFACE_BL_' || ACCT_MONTH || '_1 as select * from STLUDR.SYNC_INTERFACE_BL_' || ACCT_MONTH || '';
		SELECT 'SQL2: ' || @v_SQL;
		PREPARE STMT FROM @v_SQL;
		EXECUTE STMT;
		DEALLOCATE PREPARE STMT;
		commit;

		PROC_OUT := 'Y';
		PROC_ERRMSG := 'The table SYNC_INTERFACE_BL_' || ACCT_MONTH || '_1 have been created successful!';

        SELECT 'PROC_OUT!!!' || PROC_OUT;
        SELECT'PROC_ERRMSG!!!' || PROC_ERRMSG;


        szSysErr:='Ok';
        nReturn := 0;

        SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;
    END;
END ;;
DELIMITER ;