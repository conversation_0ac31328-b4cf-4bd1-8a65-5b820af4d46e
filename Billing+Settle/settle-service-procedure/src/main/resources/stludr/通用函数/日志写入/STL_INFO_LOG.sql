/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：通用函数-日志-存储过程日志记录
**/
DROP PROCEDURE IF EXISTS stludr.STL_INFO_LOG;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "STL_INFO_LOG"(
    P_MONTH        IN VARCHAR2,
    P_RUNSITE      IN VARCHAR2,
    P_RPT_FILENAME IN VARCHAR2,
    P_RPT_SQL      IN VARCHAR2
)
AS
  --错误日志
  --P_MONTH          结算月份
  --P_RUNSITE        位置
  --P_RPT_FILENAME   过程名
  --P_RPT_SQL        执行SQL
  P_TIME DATETIME;

BEGIN

  P_TIME := SYSDATE;
  INSERT INTO RVL_INFO_LOG
    (SETTLEMONTH, INFO_TIME, RPT_SITE, RPT_FILENAME, RPT_SQL)
  VALUES
    (P_MONTH, P_TIME, P_RUNSITE, P_RPT_FILENAME, P_RPT_SQL);

END ;;
DELIMITER ;