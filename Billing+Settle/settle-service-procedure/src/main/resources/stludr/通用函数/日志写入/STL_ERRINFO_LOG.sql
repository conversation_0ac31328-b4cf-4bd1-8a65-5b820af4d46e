/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：通用函数-日志-存储过程错误日志记录
**/
DROP PROCEDURE IF EXISTS stludr.STL_ERROR_LOG;
DELIMITER;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "STL_ERROR_LOG"(
   P_MONTH        IN VARCHAR2,
   P_ERRCODE      IN VARCHAR2,
   P_ERRMSG       IN VARCHAR2,
   P_RUNSITE      IN VARCHAR2,
   P_RPT_FILENAME IN VARCHAR2,
   P_RPT_SQL      IN VARCHAR2
)
AS
  P_TIME DATETIME;
BEGIN
INSERT INTO RVL_ERROR_LOG
(SETTLEMONTH,RPT_SQLCODE,RPT_SQLERRM,ERROR_TIME,RPT_SITE,RPT_FILENAME,RPT_SQL)
VALUES (P_MONTH,P_ERRCODE,P_ERRMSG,SYSDATE,P_RUNSITE,P_RPT_FILENAME,P_RPT_SQL);
END ;;
DELIMITER ;
