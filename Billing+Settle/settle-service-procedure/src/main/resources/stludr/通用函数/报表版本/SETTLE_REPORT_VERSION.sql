/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：通用函数-报表版本-获取最新版本号
**/
DROP PROCEDURE IF EXISTS stludr.SETTLE_REPORT_VERSION;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "SETTLE_REPORT_VERSION"(F_MONTH      IN VARCHAR2,
                                                  F_TABLE      IN VARCHAR2,
                                                  F_RES        OUT VARCHAR2,
                                                  F_RESNUM     OUT VARCHAR2,
                                                  FLAG_VERSION IN CHAR,
                                                  szSysErr OUT VARCHAR2(1000),
                                                  nReturn OUT NUMBER(4)   )
AS
  -- 获取 F_TABLE 表 新的版本号
  F_RUNSITE VARCHAR2(3);
  F_SQL     VARCHAR2(1024);
  ERRCODE   VARCHAR2(32);
  ERRMSG    VARCHAR2(2048);
  RPT_TABLE VARCHAR2(64);
  RPT_RUNSITE VARCHAR2(64);
  RPT_SQL VARCHAR2(4096);
  v_proc_name VARCHAR2(30) := 'SETTLE_REPORT_VERSION';

BEGIN

  DECLARE EXIT HANDLER FOR SQLEXCEPTION
  BEGIN
      GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
      szSysErr := substr(@p2, 1, 1000);
      nReturn  := -1;
      ROLLBACK;

      select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
  END;
BEGIN
  -- 获取数据库中对应表的最大版本，如果无版本返回 1
  F_RUNSITE := '1';
  SELECT NVL(MAX(T.VERSIONNUM), 1) INTO F_RESNUM FROM RVL_CONF_VERSION T WHERE T.TABLE_NAME = F_TABLE AND T.SETTLEMONTH = F_MONTH;

  SELECT 'F_RESNUM1 VALUE ' || F_RESNUM;

  -- 判断是否保留上一版本
  F_RUNSITE := '2';
  IF (FLAG_VERSION IS NULL OR UPPER(FLAG_VERSION) <> 'Y') THEN
    F_RES     := CONCAT('V',LPAD(F_RESNUM,2,'0'));
    SELECT 'F_RES1 VALUE ' || F_RES;
    set @F_SQL := 'DELETE FROM ' || F_TABLE || ' WHERE VERSION= ''' || F_RES || ''' AND SETTLEMONTH=''' || F_MONTH || '''';
    SELECT @F_SQL;
      PREPARE STMT FROM @F_SQL;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;
    DELETE FROM RVL_CONF_VERSION WHERE VERSIONNUM = F_RESNUM AND TABLE_NAME = F_TABLE AND SETTLEMONTH = F_MONTH;
  END IF;

  -- 获取新的版本号
  F_RUNSITE := '3';
  SELECT NVL(MAX(T.VERSIONNUM) + 1, 1) INTO F_RESNUM FROM RVL_CONF_VERSION T WHERE T.TABLE_NAME = F_TABLE AND T.SETTLEMONTH = F_MONTH;
  SELECT 'F_RESNUM2 VALUE ' || F_RESNUM;

  F_RES     := CONCAT('V',LPAD(F_RESNUM,2,'0'));

  SELECT 'F_RES2 VALUE ' || F_RES;
  F_RUNSITE := '4';
  INSERT INTO RVL_CONF_VERSION (SETTLEMONTH, TABLE_NAME, VERSION, VERSIONNUM) VALUES (F_MONTH, F_TABLE, F_RES, F_RESNUM);

	COMMIT;

	szSysErr := 'OK';
	nReturn := 0;

  SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;

  END;
END ;;
DELIMITER ;