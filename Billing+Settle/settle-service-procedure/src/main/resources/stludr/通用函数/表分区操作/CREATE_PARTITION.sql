/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：通用函数-表分区操作-创建表分区
**/
DROP PROCEDURE IF EXISTS stludr.CREATE_PARTITION;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "CREATE_PARTITION"(PROC_MONTH IN VARCHAR2,
                                             PROC_OUT   OUT VARCHAR2,
                                             szSysErr   OUT VARCHAR2(1000),
                                             nReturn    OUT NUMBER(4))
AS
  PROC_RES  VARCHAR2(64); -- 创建分区调用的存储返回信息
  PROC_CRES VARCHAR2(10000); -- 创建出问题返回信息
  v_proc_name  VARCHAR2(30) := 'CREATE_PARTITION';

  PROC_TABLE_NAME      RVL_TABLE_ALL.TABLE_NAME%TYPE;
  PROC_TABLESPACE_NAME RVL_TABLE_ALL.TABLESPACE_NAME%TYPE;
  /* CURSOR TABLE_LIST IS
  SELECT T.TABLE_NAME, T.TABLESPACE_NAME
    FROM RVL_TABLE_ALL T
   WHERE T.VALID = 'Y';*/

  -- 动态游标的使用例子
  PROC_SQL VARCHAR2(2000);
  TYPE I_CURSOR_TYPE IS REF CURSOR; -- 动态游标
  TABLE_LIST I_CURSOR_TYPE;
BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
        ROLLBACK;

        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;
BEGIN
  PROC_CRES := '';
  PROC_SQL  := 'SELECT DISTINCT T.TABLE_NAME, T.TABLESPACE_NAME FROM RVL_TABLE_ALL T WHERE T.VALID = ''Y'' ';
  OPEN TABLE_LIST FOR PROC_SQL;
  LOOP
    FETCH TABLE_LIST
      INTO PROC_TABLE_NAME, PROC_TABLESPACE_NAME;
    EXIT WHEN TABLE_LIST%NOTFOUND;
    call MODIFY_PARTITION(PROC_MONTH,
                     PROC_TABLE_NAME,
                     PROC_TABLESPACE_NAME,
                     PROC_RES,
                     szSysErr,
                     nReturn);
    IF PROC_RES <> 'Y' THEN
      PROC_CRES := PROC_CRES || PROC_RES || PROC_TABLE_NAME || '表创建账期分区失败！';
    END IF;
  END LOOP;
  CLOSE TABLE_LIST;

  PROC_SQL  := 'SELECT DISTINCT T.TABLE_NAME, T.TABLESPACE_NAME FROM RVL_STL_MONTH T WHERE T.VALID = ''Y''
            and t.table_name <> ''STL_SERVICE'' ';
  OPEN TABLE_LIST FOR PROC_SQL;
  LOOP
    FETCH TABLE_LIST
      INTO PROC_TABLE_NAME, PROC_TABLESPACE_NAME;
    EXIT WHEN TABLE_LIST%NOTFOUND;
    call MODIFY_PARTITION(PROC_MONTH,
                     PROC_TABLE_NAME,
                     PROC_TABLESPACE_NAME,
                     PROC_RES,
                     szSysErr,
                     nReturn);
    IF PROC_RES <> 'Y' THEN
      PROC_CRES := PROC_CRES || PROC_RES || PROC_TABLE_NAME || '表创建账期分区失败！';
    END IF;
  END LOOP;
  CLOSE TABLE_LIST;

  IF PROC_CRES IS NULL THEN
    PROC_OUT := 'Y';
  ELSE
    PROC_OUT := PROC_CRES;
  END IF;
  END;
END ;;
DELIMITER ;