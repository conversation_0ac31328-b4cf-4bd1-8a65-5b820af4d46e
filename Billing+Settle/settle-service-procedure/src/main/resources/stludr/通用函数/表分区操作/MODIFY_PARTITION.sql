/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：通用函数-表分区操作-修改分区
**/
DROP PROCEDURE IF EXISTS stludr.MODIFY_PARTITION;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "MODIFY_PARTITION"(F_MONTH           IN VARCHAR2,
                                             F_TABLE           IN VARCHAR2,
                                             F_TABLESPACE_NAME IN VARCHAR2,
                                             PROC_OUT          OUT VARCHAR2,
                                             szSysErr    OUT VARCHAR2(1000),
                                             nReturn     OUT NUMBER(4) )
AS
  -- 增加表的分区
  -- F_MONTH 分区值
  -- 分区表的表名
  -- F_TABLESPACE_NAME表空间
  F_SQL   VARCHAR2(1024);
  F_COUNT NUMBER;
  v_proc_name  VARCHAR2(30) := 'MODIFY_PARTITION';
BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
        ROLLBACK;

        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;
  BEGIN
  SELECT COUNT(1) FROM information_schema.partitions T WHERE TABLE_NAME = ''' || F_TABLE || ''' AND SUBSTR(T.PARTITION_NAME, -6) = ''' || F_MONTH || ''' INTO F_COUNT;
  IF F_COUNT = 0 THEN
    SET @F_SQL := 'ALTER TABLE ' || F_TABLE || ' ADD PARTITION ( partition SETTLEMONTH' || F_MONTH || ' VALUES IN (''' || F_MONTH || ''')) ';
    SELECT @F_SQL;
        PREPARE STMT FROM @F_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
  END IF;
  PROC_OUT := 'Y';

  nReturn := 0;
  szSysErr := 'OK';

  commit;

  SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;

  END;
END ;;
DELIMITER ;