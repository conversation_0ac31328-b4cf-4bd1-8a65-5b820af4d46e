/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：通用函数-获取付款单位编码-获取付款单位编码
**/
DROP FUNCTION IF EXISTS stludr.GET_ACCOUNTNUMBER;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" FUNCTION "GET_ACCOUNTNUMBER"(F_ACCOUNT_ID NUMBER) RETURN varchar(16383) CHARSET utf8mb4 COLLATE utf8mb4_0900_bin
AS
  --F_ACCOUNT_ID 付费ＩＤ
  --通过付款ID 获取付款单位编码
  F_ACCOUNT_NUMBER VARCHAR2(64);
BEGIN
  SELECT DISTINCT NVL(T.ACCOUNT_NUMBER, '')
    INTO F_ACCOUNT_NUMBER
    FROM STL_ACCOUNT T
   WHERE T.ACCOUNT_ID = F_ACCOUNT_ID;
  RETURN F_ACCOUNT_NUMBER;
EXCEPTION
  WHEN OTHERS THEN
    BEGIN
      RETURN '';
    END;
END ;;
DELIMITER ;