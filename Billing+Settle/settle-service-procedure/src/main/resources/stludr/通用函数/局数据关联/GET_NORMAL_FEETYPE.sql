/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：通用函数-局数据关联-获取费项对应的非调账费项
**/
DROP FUNCTION IF EXISTS stludr.GET_NORMAL_FEETYPE;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" FUNCTION "GET_NORMAL_FEETYPE"(F_FEETYPE varchar(100)) RETURN varchar(10) CHARSET utf8mb4 COLLATE utf8mb4_0900_bin
as
  --F_FEETYEP BBOSS出账费项编码
  --返回内容  普通费项编码
  NORMAL_FEETYPE VARCHAR(20); --对应到的非调账费项编码
  F_RES        VARCHAR(512); --返回值
BEGIN
    IF to_number(F_FEETYPE) between 1 and 50 THEN
        --01~50
        NORMAL_FEETYPE := F_FEETYPE;
        F_RES := NORMAL_FEETYPE;
    ELSIF to_number(F_FEETYPE) between 51 and 99 THEN
        --51~99
        NORMAL_FEETYPE := lpad(to_char(round(to_number(F_FEETYPE) - 50)), 2, '0');
        F_RES := NORMAL_FEETYPE;
    ELSIF to_number(F_FEETYPE) between 1001 and 4999 THEN
        --1001~4999
        NORMAL_FEETYPE := F_FEETYPE;
        F_RES := NORMAL_FEETYPE;
    ELSIF to_number(F_FEETYPE) between 5001 and 8999 THEN
        --5001~8999
        NORMAL_FEETYPE := to_char(round(to_number(F_FEETYPE) - 4000));
        F_RES := NORMAL_FEETYPE;
    END IF;
    RETURN F_RES;

END ;;
DELIMITER ;