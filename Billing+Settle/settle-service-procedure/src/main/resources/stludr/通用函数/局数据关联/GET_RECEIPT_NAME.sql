/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：通用函数-局数据关联-获取发票名称
**/
DROP FUNCTION IF EXISTS stludr.GET_RECEIPT_NAME;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" FUNCTION "GET_RECEIPT_NAME"(F_ACCOUNT_ID NUMBER) RETURN varchar(16383) CHARSET utf8mb4 COLLATE utf8mb4_0900_bin
AS
  F_RECEIPT_NAME VARCHAR2(256);
BEGIN
  SELECT DISTINCT NVL(T.RECEIPT_NAME, '')
    INTO F_RECEIPT_NAME
    FROM STL_AM_RECEIPT T
   WHERE T.ACCOUNT_ID = F_ACCOUNT_ID;
  RETURN F_RECEIPT_NAME;
EXCEPTION
  WHEN OTHERS THEN
    BEGIN
      RETURN '';
    END;
END ;;
DELIMITER ;