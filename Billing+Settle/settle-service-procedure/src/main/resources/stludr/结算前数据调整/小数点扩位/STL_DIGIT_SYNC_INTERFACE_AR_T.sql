/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能： 结算前数据调整-小数点扩位-计费与结算实收接口表金额小数点扩位
**/
DROP PROCEDURE IF EXISTS stludr.`STL_DIGIT_SYNC_INTERFACE_AR_T`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "STL_DIGIT_SYNC_INTERFACE_AR_T"(RPT_SETTLEMONTH IN VARCHAR2,
                                                           BAT IN VARCHAR2,
                                                           PROC_OUT OUT VARCHAR2,
                                                           szSysErr OUT VARCHAR2(1000),
                                                           nReturn OUT NUMBER(4) )
AS
    P_ERRCODE   VARCHAR2(32);
    P_ERRMSG    VARCHAR2(2048);
    VER_TABLE VARCHAR2(64);
    RPT_TABLE VARCHAR2(64);
    RPT_RUNSITE VARCHAR2(64);
    RPT_SQL VARCHAR2(4096);
    v_proc_name VARCHAR2(30) := 'STL_DIGIT_SYNC_INTERFACE_AR_T';
BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
GET DIAGNOSTICS CONDITION 1
    P_ERRCODE = RETURNED_SQLSTATE, P_ERRMSG = MESSAGE_TEXT;
szSysErr := substr(P_ERRMSG, 1, 1000);
        nReturn  := -1;
ROLLBACK;

select ('exception: ' || nReturn || '|'  || P_ERRCODE || '|' || szSysErr ) AS error_msg ;
call STLUDR.STL_ERROR_LOG(RPT_SETTLEMONTH,P_ERRCODE,szSysErr,'',v_proc_name,@RPT_SQL);
END;
BEGIN
    VER_TABLE   := 'sync_interface_ar_'|| RPT_SETTLEMONTH;
    RPT_RUNSITE := '6';
CASE BAT
		WHEN '1' THEN
        	set @RPT_SQL  :='UPDATE sync_interface_ar_' || RPT_SETTLEMONTH || ' T '||
                          'join STLUDR.STL_CONFIG_DIGIT T1 on T1.POSPEC_NUMBER = T.POSPECNUMBER '||
                          'AND T1.PRODUCT_NUMBER = T.SOSPECNUMBER '||
                          'set T.NOTAXFEE = T.NOTAXFEE * NVL(T1.PRODUCT_DIGIT,1) '||
                          'WHERE T.ORGMONTH = '''|| RPT_SETTLEMONTH ||''' AND T.REMARK =''2'' ';
SELECT @RPT_SQL;
PREPARE STMT FROM @RPT_SQL;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
WHEN '2' THEN
        	set @RPT_SQL  :='UPDATE sync_interface_ar_' || RPT_SETTLEMONTH || ' T '||
                          'join STLUDR.STL_CONFIG_DIGIT T1 on T1.POSPEC_NUMBER = T.POSPECNUMBER '||
                          'AND T1.PRODUCT_NUMBER = T.SOSPECNUMBER '||
                          'set T.NOTAXFEE = T.NOTAXFEE * NVL(T1.PRODUCT_DIGIT,1) '||
                          'WHERE T.ORGMONTH = '''|| RPT_SETTLEMONTH ||''' AND T.REMARK IN (''5'',''7'') ';
SELECT @RPT_SQL;
PREPARE STMT FROM @RPT_SQL;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

ELSE RPT_SQL := NULL;
END CASE;
    PROC_OUT := 'Y';
COMMIT;

szSysErr := 'OK';
    nReturn := 0;

SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;
call STL_INFO_LOG(RPT_SETTLEMONTH, RPT_RUNSITE, v_proc_name, szSysErr);
END;
END ;;
DELIMITER ;