/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能： 结算后数据稽核-结算结果稽核-检查调账标记
**/
DROP PROCEDURE IF EXISTS stludr.`CHECK_ADJUST_FLAG`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "CHECK_ADJUST_FLAG"(V_ACCT_MONTH IN VARCHAR2,
                                                  szSysErr OUT VARCHAR2(1000),
                                                  PROC_OUT OUT VARCHAR2(1000),
                                                  PROC_ERRMSG OUT VARCHAR2(1000),
                                                  nReturn OUT NUMBER(4)   )
AS
                                RPT_SQL  VARCHAR2(8000);
                                ERRCODE   VARCHAR2(32);
                                ERRMSG    VARCHAR2(2048);
                                err_flag VARCHAR2(20);
                                recv_cnt NUMBER(4);
                                paid_cnt NUMBER(4);
                                eboss_cnt NUMBER(4);
                                cpr_cnt NUMBER(4);
                                cpp_cnt NUMBER(4);
                                v_proc_name VARCHAR2(30) := 'CHECK_ADJUST_FLAG';
BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
        ROLLBACK;

        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;

  BEGIN

  ---查看不同表中的异常数据
  set @RPT_SQL := 'insert into stludr.CHECK_ADJUST_FLAG(recv_cnt) select count(*) from ( select charge_code from  stludr.ur_recv_'||V_ACCT_MONTH||'_t a'||
  '  where a.charge_code in (select  charge_item_ref  from stludr.stl_charge_item_def a where a.acct_month = '''||V_ACCT_MONTH||'''and '||
  'charge_item like ''Adjust%'' ) and a.feetype != ''1''  union all select charge_code from  stludr.ur_recv_'||V_ACCT_MONTH||'_t a'||
  ' where a.feetype is null )';
  SELECT @RPT_SQL;
    PREPARE STMT FROM @RPT_SQL;
    EXECUTE STMT;
    DEALLOCATE PREPARE STMT;

  set @RPT_SQL := 'insert into stludr.CHECK_ADJUST_FLAG(paid_cnt) select count(*) from  ( select charge_code from stludr.ur_paid_'||V_ACCT_MONTH||'_t a'||
  '  where a.charge_code in (select  charge_item_ref  from stludr.stl_charge_item_def a where a.acct_month = '''||V_ACCT_MONTH||'''and '||
  'charge_item like ''Adjust%'' ) and a.feetype != ''1'' union all  select charge_code from  stludr.ur_paid_'||V_ACCT_MONTH||'_t a where a.feetype is null)';
    SELECT @RPT_SQL;
    PREPARE STMT FROM @RPT_SQL;
    EXECUTE STMT;
    DEALLOCATE PREPARE STMT;

  set @RPT_SQL := 'insert into stludr.CHECK_ADJUST_FLAG(eboss_cnt) select count(*) from ( select charge_code from   stludr.ur_eboss_'||V_ACCT_MONTH||'_t a'||
  '  where a.charge_code in (select  charge_item_ref  from stludr.stl_charge_item_def a where a.acct_month = '''||V_ACCT_MONTH||'''and '||
  'charge_item like ''Adjust%'' ) and a.feetype != ''1''  union all  select charge_code from  stludr.ur_eboss_'||V_ACCT_MONTH||'_t a where a.feetype is null)';
    SELECT @RPT_SQL;
    PREPARE STMT FROM @RPT_SQL;
    EXECUTE STMT;
    DEALLOCATE PREPARE STMT;

  set @RPT_SQL := 'insert into stludr.CHECK_ADJUST_FLAG(cpr_cnt) select count(*) from  ( select charge_code from   stludr.ur_cpr_'||V_ACCT_MONTH||'_t a'||
  '  where a.charge_code in (select  charge_item_ref  from stludr.stl_charge_item_def a where a.acct_month = '''||V_ACCT_MONTH||'''and '||
  'charge_item like ''Adjust%'' ) and a.feetype != ''1''  union all select charge_code from   stludr.ur_cpr_'||V_ACCT_MONTH||'_t a where a.feetype is null)';
    SELECT @RPT_SQL;
    PREPARE STMT FROM @RPT_SQL;
    EXECUTE STMT;
    DEALLOCATE PREPARE STMT;

  set @RPT_SQL := 'insert into stludr.CHECK_ADJUST_FLAG(cpp_cnt) select count(*) from (select charge_code from    stludr.ur_cpp_'||V_ACCT_MONTH||'_t a'||
  '  where a.charge_code in (select  charge_item_ref  from stludr.stl_charge_item_def a where a.acct_month = '''||V_ACCT_MONTH||'''and '||
  'charge_item like ''Adjust%'' ) and a.feetype != ''1''  union all select charge_code from    stludr.ur_cpp_'||V_ACCT_MONTH||'_t a where a.feetype is null)';
    SELECT @RPT_SQL;
    PREPARE STMT FROM @RPT_SQL;
    EXECUTE STMT;
    DEALLOCATE PREPARE STMT;

    -- recv_cnt为0时候没有异常数据
    SELECT T.recv_cnt into recv_cnt FROM stludr.CHECK_ADJUST_FLAG T where T.recv_cnt  is not null ;
    SELECT T.paid_cnt into paid_cnt FROM stludr.CHECK_ADJUST_FLAG T where T.paid_cnt  is not null ;
    SELECT T.eboss_cnt into eboss_cnt FROM stludr.CHECK_ADJUST_FLAG T where T.eboss_cnt  is not null ;
    SELECT T.cpr_cnt into cpr_cnt FROM stludr.CHECK_ADJUST_FLAG T where T.cpr_cnt  is not null ;
    SELECT T.cpp_cnt into cpp_cnt FROM stludr.CHECK_ADJUST_FLAG T where T.cpp_cnt  is not null ;

    SELECT 'recv_cnt=' || recv_cnt ||' paid_cnt=' || paid_cnt ||' eboss_cnt=' || eboss_cnt ||' cpr_cnt=' || cpr_cnt ||' cpp_cnt=' || cpp_cnt;


    -- 判断是否有异常数据
    if ( recv_cnt = 0 or recv_cnt = null) and  ( paid_cnt = 0 or  paid_cnt = null ) and ( eboss_cnt = 0 ) and ( cpr_cnt = 0 ) and ( cpp_cnt = 0 ) then
      PROC_OUT := 'Y';
      PROC_ERRMSG := '调账标记稽核通过，无异常数据';
    else
      PROC_OUT := 'N';
      PROC_ERRMSG := '有数据调账标记异常，'||'异常数量如下：recv表:'||recv_cnt||'条；   paid表：'||paid_cnt||'条；   eboss表：'||eboss_cnt||'条；   cpr表：'||cpr_cnt||'条；   cpp表：'||cpp_cnt||'条。   ';
    end if;


    COMMIT;

    -- 清空CHECK_ADJUST_FLAG
    set @RPT_SQL := 'delete from stludr.CHECK_ADJUST_FLAG';
    SELECT @RPT_SQL;
      PREPARE STMT FROM @RPT_SQL;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;

    SELECT 'procedure ' || v_proc_name || ' completed successfully. PROC_OUT=' || PROC_OUT ||' PROC_ERRMSG=' || PROC_ERRMSG;

    szSysErr := 'OK';
    nReturn := 0;

    SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;

  END;
END ;;
DELIMITER ;