/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能： 结算后数据稽核-结算结果稽核-错误结算结果筛选
**/
use stludr;
DROP PROCEDURE IF EXISTS stludr.`ERR_FILTER_UOM`;
DELIMITER ;;
CREATE OR REPLACE DEFINER="stludr"@"10.%" PROCEDURE stludr.`ERR_FILTER_UOM`(inMonth in varchar2,
                                        inBatch IN VARCHAR2, 
                                        flag_version IN VARCHAR2,
                                        reserve1 IN VARCHAR2,
                                        reserve2 IN VARCHAR2,
                                        proc_out OUT VARCHAR2,
                                        outSysError OUT VARCHAR2(1000),   
                                        outReturn OUT NUMBER(4),
                                        outBL OUT VARCHAR2,
                                        outAR OUT VARCHAR2)
AS
  
  
  
  iv_Create_Sql varchar2(1000);
  RPT_SQL     VARCHAR2(8000); --拼SQL的字符串变量
  iv_Table_cnt  number(2);
  iv_Insert_Sql varchar2(6100);
  iv_Update_Sql varchar2(1000);
  i number(1);
  iv_Str varchar2(20);
  v_proc_name VARCHAR2(30) := 'ERR_FILTER_UOM';


BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        outSysError := substr(@p2, 1, 1000);
        outReturn  := -1;
        outBL := -1;
        outAR := -1;
        ROLLBACK;
        
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg ;
    END;

  BEGIN
  
   
    
  
  --清空错误结果筛选表
  SET @RPT_SQL  :='truncate table err_resettle_' || inMonth;
      SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;
  
  end;

  begin
  --将具体字段和报错信息插入err_resettle_yyyymm表
    i := 1;
    while i <= 2 loop
      if i = 1 then iv_Str := 'bill';end if;
      if i = 2 then iv_Str := 'paid';end if;
   
   set  @iv_Insert_Sql := 'insert /*+ AUTOCOMMIT_DURING_DML() */ into err_resettle_' || inMonth || ' ' ||
              ' select null, trim(substring_index(t.r01_raw_udr,'','',1)) error_code,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',2),'','',-1)) error_line,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',3),'','',-1)) biz_type,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',4),'','',-1)) data_source,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',5),'','',-1)) Stream_ID,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',6),'','',-1)) Ec_code,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',7),'','',-1)) Ec_Prov_Code,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',8),'','',-1)) offer_code,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',9),'','',-1)) product_code,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',10),'','',-1)) offer_order_id,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',11),'','',-1))  product_order_id,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',12),'','',-1))  order_prov,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',13),'','',-1))  account_id,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',14),'','',-1))  mem_number,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',15),'','',-1))  mem_prov,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',16),'','',-1))  order_mode,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',17),'','',-1))  SIGN_ENTITY,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',18),'','',-1))  charge_item,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',19),'','',-1))  charge,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',20),'','',-1))  AMOUNT_NOTAX,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',21),'','',-1))  AMOUNT_TAX,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',22),'','',-1))  TAX_RATE,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',23),'','',-1))  org_month,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',24),'','',-1))  paid_month,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',25),'','',-1))  Ticket_id,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',26),'','',-1))  settle_month,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',27),'','',-1))  Out_Object,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',28),'','',-1))  In_Object,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',29),'','',-1))  Record_id,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',30),'','',-1))  Settle_NOTAX,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',31),'','',-1))  Settle_TAX,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',32),'','',-1))  File_Id,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',33),'','',-1))  rule_id,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',34),'','',-1))  Dest_Source,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',35),'','',-1))  phase,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',36),'','',-1))  start_time,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',37),'','',-1))  res1,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',38),'','',-1))  res2,
                       trim(substring_index(substring_index(t.r01_raw_udr,'','',39),'','',-1))  res3,
                       trim(substr(t.r01_raw_udr,length(substring_index(t.r01_raw_udr, '','', 39))+2)) attachment,     
                       t.a03_err_code err_code, 
                       t.e08_err_reason err_reason, 
                       case when trim(substring_index(substring_index(t.r01_raw_udr,'','',20),'','',-1))=0 then
                         ''0''
                         else 
                       ''1''
                       end as resettle_flag, 
                       case when trim(substring_index(substring_index(t.r01_raw_udr,'','',20),'','',-1))=0 then
                         ''出账金额为0''
                         else 
                       ''''
                       end as filter_reason '||
                       'from err_' || iv_Str || '_' || inMonth || ' t';
    SELECT @iv_Insert_Sql;
        PREPARE STMT FROM @iv_Insert_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
      i := i + 1;
    end loop;
    commit;
    end;
  

  begin
    --出账为0的 修改合并如insert中。
  --出账金额为0 
  
    
    
  
  --省行业网关云mas（010101017）实收不结
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''省行业网关云mas实收不结'' ' ||
                     'where resettle_flag = ''1'' ' || 
                     'and offer_code = ''010101017'' ' ||
                     'and biz_type = ''PAID''';
        SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;
    
  
  --千里眼（50008）一次性费用不结
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''千里眼一次性费用不结'' ' ||
                     'where resettle_flag = ''1'' ' ||
                     'and offer_code = ''50008'' ' ||
                     'and exists (select charge_item_ref from stlusers.stl_charge_item_onetime a ' ||
                     'where a.charge_item_ref = t.charge_item)';
    SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;
    
  
  --企业手机报（4040104）受理模式3实收不结
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''企业手机报受理模式3实收不结'' ' ||
                     'where resettle_flag = ''1'' ' ||
                     'and offer_code = ''4040104'' ' ||
                     'and biz_type = ''PAID'' ' ||
                     'and order_mode = ''3''';
    SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;
    
  
  --9003304946产品订购，定向流量统付（无成员）按成员结算
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''9003304946产品订购，定向流量统付（无成员）按成员结算'' ' ||
                     'where resettle_flag = ''1'' ' ||
                     'and product_order_id = ''9003304946''';
    SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;
  
  --双跨（0102001）受理模式1不结算
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''双跨受理模式1不结'' ' ||
                     'where resettle_flag = ''1'' ' ||
                     'and offer_code = ''0102001'' ' ||
                     'and order_mode = ''1''';
    SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;
    
    --18 31 32费项 产品级不结
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''双跨18 31 32费项产品级不结'' ' ||
                     'where resettle_flag = ''1'' ' ||
                     'and offer_code = ''0102001'' ' ||
                     'and product_order_id is not null ' ||
                     'and charge_item in (''18'', ''68'', ''31'', ''81'', ''32'', ''82'')';
    SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;
    
    --一次性费用及1021费项不结
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''双跨一次性费用及1021费项不结'' ' ||
                     'where resettle_flag = ''1'' ' ||
                     'and offer_code = ''0102001'' ' ||
                     'and (charge_item in (''1021'', ''5021'') ' ||
                     'or exists (select charge_item_ref from stlusers.stl_charge_item_onetime a ' ||
                     'where a.charge_item_ref = t.charge_item))';
    SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;
    
  
  --通信能力开放（50006）暂无结算
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''通信能力开放暂无结算'' ' ||
                     'where resettle_flag = ''1'' ' ||
                     'and offer_code = ''50006''';
    SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;
    
  
  --农信通（010102002） 受理模式3的不结算
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''农信通受理模式3不结'' ' ||
                     'where resettle_flag = ''1'' ' ||
                     'and offer_code = ''010102002'' ' ||
                     'and order_mode in (''3'')';
    SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;
    
    --应收受理模式5的不结算
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''农信通应收受理模式5不结'' ' ||
                     'where resettle_flag = ''1'' ' ||
                     'and offer_code = ''010102002'' ' ||
                     'and order_mode in (''5'') ' ||
                     'and biz_type = ''BILL''';
    SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;
    
  
  --集团短信MAS受理模式3的不结算（010101001）
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''集团短信MAS受理模式3的不结'' ' ||
                     'where resettle_flag = ''1'' ' ||
                     'and offer_code = ''010101001'' ' ||
                     'and order_mode = ''3''';
    SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;
    
  
  --企业互联网电视（4040109）不结
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''企业互联网电视不结'' ' ||
                     'where resettle_flag = ''1'' ' ||
                     'and offer_code = ''4040109''';
    SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;
    
  
  --数据专线（01011301、01011306）受理模式4不结
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''数据专线受理模式4不结'' ' ||
                     'where resettle_flag = ''1'' ' ||
                     'and offer_code in (''01011301'', ''01011306'') ' ||
                     'and order_mode = ''4''';
    SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;
    
  
  --车务通受理模式3的不结（010109002 本地）（010109001 全网）
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''车务通受理模式3的不结'' ' ||
                     'where resettle_flag = ''1'' ' ||
                     'and offer_code in (''010109001'', ''010109002'') ' ||
                     'and order_mode = ''3''';
    SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;
    
  
  --400（01114001 移动400）业务受理模式5的不结算
    set @iv_Update_Sql := 'update err_resettle_' || inMonth || ' t ' ||
                     'set resettle_flag = ''0'', filter_reason = ''400业务受理模式5的不结算'' ' ||
                     'where resettle_flag = ''1'' ' ||
                     'and offer_code = ''01114001'' ' ||
                     'and order_mode = ''5''';
    SELECT @iv_Update_Sql;
        PREPARE STMT FROM @iv_Update_Sql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

    commit;

    
    proc_out :='0';
    outSysError := 'OK';
    outReturn := 0;
    outBL := 0;
    outAR := 0;

    SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;

    END;

END ;;

DELIMITER ;