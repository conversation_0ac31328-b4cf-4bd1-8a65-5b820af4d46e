/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能： 结算后数据稽核-特定业务告警-云MAS异网费用大于出账费用的告警
 1. 将各订购云MAS异网费用的总金额和出账金额写入云MAS告警表
 2. 标记异网费用大于出账费用的数据
**/
use stludr;
DROP PROCEDURE IF EXISTS stludr.`P_PKG_POST_SETTLE_CMAS_WARNING`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE stludr."P_PKG_POST_SETTLE_CMAS_WARNING"(
    inMonth          IN   VARCHAR2,
    outSysError      OUT  VARCHAR2,
    outReturn        OUT  NUMBER
)
AS
    vSql VARCHAR2(10240);
    v_proc_name   VARCHAR2(30) := 'P_PKG_POST_SETTLE_CMAS_WARNING';
BEGIN
    outSysError := 'OK';
    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        outSysError := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg ;
    END;

    BEGIN
        ----云MAS异网费用大于出账费用的告警
        delete from stludr.wrn_cmas where orgmonth = inMonth;

        set @vSql := 'insert into stludr.wrn_cmas  ' ||
           'select null, a.orgmonth, a.customernumber, a.pospecnumber, a.sospecnumber, a.poid, a.soid, sum(a.amount) hd_fee, nvl(sum(b.jk_fee), 0) jk_fee, ' ||
            'case when sum(a.amount)>nvl(sum(b.jk_fee), 0)  then ''1'' else ''0'' end  wrn_status ' ||
            'from sync_bl_settle_' || inMonth || ' a ' ||
            'left join (select poid, soid, sum(notaxfee) jk_fee ' ||
                         'from sync_interface_bl_' || inMonth || ' ' ||
                        'where ordermode = ''1'' and pospecnumber in (''*********'',''*********'',''*********'',''*********'') and status = ''0'' ' ||
                        'group by poid, soid) b ' ||
                   'on a.poid = b.poid  and (a.soid is null and b.soid is null or a.soid = b.soid) ' ||
           'where a.ordermode = ''1'' and a.pospecnumber in (''*********'',''*********'',''*********'',''*********'') and a.status = ''0'' ' ||
           'group by a.orgmonth, a.customernumber, a.pospecnumber, a.sospecnumber, a.poid, a.soid';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        outReturn := 0;
        outSysError := 'OK';
    END;

    /*
    BEGIN
        set @vSql := 'UPDATE stludr.wrn_cmas ' ||
                     'SET wrn_status = ''1'' ' ||
                     'WHERE hd_fee > jk_fee';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        outReturn := 0;
        outSysError := 'OK';
    END;
    */

    COMMIT;

    SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || outReturn as info;


END ;;
DELIMITER ;