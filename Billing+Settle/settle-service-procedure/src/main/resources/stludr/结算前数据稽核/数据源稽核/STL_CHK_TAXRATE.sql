/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能： 结算前数据稽核-数据源稽核-接口表税率稽核
**/
DROP PROCEDURE IF EXISTS stludr.`STL_CHK_TAXRATE`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "STL_CHK_TAXRATE"(
    inMonth          IN   VARCHAR2,
    outSysError      OUT  VARCHAR2,
    outReturn        OUT  NUMBER,
    outBL            OUT  VARCHAR2,
    outAR            OUT  VARCHAR2
)
AS

    v_proc_name       VARCHAR2(30) := 'STL_CHK_TAXRATE';
    vSql      varchar2(9999);
    TYPE i_cursor_type IS REF CURSOR;
    blCursor      i_cursor_type;
    arCursor      i_cursor_type;
    dyn_Select    VARCHAR2(1024);
    iv_Orgbid     VARCHAR2(100);
    iv_Begin_Flag NUMBER;
    iv_Sep        VARCHAR2(6);


BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        outSysError := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg ;
    END;

    BEGIN
        outSysError := '';
        outReturn := 0;
        outBL := '';
        outAR := '';

        if ( length(inMonth) < 6 )  then
            SELECT 'inMonth length less than 6. inMonth=[' || inMonth || ']' FROM dual;
            outReturn := -1;
            ROLLBACK;
            RETURN;
        end if;

        SELECT 'inMonth=' ||  inMonth FROM dual;

        -------稽核应收接口表税率

        dyn_Select := 'SELECT trim(ORGBID) FROM SYNC_INTERFACE_BL_' || inMonth ||
                      ' WHERE TAXRATE NOT IN (''0'', ''6'', ''9'', ''13'') OR TAXRATE IS NULL';
        OPEN blCursor FOR dyn_Select;
          iv_Orgbid := '';
          iv_Begin_Flag := 0;
          LOOP
            FETCH blCursor
              INTO iv_Orgbid;
            EXIT WHEN blCursor%NOTFOUND;

            IF iv_Begin_Flag = 0 THEN
              iv_Sep := '';
            ELSE iv_Sep := ', ';
            END IF;

            outBL := outBL || iv_Sep || iv_Orgbid;
            iv_Begin_Flag := iv_Begin_Flag + 1;
          END LOOP;
        CLOSE blCursor;

        outBL := '税率不符合要求的应收数据还有' || iv_Begin_Flag || '条，它们的ORGBID是：' || outBL;

        -------稽核实收接口表税率

        dyn_Select := 'SELECT trim(ORGBID) FROM SYNC_INTERFACE_AR_' || inMonth ||
                      ' WHERE TAXRATE NOT IN (''0'', ''6'', ''11'', ''17'', ''10'', ''16'', ''9'', ''13'') OR TAXRATE IS NULL';
        OPEN arCursor FOR dyn_Select;
          iv_Orgbid := '';
          iv_Begin_Flag := 0;
          LOOP
            FETCH arCursor
              INTO iv_Orgbid;
            EXIT WHEN arCursor%NOTFOUND;

            IF iv_Begin_Flag = 0 THEN
              iv_Sep := '';
            ELSE iv_Sep := ', ';
            END IF;

            outAR := outAR || iv_Sep || iv_Orgbid;
            iv_Begin_Flag := iv_Begin_Flag + 1;
          END LOOP;
        CLOSE arCursor;

        outAR := '税率不符合要求的实收数据还有' || iv_Begin_Flag || '条，它们的ORGBID是：' || outAR;

        COMMIT;

        outSysError := 'OK';
        outReturn := 0;

        SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || to_number(outReturn);
    END;

END ;;
DELIMITER ;