/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能： 结算前数据稽核-结算规则稽核-实例级别规则稽核
**/
DROP PROCEDURE IF EXISTS stludr.`AUDIT_TARIFF`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "AUDIT_TARIFF"(inMonth   in  varchar2,
                                          szSysErr  OUT VARCHAR2(1000),
                                          nReturn   OUT NUMBER(4)  )
AS

  sql_Truncate    varchar2(100);
  sql_Insert_Tariff varchar2(5000);

  iv_count        number(6);


  s_datasource    number(3);
  s_ordermode     char(1);
  s_orgmonth      char(6);
  s_pospecnumber  varchar2(9);
  s_sospecnumber  varchar2(10);
  s_poid          varchar2(20);
  s_soid          varchar2(20);
  s_feetype       varchar2(4);

  RPT_SQL VARCHAR2(4096);
  v_proc_name VARCHAR2(30) := 'AUDIT_TARIFF';

  cursor cur_tariff is
    select datasource, ordermode, orgmonth, pospecnumber, sospecnumber, poid, soid, feetype from audit_rule_tariff order by 1, 2, 3, 4, 5, 6, 7, 8;

BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
        ROLLBACK;

        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;
  BEGIN
    -- ---清空audit_rule_tariff表
    set @RPT_SQL := 'truncate table audit_rule_tariff';
        SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

    /*-----插入接口表数据到audit_rule_tariff表
    sql_Insert_Tariff := 'insert into audit_rule_tariff(datasource, ordermode, orgmonth, pospecnumber, sospecnumber, poid, soid, feetype) ' ||
        'select distinct 1, ordermode, orgmonth, pospecnumber, sospecnumber, poid, soid, feetype from sync_interface_bl_' || inMonth || ' where pospecnumber in (select pospecnumber from audit_rule_spec where rate_type = 2 and datasource = 1) ' ||
         'union all ' ||
        'select distinct 2, ordermode, orgmonth, pospecnumber, sospecnumber, poid, soid, feetype from sync_interface_ar_' || inMonth || ' where pospecnumber in (select pospecnumber from audit_rule_spec where rate_type = 2 and datasource = 2)';
    execute immediate sql_Insert_Tariff;
    commit;

    open cur_tariff;
    loop
      <<fetchnext_tariff>>
      fetch cur_tariff into s_datasource, s_ordermode, s_orgmonth, s_pospecnumber, s_sospecnumber, s_poid, s_soid, s_feetype;
      exit when cur_tariff%NOTFOUND;

      -----查TARIFF_PARAMETER无结果（F624）
      select count(*)
        into iv_count
        from stl_tariff_parameter_t@users_link
       where order_mode = s_ordermode and offer_code = s_pospecnumber and (product_code = s_sospecnumber or product_code is null and s_sospecnumber is null)
         and prod_inst_id = s_poid and (svc_inst_id = s_soid or svc_inst_id is null and s_soid is null)
         and (charge_item = s_feetype or charge_item = '-1');

      if iv_count = 0 then
        update_err_info('audit_rule_tariff', 'F624', s_datasource, s_ordermode, s_orgmonth, s_pospecnumber, s_sospecnumber, s_poid, s_soid, s_feetype);
        goto fetchnext_tariff;
      end if;

      -----查TARIFF_PARAMETER虽有结果但均不在有效期内（F625）
      select count(*)
        into iv_count
        from stl_tariff_parameter_t@users_link
       where order_mode = s_ordermode and offer_code = s_pospecnumber and (product_code = s_sospecnumber or product_code is null and s_sospecnumber is null)
         and prod_inst_id = s_poid and (svc_inst_id = s_soid or svc_inst_id is null and s_soid is null)
         and (charge_item = s_feetype or charge_item = '-1')
         and to_date(s_orgmonth, 'yyyymm') between eff_date and exp_date;

      if iv_count = 0 then
        update_err_info('audit_rule_tariff', 'F625', s_datasource, s_ordermode, s_orgmonth, s_pospecnumber, s_sospecnumber, s_poid, s_soid, s_feetype);
        goto fetchnext_tariff;
      end if;

    end loop;
    close cur_tariff;*/

    -- -------插入audit_rule_tariff表
    set @RPT_SQL := 'insert into audit_rule_tariff(datasource, ordermode, orgmonth, pospecnumber, sospecnumber, poid, soid, feetype, rule_id) ' ||
            'select a.datasource, a.ordermode, a.orgmonth, a.pospecnumber, a.sospecnumber, a.poid, a.soid, a.feetype, b.rule_id ' ||
              'from (select t.datasource, t.ordermode, t.orgmonth, t.pospecnumber, t.sospecnumber, t.poid, t.soid, t.feetype ' ||
                      'from (select distinct 1 datasource, ordermode, orgmonth, pospecnumber, sospecnumber, poid, soid, feetype ' ||
                              'from sync_interface_bl_' || inMonth || ' ' ||
                             'where pospecnumber || sospecnumber || feetype in  ' ||
                                   '(select pospecnumber || sospecnumber || feetype  ' ||
                                      'from audit_rule_spec ' ||
                                     'where rate_type = 2 and datasource = 1 and err_code is null) ' ||
                             'union all ' ||
                            'select distinct 2, ordermode, orgmonth, pospecnumber, sospecnumber, poid, soid, feetype ' ||
                              'from sync_interface_ar_' || inMonth || ' ' ||
                             'where pospecnumber || sospecnumber || feetype in ' ||
                                   '(select pospecnumber || sospecnumber || feetype ' ||
                                      'from audit_rule_spec ' ||
                                     'where rate_type = 2 and datasource = 2 and err_code is null)) t ' ||
                     'where t.soid is not null) a ' ||
              'left join (select p.*, q.charge_item ' ||
                           'from (select distinct t.offer_code, t.product_code, t.prod_inst_id, t.svc_inst_id, t.order_mode, ' ||
                                                 't.rule_id ' ||
                                   'from stlusers.stl_tariff_parameter_t t ' ||
                                  'where t.charge_item = ''-1'') p ' ||
                           'left join (select t.rule_id, t.charge_item ' ||
                                       'from stlusers.stl_rule_item_t t ' ||
                                      'where sysdate between t.eff_date and t.exp_date) q on p.rule_id = q.rule_id ' ||
                         'union all ' ||
                         'select distinct t.offer_code, t.product_code, t.prod_inst_id, t.svc_inst_id, t.order_mode, t.rule_id, t.charge_item ' ||
                           'from stlusers.stl_tariff_parameter_t t ' ||
                          'where t.charge_item <> ''-1'') b on a.ordermode = b.order_mode ' ||
                                                        'and a.pospecnumber = b.offer_code ' ||
                                                        'and a.poid = b.prod_inst_id ' ||
                                                        'and a.sospecnumber = b.product_code ' ||
                                                        'and a.soid = b.svc_inst_id ' ||
                                                        'and a.feetype = b.charge_item ' ||
            'union all ' ||
            'select a.datasource, a.ordermode, a.orgmonth, a.pospecnumber, a.sospecnumber, a.poid, a.soid, a.feetype, b.rule_id ' ||
              'from (select t.datasource, t.ordermode, t.orgmonth, t.pospecnumber, t.sospecnumber, t.poid, t.soid, t.feetype ' ||
                      'from (select distinct 1 datasource, ordermode, orgmonth, pospecnumber, sospecnumber, poid, soid, feetype ' ||
                              'from sync_interface_bl_' || inMonth || ' ' ||
                             'where pospecnumber || sospecnumber || feetype in ' ||
                                   '(select pospecnumber || sospecnumber || feetype ' ||
                                      'from audit_rule_spec ' ||
                                     'where rate_type = 2 and datasource = 1 and err_code is null) ' ||
                             'union all ' ||
                            'select distinct 2, ordermode, orgmonth, pospecnumber, sospecnumber, poid, soid, feetype ' ||
                              'from sync_interface_ar_' || inMonth || ' ' ||
                             'where pospecnumber || sospecnumber || feetype in ' ||
                                   '(select pospecnumber || sospecnumber || feetype ' ||
                                      'from audit_rule_spec ' ||
                                     'where rate_type = 2 and datasource = 2 and err_code is null)) t ' ||
                     'where t.soid is null) a ' ||
              'left join (select p.*, q.charge_item ' ||
                           'from (select distinct t.offer_code, t.product_code, t.prod_inst_id, t.svc_inst_id, t.order_mode, ' ||
                                                 't.rule_id ' ||
                                   'from stlusers.stl_tariff_parameter_t t ' ||
                                  'where t.charge_item = ''-1'') p ' ||
                           'left join (select t.rule_id, t.charge_item ' ||
                                       'from stlusers.stl_rule_item_t t ' ||
                                      'where sysdate between t.eff_date and t.exp_date) q on p.rule_id = q.rule_id ' ||
                         'union all ' ||
                         'select distinct t.offer_code, t.product_code, t.prod_inst_id, t.svc_inst_id, t.order_mode, t.rule_id, ' ||
                                         't.charge_item ' ||
                           'from stlusers.stl_tariff_parameter_t t ' ||
                          'where t.charge_item <> ''-1'') b on a.ordermode = b.order_mode ' ||
                                                        'and a.pospecnumber = b.offer_code ' ||
                                                        'and a.poid = b.prod_inst_id ' ||
                                                        'and a.feetype = b.charge_item ' ||
             'order by datasource, poid, soid, feetype';
    SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

    -- ---更新错误
    update audit_rule_tariff
       set err_code = 'F625', err_info = '查TARIFF_PARAMETER无结果 或 无费项配置'
     where rule_id is null and err_code is null;

    commit;

    nReturn := 0;
    szSysErr := 'OK';

    SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;

    END;
END ;;
DELIMITER ;