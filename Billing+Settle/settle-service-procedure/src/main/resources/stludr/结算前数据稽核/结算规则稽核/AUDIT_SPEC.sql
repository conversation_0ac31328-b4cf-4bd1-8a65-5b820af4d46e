/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：结算前数据稽核-结算规则稽核-规格级别规则稽核
**/
DROP PROCEDURE IF EXISTS stludr.`AUDIT_SPEC`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "AUDIT_SPEC"(inMonth   in  varchar2,
                                        szSysErr       OUT VARCHAR2(1000),
                                        nReturn          OUT NUMBER(4)  )
AS
  iv_count        number(6);
  iv_spec         char(1);

  s_datasource    number(5);
  s_ordermode     char(1);
  s_orgmonth      char(6);
  s_pospecnumber  varchar2(30);
  s_sospecnumber  varchar2(30);
  s_feetype       varchar2(32);

  s_rule_id       number(12);
  s_charge_item   varchar2(30);
  s_route_code    varchar2(8);
  s_out_object    varchar2(200);
  s_rate_type     number(3);
  s_in_object     varchar2(200);

  RPT_SQL VARCHAR2(4096);
  v_proc_name VARCHAR2(30) := 'AUDIT_SPEC';

  s_id     number(3);

  cursor cur_spec is
    select datasource, ordermode, orgmonth, pospecnumber, sospecnumber, feetype from audit_rule_spec order by 1, 2, 3, 4, 5, 6;

BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
        ROLLBACK;

        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;
    BEGIN
    -- 清空audit_rule_spec表
    SET @RPT_SQL := 'truncate table audit_rule_spec';
    SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

    -- ---插入接口表数据到audit_rule_spec表
    SET @RPT_SQL  := 'insert into audit_rule_spec(datasource, ordermode, orgmonth, pospecnumber, sospecnumber, feetype) ' ||
        'select distinct 1, ordermode, orgmonth, pospecnumber, decode(soid, '''', ''-1'', sospecnumber), feetype from sync_interface_bl_' || inMonth || ' where notaxfee != 0 and status = ''0'' ' ||
         'union all ' ||
        'select distinct 2, ordermode, orgmonth, pospecnumber, decode(soid, '''', ''-1'', sospecnumber), feetype from sync_interface_ar_' || inMonth || ' where notaxfee != 0 and status = ''0'' ';
    SELECT @RPT_SQL;
        PREPARE STMT FROM @RPT_SQL;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    commit;

    -- ---读取游标
    open cur_spec;
    loop
      fetch cur_spec into s_datasource, s_ordermode, s_orgmonth, s_pospecnumber, s_sospecnumber, s_feetype;
      exit when cur_spec%NOTFOUND;
      -- 初始化标签为0
      s_id := 0;
      -----查OFFER无结果（F500）
	  SELECT 'stl_offer_t-1 start';
      select count(*)
        into iv_count
        from stlusers.stl_offer_t
       where data_source = s_datasource and order_mode = s_ordermode
         and offer_code = s_pospecnumber and (product_code = s_sospecnumber or product_code = '-1');

      if iv_count = 0 then
        call update_err_info('audit_rule_spec', 'F500', s_datasource, s_ordermode, s_orgmonth, s_pospecnumber, s_sospecnumber, '', '', s_feetype,szSysErr,nReturn);
        s_id := 1;
      end if;

      -- ---查OFFER虽有结果但均不在有效期内（F501）
	   SELECT 'stl_offer_t-2 start';
      if s_id =0 then
        select count(*)
            into iv_count
            from stlusers.stl_offer_t
        where data_source = s_datasource and order_mode = s_ordermode
            and offer_code = s_pospecnumber and (product_code = s_sospecnumber or product_code = '-1')
            and to_date(s_orgmonth, 'yyyymm') between eff_date and exp_date;

        if iv_count = 0 then
            call update_err_info('audit_rule_spec', 'F501', s_datasource, s_ordermode, s_orgmonth, s_pospecnumber, s_sospecnumber, '', '', s_feetype,szSysErr,nReturn);
            s_id := 1;
        end if;
      end if;

      -- ---查RULE_ITEM无结果（F512）
	   SELECT 'stl_offer_t-3 start';
      if s_id =0 then
        select count(*)
            into iv_count
            from stlusers.stl_offer_t a, stlusers.stl_rule_item_t b
        where a.data_source = s_datasource and a.order_mode = s_ordermode
            and a.offer_code = s_pospecnumber and (a.product_code = s_sospecnumber or a.product_code = '-1')
            and to_date(s_orgmonth, 'yyyymm') between a.eff_date and a.exp_date
            and a.rule_id = b.rule_id and (b.charge_item = s_feetype or b.charge_item = '-1');

        if iv_count = 0 then
            call update_err_info('audit_rule_spec', 'F512', s_datasource, s_ordermode, s_orgmonth, s_pospecnumber, s_sospecnumber, '', '', s_feetype,szSysErr,nReturn);
            s_id := 1;
        end if;
      end if;

      -- ---查RULE_ITEM虽有结果但均不在有效期内（F513）
      -- -产品规格非空且offer表产品非空 或 产品规格为空且offer表产品-1
	   SELECT 'stl_offer_t-4 start';
      if s_id =0 then
        select count(*)
            into iv_count
            from stlusers.stl_offer_t a, stlusers.stl_rule_item_t b
        where a.data_source = s_datasource and a.order_mode = s_ordermode
            and a.offer_code = s_pospecnumber and a.product_code = s_sospecnumber
            and to_date(s_orgmonth, 'yyyymm') between a.eff_date and a.exp_date
            and a.rule_id = b.rule_id and (b.charge_item = s_feetype or b.charge_item = '-1')
            and to_date(s_orgmonth, 'yyyymm') between b.eff_date and b.exp_date;


        -- --产品规格非空且offer表产品-1
		SELECT 'stl_offer_t-5 start';
        if iv_count = 0 then
            select count(*)
            into iv_count
            from stlusers.stl_offer_t a, stlusers.stl_rule_item_t b
            where a.data_source = s_datasource and a.order_mode = s_ordermode
            and a.offer_code = s_pospecnumber and a.product_code = '-1'
            and to_date(s_orgmonth, 'yyyymm') between a.eff_date and a.exp_date
            and a.rule_id = b.rule_id and (b.charge_item = s_feetype or b.charge_item = '-1')
            and to_date(s_orgmonth, 'yyyymm') between b.eff_date and b.exp_date;

            iv_spec := 'N';
        else
            iv_spec := 'Y';
        end if;

        if iv_count = 0 then
            call update_err_info('audit_rule_spec', 'F513', s_datasource, s_ordermode, s_orgmonth, s_pospecnumber, s_sospecnumber, '', '', s_feetype,szSysErr,nReturn);
            s_id := 1;
        end if;
      end if;

      if s_id =0 then
        -- ---查OFFER的结果中存在重复的RULE_ID（F502）
        if iv_count > 1 then
            call update_err_info('audit_rule_spec', 'F502', s_datasource, s_ordermode, s_orgmonth, s_pospecnumber, s_sospecnumber, '', '', s_feetype,szSysErr,nReturn);
            s_id := 1;
        end if;
      end if;

      -- ---更新rule_id、charge_code
      -- --产品规格非空且offer表产品非空 或 产品规格为空且offer表产品-1
	  SELECT 'stl_offer_t-6 start';
      if s_id =0 then
        if iv_spec = 'Y' then
            select a.rule_id, b.charge_item, a.route_code
            into s_rule_id, s_charge_item, s_route_code
            from stlusers.stl_offer_t a, stlusers.stl_rule_item_t b
            where a.data_source = s_datasource and a.order_mode = s_ordermode
            and a.offer_code = s_pospecnumber and a.product_code = s_sospecnumber
            and to_date(s_orgmonth, 'yyyymm') between a.eff_date and a.exp_date
            and a.rule_id = b.rule_id and (b.charge_item = s_feetype or b.charge_item = '-1')
            and to_date(s_orgmonth, 'yyyymm') between b.eff_date and b.exp_date;


        -- --产品规格非空且offer表产品-1
        else
            select a.rule_id, b.charge_item, a.route_code
            into s_rule_id, s_charge_item, s_route_code
            from stlusers.stl_offer_t a, stlusers.stl_rule_item_t b
            where a.data_source = s_datasource and a.order_mode = s_ordermode
            and a.offer_code = s_pospecnumber and a.product_code = '-1'
            and to_date(s_orgmonth, 'yyyymm') between a.eff_date and a.exp_date
            and a.rule_id = b.rule_id and (b.charge_item = s_feetype or b.charge_item = '-1')
            and to_date(s_orgmonth, 'yyyymm') between b.eff_date and b.exp_date;
        end if;
        SELECT 'stl_offer_t-7 start';
        update audit_rule_spec
            set rule_id = s_rule_id, charge_item = s_charge_item
        where datasource = s_datasource and ordermode = s_ordermode and orgmonth = s_orgmonth
            and pospecnumber = s_pospecnumber and sospecnumber = s_sospecnumber and feetype = s_feetype;

        -- ---二次结算单的ROUTE_CODE错误（F503）
        if (s_datasource = 1 and s_route_code not in ('0', '3')) or (s_datasource = 2 and s_route_code not in ('0', '4'))
        then
            call update_err_info('audit_rule_spec', 'F503', s_datasource, s_ordermode, s_orgmonth, s_pospecnumber, s_sospecnumber, '', '', s_feetype,szSysErr,nReturn);
            s_id := 1;
        end if;
    end if;

        -- ---更新route_code
		SELECT 'stl_offer_t-8 start';
        update audit_rule_spec
            set route_code = s_route_code
        where datasource = s_datasource and ordermode = s_ordermode and orgmonth = s_orgmonth
            and pospecnumber = s_pospecnumber and sospecnumber = s_sospecnumber and feetype = s_feetype;



     if s_id =0 then
      -- ---查RULE无结果（F510）
      select count(*)
        into iv_count
        from stlusers.stl_rule_t a
       where a.rule_id = s_rule_id;
      SELECT 'stl_offer_t-9 start';
      if iv_count = 0 then
        call update_err_info('audit_rule_spec', 'F510', s_datasource, s_ordermode, s_orgmonth, s_pospecnumber, s_sospecnumber, '', '', s_feetype,szSysErr,nReturn);
         SELECT 'stl_offer_t-9-1 start';
		s_id := 1;
      end if;
    end if;

     if s_id =0 then
        -- ---查RULE虽有结果但均不在有效期内（F511）
        select count(*)
            into iv_count
            from stlusers.stl_rule_t a
        where a.rule_id = s_rule_id
            and to_date(s_orgmonth, 'yyyymm') between a.eff_date and a.exp_date;

        if iv_count = 0 then
            call update_err_info('audit_rule_spec', 'F511', s_datasource, s_ordermode, s_orgmonth, s_pospecnumber, s_sospecnumber, '', '', s_feetype,szSysErr,nReturn);
             SELECT 'stl_offer_t-9-2 start';
			s_id := 1;
        end if;
    end if;
    SELECT 'stl_offer_t-10 start';
        -- ---更新out_object
        select decode(b.object_type, '1', b.object_value, '2', b.field_name)
            into s_out_object
            from stlusers.stl_rule_t a, stlusers.stl_object_t b
        where a.rule_id = s_rule_id
            and a.object_id = b.object_id
            and to_date(s_orgmonth, 'yyyymm') between a.eff_date and a.exp_date;

        update audit_rule_spec
            set out_object = s_out_object
        where datasource = s_datasource and ordermode = s_ordermode and orgmonth = s_orgmonth
            and pospecnumber = s_pospecnumber and sospecnumber = s_sospecnumber and feetype = s_feetype;


    if s_id =0 then
      -- ---查RATE无结果（F530）
      select count(*)
        into iv_count
        from stlusers.stl_rate_t a
       where a.rule_id = s_rule_id;

      if iv_count = 0 then
        call update_err_info('audit_rule_spec', 'F530', s_datasource, s_ordermode, s_orgmonth, s_pospecnumber, s_sospecnumber, '', '', s_feetype,szSysErr,nReturn);
        s_id := 1;
      end if;
    end if;

      -- ---查RATE虽有结果但均不在有效期内（F531）
    if s_id =0 then
      select count(*)
        into iv_count
        from stlusers.stl_rate_t a
       where a.rule_id = s_rule_id
         and to_date(s_orgmonth, 'yyyymm') between a.eff_date and a.exp_date;

      if iv_count = 0 then
        call update_err_info('audit_rule_spec', 'F531', s_datasource, s_ordermode, s_orgmonth, s_pospecnumber, s_sospecnumber, '', '', s_feetype,szSysErr,nReturn);
        s_id := 1;
      end if;
    end if;

    -- ---一条RULE下出现多个RATE_TYPE（F504）
    if s_id =0 then
      select count(distinct rate_type)
        into iv_count
        from stlusers.stl_rate_t a
       where rule_id = s_rule_id
         and to_date(s_orgmonth, 'yyyymm') between a.eff_date and a.exp_date;

      if iv_count > 1 then
        call update_err_info('audit_rule_spec', 'F504', s_datasource, s_ordermode, s_orgmonth, s_pospecnumber, s_sospecnumber, '', '', s_feetype,szSysErr,nReturn);
        s_id := 1;
      end if;
    end if;
    SELECT 'stl_offer_t-11 start';
      -- ---更新rate_type、in_object
      select distinct rate_type, to_char(max(decode(b.object_type, '1', b.object_value, '2', b.field_name)))
        into s_rate_type, s_in_object
        from stlusers.stl_rate_t a, stlusers.stl_object_t b
       where a.rule_id = s_rule_id
         and a.in_object_id = b.object_id
       group by rate_type;

      update audit_rule_spec
         set rate_type = s_rate_type, in_object = s_in_object
       where datasource = s_datasource and ordermode = s_ordermode and orgmonth = s_orgmonth
         and pospecnumber = s_pospecnumber and sospecnumber = s_sospecnumber and feetype = s_feetype;
    SELECT 'stl_offer_t-12 start';
    end loop;
    close cur_spec;

   SELECT 'stl_offer_t-13 start';
    -----更新其它错误
	SELECT 'update-1 start';
    update audit_rule_spec
       set err_code = 'F9999', err_info = '其它错误导致规则项目更新不全'
     where (rule_id is null or route_code is null or out_object is null or in_object is null
        or charge_item is null or rate_type is null) and err_code is null;

	SELECT 'update-1 end';
    commit;

    nReturn := 0;
    szSysErr := 'OK';

	SELECT 'update-1 start-1	';
    SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;

    END;
END ;;
DELIMITER ;