DROP PROCEDURE IF EXISTS stludr.`update_err_info`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "update_err_info"(table_name     in varchar2,
                                            s_err_code     in varchar2,
                                            s_datasource   in number,
                                            s_ordermode    in char(1),
                                            s_orgmonth     in char(6),
                                            s_pospecnumber in varchar2,
                                            s_sospecnumber in varchar2,
                                            s_poid         in varchar2,
                                            s_soid         in varchar2,
                                            s_feetype      in varchar2,
                                            szSysErr  OUT VARCHAR2(1000),
                                            nReturn   OUT NUMBER(4)

  )
AS
    RPT_SQL VARCHAR2(4096);
    v_proc_name VARCHAR2(30) := 'update_err_info';
    iv_err_info   varchar2(400);

BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
        ROLLBACK;

        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;
  BEGIN
  -- ---获取err_code对应的描述信息
  select code_desc
    into iv_err_info
    from stlusers.err_code_def
   where code = s_err_code;

  -- ---更新稽核表

  IF table_name = 'audit_rule_spec' then
    update audit_rule_spec
       set err_code = s_err_code,
           err_info = iv_err_info
     where datasource = s_datasource
       and ordermode = s_ordermode
       and pospecnumber = s_pospecnumber
       and sospecnumber = s_sospecnumber
       and feetype = s_feetype;
    END IF;

  if table_name = 'audit_rule_tariff' then
    update audit_rule_tariff
       set err_code = s_err_code,
           err_info = iv_err_info
     where datasource = s_datasource
       and ordermode = s_ordermode
       and pospecnumber = s_pospecnumber
       and sospecnumber = s_sospecnumber
       and poid = s_poid
       and (soid = s_soid or soid is null and s_soid is null)
       and (feetype = s_feetype or feetype = '-1');
    END IF;

  IF table_name = 'audit_rule_repart' then
    update audit_rule_repart
       set err_code = s_err_code,
           err_info = iv_err_info
     where datasource = s_datasource
       and ordermode = s_ordermode
       and pospecnumber = s_pospecnumber
       and sospecnumber = s_sospecnumber
       and poid = s_poid
       and (soid = s_soid or soid is null and s_soid is null)
       and (feetype = s_feetype or feetype = '-1');

    END IF;

    commit;

    nReturn := 0;
    szSysErr := 'OK';

    SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;

    END;
END ;;
DELIMITER ;