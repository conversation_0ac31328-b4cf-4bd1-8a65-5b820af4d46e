/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能： 结算前数据稽核-结算结果稽核-结算规则稽核总调用
**/
DROP PROCEDURE IF EXISTS stludr.`PKG_AUDIT_SETTLE_RULE_MAIN`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "PKG_AUDIT_SETTLE_RULE_MAIN"(inMonth             in  varchar2,
                                                         szSysErr       OUT VARCHAR2(1000),
                                                         nReturn          OUT NUMBER(4)  )
AS
  v_proc_name VARCHAR2(30) := 'PKG_AUDIT_SETTLE_RULE_MAIN';
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
        ROLLBACK;

        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;
  BEGIN

    call audit_spec(inMonth,szSysErr,nReturn);
    call audit_tariff(inMonth,szSysErr,nReturn);
    call audit_repart(inMonth,szSysErr,nReturn);

    szSysErr := 'OK';
    nReturn := 0;

    SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;

  END;
END ;;
DELIMITER ;