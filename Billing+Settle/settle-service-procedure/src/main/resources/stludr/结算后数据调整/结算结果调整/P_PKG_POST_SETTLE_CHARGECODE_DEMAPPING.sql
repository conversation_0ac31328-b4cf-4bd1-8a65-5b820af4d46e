/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能： 结算后数据调整-结算结果调整-结算结果的费项反向映射
**/
DROP PROCEDURE IF EXISTS stludr.`P_PKG_POST_SETTLE_CHARGECODE_DEMAPPING`;
DELIMITER ;;
CREATE or replace DEFINER="stludr"@"10.%" PROCEDURE "P_PKG_POST_SETTLE_CHARGECODE_DEMAPPING"(
    inMonth          IN   VARCHAR2,
    outSysError      OUT  VARCHAR2,
    outReturn        OUT  NUMBER
)
AS
    vSql VARCHAR2(10240);
    v_proc_name   VARCHAR2(40) := 'P_PKG_POST_SETTLE_CHARGECODE_DEMAPPING';
BEGIN
    outSysError := 'OK';
    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
outSysError := substr(@p2, 1, 1000);
        outReturn  := -1;
ROLLBACK;


select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg ;
END;


    ----应收结果
BEGIN
        set @vSql := 'update /*+ AUTOCOMMIT_DURING_DML() index(a idx_charg_pro_off_ord) */ ur_recv_' || inMonth || '_t a ' ||
        ' join stl_chargecode_mapping b on b.order_mode = a.order_mode and b.offer_code = a.offer_code and b.product_code = a.product_code and b.mapped_charge_code = a.charge_code '||
        ' and b.match_type= ''OP'' and b.direction in (''2'', ''3'') and ''' || inMonth || ''' between b.start_month and b.end_month '||
        ' set a.charge_code =b.orig_charge_code ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
        outSysError := 'OK';
END;

BEGIN
        set @vSql := 'update /*+ AUTOCOMMIT_DURING_DML() index(a idx_charg_pro_off_ord) */ ur_recv_' || inMonth || '_t a ' ||
         ' join stl_chargecode_mapping b on b.order_mode = a.order_mode and b.offer_code = a.offer_code and b.mapped_charge_code = a.charge_code '||
         ' and b.match_type= ''O'' and b.direction in (''2'', ''3'') and ''' || inMonth || ''' between b.start_month and b.end_month '||
         ' set a.charge_code =  b.orig_charge_code ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
        outSysError := 'OK';
END;

BEGIN
        set @vSql := 'update /*+ AUTOCOMMIT_DURING_DML() index(a idx_charg_pro_off_ord) */ ur_recv_' || inMonth || '_t a ' ||
         ' join stl_chargecode_mapping b on b.order_mode = a.order_mode and b.offer_code = a.offer_code and b.product_code = a.product_code and b.mapped_charge_code = a.charge_code '||
          ' and b.match_type= ''OPR'' and b.direction in (''2'', ''3'') and ''' || inMonth || ''' between b.start_month and b.end_month '||
          ' set a.charge_code = b.orig_charge_code, '||
          ' a.duration = b.rateplan_id ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
        outSysError := 'OK';
END;


    ----实收结果
BEGIN
        set @vSql := 'update /*+ AUTOCOMMIT_DURING_DML() index(a idx_charg_pro_off_ord)*/ ur_paid_' || inMonth || '_t a ' ||
                ' join stl_chargecode_mapping b on b.order_mode = a.order_mode and b.offer_code = a.offer_code and b.product_code = a.product_code and b.mapped_charge_code = a.charge_code '||
        ' and b.match_type= ''OP'' and b.direction in (''2'', ''3'') and ''' || inMonth || ''' between b.start_month and b.end_month '||
        ' set a.charge_code =b.orig_charge_code ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
        outSysError := 'OK';
END;

BEGIN
        set @vSql := 'update /*+ AUTOCOMMIT_DURING_DML() index(a idx_charg_pro_off_ord) */ ur_paid_' || inMonth || '_t a ' ||
                 ' join stl_chargecode_mapping b on b.order_mode = a.order_mode and b.offer_code = a.offer_code and b.mapped_charge_code = a.charge_code '||
                 ' and b.match_type= ''O'' and b.direction in (''2'', ''3'') and ''' || inMonth || ''' between b.start_month and b.end_month '||
                 ' set a.charge_code =  b.orig_charge_code ';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
        outSysError := 'OK';
END;

BEGIN
        set @vSql := 'update /*+ AUTOCOMMIT_DURING_DML() index(a idx_charg_pro_off_ord) */ ur_paid_' || inMonth || '_t a ' ||
                 ' join stl_chargecode_mapping b on b.order_mode = a.order_mode and b.offer_code = a.offer_code and b.product_code = a.product_code and b.mapped_charge_code = a.charge_code '||
                 ' and b.match_type= ''OPR'' and b.direction in (''2'', ''3'') and ''' || inMonth || ''' between b.start_month and b.end_month '||
                 ' set a.charge_code = b.orig_charge_code, '||
                 ' a.duration = b.rateplan_id ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
        outSysError := 'OK';
END;



    ----二次结算结果
BEGIN
        set @vSql := 'update /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) index(a idx_charg_pro_off_ord)*/ ur_eboss_' || inMonth || '_t a ' ||
                ' join stl_chargecode_mapping b on b.order_mode = a.order_mode and b.offer_code = a.offer_code and b.product_code = a.product_code and b.mapped_charge_code = a.charge_code '||
                ' and b.match_type= ''OP'' and b.direction in (''2'', ''3'') and ''' || inMonth || ''' between b.start_month and b.end_month '||
                ' set a.charge_code =b.orig_charge_code ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
        outSysError := 'OK';
END;

BEGIN
        set @vSql := 'update /*+ AUTOCOMMIT_DURING_DML() index(a idx_charg_pro_off_ord) */ ur_eboss_' || inMonth || '_t a ' ||
                 ' join stl_chargecode_mapping b on b.order_mode = a.order_mode and b.offer_code = a.offer_code and b.mapped_charge_code = a.charge_code '||
                 ' and b.match_type= ''O'' and b.direction in (''2'', ''3'') and ''' || inMonth || ''' between b.start_month and b.end_month '||
                 ' set a.charge_code =  b.orig_charge_code ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
        outSysError := 'OK';
END;

BEGIN
        set @vSql := 'update /*+ AUTOCOMMIT_DURING_DML() index(a idx_charg_pro_off_ord) */ ur_eboss_' || inMonth || '_t a ' ||
                 ' join stl_chargecode_mapping b on b.order_mode = a.order_mode and b.offer_code = a.offer_code and b.product_code = a.product_code and b.mapped_charge_code = a.charge_code '||
                 ' and b.match_type= ''OPR'' and b.direction in (''2'', ''3'') and ''' || inMonth || ''' between b.start_month and b.end_month '||
                 ' set a.charge_code = b.orig_charge_code, '||
                 ' a.duration = b.rateplan_id ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
        outSysError := 'OK';
END;

COMMIT;

SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || outReturn as info;


END;;
DELIMITER ;