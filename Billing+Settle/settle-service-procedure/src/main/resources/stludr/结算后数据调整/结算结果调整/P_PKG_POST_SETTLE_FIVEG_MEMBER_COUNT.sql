/**
  BIL-ZQ-202407-19-5G融媒结算规则需求
  5G融媒每月成员计算
 */
use stludr;
DROP PROCEDURE IF EXISTS stludr.`P_PKG_POST_SETTLE_FIVEG_MEMBER_COUNT`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE stludr.`P_PKG_POST_SETTLE_FIVEG_MEMBER_COUNT`(
    inMonth          IN   VARCHAR2,
    outSysError      OUT  VARCHAR2,
    outReturn        OUT  NUMBER
  )
AS
    vSql VARCHAR2(10240);
    v_proc_name   VARCHAR2(50) := 'P_PKG_POST_SETTLE_FIVEG_MEMBER_COUNT';

BEGIN
    outSysError := 'OK';
    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        outSysError := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLL<PERSON>CK;



        select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg ;
    END;


    BEGIN

        set @vSql := 'delete from RVL_FiveG_MEMBER where SETTLE_MONTH = ' || inMonth || ' ';

        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        outReturn := 0;
        outSysError := 'OK';
    END;

    BEGIN
        set @vSql := 'insert into RVL_FiveG_MEMBER ' ||
                     ' SELECT null, T.OUT_OBJECT,T.OFFER_CODE, T.PRODUCT_CODE, T.PRODUCT_ORDER_ID,T.OFFER_ORDER_ID,T.CHARGE_CODE, ' ||
                     '               COUNT(T.MEMBER_CODE) AS MEMBER_COUNT,  ' ||
                     '               T.SETTLE_MONTH, SUBSTR(T.SETTLE_MONTH, 1, 4),T.ORDER_MODE,T.CUSTOMER_CODE,T.CUSTOMER_PROV,T.ORDER_PROV ' ||
                     '          FROM UR_EBOSS_' || inMonth || '_T  T  ' ||
                     '         WHERE T.OFFER_CODE = ''50126'' ' ||
                     '           AND T.PRODUCT_CODE IN( ''2024999480001225'',''2024999480002677'',''2024999480002678'' ) ' ||
                     '           AND T.DEST_SOURCE = ''5'' ' ||
                     '           AND T.ORDER_MODE IN( ''3'' ,''5'') AND T.OCCURENCE != ''5G融媒补出'' and T.FEETYPE != ''1''  ' ||
                     '         GROUP BY T.CHARGE_CODE, T.OUT_OBJECT,T.OFFER_CODE, T.PRODUCT_CODE, T.PRODUCT_ORDER_ID,T.OFFER_ORDER_ID,T.SETTLE_MONTH,T.ORDER_MODE,T.CUSTOMER_CODE,T.CUSTOMER_PROV,T.ORDER_PROV ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        outReturn := 0;
        outSysError := 'OK';
    END;

    COMMIT;

    SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || outReturn as info;

END;;
DELIMITER ;