/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能： 结算后数据稽核-结算结果调整-结算结果标记更新和数据调整
 1. 云MAS异网费项和dest_source更新
 2. 千里眼业务23年之后订购处理
**/
use stludr;
delimiter ;;
CREATE or replace DEFINER="stludr"@"10.%" PROCEDURE "P_PKG_POST_SETTLE_MARK_REPAIR"(
    inMonth          IN   VARCHAR2,
    inBatch          IN   VARCHAR2,
    flag_version     IN   VARCHAR2,
    reserve1         IN   VARCHAR2,
    reserve2         IN   VARCHAR2,
    proc_out         OUT   VARCHAR2,
    outSysError      OUT  VARCHAR2,
    outReturn        OUT  NUMBER,
    outBL            OUT  NUMBER,
    outAR            OUT  NUMBER
)
AS
    vSql VARCHAR2(10240);
    v_proc_name   VARCHAR2(30) := 'P_PKG_POST_SETTLE_MARK_REPAIR';
BEGIN
    outSysError := 'OK';
    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        outSysError := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;



        select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg ;
    END;

    BEGIN
    ----*********商品，用ur_recv结果数据填写ur_eboss结果表
      set @vSql := 'delete /*+ AUTOCOMMIT_DURING_DML() */ from ur_eboss_' || inMonth || '_t where offer_code = ''*********'' and product_order_id is null and rule_id = 0';
      SELECT @vSql;
      PREPARE STMT FROM @vSql;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;

      outReturn := 0;
      outSysError := 'OK';
    END;

    BEGIN
      set @vSql := 'insert /*+ AUTOCOMMIT_DURING_DML() */ into ur_eboss_' || inMonth || '_t(stream_id, index_id, source_id, customer_code, customer_prov, offer_code, product_code, offer_order_id, product_order_id, ' ||
             'order_prov, member_code, member_prov, account_id, sign_entity, org_month, paid_month, settle_month, charge_code, charge, occurence, duration, volume, ' ||
             'tax_rate, tax_fee, no_tax_fee, out_object_type, out_object, in_object_type, in_object, settle_taxfee, settle_notaxfee, rule_id, dest_source, ' ||
             'ori_file_id, file_id, phase, order_mode, tax_rate_ori, feetype, adjmonth) ' ||
             'select stream_id, index_id, 3, customer_code, customer_prov, offer_code, product_code, offer_order_id, product_order_id, order_prov, member_code, member_prov, ' ||
             'account_id, sign_entity, org_month, '''', settle_month, ''3870'', settle_notaxfee, occurence, duration, volume, tax_rate, 0, settle_notaxfee, '''', ' ||
             'customer_prov, '''', in_object, 0, settle_notaxfee, 0, ''5'', 0, 0, phase, order_mode, ''6'', feetype, adjmonth ' ||
             'from ur_recv_' || inMonth || '_t ' ||
             'where offer_code = ''*********'' and product_order_id is null and dest_source = ''96'' and order_mode = ''1''';
       SELECT @vSql;
       PREPARE STMT FROM @vSql;
       EXECUTE STMT;
       DEALLOCATE PREPARE STMT;

       outReturn := 0;
       outSysError := 'OK';
    END;


    BEGIN
      ----更新云MAS异网费项和dest_source
      -----应收
        set @vSql := 'UPDATE /*+ AUTOCOMMIT_DURING_DML() */ UR_RECV_' || inMonth || '_T a ' ||
           'SET a.CHARGE_CODE = decode(a.dest_source, ''97'', ''1040'', ''96'', ''3870''), a.in_object = a.customer_prov,  a.dest_source=decode(a.order_mode,''1'',''0'',''98'') ' ||
           'WHERE a.OFFER_CODE in (''*********'', ''*********'', ''*********'', ''*********'',''50063'',''50056'',''60000'',''*********'',''*********'') ' ||
           'and a.order_mode in(''1'')  ' ||
           'and a.dest_source in (''97'', ''96'') ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        outReturn := 0;
        outSysError := 'OK';
    END;

    BEGIN
        ------实收
        set @vSql := 'UPDATE /*+ AUTOCOMMIT_DURING_DML() */ UR_PAID_' || inMonth || '_T a ' ||
           'SET a.CHARGE_CODE = decode(a.dest_source, ''97'', ''1040'', ''96'', ''3870''), a.in_object = a.customer_prov,  a.dest_source=decode(a.order_mode,''1'',''0'',''98'') ' ||
           'WHERE a.OFFER_CODE in (''*********'', ''*********'', ''*********'', ''*********'',''50063'',''50056'',''60000'',''*********'',''*********'') ' ||
           'and a.order_mode in (''1'') ' ||
           'and a.dest_source in (''97'', ''96'') ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        outReturn := 0;
        outSysError := 'OK';
    END;

    BEGIN
        ---------二次结算
        set @vSql := 'UPDATE /*+ AUTOCOMMIT_DURING_DML() */ UR_EBOSS_' || inMonth || '_T a ' ||
           'SET a.out_object = a.customer_prov ,a.CHARGE_CODE = decode(a.charge_code, ''3870'', ''3870'', ''1040'') ' ||
           'WHERE (a.OFFER_CODE in (''*********'', ''*********'', ''*********'', ''*********'', ''60000'',''*********'',''*********'') or product_code=''110151'') ' ||
           'and a.order_mode in(''1'',''3'',''5'') ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        outReturn := 0;
        outSysError := 'OK';
    END;

    BEGIN
        ----------千里眼业务23年之后订购处理
        set @vSql := 'update /*+ AUTOCOMMIT_DURING_DML() */ stludr.ur_eboss_' || inMonth || '_t  set in_object =''ZYWLW'' where charge_code in(  '||
             'select charge_code from (   '||
             ' select a.* from stludr.ur_eboss_' || inMonth || '_t a, stludr.rvl_dict_config b   '||
             'where a.offer_code=''50055'' and a.offer_code = b.offer_code    '||
             'and b.po_name=''千里眼增值服务'' and a.charge_code = b.fee_type))   '||
             'and offer_order_id not in(select offer_order_id from stludr.stl_order_dict_qly)  '||
             'and product_order_id not in(select product_order_id from stludr.stl_order_dict_qly) ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        outReturn := 0;
        outSysError := 'OK';
    END;
    BEGIN
        -- 应收
      -- 20240527  、20240605取生失效范围内的属性值
      ---- 更新idc（协同营销）结算模式
        set @vSql := 'UPDATE /*+ AUTOCOMMIT_DURING_DML() */ UR_RECV_' || inMonth || '_T a ' ||
            'JOIN stlusers.stl_sync_attr b on a.OFFER_CODE=b.pospecnumber AND a.PRODUCT_ORDER_ID=b.soid and ''' || inMonth || ''' between b.ATTRSTARTMONTH and b.ATTRENDMONTH
            SET a.SETTLE_MODE =b.attr_value
           WHERE a.OFFER_CODE in (''50051'',''60015'')';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        outReturn := 0;
        outSysError := 'OK';
    END;

    BEGIN
        -- 实收
       set @vSql := 'UPDATE /*+ AUTOCOMMIT_DURING_DML() */ UR_PAID_' || inMonth || '_T a ' ||
          'JOIN stlusers.stl_sync_attr b on a.OFFER_CODE=b.pospecnumber AND a.PRODUCT_ORDER_ID=b.soid and ''' || inMonth || ''' between b.ATTRSTARTMONTH and b.ATTRENDMONTH
            SET a.SETTLE_MODE =b.attr_value
           WHERE a.OFFER_CODE in (''50051'',''60015'')';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        outReturn := 0;
        outSysError := 'OK';
    END;
    BEGIN
        --BIL-ZQ-202505-15-OneHealth结算模式调整,3118费项的税率是13
        set @vSql := 'UPDATE /*+ AUTOCOMMIT_DURING_DML() */ ur_eboss_' || inMonth || '_T a ' ||
                     ' SET a.tax_rate =''13'' WHERE a.OFFER_CODE = ''50104'' and a.product_code =''2022999400054889'' and a.charge_code in(''3118'',''7118'')  ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        outReturn := 0;
        outSysError := 'OK';
    END;

    call P_PKG_POST_SETTLE_CHARGECODE_DEMAPPING(inMonth, outSysError, outReturn);

    -- 统计5G融媒每月成员数量
    call P_PKG_POST_SETTLE_FIVEG_MEMBER_COUNT(inMonth,outSysError,outReturn);
    COMMIT;

    SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || outReturn as info;

END;;
delimiter ;