/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能： 结算后数据调整-结算结果调整-调整结果表的feetype（非调账/调账标记）
**/
DROP PROCEDURE IF EXISTS stludr.`UPDATE_ADJUST_FLAG`;
DELIMITER ;;
CREATE OR REPLACE  DEFINER="stludr"@"10.%" PROCEDURE "UPDATE_ADJUST_FLAG"( V_ACCT_MONTH IN VARCHAR2,
                        szSysErr OUT VARCHAR2(1000),
                        nReturn OUT NUMBER(4)  )
AS
  P_SQL VARCHAR2(8000);
  V_SQL  VARCHAR2(2000);
  V_FLAG VARCHAR2(20);
  v_proc_name   VARCHAR2(30) := 'UPDATE_ADJUST_FLAG';
BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        szSysErr := substr(@p2, 1, 1000);
        nReturn  := -1;
        ROLLBACK;

        select ('exception: ' || nReturn || '|'  || @p1 || '|' || szSysErr ) AS error_msg ;
    END;
    BEGIN

  ---先更新feetype为空的数据为0
set @V_SQL := 'update  stludr.ur_recv_'||V_ACCT_MONTH||'_t a set a.feetype = ''0''  where a.feetype is null';
    SELECT @V_SQL;
      PREPARE STMT FROM @V_SQL;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;
  COMMIT;

set @V_SQL := 'update  stludr.ur_eboss_'||V_ACCT_MONTH||'_t a set a.feetype = ''0''  where a.feetype is null';
    SELECT @V_SQL;
      PREPARE STMT FROM @V_SQL;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;
  COMMIT;

set @V_SQL := 'update  stludr.ur_paid_'||V_ACCT_MONTH||'_t a set a.feetype = ''0''  where a.feetype is null';
    SELECT @V_SQL;
      PREPARE STMT FROM @V_SQL;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;
  COMMIT;

set @V_SQL := 'update  stludr.ur_cpr_'||V_ACCT_MONTH||'_t a set a.feetype = ''0''  where a.feetype is null';
    SELECT @V_SQL;
      PREPARE STMT FROM @V_SQL;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;
  COMMIT;

set @V_SQL := 'update  stludr.ur_cpp_'||V_ACCT_MONTH||'_t a set a.feetype = ''0''  where a.feetype is null';
    SELECT @V_SQL;
      PREPARE STMT FROM @V_SQL;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;
  COMMIT;


---更新charge_code为调账费项的数据feetype为1
set @V_SQL := 'update  stludr.ur_recv_'||V_ACCT_MONTH||'_t a '||
              ' join (select distinct charge_item_ref  from stludr.stl_charge_item_def a where a.acct_month = '''||V_ACCT_MONTH||'''and charge_item like ''Adjust%'' ) b on a.charge_code=b.charge_item_ref '||
              ' set a.feetype = ''1'' ';
 SELECT @V_SQL;
      PREPARE STMT FROM @V_SQL;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;
  COMMIT;

set @V_SQL := 'update  stludr.ur_eboss_'||V_ACCT_MONTH||'_t a '||
              ' join (select distinct charge_item_ref  from stludr.stl_charge_item_def a where a.acct_month = '''||V_ACCT_MONTH||'''and charge_item like ''Adjust%'' ) b on a.charge_code=b.charge_item_ref '||
              ' set a.feetype = ''1'' ';
    SELECT @V_SQL;
      PREPARE STMT FROM @V_SQL;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;
  COMMIT;

set @V_SQL := 'update  stludr.ur_paid_'||V_ACCT_MONTH||'_t a '||
              ' join (select distinct charge_item_ref  from stludr.stl_charge_item_def a where a.acct_month = '''||V_ACCT_MONTH||'''and charge_item like ''Adjust%'' ) b on a.charge_code=b.charge_item_ref '||
              ' set a.feetype = ''1'' ';
    SELECT @V_SQL;
      PREPARE STMT FROM @V_SQL;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;
  COMMIT;

set @V_SQL := 'update /*+ index(a idx_charcode) join_order(b,a) */  stludr.ur_cpr_'||V_ACCT_MONTH||'_t a '||
              ' join (select distinct charge_item_ref  from stludr.stl_charge_item_def a where a.acct_month = '''||V_ACCT_MONTH||'''and charge_item like ''Adjust%'' ) b on a.charge_code=b.charge_item_ref '||
              ' set a.feetype = ''1'' ';
    SELECT @V_SQL;
      PREPARE STMT FROM @V_SQL;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;
  COMMIT;

set @V_SQL := 'update  stludr.ur_cpp_'||V_ACCT_MONTH||'_t a '||
              ' join (select distinct charge_item_ref  from stludr.stl_charge_item_def a where a.acct_month = '''||V_ACCT_MONTH||'''and charge_item like ''Adjust%'' ) b on a.charge_code=b.charge_item_ref '||
              ' set a.feetype = ''1'' ';
    SELECT @V_SQL;
      PREPARE STMT FROM @V_SQL;
      EXECUTE STMT;
      DEALLOCATE PREPARE STMT;
  COMMIT;

  nReturn := 0;
  szSysErr := 'OK';

  commit;

  SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || nReturn;

  END;
END;
DELIMITER ;