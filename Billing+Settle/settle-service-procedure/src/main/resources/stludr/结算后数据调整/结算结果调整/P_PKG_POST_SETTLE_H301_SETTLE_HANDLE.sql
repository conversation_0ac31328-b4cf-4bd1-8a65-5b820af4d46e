/**
  H301
 */
use stludr;
DELIMITER ;;
CREATE or replace DEFINER="stludr"@"10.%" PROCEDURE "P_PKG_POST_SETTLE_H301_SETTLE_HANDLE"(
    inMonth          IN   VARCHAR2,
    outSysError      OUT  VARCHAR2,
    outReturn        OUT  NUMBER
  )
AS
v_table_name VARCHAR2(64);
v_sql VARCHAR2(4000);
v_proc_name   VARCHAR2(50) := 'P_PKG_POST_SETTLE_H301_SETTLE_HANDLE';
year_str VARCHAR2(4);
month_str VARCHAR2(2);
year INT;
month INT;
quarter INT;

begin
    outSysError := '';
    outReturn := 0;

    -- 	    截取年份和月份
    SET year_str = SUBSTRING(inMonth, 1, 4);
    SET month_str = SUBSTRING(inMonth, 5, 2);

-- 	    转换为整数
    SET year = CAST(year_str AS UNSIGNED);
    SET month = CAST(month_str AS UNSIGNED);
    --
-- 	    计算季度
    SET quarter = CEIL(month / 3);

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        outSysError := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg ;
    END;

    BEGIN

        set @vSql := 'delete from rvl_report_zbtc_cfg where SETTLE_YEAR = ' || year || ' AND QUARTER = '|| quarter;

        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        outReturn := 0;
        outSysError := 'OK';
    END;

    BEGIN


        SELECT CONCAT('Year: ', year, ', Month: ', month, ', Quarter: ', quarter, ' - First to Third Quarter Logic');
        --根据季度执行不同逻辑
        IF quarter IN (1, 2, 3) THEN

            INSERT INTO stludr.rvl_report_zbtc_cfg(SETTLE_YEAR, QUARTER, SETT_PROV, PRODUCT_NAME, SETT_AMOUNT, ADJUST, TAXRATE)
            SELECT settle_year,  quarter as settle_quarter,  sett_prov,  product_name,  round(ZB_SETTLE_AMOUNT * 0.25,2) AS zb_amount,0,0.06
            FROM  STLUDR.RVL_ZBTC_ZB_AMOUNT_CONFIG  WHERE  settle_year = year;

        else IF quarter = 4 THEN
            --全年总金额减去前三季度之和
                SET @RPT_SQL     := 'INSERT INTO stludr.rvl_report_zbtc_cfg(SETTLE_YEAR, QUARTER, SETT_PROV, PRODUCT_NAME, SETT_AMOUNT, ADJUST, TAXRATE)
                        WITH
                        annual_total AS (
                                    --全年总金额
                            SELECT
                                settle_year,
                                sett_prov,
                                product_name,
                                ZB_SETTLE_AMOUNT AS zb_amount,
                                0 AS adjust,
                                0.06 AS taxrate
                            FROM STLUDR.RVL_ZBTC_ZB_AMOUNT_CONFIG
                            WHERE settle_year =' || year || '
                        ),
                        -- 前三季度总金额
                        first_three_quarters AS (
                            SELECT
                                SETTLE_YEAR,
                                SETT_PROV,
                                PRODUCT_NAME,
                                round(sum(SETT_AMOUNT), 2) AS q1_q3_amount,
                                ADJUST,
                                TAXRATE
                            FROM stludr.rvl_report_zbtc_cfg
                            WHERE settle_year = ' || year || ' AND QUARTER IN (1, 2, 3)
                            GROUP BY SETTLE_YEAR, SETT_PROV, PRODUCT_NAME, ADJUST, TAXRATE
                        )
                        -- 计算第四季度金额
                        SELECT
                            a.settle_year,
                            4 as quarter,
                            a.sett_prov,
                            a.product_name,
                            a.zb_amount - COALESCE(f.q1_q3_amount, 0) AS q4_amount,
                            a.adjust,
                            a.taxrate
                        FROM annual_total a
                        LEFT JOIN first_three_quarters f ON
                            a.settle_year = f.SETTLE_YEAR AND
                            a.sett_prov = f.SETT_PROV AND
                            a.product_name = f.PRODUCT_NAME';
                SELECT @RPT_SQL;
                PREPARE STMT FROM @RPT_SQL;
                EXECUTE STMT;
                DEALLOCATE PREPARE STMT;
            END IF;
        END IF;

        COMMIT;

        outReturn := 0;
        outSysError := 'OK';


        SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || outReturn as info;

    END;
end ;;
DELIMITER ;