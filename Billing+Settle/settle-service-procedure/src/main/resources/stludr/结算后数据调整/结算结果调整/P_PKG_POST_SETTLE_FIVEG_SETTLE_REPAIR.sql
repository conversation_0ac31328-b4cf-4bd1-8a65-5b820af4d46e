/**
  BIL-ZQ-202407-19-5G融媒结算规则需求
  5G融媒,次年1月根据省内实际收入补出规则，计算成员补出
 */
DROP PROCEDURE IF EXISTS stludr.`P_PKG_POST_SETTLE_FIVEG_SETTLE_REPAIR`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "P_PKG_POST_SETTLE_FIVEG_SETTLE_REPAIR"(
    inMonth          IN   VARCHAR2,
    outSysError      OUT  VARCHAR2,
    outReturn        OUT  NUMBER
  )
AS
    v_table_name VARCHAR2(64);
v_sql VARCHAR2(4000);
v_proc_name   VARCHAR2(50) := 'P_PKG_POST_SETTLE_FIVEG_SETTLE_REPAIR';

-- Define a record type for the result set
TYPE ResultRowType IS RECORD (
      PROV_CODE          VARCHAR2(3),
      OFFER_CODE        VARCHAR2(20),
      PRODUCT_CODE      VARCHAR2(20),
      <PERSON>AR<PERSON>_CODE       VARCHAR2(10),
      POID       		VARCHAR2(20),
      SOID       		VARCHAR2(20),
      MEMBER_SUM        VARCHAR2(64),
      SETTLE_YEAR       VARCHAR2(6),
      SETTLE_FEE		NUMBER(64),
      ORDER_MODE		VARCHAR2(3),
      CUSTOMER_CODE		VARCHAR2(32),
      CUSTOMER_PROV		VARCHAR2(3),
      ORDER_PROV		VARCHAR2(3)
    );
TYPE ResultTableType IS TABLE OF ResultRowType INDEX BY PLS_INTEGER;
v_result_table ResultTableType;
v_counter PLS_INTEGER := 0;
-- 使用游标前需要先声明游标变量
CURSOR cur1 IS SELECT PROV_CODE , BASE_AMOUNT , TARGET_AMOUNT, PROV_TOTAL_AMOUNT FROM RVL_FIVEG_AMOUNT_CONFIG WHERE AMOUNT_YEAR =SUBSTR(inMonth, 1, 4)-1;
v_PROV_CODE RVL_FIVEG_AMOUNT_CONFIG.PROV_CODE%TYPE;
v_BASE_AMOUNT RVL_FIVEG_AMOUNT_CONFIG.BASE_AMOUNT%TYPE;
v_TARGET_AMOUNT RVL_FIVEG_AMOUNT_CONFIG.TARGET_AMOUNT%TYPE;
v_PROV_TOTAL_AMOUNT RVL_FIVEG_AMOUNT_CONFIG.PROV_TOTAL_AMOUNT%TYPE;

BEGIN
    outSysError := '';
    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        outSysError := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg ;
    END;

    BEGIN

        set @vSql := 'delete from ur_eboss_' || inMonth || '_t where offer_code = ''50126'' AND OCCURENCE = ''5G融媒补出'' ';

        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        outReturn := 0;
        outSysError := 'OK';
    END;

    BEGIN
        OPEN cur1;

        LOOP
            FETCH cur1 INTO v_PROV_CODE, v_BASE_AMOUNT, v_TARGET_AMOUNT,v_PROV_TOTAL_AMOUNT;

            EXIT WHEN cur1%NOTFOUND;  -- 当没有更多行时退出循环

            -- 省收入小于基础目标
            IF TO_NUMBER(v_PROV_TOTAL_AMOUNT)  < TO_NUMBER(v_BASE_AMOUNT) THEN
                FOR r IN (
                    SELECT a.prov_code ,a.offer_code,a.product_code,a.charge_code,a.poid,a.soid,TO_CHAR(a.member_sum) member_sum,
                           a.settle_year ,TO_NUMBER(a.member_sum*(b.LT_PRICE-b.GT_PRICE)) settle_fee,a.ORDER_MODE,a.CUSTOMER_CODE,a.CUSTOMER_PROV,a.ORDER_PROV
                    FROM (
                             SELECT PROV_CODE,  OFFER_CODE ,PRODUCT_CODE ,CHARGE_CODE,poid,soid,SUM(MEMBER_COUNT) MEMBER_sum,settle_year,ORDER_MODE,CUSTOMER_CODE,CUSTOMER_PROV,ORDER_PROV
                             FROM RVL_FIVEG_MEMBER WHERE settle_year=SUBSTR(inMonth, 1, 4)-1 GROUP BY PROV_CODE ,CHARGE_CODE,  OFFER_CODE ,PRODUCT_CODE,settle_year,poid,soid,ORDER_MODE,CUSTOMER_CODE,CUSTOMER_PROV,ORDER_PROV
                         )a
                             LEFT JOIN RVL_FiveG_AMOUNT_RULE b
                                       ON
                                           (b.MATCH_TYPE = 'OP' and a.offer_code = b.offer_code and a.product_code = b.product_code
                                               or b.MATCH_TYPE = 'O' and a.offer_code = b.offer_code
                                               or b.MATCH_TYPE = 'OPF' and a.offer_code = b.offer_code and a.product_code = b.product_code and a.charge_code = b.charge_code)
                    WHERE a.prov_code= v_PROV_CODE
                    ) LOOP
                        v_counter := v_counter + 1;
                        v_result_table(v_counter).PROV_CODE := r.prov_code;
                        v_result_table(v_counter).OFFER_CODE := r.offer_code;
                        v_result_table(v_counter).PRODUCT_CODE := r.product_code;
                        v_result_table(v_counter).CHARGE_CODE := r.charge_code;
                        v_result_table(v_counter).POID := r.poid;
                        v_result_table(v_counter).SOID := r.soid;
                        v_result_table(v_counter).MEMBER_SUM := r.member_sum;
                        v_result_table(v_counter).SETTLE_YEAR := r.settle_year;
                        v_result_table(v_counter).SETTLE_FEE := r.settle_fee;
                        v_result_table(v_counter).ORDER_MODE := r.ORDER_MODE;
                        v_result_table(v_counter).CUSTOMER_CODE := r.CUSTOMER_CODE;
                        v_result_table(v_counter).CUSTOMER_PROV := r.CUSTOMER_PROV;
                        v_result_table(v_counter).ORDER_PROV := r.ORDER_PROV;
                    END LOOP;
            ELSIF TO_NUMBER(v_BASE_AMOUNT) <= TO_NUMBER(v_PROV_TOTAL_AMOUNT) AND TO_NUMBER(v_PROV_TOTAL_AMOUNT) < TO_NUMBER(v_TARGET_AMOUNT)  THEN
                FOR r IN (
                    SELECT a.prov_code,a.offer_code,a.product_code,a.charge_code,a.poid,a.soid,TO_CHAR(a.member_sum) member_sum,a.settle_year,
                           TO_NUMBER(a.member_sum*(b.MID_PRICE-b.GT_PRICE)) settle_fee,a.ORDER_MODE,a.CUSTOMER_CODE,a.CUSTOMER_PROV,a.ORDER_PROV
                    FROM (
                             SELECT PROV_CODE,  OFFER_CODE ,PRODUCT_CODE ,CHARGE_CODE,poid,soid,SUM(MEMBER_COUNT) MEMBER_sum,settle_year,ORDER_MODE,CUSTOMER_CODE,CUSTOMER_PROV,ORDER_PROV
                             FROM RVL_FIVEG_MEMBER WHERE settle_year=SUBSTR(inMonth, 1, 4)-1 GROUP BY PROV_CODE ,CHARGE_CODE,  OFFER_CODE ,PRODUCT_CODE,settle_year,poid,soid,ORDER_MODE,CUSTOMER_CODE,CUSTOMER_PROV,ORDER_PROV
                         )a
                             LEFT JOIN RVL_FiveG_AMOUNT_RULE b
                                       ON
                                           (b.MATCH_TYPE = 'OP' and a.offer_code = b.offer_code and a.product_code = b.product_code
                                               or b.MATCH_TYPE = 'O' and a.offer_code = b.offer_code
                                               or b.MATCH_TYPE = 'OPF' and a.offer_code = b.offer_code and a.product_code = b.product_code and a.charge_code = b.charge_code)
                    WHERE a.prov_code= v_PROV_CODE
                    ) LOOP
                        v_counter := v_counter + 1;
                        v_result_table(v_counter).PROV_CODE := r.prov_code;
                        v_result_table(v_counter).OFFER_CODE := r.offer_code;
                        v_result_table(v_counter).PRODUCT_CODE := r.product_code;
                        v_result_table(v_counter).CHARGE_CODE := r.charge_code;
                        v_result_table(v_counter).POID := r.poid;
                        v_result_table(v_counter).SOID := r.soid;
                        v_result_table(v_counter).MEMBER_SUM := r.member_sum;
                        v_result_table(v_counter).SETTLE_YEAR := r.settle_year;
                        v_result_table(v_counter).SETTLE_FEE := r.settle_fee;
                        v_result_table(v_counter).ORDER_MODE := r.ORDER_MODE;
                        v_result_table(v_counter).CUSTOMER_CODE := r.CUSTOMER_CODE;
                        v_result_table(v_counter).CUSTOMER_PROV := r.CUSTOMER_PROV;
                        v_result_table(v_counter).ORDER_PROV := r.ORDER_PROV;
                    END LOOP;
            END IF;
        END LOOP;

        CLOSE cur1;

        -- 根据传入的参数构建表名
        v_table_name := 'UR_EBOSS_' || inMonth || '_T';
        v_sql := 'INSERT INTO ' || v_table_name ||
                 ' (STREAM_ID,INDEX_ID,SOURCE_ID,CUSTOMER_CODE, offer_code, product_code, OFFER_ORDER_ID,PRODUCT_ORDER_ID,ORG_MONTH,SETTLE_MONTH, ' ||
                 'charge_code,CHARGE,ORI_FILE_ID,FILE_ID,PHASE, SETTLE_NOTAXFEE,OUT_OBJECT,IN_OBJECT,DEST_SOURCE,TAX_RATE,ORDER_MODE,FEETYPE,OCCURENCE, ' ||
                 '	CUSTOMER_PROV,ORDER_PROV,TAX_RATE_ORI) ' ||
                 'VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?,?,?,?,?,?)';

--     遍历v_result_table并插入数据
        IF v_result_table.COUNT <> 0 THEN
            FOR i IN v_result_table.FIRST .. v_result_table.LAST LOOP
                    EXECUTE IMMEDIATE v_sql USING   v_result_table(i).POID,inMonth,'3',v_result_table(i).CUSTOMER_CODE,
                        v_result_table(i).offer_code,
                        v_result_table(i).product_code,  v_result_table(i).soid,v_result_table(i).POID,inMonth,inMonth,
                        v_result_table(i).charge_code,  '1','1','1','1',
                        v_result_table(i).settle_fee,v_result_table(i).PROV_CODE,'MG','5','6',v_result_table(i).ORDER_MODE,'0','5G融媒补出',
                        v_result_table(i).CUSTOMER_PROV,v_result_table(i).ORDER_PROV,'6';
                END LOOP;
        END IF;
        COMMIT;

    END;
    SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || outReturn as info;

END;;
DELIMITER ;