/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能： 结算后数据调整-未经storm结算-三次结算（合作伙伴结算）明细数据生成
**/
use stludr;
DROP PROCEDURE IF EXISTS stludr.`P_PKG_POST_SETTLE_SETTLE_PARTNER`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE "P_PKG_POST_SETTLE_SETTLE_PARTNER"(
    inMonth          IN   VARCHAR2,
    outSysError      OUT  VARCHAR2,
    outReturn        OUT  NUMBER
)
IS
    iv_Sql VARCHAR2(2000);
    iv_Sql_bl VARCHAR2(2000);
    iv_Sql_ar VARCHAR2(2000);
    vSql VARCHAR2(2000);
    v_proc_name   VARCHAR2(35) := 'P_PKG_POST_SETTLE_SETTLE_PARTNER';
  BEGIN
    outSysError := 'OK';
    outReturn := 0;
    iv_Sql_bl := '';
    iv_Sql_ar := '';

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        outSysError := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg ;
    END;

    begin

       ----根据二次结算账单数据填写合作伙伴（三次结算）应收结果表
        BEGIN
            set @vSql := 'delete /*+ AUTOCOMMIT_DURING_DML() */ from ur_partner_' || inMonth || '_t ';
            SELECT @vSql;
            PREPARE STMT FROM @vSql;
            EXECUTE STMT;
            DEALLOCATE PREPARE STMT;

            outReturn := 0;
            outSysError := 'OK';
        END;

       call stludr.STL_INFO_LOG(inMonth, '',v_proc_name||'_00', '数据清理完成开始执行开始时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

       -- 插入通用业务订购数据
       BEGIN
            set @vSql := 'insert /*+ AUTOCOMMIT_DURING_DML() */ into ur_partner_' || inMonth || '_t (stream_id, index_id, source_id, customer_code, customer_prov, offer_code, product_code, offer_order_id, product_order_id, order_prov, member_code, member_prov, ' ||
            ' account_id, sign_entity, org_month, paid_month, settle_month, charge_code, charge, occurence, duration, volume, tax_rate, tax_fee, no_tax_fee, out_object_type, out_object, in_object_type, in_object,  ' ||
            'settle_taxfee, settle_notaxfee, rule_id, dest_source, ori_file_id, file_id, phase, order_mode, tax_rate_ori, feetype, adjmonth)'||
            'select seq_mc.nextval stream_id, seq_mc.nextval index_id,source_id, customer_code, customer_prov, offer_code, product_code, offer_order_id, product_order_id, order_prov, member_code, member_prov, ' ||
            ' account_id, sign_entity, org_month, paid_month, settle_month, charge_code, charge, occurence, duration, volume, tax_rate, settle_taxfee, settle_notaxfee, in_object_type, in_object, in_object_type, null,  ' ||
            'settle_taxfee, settle_notaxfee, rule_id, dest_source, ori_file_id, file_id, phase, order_mode, tax_rate_ori, feetype, adjmonth ' ||
            'from ur_eboss_' || inMonth || '_t a where  occurence not in (''YDY'',''ESP'')' ||
            ' and exists (select 1 from rvl_partner_config rpc where a.offer_code=rpc.offer_code and a.product_code=rpc.product_code ' ||
            ' and a.dest_source=rpc.dest_source and rpc.mem_rights=''0'' and sysdate BETWEEN TO_DATE(rpc.eff_month, ''YYYYMM'') AND TO_DATE(rpc.exp_month, ''YYYYMM''))';

           SELECT @vSql;
            PREPARE STMT FROM @vSql;
            EXECUTE STMT;
            DEALLOCATE PREPARE STMT;

            outReturn := 0;
            outSysError := 'OK';
        END;
       call stludr.STL_INFO_LOG(inMonth, '',v_proc_name||'_01', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

       -- 插入部分移动办公产品领取成员权益的订购数据
        BEGIN
            set @vSql := 'insert /*+ AUTOCOMMIT_DURING_DML() */ into ur_partner_' || inMonth || '_t (stream_id, index_id, source_id, customer_code, customer_prov, offer_code, product_code, offer_order_id, product_order_id, order_prov, member_code, member_prov, ' ||
            ' account_id, sign_entity, org_month, paid_month, settle_month, charge_code, charge, occurence, duration, volume, tax_rate, tax_fee, no_tax_fee, out_object_type, out_object, in_object_type, in_object,  ' ||
            'settle_taxfee, settle_notaxfee, rule_id, dest_source, ori_file_id, file_id, phase, order_mode, tax_rate_ori, feetype, adjmonth)'||
            'select seq_mc.nextval stream_id, seq_mc.nextval index_id,source_id, customer_code, customer_prov, offer_code, product_code, offer_order_id, product_order_id, order_prov, member_code, member_prov, ' ||
            ' account_id, sign_entity, org_month, paid_month, settle_month, charge_code, charge, occurence, duration, volume, tax_rate, settle_taxfee, settle_notaxfee, in_object_type, in_object, in_object_type, null,  ' ||
            'settle_taxfee, settle_notaxfee, rule_id, dest_source, ori_file_id, file_id, phase, order_mode, tax_rate_ori, feetype, adjmonth ' ||
            'from ur_eboss_' || inMonth || '_t a where  occurence not in (''YDY'',''ESP'')' ||
            ' and exists (select 1 from rvl_partner_config rpc where a.offer_code=rpc.offer_code and a.product_code=rpc.product_code ' ||
            ' and a.dest_source=rpc.dest_source and rpc.mem_rights=''1'' and sysdate BETWEEN TO_DATE(rpc.eff_month, ''YYYYMM'') AND TO_DATE(rpc.exp_month, ''YYYYMM'') )' ||
            ' and exists (select 1 from stlusers.stl_mem_interests smi where smi.customer_num=a.customer_code and smi.prodist_sku_num=a.product_order_id and smi.member_num = a.member_code)' ;
            SELECT @vSql;
            PREPARE STMT FROM @vSql;
            EXECUTE STMT;
            DEALLOCATE PREPARE STMT;

            outReturn := 0;
            outSysError := 'OK';
        END;
       call stludr.STL_INFO_LOG(inMonth, '',v_proc_name||'_02', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

       -- 计算合作伙伴结算金额并更新到结果表中
       -- 按比例计算-匹配到产品编码
        BEGIN
            set @vSql := ' update /*+ AUTOCOMMIT_DURING_DML() */ ur_partner_' || inMonth || '_t a set (a.settle_notaxfee,a.in_object,a.partner_code) = (select  a.no_tax_fee * rbp.par_rate, rbp.partner_name,rbp.partner_code   ' ||
            ' from rvl_bboss_partner rbp,rvl_partner_config rpc where rpc.fcategory=''1'' and  rbp.product_code=rpc.product_code and rbp.par_chargecode is null  and rbp.product_code=a.product_code)  ' ||
            '  where  exists (select 1 from rvl_bboss_partner rbp,rvl_partner_config rpc where rpc.fcategory=''1'' and  rbp.product_code=rpc.product_code and rbp.par_chargecode is null  and rbp.product_code=a.product_code) and a.in_object is null ';
            SELECT @vSql;
            PREPARE STMT FROM @vSql;
            EXECUTE STMT;
            DEALLOCATE PREPARE STMT;

            outReturn := 0;
            outSysError := 'OK';
        END;
       call stludr.STL_INFO_LOG(inMonth, '',v_proc_name||'_03', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

       -- 按比例计算-匹配到产品编码和费项编码（服务项）
        BEGIN
            set @vSql := ' update /*+ AUTOCOMMIT_DURING_DML() */ ur_partner_' || inMonth || '_t a set (a.settle_notaxfee,a.in_object,a.partner_code) = (select  a.no_tax_fee * rbp.par_rate, rbp.partner_name,rbp.partner_code   ' ||
            ' from rvl_bboss_partner rbp,rvl_partner_config rpc where rpc.fcategory=''1'' and  rbp.product_code=rpc.product_code and rbp.par_chargecode is not null  and rbp.product_code=a.product_code and rbp.par_chargecode=a.charge_code )  ' ||
            '  where  exists (select 1 from rvl_bboss_partner rbp,rvl_partner_config rpc where rpc.fcategory=''1'' and  rbp.product_code=rpc.product_code and rbp.par_chargecode is not null  and rbp.product_code=a.product_code and rbp.par_chargecode=a.charge_code ) and a.in_object is null ';
            SELECT @vSql;
            PREPARE STMT FROM @vSql;
            EXECUTE STMT;
            DEALLOCATE PREPARE STMT;

            outReturn := 0;
            outSysError := 'OK';
        END;
       call stludr.STL_INFO_LOG(inMonth, '',v_proc_name||'_04', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

       -- dict优先以crm归档给计费的合作伙伴中文为准
       BEGIN
            set @vSql  := 'update /*+ AUTOCOMMIT_DURING_DML() */  ur_partner_' || inMonth || '_t rpt join (select rpc.offer_code,rpc.product_code,rdoc.product_order_id,max(rdoc.partner_name) partner_name ' ||
                ' from rvl_partner_config rpc, rvl_dict_order_config rdoc where rpc.fcategory = ''2'' and rpc.offer_code = rdoc.offer_code and rpc.product_code = rdoc.product_code ' ||
                ' group by rpc.offer_code, rpc.product_code, rdoc.product_order_id) a  ' ||
                ' on (rpt.offer_code=a.offer_code and rpt.product_code=a.product_code and rpt.product_order_id=a.product_order_id) ' ||
                ' set rpt.in_object =a.partner_name ';
            SELECT @vSql;
            PREPARE STMT FROM @vSql;
            EXECUTE STMT;
            DEALLOCATE PREPARE STMT;

            outReturn := 0;
            outSysError := 'OK';
        END;
       call stludr.STL_INFO_LOG(inMonth, '',v_proc_name||'_05', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

       -- dict crm没同步的以结算配置表为准
       BEGIN
            set @vSql  := 'update /*+ AUTOCOMMIT_DURING_DML() */ ur_partner_' || inMonth || '_t rpt set rpt.in_object = (select distinct rdc.partner_name  from rvl_partner_config rpc,rvl_dict_config rdc ' ||
                         '  where rpc.fcategory=''2'' and rpt.offer_code=rdc.offer_code and rpt.product_code=rdc.product_code   ' ||
                         '  and rpt.offer_code=rdc.offer_code and rpt.product_code=rdc.product_code and rpt.charge_code=rdc.fee_type )  ' ||
                        ' where  exists (select 1 from rvl_partner_config rpc,rvl_dict_config rdc  ' ||
                        '   where rpc.fcategory=''2'' and rpt.offer_code=rdc.offer_code and rpt.product_code=rdc.product_code   ' ||
                        '   and rpt.offer_code=rdc.offer_code and rpt.product_code=rdc.product_code and rpt.charge_code=rdc.fee_type ) and rpt.in_object is null ';
            SELECT @vSql;
            PREPARE STMT FROM @vSql;
            EXECUTE STMT;
            DEALLOCATE PREPARE STMT;

            outReturn := 0;
            outSysError := 'OK';
        END;
       call stludr.STL_INFO_LOG(inMonth, '',v_proc_name||'_06', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

    END;
        COMMIT;
        SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || outReturn as info;
END ;;
DELIMITER ;
