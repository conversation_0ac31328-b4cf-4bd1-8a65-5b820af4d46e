/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能： 结算后数据调整-未经storm结算-不进行结算，直接将接口表数据写入结果表的业务部分
 1. 移动云（4.16）接口数据写入应实收结果表
 2. 政企行业产品（4.6）接口数据写入应实收结果表
**/
DROP PROCEDURE IF EXISTS stludr.`P_PKG_POST_SETTLE_SETTLE_SKIPPED`;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" PROCEDURE stludr."P_PKG_POST_SETTLE_SETTLE_SKIPPED"(
    inMonth          IN   VARCHAR2,
    inBatch          IN   VARCHAR2,
    flag_version     IN   VARCHAR2,
    reserve1         IN   VARCHAR2,
    reserve2         IN   VARCHAR2,
    proc_out         OUT  VARCHAR2,
    outSysError      OUT  VARCHAR2,
    outReturn        OUT  NUMBER,
    outBL            OUT  VARCHAR2,
    outAR            OUT  VARCHAR2
)
AS
    vSql VARCHAR2(10240);
    v_proc_name   VARCHAR2(35) := 'P_PKG_POST_SETTLE_SETTLE_SKIPPED';
    iv_Sql_ar VARCHAR2(2000);
BEGIN
    outSysError := 'OK';
    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        outSysError := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg ;
    END;



     ----用移动云账单数据填写应收结果表
    BEGIN

        set @vSql := 'delete from ur_recv_' || inMonth || '_t ' ||
              'where occurence = ''YDY''';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        outReturn := 0;
        outSysError := 'OK';
    END;
    call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_YDY_RECV', '移动云应收开始执行时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));
    BEGIN
        set @vSql := 'insert into ur_recv_' || inMonth || '_t(stream_id, index_id, customer_code, offer_code, product_code, offer_order_id, product_order_id, account_id, sign_entity, ' ||
             'org_month, settle_month, charge_code, charge, tax_rate, tax_fee, no_tax_fee, out_object, in_object, settle_taxfee, settle_notaxfee, dest_source, ' ||
             'ori_file_id, file_id, phase, order_mode, feetype, occurence) ' ||
      'select seq_mc.nextval stream_id, seq_mc.nextval index_id, a.customer_province_number customer_code, a.po_id offer_code, ' ||
             'a.one_product_id product_code, a.po_subs_id offer_order_id, a.co_product_id product_order_id, 0 account_id, ' ||
             'a.contract_main sign_entity, a.billing_term org_month, a.billing_term settle_month, a.one_pro_charge_code charge_code, ' ||
             'round(a.fee_val / 10) charge, a.tax_rate * 100 tax_rate, 0 tax_fee, round(a.fee_val / 10) no_tax_fee, ' ||
             'b.settlement_party_out out_object, b.settlement_party_in in_object, 0 settle_taxfee, round(b.settlement_amount / 10) settle_notaxfee, ' ||
             '0 dest_source, 1 ori_file_id, 1 file_id, 2 phase, a.order_mode order_mode, decode(a.fee_flag, 1, 0, 2, 1) feetype, ''YDY'' occurence ' ||
        'from sync_interface_mc_' || inMonth || ' a, sync_interface_mc_p2p_' || inMonth || ' b ' ||
       'where a.id = b.id and a.file_name = b.file_name ' ||
         'and a.status = 0 and b.status = 0 and a.pay_tag = 1';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        outReturn := 0;
        outSysError := 'OK';
    END;

    call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_YDY_RECV', '移动云应收执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));


    ----用ESP账单数据填写应收结果表
    BEGIN
        set @vSql := 'delete from ur_recv_' || inMonth || '_t ' ||
              'where occurence = ''ESP''';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        outReturn := 0;
        outSysError := 'OK';
    END;

    call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_ESP_RECV', 'ESP应收开始执行时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

    BEGIN
        set @vSql := 'insert into ur_recv_' || inMonth || '_t(stream_id, index_id, customer_code, customer_prov, offer_code, product_code, offer_order_id, product_order_id, order_prov, account_id, sign_entity, ' ||
             'org_month, settle_month, charge_code, charge, tax_rate, tax_fee, no_tax_fee, out_object, in_object, settle_taxfee, settle_notaxfee, dest_source, ' ||
             'ori_file_id, file_id, phase, order_mode, feetype, occurence) ' ||
      'select seq_mc.nextval stream_id, seq_mc.nextval index_id, a.eboss_customer_number customer_code, a.address_prov_code customer_prov, a.product_id offer_code, ' ||
             ''''' product_code, a.subs_id offer_order_id, null product_order_id, a.prov_code order_prov, a.account_id account_id, ' ||
             'a.main_contract sign_entity, a.billing_term org_month, a.billing_term settle_month, a.charge_code charge_code, ' ||
             'round(a.fee_val / 10) charge, a.tax_rate * 100 tax_rate, 0 tax_fee, round(a.fee_val / 10) no_tax_fee, ' ||
             'b.settlement_party_out out_object, b.settlement_party_in in_object, 0 settle_taxfee, round(b.settlement_amount / 10) settle_notaxfee, ' ||
             '0 dest_source, 1 ori_file_id, 1 file_id, 2 phase, a.busi_mode order_mode, decode(a.fee_flag, 1, 0, 2, 1) feetype, ''ESP'' occurence ' ||
        'from sync_interface_esp_' || inMonth || ' a, sync_interface_esp_p2p_' || inMonth || ' b ' ||
       'where a.id = b.id and a.file_name = b.file_name ' ||
         'and a.status = 0 and b.status = 0 and a.pay_tag = 0 and a.inner_ec_flag = ''0''';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        outReturn := 0;
        outSysError := 'OK';
    END;

    call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_ESP_RECV', 'ESP应收执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));


    ----用移动云账单数据填写实收结果表
    BEGIN
        set @vSql := 'delete from ur_paid_' || inMonth || '_t ' ||
              'where occurence = ''YDY''';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        outReturn := 0;
        outSysError := 'OK';
    END;

    call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_YDY_PAID', '移动云实收开始执行时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

    BEGIN
        set @vSql := 'insert into ur_paid_' || inMonth || '_t(stream_id, index_id, customer_code, offer_code, product_code, offer_order_id, product_order_id, account_id, sign_entity, ' ||
             'org_month, paid_month, settle_month, charge_code, charge, tax_rate, tax_fee, no_tax_fee, out_object, in_object, settle_taxfee, settle_notaxfee, dest_source, ' ||
             'ori_file_id, file_id, phase, order_mode, feetype, occurence) ' ||
      'select seq_mc.nextval stream_id, seq_mc.nextval index_id, a.customer_province_number customer_code, a.po_id offer_code, ' ||
             'a.one_product_id product_code, a.po_subs_id offer_order_id, a.co_product_id product_order_id, 0 account_id, ' ||
             'a.contract_main sign_entity, a.billing_term org_month, IFNULL(a.pay_term,'''') paid_month, IFNULL(a.pay_term,'''') settle_month, a.one_pro_charge_code charge_code, ' ||
             'round(a.fee_val / 10) charge, a.tax_rate * 100 tax_rate, 0 tax_fee, round(a.fee_val / 10) no_tax_fee, ' ||
             'b.settlement_party_out out_object, b.settlement_party_in in_object, 0 settle_taxfee, round(b.settlement_amount / 10) settle_notaxfee, ' ||
             '0 dest_source, 1 ori_file_id, 1 file_id, 2 phase, a.order_mode order_mode, decode(a.fee_flag, 1, 0, 2, 1) feetype, ''YDY'' occurence ' ||
        'from sync_interface_mc_' || inMonth || ' a, sync_interface_mc_p2p_' || inMonth || ' b ' ||
       'where a.id = b.id and a.file_name = b.file_name ' ||
         'and a.status = 0 and b.status = 0 and a.pay_tag = 2';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        outReturn := 0;
        outSysError := 'OK';
    END;

    call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_YDY_PAID', '移动云实收结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

     ----用ESP账单数据填写实收结果表
    BEGIN
        set @vSql := 'delete from ur_paid_' || inMonth || '_t ' ||
              'where occurence = ''ESP''';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        outReturn := 0;
        outSysError := 'OK';
    END;

    call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_ESP_PAID', 'ESP实收开始执行时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

    BEGIN
        set @vSql := 'insert into ur_paid_' || inMonth || '_t(stream_id, index_id, customer_code, customer_prov, offer_code, product_code, offer_order_id, product_order_id, order_prov, account_id, sign_entity, ' ||
             'org_month, paid_month, settle_month, charge_code, charge, tax_rate, tax_fee, no_tax_fee, out_object, in_object, settle_taxfee, settle_notaxfee, dest_source, ' ||
             'ori_file_id, file_id, phase, order_mode, feetype, occurence) ' ||
      'select seq_mc.nextval stream_id, seq_mc.nextval index_id, a.eboss_customer_number customer_code, a.address_prov_code customer_prov, a.product_id offer_code, ' ||
             ''''' product_code, a.subs_id offer_order_id, null product_order_id, a.prov_code order_prov, a.account_id account_id, ' ||
             'a.main_contract sign_entity, a.billing_term org_month, IFNULL(a.pay_term,'''') paid_month, IFNULL(a.pay_term,'''') settle_month, a.charge_code charge_code, ' ||
             'round(a.fee_val / 10) charge, a.tax_rate * 100 tax_rate, 0 tax_fee, round(a.fee_val / 10) no_tax_fee, ' ||
             'b.settlement_party_out out_object, b.settlement_party_in in_object, 0 settle_taxfee, round(b.settlement_amount / 10) settle_notaxfee, ' ||
             '0 dest_source, 1 ori_file_id, 1 file_id, 2 phase, a.busi_mode order_mode, decode(a.fee_flag, 1, 0, 2, 1) feetype, ''ESP'' occurence ' ||
        'from sync_interface_esp_' || inMonth || ' a, sync_interface_esp_p2p_' || inMonth || ' b ' ||
       'where a.id = b.id and a.file_name = b.file_name ' ||
         'and a.status = 0 and b.status = 0 and a.pay_tag = 1 and a.inner_ec_flag = ''0''';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        outReturn := 0;
        outSysError := 'OK';
    END;

    call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_ESP_PAID', 'ESP实收结束执行开始时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

    --跨省专线卫士，应收模拟实收，2批执行
    CASE inBatch
       WHEN '2' THEN
      BEGIN
        set @vSql := 'delete from ur_paid_' || inMonth || '_t ' ||
              'where offer_code = ''50118'' and product_code=''****************'' ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        outReturn := 0;
        outSysError := 'OK';
      END;

      call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_ZXWS', '专线卫士实收开始执行时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

      BEGIN
        set @vSql := 'insert into ur_paid_' || inMonth || '_t(stream_id, index_id, customer_code, customer_prov, offer_code, product_code, offer_order_id, product_order_id, order_prov, member_code, member_prov, account_id, sign_entity, ' ||
                'org_month, paid_month, settle_month, charge_code, charge, occurence, duration, volume, tax_rate, tax_fee, no_tax_fee, out_object_type, out_object, in_object_type, in_object, settle_taxfee, settle_notaxfee, dest_source, ' ||
                'ori_file_id, file_id, phase, order_mode, feetype, adjmonth) ' ||
        'select stream_id, index_id, customer_code, customer_prov, offer_code, product_code, offer_order_id, product_order_id, order_prov, member_code, member_prov, account_id, sign_entity, ' ||
                'org_month, ' || inMonth || ', settle_month, charge_code, charge, occurence, duration, volume, tax_rate, tax_fee, no_tax_fee, out_object_type, out_object, in_object_type, in_object, settle_taxfee, settle_notaxfee, dest_source, ' ||
                'ori_file_id, file_id, 2 phase, order_mode, feetype, adjmonth '||
            'from ur_recv_' || inMonth || '_t '||
        'where offer_code=''50118'' and product_code=''****************'' and dest_source=''0'' ';

            SELECT @vSql;
            PREPARE STMT FROM @vSql;
            EXECUTE STMT;
            DEALLOCATE PREPARE STMT;

            outReturn := 0;
            outSysError := 'OK';
        END;
      call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_ZXWS', '专线卫士实收结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));

       ELSE  iv_Sql_ar := NULL;


        END CASE;

      /* ----跨省专线业务  应收结果表 填写 实收结果表  相当于应收模拟实收
      execute immediate 'delete from ur_paid_' || inMonth || '_t ' ||
              'where offer_code = ''50118'' and product_code=''****************'' ';

      iv_Sql_ar := 'insert into ur_paid_' || inMonth || '_t(stream_id, index_id, customer_code, customer_prov, offer_code, product_code, offer_order_id, product_order_id, order_prov, member_code, member_prov, account_id, sign_entity, ' ||
             'org_month, paid_month, settle_month, charge_code, charge, occurence, duration, volume, tax_rate, tax_fee, no_tax_fee, out_object_type, out_object, in_object_type, in_object, settle_taxfee, settle_notaxfee, dest_source, ' ||
             'ori_file_id, file_id, phase, order_mode, feetype, adjmonth) ' ||
      'select stream_id, index_id, customer_code, customer_prov, offer_code, product_code, offer_order_id, product_order_id, order_prov, member_code, member_prov, account_id, sign_entity, ' ||
             'org_month, ' || inMonth || ', settle_month, charge_code, charge, occurence, duration, volume, tax_rate, tax_fee, no_tax_fee, out_object_type, out_object, in_object_type, in_object, settle_taxfee, settle_notaxfee, dest_source, ' ||
             'ori_file_id, file_id, phase, order_mode, feetype, adjmonth '||
        'from ur_recv_' || inMonth || '_t '||
       'where offer_code=''50118'' and product_code=''****************'' and dest_source=''0'' ';

       execute immediate iv_Sql_ar;
       commit;
*/
       ----根据二次结算账单数据填写合作伙伴（三次结算）应收结果表
    call P_PKG_POST_SETTLE_SETTLE_PARTNER(inMonth,outSysError,outReturn);



    COMMIT;

    SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || outReturn as info;

END ;;
DELIMITER ;