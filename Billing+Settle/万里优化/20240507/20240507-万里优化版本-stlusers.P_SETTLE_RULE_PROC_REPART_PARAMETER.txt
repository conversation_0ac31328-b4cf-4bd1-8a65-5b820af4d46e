DELIMITER //
CREATE OR REPLACE DEFINER="stlusers"@"10.%" PROCEDURE "P_SETTLE_RULE_PROC_REPART_PARAMETER"(
    inMonth          IN   VARCHAR2,
    batch            IN   VARCHAR2,
    flag_version     IN   VARCHAR2,
    reserve1         IN   VARCHAR2,
    reserve2         IN   VARCHAR2,
    proc_out         IN   VARCHAR2,
    outSysError      OUT  VARCHAR2,
    outReturn        OUT  NUMBER,
    outBL            OUT  NUMBER,
    outAR            OUT  NUMBER
)
AS
    vSql VARCHAR2(10240);
    iv_Seq NUMBER(12);
    iv_Poid VARCHAR2(20);
    iv_Soid VARCHAR2(20);
    iv_Feetype VARCHAR2(4);
    iv_RuleStartMonth CHAR(6);
    iv_RuleEndMonth CHAR(6);
    dyn_Select VARCHAR2(1024);
    iv_Gprs_Main VARCHAR2(10);
    iv_Gprs_Supp VARCHAR2(10);
    iv_Gprs_Supp1 varchar2(10);
    iv_test varchar2(5000);
    iv_prov_cd varchar2(3);
    ivNextMonth varchar2(6);

    v_proc_name   VARCHAR2(36) := 'P_SETTLE_RULE_PROC_REPART_PARAMETER';
cursor cur_prov is select prov_cd from stl_province_cd;
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
outSysError := substr(@p2, 1, 1000);
        outReturn  := -1;
ROLLBACK;


select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg ;
END;

BEGIN

         /*优化内容： call P_SETTLE_RULE_PROC_REPART_PARAMETER_WL2('202307','2','1','','','',@p1,@p2,@p3,@p4);
          1、delete STL_REPART_PARAMETER_T 修改为truncate partition
          2、取消STL_GPRSDOM_RULE的插入排序
          alter table stlusers.STL_CDN_HW_RULE modify RATIO varchar(64);
          alter table stludr.sync_interface_bl_202307 add index idx_pospecnumber(pospecnumber)
          alter table stludr.cust_prod_info add index idx_pc_sc_am(product_code, service_code, acct_month);
          alter table stlusers.stl_national_rate add index idx_am_bt(acct_month, biz_type);
          alter table stludr.ur_cdn_202307_t add index idx_1(rate_back_id,sett_prov,ACCU_VOLUME,ACCU_DURATION);
          alter table stl_cdn_rule modify PRODUCT_CODE varchar(64);
          alter table stludr.ur_cdn_202307_t add key idx_accu_volume(accu_volume);
        alter table stludr.ur_cdn_202307_t add key idx_accu_duration(accu_duration);
        alter table stludr.ur_cdn_202307_t add index idx_rbi_sgn_dt (rate_back_id,sub_group_num,dup_time);
        alter table stludr.UR_GPRSDOM_202307_T add index idx_provcode(provcode);
          */

        outSysError := 'OK';
        outReturn := 0;
        ivNextMonth := to_char(add_months(to_date(inMonth, 'yyyymm'), 1), 'yyyymm');
select concat('1_', now());
call P_SETTLE_RULE_PROC_PARTITION_BUILD(inMonth);

select dictvalue into iv_Gprs_Main from stludr.stl_conf_dict where item = 'gprsdom_main';
select dictvalue into iv_Gprs_Supp from stludr.stl_conf_dict where item = 'gprsdom_supp';
select dictvalue into iv_Gprs_Supp1 from stludr.stl_conf_dict where item = 'gprsdom_supp_1';


--set @vSql := 'DELETE FROM STL_REPART_PARAMETER_T ' || 'WHERE ACCT_MONTH = ''' || inMonth || '''';
set @vSql := 'ALTER TABLE STL_REPART_PARAMETER_T TRUNCATE PARTITION P_'|| ivNextMonth;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
            outSysError := 'OK';

select concat('2_', now());
set div_precision_increment = 7;


            --流量统付（企业流量池）
            set @vSql := 'TRUNCATE TABLE STL_GPRSDOM_RULE';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
            outSysError := 'OK';

      --注意此处与最初存储过程业务不符，按这个逻辑计算ratio总是100，开发注意确认。
           --临时表使用前清空。
truncate table stl_gprsdom_rule_tmp;
--临时表stl_gprsdom_rule_tmp用于存储按省份统计的数据
set @vSql := 'insert into stl_gprsdom_rule_tmp '||
                   'SELECT ACCT_MONTH,EC_CODE,OFFER_CODE,PRODUCT_CODE,OFFER_ORDER_ID,PRODUCT_ORDER_ID, PROVCODE, sum(DATA_VALUE) prov_amount '||
                   'FROM stludr.UR_GPRSDOM_' || inMonth || '_T '||
                   'GROUP BY  ACCT_MONTH,EC_CODE,OFFER_CODE,PRODUCT_CODE,OFFER_ORDER_ID,PRODUCT_ORDER_ID, PROVCODE';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

--使用临时表stl_gprsdom_rule_tmp 的数据汇总全部的数量并计算各省占全部的比例。
set @vSql := 'INSERT INTO STL_GPRSDOM_RULE ' ||
                 '(ORGMONTH, CUSTOMERNUMBER, POSPECNUMBER, SOSPECNUMBER, ' ||
                 'POID, SOID, MEMPROV_CD, AMOUNT, TOTAL_AMOUNT, RATIO) ' ||
                 'SELECT a.ACCT_MONTH, ' ||
                        'a.EC_CODE, ' ||
                        'a.OFFER_CODE, ' ||
                        'a.PRODUCT_CODE, ' ||
                        'a.OFFER_ORDER_ID, ' ||
                        'a.PRODUCT_ORDER_ID, ' ||
                        'a.PROVCODE, ' ||
                        'a.prov_amount, ' ||
                        'b.total_amount, ' ||
                        'round(a.prov_amount / b.total_amount * 100, 7) ratio ' ||
                   'FROM (SELECT ACCT_MONTH, ' ||
                                'EC_CODE, ' ||
                                'OFFER_CODE, ' ||
                                'PRODUCT_CODE, ' ||
                                'OFFER_ORDER_ID, ' ||
                                'PRODUCT_ORDER_ID, ' ||
                                'PROVCODE, ' ||
                                'prov_amount ' ||
                           'FROM  stl_gprsdom_rule_tmp) a, ' ||
                        '(SELECT ACCT_MONTH, ' ||
                                'EC_CODE, ' ||
                                'OFFER_CODE, ' ||
                                'PRODUCT_CODE, ' ||
                                'OFFER_ORDER_ID, ' ||
                                'PRODUCT_ORDER_ID, ' ||
                                'sum(prov_amount) total_amount ' ||
                           'FROM stl_gprsdom_rule_tmp '||
                          'GROUP BY ACCT_MONTH, EC_CODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID) b ' ||
                  'WHERE a.ACCT_MONTH = b.ACCT_MONTH ' ||
                    'AND a.OFFER_ORDER_ID = b.OFFER_ORDER_ID ' ||
                    'AND a.PRODUCT_ORDER_ID = b.PRODUCT_ORDER_ID ' ||
                    'AND b.total_amount <> 0 ';


SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

select concat('22_', now());


select concat('3_', now());

--结给成员归属省97%
INSERT INTO STL_REPART_PARAMETER_T
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*, ft.FEETYPE, 0 dest_source, 1 route_flag
FROM
    (SELECT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
            a.POID poid_inst_id, a.SOID svc_inst_id, d.PROD_ORDER_MODE order_mode, b.RULE_ID rule_id,
            c.RATE_ID rate_id, row_number() over(PARTITION BY a.SOID ORDER BY to_number(a.RATIO))
                  calc_priority, a.MEMPROV_CD object_value, e.tariff_type tariff_type,
            round(a.RATIO * decode(d.prod_order_mode,'1',iv_Gprs_Supp1,'3',iv_Gprs_Supp) / 100,9) rate_value
         /*a.RATIO * iv_Gprs_Supp / 100 rate_value*/, to_date(a.ORGMONTH, 'yyyymm') eff_date, add_months(to_date(
                                                                                                                 a.ORGMONTH, 'yyyymm'), 1) - 1 exp_date, a.ORGMONTH acct_month
     FROM STL_GPRSDOM_RULE a,
          STL_OFFER_T b,
          STL_RATE_T c,
          STL_SERV_BIZ_CODE d,
          STL_REPART_RATE_T e
     WHERE a.POSPECNUMBER = b.OFFER_CODE
       AND b.PRODUCT_CODE = -1
       AND d.PROD_ORDER_MODE = b.ORDER_MODE
       AND b.RULE_ID = c.RULE_ID
       AND inMonth BETWEEN to_char(d.EFFECTIVE_DATE, 'yyyymm')
         AND to_char(d.EXPIRY_DATE, 'yyyymm')
       AND a.SOID = d.ORDER_ID
       AND a.SOID IN (SELECT PRODUCTID FROM STL_PRODUCT_RATERPLAN pc WHERE
             pc.RATERPLAN IN ('1', '2', '3', '5'))
       AND b.DATA_SOURCE = 1
       AND c.RATE_ID = e.RATE_ID
       AND a.ORGMONTH = inMonth) data,
    (SELECT '02' feetype FROM DUAL UNION SELECT '16' FROM DUAL
     UNION SELECT '18' FROM DUAL UNION SELECT '1006' FROM DUAL
     UNION
     SELECT '52' feetype FROM DUAL UNION SELECT '66' FROM DUAL
     UNION SELECT '68' FROM DUAL UNION SELECT '5006' FROM DUAL) ft;
select concat('4_', now());

--结给主办省3%  object_value  null-> object_value 2021.1.4
INSERT INTO STL_REPART_PARAMETER_T
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*, ft.FEETYPE, 0 dest_source, 1 route_flag
FROM
    (SELECT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
            a.POID poid_inst_id, a.SOID svc_inst_id, d.PROD_ORDER_MODE order_mode, b.RULE_ID rule_id,
            c.RATE_ID rate_id, 0 calc_priority, '${EC_PROV_CODE_07}' object_value, e.tariff_type tariff_type,
            iv_Gprs_Main rate_value, to_date(a.ORGMONTH, 'yyyymm') eff_date, add_months(to_date(
                                                                                                a.ORGMONTH, 'yyyymm'), 1) - 1 exp_date, a.ORGMONTH acct_month
     FROM (SELECT DISTINCT ORGMONTH, CUSTOMERNUMBER, POSPECNUMBER, SOSPECNUMBER, POID, SOID FROM STL_GPRSDOM_RULE) a,
          STL_OFFER_T b,
          STL_RATE_T c,
          STL_SERV_BIZ_CODE d,
          STL_REPART_RATE_T e
     WHERE a.POSPECNUMBER = b.OFFER_CODE
       AND b.PRODUCT_CODE = -1
       AND d.PROD_ORDER_MODE = '3' and d.prod_order_mode = b.ORDER_MODE
       AND b.RULE_ID = c.RULE_ID
       AND inMonth BETWEEN to_char(d.EFFECTIVE_DATE, 'yyyymm')
         AND to_char(d.EXPIRY_DATE, 'yyyymm')
       AND a.SOID = d.ORDER_ID
       AND a.SOID IN (SELECT PRODUCTID FROM STL_PRODUCT_RATERPLAN pc WHERE
             pc.RATERPLAN IN ('1', '2', '3', '5'))
       AND b.DATA_SOURCE = 1
       AND c.RATE_ID = e.RATE_ID
       AND a.ORGMONTH = inMonth) data,
    (SELECT '02' feetype FROM DUAL UNION SELECT '16' FROM DUAL
     UNION SELECT '18' FROM DUAL UNION SELECT '1006' FROM DUAL
     UNION
     SELECT '52' feetype FROM DUAL UNION SELECT '66' FROM DUAL
     UNION SELECT '68' FROM DUAL UNION SELECT '5006' FROM DUAL) ft;


select concat('5_', now());

--流量统付（个人流量包-产品）
set @vSql := 'TRUNCATE TABLE STL_GPRSDOM_PER_RULE';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
            outSysError := 'OK';

            set @vSql := 'INSERT INTO STL_GPRSDOM_PER_RULE ' ||
                           '(ORGMONTH, CUSTOMERNUMBER, POSPECNUMBER, SOSPECNUMBER, POID, SOID, ' ||
                           'MEMPROV_CD, FEE, TOTAL_FEE, RATIO) ' ||
                           'SELECT a.orgmonth, ' ||
                           'a.customernumber, ' ||
                           'a.pospecnumber, ' ||
                           'a.sospecnumber, ' ||
                           'a.poid, ' ||
                           'a.soid, ' ||
                           'a.memprov_cd, ' ||
                           'a.orgfee, ' ||
                           'b.total_fee, ' ||
                           'round(a.orgfee / decode(b.total_fee, 0, 1, b.total_fee) * 100, 7) ratio ' ||
                           'FROM ' ||
                          '(SELECT ORGMONTH, ' ||
                                 'CUSTOMERNUMBER, ' ||
                                 'POSPECNUMBER, ' ||
                                 'SOSPECNUMBER, ' ||
                                 'POID, ' ||
                                 'SOID, ' ||
                                 'PROV_CD memprov_cd, ' ||
                                 'sum(AMOUNT) orgfee ' ||
                            'FROM stludr.SYNC_BL_RULE_' || inMonth || ' ' ||
                           'WHERE STATUS = ''0'' ' ||
                             'AND SOSPECNUMBER IN (SELECT PRODUCT_CODE FROM STL_BUSINESS_TYPE ' ||
                                                   'WHERE BIZ_TYPE = ''GPRS'') ' ||
                             'AND DATASOURCE = ''BL'' ' ||
                             'AND ORGMONTH = ' || inMonth || ' ' ||
                           'GROUP BY ORGMONTH, CUSTOMERNUMBER, POSPECNUMBER, SOSPECNUMBER, POID, SOID, PROV_CD) a, ' ||
                          '(SELECT ORGMONTH, ' ||
                                 'CUSTOMERNUMBER, ' ||
                                 'SOSPECNUMBER, ' ||
                                 'POID, ' ||
                                 'SOID, ' ||
                                 'sum(AMOUNT) total_fee ' ||
                            'FROM stludr.SYNC_BL_RULE_' || inMonth || ' ' ||
                           'WHERE STATUS = ''0'' ' ||
                             'AND SOSPECNUMBER IN (SELECT PRODUCT_CODE FROM STL_BUSINESS_TYPE ' ||
                                                   'WHERE BIZ_TYPE = ''GPRS'') ' ||
                             'AND DATASOURCE = ''BL'' ' ||
                             'AND ORGMONTH = ' || inMonth || ' ' ||
                           'GROUP BY ORGMONTH, CUSTOMERNUMBER, SOSPECNUMBER, POID, SOID) b ' ||
                     'WHERE a.poid = b.poid ' ||
                       'AND a.soid = b.soid ' ||
                     'ORDER BY orgmonth, soid';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
            outSysError := 'OK';
select concat('6_', now());

--给成员归属省97%
INSERT INTO STL_REPART_PARAMETER_T
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*, ft.FEETYPE, 0 dest_source, 1 route_flag
FROM
    (SELECT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
            a.POID poid_inst_id, a.SOID svc_inst_id, d.PROD_ORDER_MODE order_mode, b.RULE_ID rule_id,
            c.RATE_ID rate_id, row_number() over(PARTITION BY a.SOID ORDER BY to_number(a.RATIO))
                 calc_priority, a.MEMPROV_CD object_value, e.Tariff_Type tariff_type,
            round(a.RATIO * decode(d.prod_order_mode,'1',iv_Gprs_Supp1,'3',iv_Gprs_Supp) / 100, 9) rate_value
         /*a.RATIO * iv_Gprs_Supp / 100 rate_value*/, to_date(a.ORGMONTH, 'yyyymm') eff_date, add_months(to_date(
                                                                                                                 a.ORGMONTH, 'yyyymm'), 1) - 1 exp_date, a.ORGMONTH acct_month
     FROM STL_GPRSDOM_PER_RULE a,
          STL_OFFER_T b,
          STL_RATE_T c,
          STL_SERV_BIZ_CODE d,
          STL_REPART_RATE_T e
     WHERE a.POSPECNUMBER = b.OFFER_CODE
       AND b.PRODUCT_CODE = -1
       AND d.PROD_ORDER_MODE = b.ORDER_MODE
       AND b.RULE_ID = c.RULE_ID
       AND a.SOID = d.ORDER_ID
       AND inMonth BETWEEN to_char(d.EFFECTIVE_DATE, 'yyyymm')
         AND to_char(d.EXPIRY_DATE, 'yyyymm')
       AND a.SOID IN (SELECT PRODUCTID FROM STL_PRODUCT_RATERPLAN pc WHERE
             pc.RATERPLAN IN ('4'))
       AND b.DATA_SOURCE = 1
       AND c.RATE_ID = e.RATE_ID
       AND a.ORGMONTH = inMonth) data,
    (SELECT '02' feetype FROM DUAL
     UNION SELECT '1006' FROM DUAL
     UNION SELECT '1035' FROM DUAL
     UNION
     SELECT '52' FROM DUAL
     UNION SELECT '5006' FROM DUAL
     UNION SELECT '5035' FROM DUAL) ft;
select concat('7_', now());

-- 给主办省3%   null->'${EC_PROV_CODE_07}'
INSERT INTO STL_REPART_PARAMETER_T
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*, ft.FEETYPE, 0 dest_source, 1 route_flag
FROM
    (SELECT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
            a.POID poid_inst_id, a.SOID svc_inst_id, d.PROD_ORDER_MODE order_mode, b.RULE_ID rule_id,
            c.RATE_ID rate_id, 0 calc_priority, '${EC_PROV_CODE_07}' object_value, e.tariff_type tariff_type,
            iv_Gprs_Main rate_value, to_date(a.ORGMONTH, 'yyyymm') eff_date, add_months(to_date(
                                                                                                a.ORGMONTH, 'yyyymm'), 1) - 1 exp_date, a.ORGMONTH acct_month
     FROM (SELECT DISTINCT ORGMONTH, CUSTOMERNUMBER, POSPECNUMBER, SOSPECNUMBER, POID, SOID FROM STL_GPRSDOM_PER_RULE) a,
          STL_OFFER_T b,
          STL_RATE_T c,
          STL_SERV_BIZ_CODE d,
          STL_REPART_RATE_T e
     WHERE a.POSPECNUMBER = b.OFFER_CODE
       AND b.PRODUCT_CODE = -1
       AND d.PROD_ORDER_MODE = '3' and d.prod_order_mode = b.ORDER_MODE
       AND b.RULE_ID = c.RULE_ID
       AND a.SOID = d.ORDER_ID
       AND inMonth BETWEEN to_char(d.EFFECTIVE_DATE, 'yyyymm')
         AND to_char(d.EXPIRY_DATE, 'yyyymm')
       AND a.SOID IN (SELECT PRODUCTID FROM STL_PRODUCT_RATERPLAN pc WHERE
             pc.RATERPLAN IN ('4'))
       AND b.DATA_SOURCE = 1
       AND c.RATE_ID = e.RATE_ID
       AND a.ORGMONTH = inMonth) data,
    (SELECT '02' feetype FROM DUAL
     UNION SELECT '1006' FROM DUAL
     UNION SELECT '1035' FROM DUAL
     UNION
     SELECT '52' FROM DUAL
     UNION SELECT '5006' FROM DUAL
     UNION SELECT '5035' FROM DUAL) ft;
select concat('8_', now());

--流量统付（个人流量包-成员） null ->'${MEM_PROV_15}'
--结给成员的97%
INSERT INTO STL_REPART_PARAMETER_T
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*, ft.FEETYPE, 0 dest_source, 1 route_flag
FROM
    (SELECT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
            a.POID poid_inst_id, a.SOID svc_inst_id, d.PROD_ORDER_MODE order_mode, b.RULE_ID rule_id,
            c.RATE_ID rate_id, 1 calc_priority,
            '${MEM_PROV_15}' object_value, e.tariff_type tariff_type,
            decode(d.prod_order_mode,'1',iv_Gprs_Supp1,'3',iv_Gprs_Supp) rate_value, to_date(a.ORGMONTH, 'yyyymm') eff_date, add_months(to_date(
                                                                                                                                                a.ORGMONTH, 'yyyymm'), 1) - 1 exp_date, a.ORGMONTH acct_month
     FROM (SELECT DISTINCT POSPECNUMBER, SOSPECNUMBER, POID, SOID, ORGMONTH
           FROM STL_GPRSDOM_PER_RULE) a,
          STL_OFFER_T b,
          STL_RATE_T c,
          STL_SERV_BIZ_CODE d,
          STL_REPART_RATE_T e
     WHERE a.POSPECNUMBER = b.OFFER_CODE
       AND b.PRODUCT_CODE = -1
       AND d.PROD_ORDER_MODE = b.ORDER_MODE
       AND b.RULE_ID = c.RULE_ID
       AND a.SOID = d.ORDER_ID
       AND inMonth BETWEEN to_char(d.EFFECTIVE_DATE, 'yyyymm')
         AND to_char(d.EXPIRY_DATE, 'yyyymm')
       AND a.SOID IN (SELECT PRODUCTID FROM STL_PRODUCT_RATERPLAN pc WHERE
             pc.RATERPLAN IN ('4'))
       AND b.DATA_SOURCE = 1
       AND c.RATE_ID = e.RATE_ID
       AND a.ORGMONTH = inMonth) data,
    (SELECT '04' feetype FROM DUAL
     UNION SELECT '18' FROM DUAL
     UNION
     SELECT '54' FROM DUAL
     UNION SELECT '68' FROM DUAL) ft;
select concat('9_', now());

--结给主办省的3% NULL -> ${EC_PROV_CODE_07}
INSERT INTO STL_REPART_PARAMETER_T
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*, ft.FEETYPE, 0 dest_source, 1 route_flag
FROM
    (SELECT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
            a.POID poid_inst_id, a.SOID svc_inst_id, d.PROD_ORDER_MODE order_mode, b.RULE_ID rule_id,
            c.RATE_ID rate_id, 0 calc_priority,
            '${EC_PROV_CODE_07}' object_value, e.tariff_type tariff_type,
            iv_Gprs_Main rate_value, to_date(a.ORGMONTH, 'yyyymm') eff_date, add_months(to_date(
                                                                                                a.ORGMONTH, 'yyyymm'), 1) - 1 exp_date, a.ORGMONTH acct_month
     FROM (SELECT DISTINCT POSPECNUMBER, SOSPECNUMBER, POID, SOID, ORGMONTH
           FROM STL_GPRSDOM_PER_RULE) a,
          STL_OFFER_T b,
          STL_RATE_T c,
          STL_SERV_BIZ_CODE d,
          STL_REPART_RATE_T e
     WHERE a.POSPECNUMBER = b.OFFER_CODE
       AND b.PRODUCT_CODE = -1
       AND d.PROD_ORDER_MODE = '3' and d.prod_order_mode = b.ORDER_MODE
       AND b.RULE_ID = c.RULE_ID
       AND a.SOID = d.ORDER_ID
       AND inMonth BETWEEN to_char(d.EFFECTIVE_DATE, 'yyyymm')
         AND to_char(d.EXPIRY_DATE, 'yyyymm')
       AND a.SOID IN (SELECT PRODUCTID FROM STL_PRODUCT_RATERPLAN pc WHERE
             pc.RATERPLAN IN ('4'))
       AND b.DATA_SOURCE = 1
       AND c.RATE_ID = e.RATE_ID
       AND a.ORGMONTH = inMonth) data,
    (SELECT '04' feetype FROM DUAL
     UNION SELECT '18' FROM DUAL
     UNION
     SELECT '54' FROM DUAL
     UNION SELECT '68' FROM DUAL) ft;

select concat('9_', now());
--行业WLAN
set @vSql := 'TRUNCATE TABLE STL_WLHY_RULE';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
            outSysError := 'OK';


            set @vSql := 'INSERT INTO STL_WLHY_RULE ' ||
                 '(ORGMONTH, ORDER_MODE, CUSTOMERNUMBER, POSPECNUMBER, SOSPECNUMBER, ' ||
                 'POID, SOID, PROV_CD, AMOUNT, TOTAL_AMOUNT, RATIO) ' ||
                 'SELECT a.ACCT_MONTH, ' ||
                        'a.ORDER_MODE, ' ||
                        'a.EC_CODE, ' ||
                        'a.OFFER_CODE, ' ||
                        'a.PRODUCT_CODE, ' ||
                        'a.OFFER_ORDER_ID, ' ||
                        'a.PRODUCT_ORDER_ID, ' ||
                        'a.SETT_PROV, ' ||
                        'a.prov_amount, ' ||
                        'b.total_amount, ' ||
                        'round(a.prov_amount / b.total_amount * 100, 7) ratio ' ||
                   'FROM (SELECT ACCT_MONTH, ' ||
                                'ORDER_MODE, ' ||
                                'EC_CODE, ' ||
                                'OFFER_CODE, ' ||
                                'PRODUCT_CODE, ' ||
                                'OFFER_ORDER_ID, ' ||
                                'PRODUCT_ORDER_ID, ' ||
                                'SETT_PROV, ' ||
                                'sum(ACCU_DURATION) prov_amount ' ||
                           'FROM stludr.UR_WLHY_' || inMonth || '_T ' ||
                          'GROUP BY ACCT_MONTH, ORDER_MODE, EC_CODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID, SETT_PROV) a, ' ||
                        '(SELECT ACCT_MONTH, ' ||
                                'ORDER_MODE, ' ||
                                'EC_CODE, ' ||
                                'OFFER_CODE, ' ||
                                'PRODUCT_CODE, ' ||
                                'OFFER_ORDER_ID, ' ||
                                'PRODUCT_ORDER_ID, ' ||
                                'sum(ACCU_DURATION) total_amount ' ||
                           'FROM stludr.UR_WLHY_' || inMonth || '_T ' ||
                          'GROUP BY ACCT_MONTH, ORDER_MODE, EC_CODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID) b ' ||
                  'WHERE a.ACCT_MONTH = b.ACCT_MONTH ' ||
                    'AND a.OFFER_ORDER_ID = b.OFFER_ORDER_ID ' ||
                    'AND a.PRODUCT_ORDER_ID = b.PRODUCT_ORDER_ID ' ||
                    'AND b.total_amount <> 0 ' ||
                  'ORDER BY ACCT_MONTH, PRODUCT_ORDER_ID';
select concat('10_', now());
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
            outSysError := 'OK';


          --共享版
INSERT INTO STL_REPART_PARAMETER_T
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*, -1 FEETYPE, 0 dest_source, 1 route_flag
FROM
    (SELECT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
            a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDER_MODE, b.RULE_ID rule_id,
            c.RATE_ID rate_id, row_number() over(PARTITION BY a.SOID ORDER BY to_number(a.RATIO)) - 1
                 calc_priority, a.PROV_CD object_value, '1' tariff_type,
            a.RATIO / 100 rate_value, to_date(a.ORGMONTH, 'yyyymm') eff_date, add_months(to_date(
                                                                                                 a.ORGMONTH, 'yyyymm'), 1) - 1 exp_date, a.ORGMONTH acct_month
     FROM STL_WLHY_RULE a,
          STL_OFFER_T b,
          STL_RATE_T c
     WHERE a.POSPECNUMBER = b.OFFER_CODE
       AND a.POSPECNUMBER in ('50009')
       AND b.PRODUCT_CODE = -1
       AND b.RULE_ID = c.RULE_ID
       AND b.DATA_SOURCE = 1
       AND a.ORGMONTH = inMonth) data;

select concat('11_', now());


--SIM盾
set @vSql := 'TRUNCATE TABLE STL_SIM_RULE';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
            outSysError := 'OK';


            set @vSql := 'INSERT INTO STL_SIM_RULE ' ||
                 '(ORGMONTH, ORDER_MODE, CUSTOMERNUMBER, POSPECNUMBER, SOSPECNUMBER, ' ||
                 'POID, SOID, PROV_CD, AMOUNT, TOTAL_AMOUNT, RATIO) ' ||
                 'SELECT a.ACCT_MONTH, ' ||
                        'a.ORDER_MODE, ' ||
                        'a.EC_CODE, ' ||
                        'a.OFFER_CODE, ' ||
                        'a.PRODUCT_CODE, ' ||
                        'a.OFFER_ORDER_ID, ' ||
                        'a.PRODUCT_ORDER_ID, ' ||
                        'a.SETT_PROV, ' ||
                        'a.prov_amount, ' ||
                        'b.total_amount, ' ||
                        'round(a.prov_amount / b.total_amount * 100 * 0.6, 7) ratio ' ||
                   'FROM (SELECT ACCT_MONTH, ' ||
                                'PROD_ORDER_MODE ORDER_MODE, ' ||
                                'EC_CODE, ' ||
                                'OFFER_CODE, ' ||
                                'PRODUCT_CODE, ' ||
                                'OFFER_ORDER_ID, ' ||
                                'PRODUCT_ORDER_ID, ' ||
                                'MEM_PROVCODE SETT_PROV, ' ||
                                'COUNT(*) prov_amount ' ||
                           'FROM stludr.UR_SIM_' || inMonth || '_T ' ||
                          'GROUP BY ACCT_MONTH, PROD_ORDER_MODE, EC_CODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID, MEM_PROVCODE) a, ' ||
                        '(SELECT ACCT_MONTH, ' ||
                                'PROD_ORDER_MODE ORDER_MODE, ' ||
                                'EC_CODE, ' ||
                                'OFFER_CODE, ' ||
                                'PRODUCT_CODE, ' ||
                                'OFFER_ORDER_ID, ' ||
                                'PRODUCT_ORDER_ID, ' ||
                                'COUNT(*) total_amount ' ||
                           'FROM stludr.UR_SIM_' || inMonth || '_T ' ||
                          'GROUP BY ACCT_MONTH, PROD_ORDER_MODE, EC_CODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID) b ' ||
                  'WHERE a.ACCT_MONTH = b.ACCT_MONTH ' ||
                    'AND a.OFFER_ORDER_ID = b.OFFER_ORDER_ID ' ||
                    'AND a.PRODUCT_ORDER_ID = b.PRODUCT_ORDER_ID ' ||
                    'AND b.total_amount <> 0 ' ||
                  'ORDER BY ACCT_MONTH, PRODUCT_ORDER_ID';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
select concat('12_', now());
outReturn := 0;
            outSysError := 'OK';



insert into stl_sim_rule
select orgmonth, order_mode, customernumber, pospecnumber, poid, sospecnumber, soid,
       decode(order_mode, 1, '000', 3,'${EC_PROV_CODE_07}'), null, null, 20
from stl_sim_rule
where orgmonth = inMonth
group by orgmonth, order_mode, customernumber, pospecnumber, poid, sospecnumber, soid
union all
select orgmonth, order_mode, customernumber, pospecnumber, poid, sospecnumber, soid,
       '020', null, null, 20
from stl_sim_rule
where orgmonth = inMonth
group by orgmonth, order_mode, customernumber, pospecnumber, poid, sospecnumber, soid;
select concat('13_', now());
INSERT INTO STL_REPART_PARAMETER_T
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*, -1 FEETYPE, 0 dest_source, 1 route_flag
FROM
    (SELECT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,
            a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDER_MODE, b.RULE_ID rule_id,
            c.RATE_ID rate_id, row_number() over(PARTITION BY a.SOID ORDER BY to_number(a.RATIO)) - 1
                    calc_priority, a.PROV_CD object_value, '1' tariff_type,
            a.RATIO / 100 rate_value, to_date(a.ORGMONTH, 'yyyymm') eff_date, add_months(to_date(
                                                                                                 a.ORGMONTH, 'yyyymm'), 1) - 1 exp_date, a.ORGMONTH acct_month
     FROM STL_SIM_RULE a,
          STL_OFFER_T b,
          STL_RATE_T c
     WHERE a.POSPECNUMBER = b.OFFER_CODE
       AND a.POSPECNUMBER in ('50015')
       AND b.PRODUCT_CODE = -1
       AND b.RULE_ID = c.RULE_ID
       AND b.DATA_SOURCE = 1
       AND a.ORGMONTH = inMonth
       and a.order_mode = b.order_mode) data;
select concat('14_', now());
--企业互联网电视（基础产品）
INSERT INTO STL_REPART_PARAMETER_T
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, '4040109' offer_code, '4010901' product_code,
       a.POID poid_inst_id, a.SOID svc_inst_id, d.PROD_ORDER_MODE order_mode, b.RULE_ID rule_id,
       c.RATE_ID rate_id, 0 calc_priority, a.ORDERPROV object_value, '1' tariff_type,
       '1' rate_value, to_date(a.ORGMONTH, 'yyyymm') eff_date, add_months(to_date(
                                                                                  a.ORGMONTH, 'yyyymm'), 1) - 1 exp_date, a.ORGMONTH acct_month, '-1' charge_item, 0 dest_source,
       1 route_flag
FROM STL_INTER_ENT_TV a,
     STL_OFFER_T b,
     STL_RATE_T c,
     STL_SERV_BIZ_CODE d
WHERE b.OFFER_CODE = '4040109'
  AND b.PRODUCT_CODE = '4010901'
  AND d.PROD_ORDER_MODE = b.ORDER_MODE
  AND b.RULE_ID = c.RULE_ID
  AND a.SOID = d.ORDER_ID
  AND inMonth BETWEEN to_char(d.EFFECTIVE_DATE, 'yyyymm')
    AND to_char(d.EXPIRY_DATE, 'yyyymm')
  AND b.DATA_SOURCE = 5
  AND a.ORGMONTH = inMonth;
select concat('15_', now());

----云MAS

---异网大于本网且异网结算方家数为2的
--受理模式
set @vSql := 'INSERT INTO STLUSERS.STL_REPART_PARAMETER_T ' ||
                         'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.* FROM ' ||
                         '(SELECT a.pospecnumber, a.sospecnumber, ' ||
                               'a.offer_order_id poid_inst_id, a.product_order_id svc_inst_id, a.ordermode, b.RULE_ID rule_id, ' ||
                               'c.RATE_ID rate_id, row_number() over(PARTITION BY a.offer_order_id ' ||
                               'ORDER BY a.RATE) calc_priority, a.oth_sp object_value, ' ||
                               'e.tariff_type tariff_type, decode(row_number() over(PARTITION BY a.offer_order_id ' ||
                               'ORDER BY a.RATE), 1, a.rate, ''$'') rate_value, ' ||
                               'to_date(a.orgmonth, ''yyyymm'') eff_date, add_months(to_date( ' ||
                               'a.orgmonth, ''yyyymm''), 1) - 1 exp_date, a.orgmonth, ''-1'' charge_item, ' ||
                               'decode(a.charge_code, ''1040'', 97, ''3870'', 96) dest_source, decode(a.charge_code, ''1040'', 1, ''3870'', 0)  route_flag ' ||
                          'FROM stludr.cmas_swap_02 a, ' ||
                               'STL_OFFER_T b, ' ||
                               'STL_RATE_T c, ' ||
                               'STL_REPART_RATE_T e ' ||
                         'WHERE a.pospecnumber = b.OFFER_CODE ' ||
                           'and b.order_mode = ''1'' ' ||
                           'AND a.product_order_id IS NULL ' ||
                           'AND b.RULE_ID = c.RULE_ID ' ||
                           'AND c.RATE_ID = e.RATE_ID ' ||
                           'AND e.MATCH_MODE = 1 ' ||
                           'and e.tariff_type = 1 ' ||
                           'AND b.DATA_SOURCE = 1 ' ||
                           'AND c.RATE_TYPE = 3 ' ||
                           'and a.ordermode = b.order_mode ' ||
                           'AND ''' || inMonth || ''' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') ' ||
                           'and ''' || inMonth || ''' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') ' ||
                           'AND a.orgmonth = ''' || inMonth || ''' ' ||
                           'AND (a.pospecnumber IN (SELECT DISTINCT OFFER_CODE FROM STL_BUSINESS_TYPE ' ||
                                                 'WHERE BIZ_TYPE IN (''CMAS1'')) or a.sospecnumber = ''110151'') ' ||
                          'UNION ALL ' ||
                       'SELECT DISTINCT a.pospecnumber, a.sospecnumber, ' ||
                               'a.offer_order_id poid_inst_id, a.product_order_id svc_inst_id, a.ORDERMODE, b.RULE_ID rule_id, ' ||
                               'c.RATE_ID rate_id, row_number() over(PARTITION BY a.offer_order_id ' ||
                               'ORDER BY a.RATE) calc_priority, a.oth_sp object_value, ' ||
                               'e.tariff_type tariff_type, decode(row_number() over(PARTITION BY a.offer_order_id, a.product_order_id ' ||
                               'ORDER BY a.RATE), 1, a.rate, ''$'') rate_value, ' ||
                               'to_date(a.orgmonth, ''yyyymm'') eff_date, add_months(to_date( ' ||
                               'a.orgmonth, ''yyyymm''), 1) - 1 exp_date, a.orgmonth, ''-1'' charge_item, ' ||
                               'decode(a.charge_code, ''1040'', 97, ''3870'', 96) dest_source, decode(a.charge_code, ''1040'', 1, ''3870'', 0)  route_flag ' ||
                          'FROM stludr.cmas_swap_02 a, ' ||
                               'STL_OFFER_T b, ' ||
                               'STL_RATE_T c, ' ||
                               'STL_REPART_RATE_T e ' ||
                         'WHERE a.pospecnumber = b.OFFER_CODE ' ||
                           'and b.order_mode = ''1'' ' ||
                           'AND (a.sospecnumber = b.PRODUCT_CODE OR a.sospecnumber IS NULL) ' ||
                           'AND a.product_order_id IS NOT NULL ' ||
                           'AND b.RULE_ID = c.RULE_ID ' ||
                           'AND c.RATE_ID = e.RATE_ID ' ||
                           'AND e.MATCH_MODE = 2 ' ||
                           'and e.tariff_type = 1 ' ||
                           'AND b.DATA_SOURCE = 1 ' ||
                           'AND c.RATE_TYPE = 3 ' ||
                           'and a.ordermode = b.order_mode ' ||
                           'AND ''' || inMonth || ''' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') ' ||
                           'and ''' || inMonth || ''' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') ' ||
                           'AND a.orgmonth = ''' || inMonth || ''' ' ||
                           'AND (a.pospecnumber IN (SELECT DISTINCT OFFER_CODE FROM STL_BUSINESS_TYPE ' ||
                                   'WHERE BIZ_TYPE IN (''CMAS1'')) or a.sospecnumber = ''110151'')) data';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
select concat('16_', now());
outReturn := 0;
            outSysError := 'OK';


         ---异网不大于本网或异网大于本网但异网结算方家数为1的
         --受理模式1
                 set @vSql := 'INSERT INTO STLUSERS.STL_REPART_PARAMETER_T ' ||
                              'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.* FROM ' ||
                              '(SELECT DISTINCT a.pospecnumber, a.sospecnumber, ' ||
                                    'a.poid poid_inst_id, a.soid svc_inst_id, a.ordermode, b.RULE_ID rule_id, ' ||
                                    'c.RATE_ID rate_id, 0 calc_priority, a.prov_cd object_value, ' ||
                                    'e.tariff_type tariff_type, sum(a.amount) rate_value, ' ||
                                    'to_date(a.orgmonth, ''yyyymm'') eff_date, add_months(to_date( ' ||
                                    'a.orgmonth, ''yyyymm''), 1) - 1 exp_date, a.orgmonth, ''-1'' charge_item, ' ||
                                    'decode(a.feetype, ''1040'', 97, ''3870'', 96) dest_source, decode(a.feetype, ''1040'', 1, ''3870'', 0)  route_flag ' ||
                               'FROM stludr.SYNC_BL_RULE_' || inMonth || ' a, ' ||
                                    'STL_OFFER_T b, ' ||
                                    'STL_RATE_T c, ' ||
                                    'STL_REPART_RATE_T e ' ||
                              'WHERE a.pospecnumber = b.OFFER_CODE ' ||
                                'and b.order_mode = ''1'' ' ||
                                'AND a.soid IS NULL ' ||
                                'AND b.RULE_ID = c.RULE_ID ' ||
                                'AND c.RATE_ID = e.RATE_ID ' ||
                                'AND e.MATCH_MODE = 1 ' ||
                                'and e.tariff_type = 2 ' ||
                                'AND b.DATA_SOURCE = 1 ' ||
                                'AND c.RATE_TYPE = 3 ' ||
                                'and a.ordermode = b.order_mode ' ||
                                'and a.status = ''0'' ' ||
                                'AND ' || inMonth || ' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') ' ||
                                'and ' || inMonth || ' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') ' ||
                                'AND a.orgmonth = ' || inMonth || ' ' ||
                                'AND (a.pospecnumber IN (SELECT DISTINCT OFFER_CODE FROM STL_BUSINESS_TYPE ' ||
                                                      'WHERE BIZ_TYPE IN (''CMAS1'')) or a.sospecnumber = ''110151'') ' ||
                              'GROUP BY a.pospecnumber, a.sospecnumber, a.poid, a.soid, a.ORDERMODE, b.RULE_ID, ' ||
                                    'c.RATE_ID, e.tariff_type, a.orgmonth, a.feetype, a.prov_cd ' ||
                               'UNION ALL ' ||
                            'SELECT DISTINCT a.pospecnumber, a.sospecnumber, ' ||
                                    'a.poid poid_inst_id, a.soid svc_inst_id, a.ORDERMODE, b.RULE_ID rule_id, ' ||
                                    'c.RATE_ID rate_id, 0 calc_priority, a.prov_cd object_value, ' ||
                                    'e.tariff_type tariff_type, sum(a.amount) rate_value, ' ||
                                    'to_date(a.orgmonth, ''yyyymm'') eff_date, add_months(to_date( ' ||
                                    'a.orgmonth, ''yyyymm''), 1) - 1 exp_date, a.orgmonth, ''-1'' charge_item, ' ||
                                    'decode(a.feetype, ''1040'', 97, ''3870'', 96) dest_source, decode(a.feetype, ''1040'', 1, ''3870'', 0) route_flag ' ||
                               'FROM stludr.SYNC_BL_RULE_' || inMonth || ' a, ' ||
                                    'STL_OFFER_T b, ' ||
                                    'STL_RATE_T c, ' ||
                                    'STL_REPART_RATE_T e ' ||
                              'WHERE a.pospecnumber = b.OFFER_CODE ' ||
                                'and b.order_mode = ''1'' ' ||
                                'AND (a.sospecnumber = b.PRODUCT_CODE OR a.sospecnumber IS NULL) ' ||
                                'AND a.soid IS NOT NULL ' ||
                                'AND b.RULE_ID = c.RULE_ID ' ||
                                'AND c.RATE_ID = e.RATE_ID ' ||
                                'AND e.MATCH_MODE = 2 ' ||
                                'and e.tariff_type = 2 ' ||
                                'AND b.DATA_SOURCE = 1 ' ||
                                'AND c.RATE_TYPE = 3 ' ||
                                'and a.ordermode = b.order_mode ' ||
                                'and a.status = ''0'' ' ||
                                'AND ' || inMonth || ' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') ' ||
                                'and ' || inMonth || ' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') ' ||
                                'AND a.orgmonth = ' || inMonth || ' ' ||
                                'AND (a.pospecnumber IN (SELECT DISTINCT OFFER_CODE FROM STL_BUSINESS_TYPE ' ||
                                                      'WHERE BIZ_TYPE IN (''CMAS1'')) or a.sospecnumber = ''110151'') ' ||
                              'GROUP BY a.pospecnumber, a.sospecnumber, a.poid, a.soid, a.ORDERMODE, b.RULE_ID, ' ||
                                    'c.RATE_ID, e.tariff_type, a.orgmonth, a.feetype, a.prov_cd) data';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
select concat('17_', now());
outReturn := 0;
            outSysError := 'OK';



            /*--受理模式3
            set @vSql := 'INSERT INTO STLUSERS.STL_REPART_PARAMETER_T ' ||
                         'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.* FROM ' ||
                         '(SELECT DISTINCT a.pospecnumber, a.sospecnumber, ' ||
                               'a.poid poid_inst_id, a.soid svc_inst_id, a.ordermode, b.RULE_ID rule_id, ' ||
                               'c.RATE_ID rate_id, 0 calc_priority, a.prov_cd object_value, ' ||
                               'e.tariff_type tariff_type, sum(a.amount) rate_value, ' ||
                               'to_date(a.orgmonth, ''yyyymm'') eff_date, add_months(to_date( ' ||
                               'a.orgmonth, ''yyyymm''), 1) - 1 exp_date, a.orgmonth, ''-1'' charge_item, ' ||
                               'decode(a.feetype, ''1040'', 97, ''3870'', 96) dest_source, decode(a.feetype, ''1040'', 1, ''3870'', 0) route_flag ' ||
                          'FROM stludr.SYNC_BL_RULE_' || inMonth || ' a, ' ||
                               'STL_OFFER_T b, ' ||
                               'STL_RATE_T c, ' ||
                               'STL_REPART_RATE_T e ' ||
                         'WHERE a.pospecnumber = b.OFFER_CODE ' ||
                           'and b.order_mode = ''3'' ' ||
                           'AND a.soid IS NULL ' ||
                           'AND b.RULE_ID = c.RULE_ID ' ||
                           'AND c.RATE_ID = e.RATE_ID ' ||
                           'AND e.MATCH_MODE = 1 ' ||
                   'and e.tariff_type = 2 ' ||-----------new
                           'AND b.DATA_SOURCE = 1 ' ||
                           'AND c.RATE_TYPE = 3 ' ||
                           'and a.ordermode = b.order_mode ' ||
                           'and a.status = ''0'' ' ||
                           'AND ' || inMonth || ' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') ' ||
                           'and ' || inMonth || ' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') ' ||
                           'AND a.orgmonth = ' || inMonth || ' ' ||
                           'AND (a.pospecnumber IN (SELECT DISTINCT OFFER_CODE FROM STL_BUSINESS_TYPE ' ||
                                                 'WHERE BIZ_TYPE IN (''CMAS1'')) or a.sospecnumber = ''110151'') ' ||
                         'GROUP BY a.pospecnumber, a.sospecnumber, a.poid, a.soid, a.ORDERMODE, b.RULE_ID, ' ||
                               'c.RATE_ID, e.tariff_type, a.orgmonth, a.feetype, a.prov_cd ' ||
                          'UNION ALL ' ||
                       'SELECT DISTINCT a.pospecnumber, a.sospecnumber, ' ||
                               'a.poid poid_inst_id, a.soid svc_inst_id, a.ORDERMODE, b.RULE_ID rule_id, ' ||
                               'c.RATE_ID rate_id, 0 calc_priority, a.prov_cd object_value, ' ||
                               'e.tariff_type tariff_type, sum(a.amount) rate_value, ' ||
                               'to_date(a.orgmonth, ''yyyymm'') eff_date, add_months(to_date( ' ||
                               'a.orgmonth, ''yyyymm''), 1) - 1 exp_date, a.orgmonth, ''-1'' charge_item, ' ||
                               'decode(a.feetype, ''1040'', 97, ''3870'', 96) dest_source, decode(a.feetype, ''1040'', 1, ''3870'', 0)  route_flag ' ||
                          'FROM stludr.SYNC_BL_RULE_' || inMonth || ' a, ' ||
                               'STL_OFFER_T b, ' ||
                               'STL_RATE_T c, ' ||
                               'STL_REPART_RATE_T e ' ||
                         'WHERE a.pospecnumber = b.OFFER_CODE ' ||
                           'and b.order_mode = ''3'' ' ||
                           'AND (a.sospecnumber = b.PRODUCT_CODE OR a.sospecnumber IS NULL) ' ||
                           'AND a.soid IS NOT NULL ' ||
                           'AND b.RULE_ID = c.RULE_ID ' ||
                           'AND c.RATE_ID = e.RATE_ID ' ||
                           'AND e.MATCH_MODE = 2 ' ||
                   'and e.tariff_type = 2 ' || ------------new
                           'AND b.DATA_SOURCE = 1 ' ||
                           'AND c.RATE_TYPE = 3 ' ||
                           'and a.ordermode = b.order_mode ' ||
                           'and a.status = ''0'' ' ||
                           'AND ' || inMonth || ' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') ' ||
                           'and ' || inMonth || ' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') ' ||
                           'AND a.orgmonth = ' || inMonth || ' ' ||
                           'AND (a.pospecnumber IN (SELECT DISTINCT OFFER_CODE FROM STL_BUSINESS_TYPE ' ||
                                                 'WHERE BIZ_TYPE IN (''CMAS1''))  or a.sospecnumber = ''110151'') ' ||
                         'GROUP BY a.pospecnumber, a.sospecnumber, a.poid, a.soid, a.ORDERMODE, b.RULE_ID, ' ||
                               'c.RATE_ID, e.tariff_type, a.orgmonth, a.feetype, a.prov_cd) data';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
select concat('18_', now());
outReturn := 0;
            outSysError := 'OK';*/




            --本网 null->${EC_PROV_CODE_07}
            set @vSql := 'INSERT INTO STL_REPART_PARAMETER_T ' ||
                         'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, t.* FROM ' ||
                         '(SELECT DISTINCT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,  ' ||
                                'a.POID poid_inst_id, a.SOID svc_inst_id, a.ordermode, b.RULE_ID rule_id, ' ||
                                'c.RATE_ID rate_id, 1 calc_priority, ''${EC_PROV_CODE_07}'' object_value,  ' ||
                                'e.tariff_type tariff_type,  ''$'' rate_value,  ' ||
                                'to_date(a.ORGMONTH, ''yyyymm'') eff_date, add_months(to_date(  ' ||
                                'a.ORGMONTH, ''yyyymm''), 1) - 1 exp_date, a.ORGMONTH, ''-1'' charge_item, decode(a.ordermode, ''1'', 0, ''3'', 98) dest_source, ' ||
                                '0 route_flag ' ||
                           'FROM stludr.SYNC_INTERFACE_BL_' || inMonth || ' a,  ' ||
                                'STL_OFFER_T b,  ' ||
                                'STL_RATE_T c,  ' ||
                                'STL_REPART_RATE_T e  ' ||
                         'WHERE a.POSPECNUMBER = b.OFFER_CODE  ' ||
                            'AND A.SOID IS NULL  ' ||
                            'and a.poid not in (select distinct offer_order_id from stludr.cmas_swap_02) ' ||
                            'AND b.RULE_ID = c.RULE_ID  ' ||
                            'AND c.RATE_ID = e.RATE_ID  ' ||
                            'AND e.MATCH_MODE = 1 ' ||
                            'and e.tariff_type = 2 ' ||
                            'AND c.RATE_TYPE = 3 ' ||
							'and a.ordermode = ''1'' ' ||
                            'and a.ordermode = b.order_mode ' ||
                            'AND ' || inMonth || ' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') ' ||
                            'and ' || inMonth || ' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') ' ||
                            'AND (a.POSPECNUMBER IN (SELECT DISTINCT OFFER_CODE FROM stlusers.STL_BUSINESS_TYPE ' ||
                                                         'WHERE BIZ_TYPE IN (''CMAS1''))  or a.sospecnumber = ''110151'') ' ||
                            'AND decode(a.DATASOURCE, ''BL'', ''1'', ''AR'', ''2'') = b.DATA_SOURCE  ' ||
                            'AND a.ORGMONTH = ' || inMonth || ' ' ||
                            'UNION ALL ' ||
                         'SELECT DISTINCT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,  ' ||
                                'a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERMODE, b.RULE_ID rule_id, ' ||
                                'c.RATE_ID rate_id, 1 calc_priority, ''${EC_PROV_CODE_07}'' object_value,  ' ||
                                'e.tariff_type tariff_type,  ''$'' rate_value,  ' ||
                                'to_date(a.ORGMONTH, ''yyyymm'') eff_date, add_months(to_date(  ' ||
                                'a.ORGMONTH, ''yyyymm''), 1) - 1 exp_date, a.ORGMONTH, ''-1'' charge_item, decode(a.ordermode, ''1'', 0, ''3'', 98) dest_source, ' ||
                                '0 route_flag ' ||
                           'FROM stludr.SYNC_INTERFACE_BL_' || inMonth || ' a,  ' ||
                                'STL_OFFER_T b,  ' ||
                                'STL_RATE_T c,  ' ||
                                'STL_REPART_RATE_T e  ' ||
                         'WHERE a.POSPECNUMBER = b.OFFER_CODE  ' ||
                            'AND a.SOSPECNUMBER = b.PRODUCT_CODE ' ||
                            'AND a.SOID IS NOT NULL ' ||
                            'and a.soid not in (select distinct product_order_id from stludr.cmas_swap_02 where product_order_id is not null) ' ||
                            'AND b.RULE_ID = c.RULE_ID  ' ||
                            'AND c.RATE_ID = e.RATE_ID  ' ||
                            'AND e.MATCH_MODE = 2 ' ||
                            'and e.tariff_type = 2 ' ||
                            'AND c.RATE_TYPE = 3 ' ||
							'and a.ordermode = ''1'' ' ||
                            'and a.ordermode = b.order_mode ' ||
                            'AND ' || inMonth || ' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') ' ||
                            'and ' || inMonth || ' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') ' ||
                            'AND (a.POSPECNUMBER IN (SELECT DISTINCT OFFER_CODE FROM stlusers.STL_BUSINESS_TYPE ' ||
                                                         'WHERE BIZ_TYPE IN (''CMAS1''))  or a.sospecnumber = ''110151'') ' ||
                            'AND decode(a.DATASOURCE, ''BL'', ''1'', ''AR'', ''2'') = b.DATA_SOURCE  ' ||
                            'AND a.ORGMONTH = ' || inMonth || ') t';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
select concat('19_', now());
outReturn := 0;
            outSysError := 'OK';



         /* --云MAS（受理模式5）   2024.1.4注释    原因： 望京是注释掉的
          --一次结算
          --异网
            set @vSql := 'INSERT INTO STLUSERS.STL_REPART_PARAMETER_T ' ||
                         'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.* FROM ' ||
                         '(SELECT DISTINCT a.pospecnumber, a.sospecnumber, ' ||
                               'a.poid poid_inst_id, a.soid svc_inst_id, a.ordermode, b.RULE_ID rule_id, ' ||
                               'c.RATE_ID rate_id, 0 calc_priority, a.prov_cd object_value, ' ||
                               'e.tariff_type tariff_type, sum(a.amount) rate_value, ' ||
                               'to_date(a.orgmonth, ''yyyymm'') eff_date, add_months(to_date( ' ||
                               'a.orgmonth, ''yyyymm''), 1) - 1 exp_date, a.orgmonth, ''-1'' charge_item, ' ||
                               '97 dest_source, 1 route_flag ' ||
                          'FROM stludr.SYNC_BL_RULE_' || inMonth || ' a, ' ||
                               'STL_OFFER_T b, ' ||
                               'STL_RATE_T c, ' ||
                               'STL_REPART_RATE_T e ' ||
                         'WHERE a.pospecnumber = b.OFFER_CODE ' ||
                           'AND b.PRODUCT_CODE = ''-1'' ' ||
                           'AND b.RULE_ID = c.RULE_ID ' ||
                           'AND c.RATE_ID = e.RATE_ID ' ||
                           'AND e.MATCH_MODE = 2 ' ||
                           'AND b.DATA_SOURCE = 1 ' ||
                           'AND c.RATE_TYPE = 3 ' ||
                           'and a.ordermode = b.order_mode ' ||
                           'and a.status = ''0'' ' ||
                           'AND ' || inMonth || ' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') ' ||
                           'AND a.orgmonth = ' || inMonth || ' ' ||
                           'AND a.pospecnumber IN (SELECT DISTINCT OFFER_CODE FROM STL_BUSINESS_TYPE ' ||
                                                 'WHERE BIZ_TYPE IN (''CMAS5'')) ' ||
                         'GROUP BY a.pospecnumber, a.sospecnumber, a.poid, a.soid, a.ORDERMODE, b.RULE_ID, ' ||
                               'c.RATE_ID, e.tariff_type, a.orgmonth, a.feetype, a.prov_cd) data';
            SELECT @vSql;
            PREPARE STMT FROM @vSql;
            EXECUTE STMT;
            DEALLOCATE PREPARE STMT;
    select concat('20_', now());
            outReturn := 0;
            outSysError := 'OK';


            --本网
            set @vSql := 'INSERT INTO STL_REPART_PARAMETER_T ' ||
                         'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, t.* FROM ' ||
                         '(SELECT DISTINCT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,  ' ||
                                'a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERMODE, b.RULE_ID rule_id, ' ||
                                'c.RATE_ID rate_id, 1 calc_priority, ''NULL'' object_value,  ' ||
                                'e.tariff_type tariff_type,  ''$'' rate_value,  ' ||
                                'to_date(a.ORGMONTH, ''yyyymm'') eff_date, add_months(to_date(  ' ||
                                'a.ORGMONTH, ''yyyymm''), 1) - 1 exp_date, a.ORGMONTH, ''-1'' charge_item, 98 dest_source, ' ||
                                '0 route_flag ' ||
                           'FROM stludr.SYNC_INTERFACE_BL_' || inMonth || ' a,  ' ||
                                'STL_OFFER_T b,  ' ||
                                'STL_RATE_T c,  ' ||
                                'STL_REPART_RATE_T e  ' ||
                         'WHERE a.POSPECNUMBER = b.OFFER_CODE  ' ||
                            'AND b.PRODUCT_CODE = ''-1'' ' ||
                            'AND b.RULE_ID = c.RULE_ID  ' ||
                            'AND c.RATE_ID = e.RATE_ID  ' ||
                            'AND e.MATCH_MODE = 2 ' ||
                            'AND c.RATE_TYPE = 3 ' ||
                            'and a.ordermode = b.order_mode ' ||
                            'AND ' || inMonth || ' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') ' ||
                            'AND a.POSPECNUMBER IN (SELECT DISTINCT OFFER_CODE FROM stlusers.STL_BUSINESS_TYPE ' ||
                                                         'WHERE BIZ_TYPE IN (''CMAS5'')) ' ||
                            'AND decode(a.DATASOURCE, ''BL'', ''1'', ''AR'', ''2'') = b.DATA_SOURCE  ' ||
                            'AND a.ORGMONTH = ' || inMonth || ') t';

            SELECT @vSql;
            PREPARE STMT FROM @vSql;
            EXECUTE STMT;
            DEALLOCATE PREPARE STMT;
    select concat('21_', now());
            outReturn := 0;
            outSysError := 'OK';*/


            ----SD-WAN组网业务政企收入还原
            --国际公司 【冗余】
            set @vSql := 'INSERT INTO STLUSERS.STL_REPART_PARAMETER_T ' ||
                         'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.* FROM   '||
                         '(SELECT DISTINCT a.pospecnumber, a.sospecnumber,   '||
                         'a.poid poid_inst_id, a.soid svc_inst_id, a.ORDERMODE, b.RULE_ID rule_id, '||
                         'c.RATE_ID rate_id, 0 calc_priority, ''030'' object_value, '||
                         'e.tariff_type tariff_type, a.notaxfee rate_value, '||
                         'to_date(a.orgmonth, ''yyyymm'') eff_date, add_months(to_date(   '||
                         'a.orgmonth, ''yyyymm''), 1) - 1 exp_date, a.orgmonth, a.feetype charge_item, '||
                         '99 dest_source, 1 route_flag '||
                         'FROM stludr.SYNC_INTERFACE_SDWAN_' || inMonth || ' a,  '||
                         'STL_OFFER_T b, '||
                         'STL_RATE_T c,  '||
                         'STL_REPART_RATE_T e  '||
                         'WHERE a.pospecnumber = b.OFFER_CODE '||
                         'and b.order_mode = ''1''  '||
                         'AND (a.sospecnumber = b.PRODUCT_CODE OR a.sospecnumber IS NULL) '||
                         'AND a.soid IS NOT NULL   '||
                         'AND b.RULE_ID = c.RULE_ID   '||
                         'AND c.RATE_ID = e.RATE_ID   '||
                         'AND e.MATCH_MODE = 2   '||
                         'AND b.DATA_SOURCE = 1   '||
                         'AND c.RATE_TYPE = 3  '||
                         'and a.ordermode = b.order_mode  '||
                         'and a.feetype not in(''3682'',''3683'',''3684'',''3685'',''3865'' ) '||
                         'AND ' || inMonth || ' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') '||
                         'and ' || inMonth || ' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') '||
                         'AND a.orgmonth = ' || inMonth || '  '||
                         'GROUP BY a.pospecnumber, a.sospecnumber, a.poid, a.soid, a.ORDERMODE, b.RULE_ID, '||
                         ' c.RATE_ID, e.tariff_type, a.orgmonth, a.notaxfee, c.in_object_id, a.feetype) data ';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
select concat('22_', now());
outReturn := 0;
            outSysError := 'OK';




            -- 出账金额-国际公司金额=EC所结金额  NULL->${EC_PROV_CODE_07}
            set @vSql := 'INSERT INTO STLUSERS.STL_REPART_PARAMETER_T ' ||
                         'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, t.* FROM   '||
                         '(SELECT DISTINCT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,  '||
                         'a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERMODE, b.RULE_ID rule_id,  '||
                         'c.RATE_ID rate_id, 1 calc_priority, ''${EC_PROV_CODE_07}'' object_value,  '||
                         'e.tariff_type tariff_type,  ''$'' rate_value,  '||
                         'to_date(a.ORGMONTH, ''yyyymm'') eff_date, add_months(to_date(   '||
                         'a.ORGMONTH, ''yyyymm''), 1) - 1 exp_date, a.ORGMONTH, a.feetype charge_item, decode(a.ordermode, ''1'', 0, ''3'', 98) dest_source, '||
                         '0 route_flag '||
                         'FROM stludr.SYNC_INTERFACE_BL_' || inMonth || ' a,  '||
                         'STL_OFFER_T b,  '||
                         'STL_RATE_T c,   '||
                         'STL_REPART_RATE_T e  '||
                         'WHERE a.POSPECNUMBER = b.OFFER_CODE  '||
                         'AND a.SOSPECNUMBER = b.PRODUCT_CODE '||
                         'AND a.SOID IS NOT NULL   '||
                         'AND b.RULE_ID = c.RULE_ID   '||
                         'AND c.RATE_ID = e.RATE_ID   '||
                         'AND e.MATCH_MODE = 2  '||
                         'and e.tariff_type = 2   '||
                         'AND c.RATE_TYPE = 3   '||
                         'and a.ordermode = b.order_mode '||
                         'and a.feetype in(''02'',''08'',''2163'') '||
                         'AND ' || inMonth || ' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') '||
                         'and ' || inMonth || ' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') '||
                         'AND decode(a.DATASOURCE, ''BL'', ''1'', ''AR'', ''2'') = b.DATA_SOURCE '||
                         'AND a.ORGMONTH = ' || inMonth || ') t ';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
select concat('23_', now());
outReturn := 0;
            outSysError := 'OK';



           --成员视频彩铃    省间结算金额  NULL ${MEM_PROV_15}
           set @vSql := 'INSERT INTO STLUSERS.STL_REPART_PARAMETER_T ' ||
             'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.* FROM  '||
            '(SELECT DISTINCT a.pospecnumber, a.sospecnumber,    '||
                 'a.poid poid_inst_id, a.soid svc_inst_id, a.ORDERMODE, b.RULE_ID rule_id,  '||
                 'c.RATE_ID rate_id, 0 calc_priority, ''${MEM_PROV_15}'' object_value,   '||
                 'e.tariff_type tariff_type, a.notaxfee rate_value,   '||
                 'to_date(a.orgmonth, ''yyyymm'') eff_date, add_months(to_date(    '||
                 'a.orgmonth, ''yyyymm''), 1) - 1 exp_date, a.orgmonth, a.feetype charge_item,  '||
                '99 dest_source, 1 route_flag  '||
            'FROM stludr.SYNC_INTERFACE_SDWAN_' || inMonth || ' a,  '||
                 'STL_OFFER_T b,   '||
                 'STL_RATE_T c,   '||
                 'STL_REPART_RATE_T e  '||
             'WHERE a.pospecnumber = b.OFFER_CODE   '||
             'and b.order_mode = ''1''  '||
             'AND (a.sospecnumber = b.PRODUCT_CODE OR a.sospecnumber IS NULL)   '||
             'AND a.soid IS NOT NULL   '||
             'AND b.RULE_ID = c.RULE_ID    '||
             'AND c.RATE_ID = e.RATE_ID    '||
             'AND e.MATCH_MODE = 2     '||
             'AND b.DATA_SOURCE = 1    '||
             'AND c.RATE_TYPE = 3   '||
             'and a.ordermode = b.order_mode   '||
             'and a.feetype in(''3682'',''3683'',''3684'',''3685'',''3865'')   '||
             'AND ' || inMonth || ' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') '||
             'and ' || inMonth || ' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'')  '||
             'AND a.orgmonth = ' || inMonth || '  '||
             'GROUP BY a.pospecnumber, a.sospecnumber, a.poid, a.soid, a.ORDERMODE, b.RULE_ID,  '||
                'c.RATE_ID, e.tariff_type, a.orgmonth, a.notaxfee, c.in_object_id, a.feetype) data  ';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
select concat('24_', now());
outReturn := 0;
             outSysError := 'OK';


           --出账金额-省间结算金额=EC所结金额  NULL-> ${EC_PROV_CODE_07}
           set @vSql := 'INSERT INTO STLUSERS.STL_REPART_PARAMETER_T ' ||
            'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, t.* FROM   '||
                '(SELECT DISTINCT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,  '||
                'a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERMODE, b.RULE_ID rule_id,  '||
                 'c.RATE_ID rate_id, 1 calc_priority, ''${EC_PROV_CODE_07}'' object_value,   '||
                 'e.tariff_type tariff_type,  ''$'' rate_value, '||
                 'to_date(a.ORGMONTH, ''yyyymm'') eff_date, add_months(to_date(  '||
                 'a.ORGMONTH, ''yyyymm''), 1) - 1 exp_date, a.ORGMONTH, a.feetype charge_item, decode(a.ordermode, ''1'', 0, ''3'', 98) dest_source,  '||
                 '0 route_flag  '||
            'FROM stludr.SYNC_INTERFACE_BL_' || inMonth || ' a,  '||
                 'STL_OFFER_T b, '||
                 'STL_RATE_T c,   '||
                 'STL_REPART_RATE_T e '||
            'WHERE a.POSPECNUMBER = b.OFFER_CODE  '||
             'AND a.SOSPECNUMBER = b.PRODUCT_CODE   '||
             'AND a.SOID IS NOT NULL    '||
             'AND b.RULE_ID = c.RULE_ID   '||
             'AND c.RATE_ID = e.RATE_ID   '||
             'AND e.MATCH_MODE = 2   '||
             'and e.tariff_type = 2    '||
             'AND c.RATE_TYPE = 3   '||
             'and a.ordermode = b.order_mode  '||
             'and a.feetype in(''3682'',''3683'',''3684'',''3685'',''3865'')   '||
             'AND ' || inMonth || ' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'')  '||
             'and ' || inMonth || ' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'')   '||
             'AND decode(a.DATASOURCE, ''BL'', ''1'', ''AR'', ''2'') = b.DATA_SOURCE   '||
             'AND a.ORGMONTH = ' || inMonth || ') t ';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
            outSysError := 'OK';
select concat('25_', now());


---- OneVillage  分段计算单价
set @vSql := 'merge into stludr.sync_interface_amount_' || inMonth || '  f '||
                          'using (select poid, soid, feetype,   '||
                          '      (case                     '||
                          '       when price >= 119250 then  '||
                          '      notaxfee * 0.8     '||
                          '    else                '||
                          '     95400 * amount    '||
                          '  end) settle_fee      '||
                          '  from (select /*+ hash_join(a,b)*/a.poid,      '||
                          '         a.soid,      '||
                          '         a.feetype,   '||
                          '        round(a.notaxfee / b.amount) price,  '||
                          '       a.notaxfee,    '||
                          '       b.amount        '||
                          '  from stludr.sync_interface_bl_' || inMonth || '  a,   '||
                          '      stludr.sync_interface_amount_' || inMonth || '  b   '||
                          ' where a.sospecnumber = ''2022999400055822'' '||
                          '   and a.poid = b.poid   '||
                          '  and a.soid = b.soid   '||
                          '  and a.feetype = b.feetype)) t  '||
                          'on (f.poid = t.poid and f.soid = t.soid and f.feetype = t.feetype)  '||
                          'when matched then  '||
                          'update set f.notaxfee = t.settle_fee ';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
            outSysError := 'OK';
select concat('26_', now());


--- OneVillage 规则计算  null-> ${EC_PROV_CODE_07}
set @vSql := 'INSERT INTO STLUSERS.STL_REPART_PARAMETER_T    '||
            'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, t.* FROM  '||
            '  (SELECT /*+ hash_join(a,b,c,e)*/DISTINCT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,  '||
            'a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERMODE, b.RULE_ID rule_id,  '||
            'c.RATE_ID rate_id, 1 calc_priority, ''${EC_PROV_CODE_07}'' object_value, '||
            'e.tariff_type tariff_type,  a.notaxfee rate_value,  '||
            'to_date(a.ORGMONTH, ''yyyymm'') eff_date, add_months(to_date(  '||
            'a.ORGMONTH, ''yyyymm''), 1) - 1 exp_date, a.ORGMONTH,  ''-1'' charge_item, 98 dest_source, '||
            '1 route_flag   '||
            'FROM stludr.SYNC_INTERFACE_AMOUNT_' || inMonth || ' a, '||
            ' STL_OFFER_T b,  '||
            ' STL_RATE_T c,  '||
            ' STL_REPART_RATE_T e  '||
            ' WHERE a.POSPECNUMBER = b.OFFER_CODE  '||
            'AND a.SOSPECNUMBER = b.PRODUCT_CODE  '||
            'AND a.SOID IS NOT NULL   '||
            'AND b.RULE_ID = c.RULE_ID  '||
            'AND c.RATE_ID = e.RATE_ID  '||
            'AND e.MATCH_MODE = 2 '||
            'and e.tariff_type = 2  '||
            'AND c.RATE_TYPE = 3   '||
            'and a.ordermode = b.order_mode '||
            'and a.sospecnumber=''2022999400055822'' '||
            'AND ' || inMonth || ' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') '||
            'and ' || inMonth || ' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') '||
            'AND decode(a.DATASOURCE, ''BL'', ''1'', ''AR'', ''2'') = b.DATA_SOURCE  '||
            'AND a.ORGMONTH = ' || inMonth || ') t ';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
            outSysError := 'OK';
select concat('27_', now());

--SIP音视频
--固话通信费部分
--出账费项为普通通信费
set @vSql := 'INSERT INTO STLUSERS.STL_REPART_PARAMETER_T ' ||
           'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.* FROM ' ||
           '(SELECT /*+ hash_join(a,b,c,d,e)*/DISTINCT a.pospecnumber, a.sospecnumber, ' ||
                 'a.poid poid_inst_id, a.soid svc_inst_id, a.ordermode, b.RULE_ID rule_id, ' ||
                 'c.RATE_ID rate_id, 0 calc_priority, a.prov_cd object_value, ' ||
                 'e.tariff_type tariff_type, round(sum(a.amount) / 10 * 0.3) rate_value, ' ||
                 'to_date(a.orgmonth, ''yyyymm'') eff_date, add_months(to_date( ' ||
                 'a.orgmonth, ''yyyymm''), 1) - 1 exp_date, a.orgmonth, d.charge_item charge_item, ' ||
                 '0 dest_source, 1 route_flag ' ||
            'FROM stludr.SYNC_BL_RULE_' || inMonth || ' a, ' ||
                 'STL_OFFER_T b, ' ||
                 'STL_RATE_T c, ' ||
                 'stl_rule_item_t d, ' ||
                 'STL_REPART_RATE_T e ' ||
           'WHERE a.pospecnumber = b.OFFER_CODE ' ||
             'AND b.RULE_ID = c.RULE_ID ' ||
             'AND c.RATE_ID = e.RATE_ID ' ||
             'AND e.MATCH_MODE = 2 ' ||
             'AND b.DATA_SOURCE = 1 ' ||
             'AND c.RATE_TYPE = 3 ' ||
             'and d.charge_item = a.feetype and d.rule_id = b.rule_id ' ||
             'and a.ordermode = b.order_mode ' ||
             'AND ' || inMonth || ' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') ' ||
             'and ' || inMonth || ' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') ' ||
             'and ' || inMonth || ' BETWEEN to_char(d.EFF_DATE, ''yyyymm'') AND to_char(d.EXP_DATE, ''yyyymm'') ' ||
             'AND a.orgmonth = ' || inMonth || ' ' ||
             'AND a.pospecnumber = ''50016'' and a.sospecnumber = ''5001613'' ' ||
           'GROUP BY a.pospecnumber, a.sospecnumber, a.poid, a.soid, a.ORDERMODE, a.prov_cd, b.RULE_ID, ' ||
                 'c.RATE_ID, e.tariff_type, a.orgmonth, d.charge_item) data';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
            outSysError := 'OK';
select concat('28_', now());

--出账费项为低消通信费
set @vSql := 'INSERT INTO STLUSERS.STL_REPART_PARAMETER_T ' ||
           'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.* FROM ' ||
           '(SELECT /*+ hash_join(a,b,c,d,e)*/DISTINCT a.pospecnumber, a.sospecnumber, ' ||
                 'a.poid poid_inst_id, a.soid svc_inst_id, a.ordermode, b.RULE_ID rule_id, ' ||
                 'c.RATE_ID rate_id, 0 calc_priority, a.prov_cd object_value, ' ||
                 'e.tariff_type tariff_type, round(sum(a.amount) / 10 * 0.3) rate_value, ' ||
                 'to_date(a.orgmonth, ''yyyymm'') eff_date, add_months(to_date( ' ||
                 'a.orgmonth, ''yyyymm''), 1) - 1 exp_date, a.orgmonth, ' ||
                 'decode(d.charge_item, ''1597'', ''1599'', ''1598'', ''1599'', ''5597'', ''5599'', ''5598'', ''5599'') charge_item, ' ||
                 '0 dest_source, 1 route_flag ' ||
            'FROM stludr.SYNC_BL_RULE_' || inMonth || ' a, ' ||
                 'STL_OFFER_T b, ' ||
                 'STL_RATE_T c, ' ||
                 'stl_rule_item_t d, ' ||
                 'STL_REPART_RATE_T e ' ||
           'WHERE a.pospecnumber = b.OFFER_CODE ' ||
             'AND b.RULE_ID = c.RULE_ID ' ||
             'AND c.RATE_ID = e.RATE_ID ' ||
             'AND e.MATCH_MODE = 2 ' ||
             'AND b.DATA_SOURCE = 1 ' ||
             'AND c.RATE_TYPE = 3 ' ||
             'and d.charge_item = a.feetype and d.rule_id = b.rule_id ' ||
             'and a.ordermode = b.order_mode ' ||
             'and d.charge_item in(''1597'',''1598'',''5597'',''5598'')  '||
             'AND ' || inMonth || ' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') ' ||
             'and ' || inMonth || ' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') ' ||
             'and ' || inMonth || ' BETWEEN to_char(d.EFF_DATE, ''yyyymm'') AND to_char(d.EXP_DATE, ''yyyymm'') ' ||
             'AND a.orgmonth = ' || inMonth || ' ' ||
             'AND a.pospecnumber = ''50016'' and a.sospecnumber = ''5001613'' ' ||
           'GROUP BY a.pospecnumber, a.sospecnumber, a.poid, a.soid, a.ORDERMODE, a.prov_cd, b.RULE_ID, ' ||
                 'c.RATE_ID, e.tariff_type, a.orgmonth, d.charge_item) data';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
            outSysError := 'OK';
select concat('29_', now());



--其它部分（特服通信费+低消通信费与普通通信费的差额） NULL->${EC_PROV_CODE_07}
set @vSql := 'INSERT INTO STL_REPART_PARAMETER_T ' ||
          'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, t.* FROM ' ||
          '(SELECT /*+ hash_join(a,b,c,d,e)*/DISTINCT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code,  ' ||
                 'a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERMODE, b.RULE_ID rule_id, ' ||
                 'c.RATE_ID rate_id, 1 calc_priority, ''${EC_PROV_CODE_07}'' object_value,  ' ||
                 'e.tariff_type tariff_type,  ''$'' rate_value,  ' ||
                 'to_date(a.ORGMONTH, ''yyyymm'') eff_date, add_months(to_date(  ' ||
                 'a.ORGMONTH, ''yyyymm''), 1) - 1 exp_date, a.ORGMONTH, d.charge_item charge_item, 0 dest_source, ' ||
                 '1 route_flag ' ||
            'FROM stludr.SYNC_INTERFACE_BL_' || inMonth || ' a,  ' ||
                 'STL_OFFER_T b,  ' ||
                 'STL_RATE_T c,  ' ||
                 'stl_rule_item_t d, ' ||
                 'STL_REPART_RATE_T e  ' ||
          'WHERE a.POSPECNUMBER = b.OFFER_CODE  ' ||
             'AND b.RULE_ID = c.RULE_ID  ' ||
             'AND c.RATE_ID = e.RATE_ID  ' ||
             'AND e.MATCH_MODE = 2 ' ||
             'AND c.RATE_TYPE = 3 ' ||
             'and d.charge_item = a.feetype and d.rule_id = b.rule_id ' ||
             'and a.ordermode = b.order_mode ' ||
             'AND ' || inMonth || ' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') ' ||
             'AND ' || inMonth || ' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') ' ||
             'AND ' || inMonth || ' BETWEEN to_char(d.EFF_DATE, ''yyyymm'') AND to_char(d.EXP_DATE, ''yyyymm'') ' ||
             'AND a.pospecnumber = ''50016'' and a.sospecnumber = ''5001613'' ' ||
             'AND decode(a.DATASOURCE, ''BL'', ''1'', ''AR'', ''2'') = b.DATA_SOURCE  ' ||
             'AND a.ORGMONTH = ' || inMonth || ') t';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
            outSysError := 'OK';
select concat('30_', now());


--插入双跨结算规则
--话单部分
set @vSql := 'INSERT INTO STL_REPART_PARAMETER_T ' ||
           'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.* FROM ' ||
           '(SELECT distinct a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code, ' ||
                 'a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERMODE order_mode, b.RULE_ID rule_id, ' ||
                 'c.RATE_ID rate_id, 0 calc_priority, ''${EC_PROV_CODE_07}'' object_value, ' ||
                 '''2'' tariff_type,  sum(a.AMOUNT) rate_value, ' ||
                 'to_date(a.ORGMONTH, ''yyyymm'') eff_date, add_months(to_date( ' ||
                 'a.ORGMONTH, ''yyyymm''), 1) - 1 exp_date, a.ORGMONTH, ''-1'' charge_item,  ' ||
                 '99 dest_source, 0 route_flag ' ||
            'FROM stludr.SYNC_BL_SETTLE_' || inMonth || ' a, ' ||
                 'stlusers.STL_OFFER_T b, ' ||
                 'stlusers.STL_RATE_T c ' ||
           'WHERE a.POSPECNUMBER = b.OFFER_CODE ' ||
             'AND b.PRODUCT_CODE = -1 ' ||
             'and a.ordermode = b.order_mode ' ||
			 'and a.ordermode = ''3'' ' ||
             'AND b.RULE_ID = c.RULE_ID ' ||
             'AND c.RATE_TYPE = 3 ' ||
             'AND a.POSPECNUMBER = ''0102001'' ' ||
             'AND a.FEETYPE = ''1080'' ' ||
             'AND a.SOSPECNUMBER IS NULL ' ||
             'AND decode(a.DATASOURCE, ''BL'', ''1'', ''AR'', ''2'') = b.DATA_SOURCE ' ||
             'AND a.ORGMONTH = ' || inMonth || ' ' ||
             'and to_date(' || inMonth || ', ''yyyymm'') between b.eff_date and b.exp_date ' ||
             'and to_date(' || inMonth || ', ''yyyymm'') between c.eff_date and c.exp_date ' ||
             'GROUP BY a.POSPECNUMBER, a.SOSPECNUMBER, a.POID, a.SOID, a.ORDERMODE, a.ORGMONTH, ' ||
                 'b.RULE_ID, c.RATE_ID) data';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
            outSysError := 'OK';
select concat('31_', now());

--（帐单-话单）部分
set @vSql := 'INSERT INTO STL_REPART_PARAMETER_T ' ||
           'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, t.* FROM ' ||
           '(SELECT DISTINCT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code, ' ||
                 'a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERMODE order_mode, b.RULE_ID rule_id, ' ||
                 'c.RATE_ID rate_id, 1 calc_priority, ''${EC_PROV_CODE_07}'' object_value, ' ||
                 '''2'' tariff_type,  ''$'' rate_value, ' ||
                 'to_date(a.ORGMONTH, ''yyyymm'') eff_date, add_months(to_date( ' ||
                 'a.ORGMONTH, ''yyyymm''), 1) - 1 exp_date, a.ORGMONTH, ''-1'' charge_item, 98 dest_source, ' ||
                 '1 route_flag ' ||
            'FROM stludr.SYNC_INTERFACE_BL_' || inMonth || ' a, ' ||
                 'stlusers.STL_OFFER_T b, ' ||
                 'stlusers.STL_RATE_T c ' ||
            'WHERE a.POSPECNUMBER = b.OFFER_CODE ' ||
             'AND b.PRODUCT_CODE = -1 ' ||
             'and a.ordermode = b.order_mode ' ||
			 'and a.ordermode = ''3'' ' ||
             'AND b.RULE_ID = c.RULE_ID ' ||
             'AND c.RATE_TYPE = 3 ' ||
             'AND a.POSPECNUMBER = ''0102001'' ' ||
             'AND a.SOID IS NULL ' ||
             'AND decode(a.DATASOURCE, ''BL'', ''1'', ''AR'', ''2'') = b.DATA_SOURCE ' ||
             'and to_date(' || inMonth || ', ''yyyymm'') between b.eff_date and b.exp_date ' ||
             'and to_date(' || inMonth || ', ''yyyymm'') between c.eff_date and c.exp_date ' ||
             'AND a.ORGMONTH = ' || inMonth || ') t';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
            outSysError := 'OK';
select concat('32_', now());


--插入固话云视讯结算规则
--话单部分
set @vSql := 'INSERT INTO STL_REPART_PARAMETER_T ' ||
           'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.* FROM ' ||
           '(SELECT distinct a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code, ' ||
                 'a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERMODE order_mode, b.RULE_ID rule_id, ' ||
                 'c.RATE_ID rate_id, 0 calc_priority, ''${EC_PROV_CODE_07}'' object_value, ' || --这个地方写EC归属省是因为取不到成员归属省且反正是冗余数据
                 '''2'' tariff_type,  sum(a.AMOUNT) rate_value, ' ||
                 'to_date(a.ORGMONTH, ''yyyymm'') eff_date, add_months(to_date( ' ||
                 'a.ORGMONTH, ''yyyymm''), 1) - 1 exp_date, a.ORGMONTH, ''-1'' charge_item,  ' ||
                 '99 dest_source, 0 route_flag ' ||
            'FROM stludr.SYNC_BL_SETTLE_' || inMonth || ' a, ' ||
                 'stlusers.STL_OFFER_T b, ' ||
                 'stlusers.STL_RATE_T c ' ||
           'WHERE a.POSPECNUMBER = b.OFFER_CODE ' ||
             'AND b.PRODUCT_CODE = a.sospecnumber ' ||
             'AND b.RULE_ID = c.RULE_ID ' ||
             'AND c.RATE_TYPE = 3 ' ||
             'AND a.SOSPECNUMBER = ''5001702'' ' ||
             'AND a.FEETYPE = ''1080'' ' ||
             'AND decode(a.DATASOURCE, ''BL'', ''1'', ''AR'', ''2'') = b.DATA_SOURCE ' ||
             'AND a.ORGMONTH = ' || inMonth || ' ' ||

             /*'and to_date(' || inMonth || ', ''yyyymm'') between b.eff_date and b.exp_date ' ||
             'and to_date(' || inMonth || ', ''yyyymm'') between c.eff_date and c.exp_date ' ||*/
             'GROUP BY a.POSPECNUMBER, a.SOSPECNUMBER, a.POID, a.SOID, a.ORDERMODE, a.ORGMONTH, ' ||
                 'b.RULE_ID, c.RATE_ID) data';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
            outSysError := 'OK';
select concat('33_', now());

--（帐单-话单）部分
set @vSql := 'INSERT INTO STL_REPART_PARAMETER_T ' ||
                         'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, t.* FROM ' ||
                         '(SELECT DISTINCT a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code, ' ||
                               'a.POID poid_inst_id, a.SOID svc_inst_id, a.ORDERMODE order_mode, b.RULE_ID rule_id, ' ||
                               'c.RATE_ID rate_id, 1 calc_priority, ''${EC_PROV_CODE_07}'' object_value, ' ||
                               '''2'' tariff_type,  ''$'' rate_value, ' ||
                               'to_date(a.ORGMONTH, ''yyyymm'') eff_date, add_months(to_date( ' ||
                               'a.ORGMONTH, ''yyyymm''), 1) - 1 exp_date, a.ORGMONTH, ''-1'' charge_item, 0 dest_source, ' ||
                               '1 route_flag ' ||
                          'FROM stludr.SYNC_INTERFACE_BL_' || inMonth || ' a, ' ||
                               'stlusers.STL_OFFER_T b, ' ||
                               'stlusers.STL_RATE_T c ' ||

                               /*'stlusers.stl_rule_item_t d ' ||*/
                          'WHERE a.POSPECNUMBER = b.OFFER_CODE ' ||
                           'AND b.PRODUCT_CODE = a.sospecnumber ' ||
                           'AND b.RULE_ID = c.RULE_ID ' ||
                           'AND c.RATE_TYPE = 3 ' ||
                           'AND a.SOSPECNUMBER = ''5001702'' ' ||
                           'AND decode(a.DATASOURCE, ''BL'', ''1'', ''AR'', ''2'') = b.DATA_SOURCE ' ||


                           /*'and d.rule_id = c.rule_id ' ||
                           'and d.charge_item = a.feetype ' ||*/
                           /*'and to_date(' || inMonth || ', ''yyyymm'') between b.eff_date and b.exp_date ' ||
                           'and to_date(' || inMonth || ', ''yyyymm'') between c.eff_date and c.exp_date ' ||
                           'and to_date(' || inMonth || ', ''yyyymm'') between d.eff_date and d.exp_date ' ||*/
                           'AND a.ORGMONTH = ' || inMonth || ') t';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
            outSysError := 'OK';
select concat('34_', now());


--插入WLAN统付结算规则
set @vSql := 'INSERT INTO STL_REPART_PARAMETER_T ' ||
                         'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, datax.* FROM ( '||
                         'SELECT  a.POSPECNUMBER offer_code, a.SOSPECNUMBER product_code, ' ||
                              'a.POID prod_inst_id, a.SOID svc_inst_id, a.ORDERMODE order_mode, c.RULE_ID, d.RATE_ID, row_number() ' ||
                              'over(PARTITION BY a.SOID, a.feetype ORDER BY to_number(a.AMOUNT)) - 1 calc_priority, a.PROV_CD object_value, ' ||
                              '''1'' tariff_type, round(a.AMOUNT / decode(b.TOTAL, 0, 1, b.TOTAL), 15) rate_value, ' ||
                              'to_date(a.ORGMONTH, ''yyyymm'') eff_date, add_months(to_date(a.ORGMONTH, ''yyyymm''), 1) - 1 exp_date, ' ||
                              'a.ORGMONTH acct_month, a.feetype charge_item, 0 dest_source, 1 route_flag ' ||
                         'FROM stludr.SYNC_BL_RULE_' || inMonth || ' a, ' ||
                             '(SELECT POID, SOID, feetype, SUM(AMOUNT) total ' ||
                               'FROM stludr.SYNC_BL_RULE_' || inMonth || ' ' ||
                              'WHERE PROV_CD IS NOT NULL ' ||
                                'AND SOSPECNUMBER IN (SELECT PRODUCT_CODE FROM STL_BUSINESS_TYPE ' ||
                                                      'WHERE BIZ_TYPE = ''GPRSW'') ' ||
                              'GROUP BY POID, SOID, feetype) b, ' ||
                              'STL_OFFER_T c, ' ||
                              'STL_RATE_T d ' ||
                         'WHERE a.POID = b.POID ' ||
                          'AND a.SOID = b.SOID ' ||
                          'and a.feetype = b.feetype ' ||
                          'AND a.PROV_CD IS NOT NULL ' ||
                          'AND a.POSPECNUMBER = c.OFFER_CODE ' ||
                          'AND c.PRODUCT_CODE = -1 ' ||
                          'AND a.ORDERMODE = c.ORDER_MODE ' ||
                          'AND c.RULE_ID = d.RULE_ID ' ||
                          'AND ' || inMonth || ' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') ' ||
                              'AND to_char(c.EXP_DATE, ''yyyymm'') ' ||
                          'AND c.DATA_SOURCE = 1' ||
                          ') datax';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
            outSysError := 'OK';
select concat('35_', now());

--插入CDN结算规则
set @vSql := 'TRUNCATE TABLE STL_CDN_RULE';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
            outSysError := 'OK';



            set @vSql := 'TRUNCATE TABLE STL_CDN_HW_RULE';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
            outSysError := 'OK';


            ----计算全网订购比例
            set @vSql := 'delete from stl_national_rate where acct_month = ''' || inMonth || ''' and biz_type in (''CDN'', ''CDN-HW'')';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
            outSysError := 'OK';


select concat('36_', now());

truncate table stlusers.repart_parameter_tmp;

--当前月月表的汇总
set @vSql := 'insert into stlusers.repart_parameter_tmp(acct_month,ec_code,order_mode,offer_code,product_code, offer_order_id,product_order_id,sett_prov,rate_back_id,sub_group_num,dup_time,prov_quantity)
              select acct_month, ec_code,order_mode,offer_code, product_code,offer_order_id,product_order_id,sett_prov, rate_back_id,sub_group_num, substr(dup_time, 1, 6) dup_time, sum(nvl(accu_volume, 0) + nvl(accu_duration, 0)) prov_quantity
                from stludr.ur_cdn_' || inMonth || '_t ' ||
               'where (accu_volume <> 0 or accu_duration<>0)
               group by acct_month,ec_code,order_mode, offer_code, product_code,offer_order_id, product_order_id,sett_prov, rate_back_id, sub_group_num,substr(dup_time, 1, 6)';
          
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

--下月月表当前月数据汇总（华为结算使用） 
--月表中的字段acct_month就是账期月份，而dup_time会含少量上月数据。使用acct_month来区分临时表中的数据是当前月还是下个月的。
set @vSql := 'insert into stlusers.repart_parameter_tmp(acct_month,ec_code,order_mode,offer_code,product_code, offer_order_id,product_order_id,sett_prov,rate_back_id,sub_group_num,dup_time,prov_quantity)
select acct_month, ec_code, order_mode,offer_code, product_code, offer_order_id, product_order_id, sett_prov, rate_back_id, ''1'','||inMonth||',
       sum(nvl(ACCU_VOLUME, 0) + nvl(ACCU_DURATION, 0)) PROV_QUANTITY
  from stludr.ur_cdn_' || ivNextMonth || '_t
 where sub_group_num = ''1''  
   and dup_time like ''' || inMonth || '%''
   and (accu_volume <> 0 or accu_duration <> 0)
 group by acct_month,ec_code,order_mode, offer_code, product_code, offer_order_id, product_order_id, sett_prov,rate_back_id';
 
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

/*
create table stlusers.repart_parameter_tmp_1(sett_prov varchar(3), prov_quantity bigint) comment 'shard=shard2';
*/

truncate table stlusers.repart_parameter_tmp_1;
--中间表数据量不大，去掉游标循环
/*open cur_prov;
loop
fetch cur_prov into iv_prov_cd;
                exit when cur_prov%NOTFOUND;
                ----省间结算部分 
                    set @vSql := 'insert into stlusers.repart_parameter_tmp_1 ' ||chr(10)||
                         'select '''||iv_prov_cd||''', sum(nvl(ACCU_VOLUME, 0) + nvl(ACCU_DURATION, 0)) prov_quantity ' ||chr(10)||
                         'from stludr.ur_cdn_' || inMonth || '_t ' ||chr(10)||
                         'where rate_back_id = ''1'' and sett_prov='''||iv_prov_cd||''' having prov_quantity > 0';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
                outSysError := 'OK';
end loop;
close cur_prov;*/

--省间结算部分  使用中间表的数据来计算
set @vSql := 'insert into stlusers.repart_parameter_tmp_1  
                         select sett_prov, sum(prov_quantity) prov_quantity  
                         from repart_parameter_tmp  
                         where rate_back_id = ''1''  and acct_month=' || inMonth || ' group by sett_prov having prov_quantity > 0';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := 'insert into stl_national_rate ' ||chr(10)||
                     'select acct_month, biz_type, prov_cd, prov_quantity, total_quantity, ' ||chr(10)||
                      'round(prov_quantity / total_quantity * 100, 12) rate, ''jcyw'' ' ||chr(10)||
                 'from (select ''' || inMonth || ''' acct_month, ''CDN'' biz_type, sett_prov prov_cd, a.prov_quantity, b.total_quantity ' ||chr(10)||
                         'from stlusers.repart_parameter_tmp_1 a, ' ||chr(10)||
                              '(select sum(prov_quantity) total_quantity from stlusers.repart_parameter_tmp_1) b) t ' ||chr(10)||
                'where total_quantity <> 0 and prov_quantity is not null';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;




---- 省间结算部分超低时延
set @vSql := 'insert into stl_national_rate ' ||chr(10)||
                 'select acct_month, biz_type, prov_cd, prov_quantity, total_quantity, ' ||chr(10)||
                  'round(prov_quantity / total_quantity * 100, 12) rate,''cdsy'' ' ||chr(10)||
             'from (select ''' || inMonth || ''' acct_month, ''CDN'' biz_type, sett_prov prov_cd, a.prov_quantity, b.total_quantity ' ||chr(10)||
                     'from (select sett_prov, sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0)) prov_quantity ' ||chr(10)||
                             'from stludr.ur_cdnappend_' || inMonth || '_t ' ||chr(10)||
                            'where FLOW_TYPE = ''1'' ' ||
                            'group by sett_prov) a, ' ||
                          '(select sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0)) total_quantity ' ||chr(10)||
                             'from stludr.ur_cdnappend_' || inMonth || '_t ' ||chr(10)||
                            'where FLOW_TYPE = ''1'') b) t ' ||chr(10)||
            'where total_quantity <> 0';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
----省间结算部分全站加速  和需求沟通都是各算各的  总流量和分省流量都是全站加速的
set @vSql :=  'insert into stl_national_rate ' ||
                 'select acct_month, biz_type, prov_cd, prov_quantity, total_quantity, ' ||
                 'round(prov_quantity / total_quantity * 100, 12) rate,''qzjs'' ' ||
                 'from (select ''' || inMonth ||
                 ''' acct_month, ''CDN'' biz_type, sett_prov prov_cd, a.prov_quantity, b.total_quantity ' ||
                 'from (select sett_prov, sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0) + nvl(UP_FLOW,0)) prov_quantity ' ||
                 'from stludr.ur_cdnappend_' || inMonth || '_t ' ||
                 'where FLOW_TYPE = ''1'' and service_type in(''1'',''2'') ' ||
                 'group by sett_prov) a, ' ||
                 '(select sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0) + nvl(UP_FLOW,0)) total_quantity ' ||
                 'from stludr.ur_cdnappend_' || inMonth || '_t ' ||
                 'where FLOW_TYPE = ''1'' and service_type in(''1'',''2'') ) b) t ' ||
                 'where total_quantity <> 0';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

select concat('37_', now());

truncate table stlusers.repart_parameter_tmp_1;

/*open cur_prov;
loop
fetch cur_prov into iv_prov_cd;
                exit when cur_prov%NOTFOUND;
                ----华为结算部分
                set @vSql := 'insert into stlusers.repart_parameter_tmp_1 ' ||chr(10)||
                         'select '''||iv_prov_cd||''', sum(prov_quantity) prov_quantity ' ||chr(10)||
                         'from (select sum(nvl(ACCU_VOLUME, 0) + nvl(ACCU_DURATION, 0)) prov_quantity from stludr.ur_cdn_' || inMonth || '_t ' ||chr(10)||
                                ' where rate_back_id = ''1'' and sub_group_num = ''1'' and dup_time like ''' || inMonth || '%'' and sett_prov='''||iv_prov_cd||'''' ||chr(10)||
                                'union all ' ||chr(10)||
                                'select sum(nvl(ACCU_VOLUME, 0) + nvl(ACCU_DURATION, 0)) prov_quantity from stludr.ur_cdn_' || ivNextMonth || '_t '  ||chr(10)||
                                ' where rate_back_id = ''1'' and sub_group_num = ''1'' and dup_time like ''' || inMonth || '%'' and sett_prov='''||iv_prov_cd||''')'||chr(10)||
                         ' having prov_quantity is not null';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
end loop;
close cur_prov;*/

--华为结算部分
set @vSql := 'insert into stlusers.repart_parameter_tmp_1 
           select sett_prov, sum(prov_quantity) prov_quantity from stlusers.repart_parameter_tmp  
            where rate_back_id = ''1'' and sub_group_num = ''1'' and dup_time = ''' || inMonth || ''' group by sett_prov 
            having prov_quantity is not null';
            
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


            set @vSql := 'insert into stl_national_rate ' ||
                     'select acct_month, biz_type, prov_cd, prov_quantity, total_quantity, ' ||
                      'round(prov_quantity / total_quantity * 100, 12) rate,''jcyw'' ' ||
                 'from (select ''' || inMonth || ''' acct_month, ''CDN-HW'' biz_type, sett_prov prov_cd, a.prov_quantity, b.total_quantity ' ||
                         'from stlusers.repart_parameter_tmp_1 a, ' ||
                              '(select sum(prov_quantity) total_quantity from stlusers.repart_parameter_tmp_1) b) t ' ||
                'where total_quantity <> 0';

            SELECT @vSql;
            PREPARE STMT FROM @vSql;
            EXECUTE STMT;
            DEALLOCATE PREPARE STMT;




----华为结算部分超低时延
set @vSql := 'insert into stl_national_rate ' ||
                 'select acct_month, biz_type, prov_cd, prov_quantity, total_quantity, ' ||
                  'round(prov_quantity / total_quantity * 100, 12) rate,''cdsy'' ' ||
             'from (select ''' || inMonth || ''' acct_month, ''CDN-HW'' biz_type, sett_prov prov_cd, a.prov_quantity, b.total_quantity ' ||
                     'from (select sett_prov, sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0)) prov_quantity ' ||
                             'from (select * from stludr.ur_cdnappend_' || inMonth || '_t ' ||
                                    'union all ' ||
                                   'select * from stludr.ur_cdnappend_' || ivNextMonth || '_t) ' ||
                            'where FLOW_TYPE = ''1'' and service_type = ''3'' and DISTRIBUTION_PLANE = ''1'' ' ||
                              'and substr(dup_time, 1, 6) = ''' || inMonth || ''' ' ||
                            'group by sett_prov) a, ' ||
                          '(select sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0)) total_quantity ' ||
                             'from (select * from stludr.ur_cdnappend_' || inMonth || '_t ' ||
                                    'union all ' ||
                                   'select * from stludr.ur_cdnappend_' || ivNextMonth || '_t) ' ||
                            'where FLOW_TYPE = ''1''  and service_type = ''3'' and DISTRIBUTION_PLANE = ''1'' ' ||
                              'and substr(dup_time, 1, 6) = ''' || inMonth || ''') b) t ' ||
            'where total_quantity <> 0';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
----华为结算部分全站加速
set @vSql := 'insert into stl_national_rate ' ||
                 'select acct_month, biz_type, prov_cd, prov_quantity, total_quantity, ' ||
                 'round(prov_quantity / total_quantity * 100, 12) rate,''qzjs'' ' ||
                 'from (select ''' || inMonth ||
                 ''' acct_month, ''CDN-HW'' biz_type, sett_prov prov_cd, a.prov_quantity, b.total_quantity ' ||
                 'from (select sett_prov, sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0) + nvl(UP_FLOW,0)) prov_quantity ' ||
                 'from (select * from stludr.ur_cdnappend_' || inMonth || '_t ' ||
                 'union all ' ||
                 'select * from stludr.ur_cdnappend_' || ivNextMonth || '_t) ' ||
                 'where FLOW_TYPE = ''1'' and service_type in(''1'',''2'') and DISTRIBUTION_PLANE = ''1'' ' ||
                 'and substr(dup_time, 1, 6) = ''' || inMonth || ''' ' ||
                 'group by sett_prov) a, ' ||
                 '(select sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0) + nvl(UP_FLOW,0)) total_quantity ' ||
                 'from (select * from stludr.ur_cdnappend_' || inMonth || '_t ' ||
                 'union all ' ||
                 'select * from stludr.ur_cdnappend_' || ivNextMonth || '_t) ' ||
                 'where FLOW_TYPE = ''1'' and service_type in(''1'',''2'') and DISTRIBUTION_PLANE = ''1'' ' ||
                 'and substr(dup_time, 1, 6) = ''' || inMonth || ''') b) t ' ||
                 'where total_quantity <> 0';

            outReturn := 0;
            outSysError := 'OK';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
select concat('38_', now());

/*CREATE TABLE repart_parameter_tmp_2 (

ACCT_MONTH char(6), EC_CODE varchar(32), ORDER_MODE decimal(2,0), OFFER_CODE varchar(64), PRODUCT_CODE varchar(64),
OFFER_ORDER_ID decimal(15,0), PRODUCT_ORDER_ID decimal(15,0), SETT_PROV varchar(3), prov_amount bigint) comment 'shard=shard2';
*/

truncate table repart_parameter_tmp_2;
/*open cur_prov;
loop
fetch cur_prov into iv_prov_cd;
                exit when cur_prov%NOTFOUND;
                ----含网内流量订购的比例计算
                --省间结算部分
                set @vSql := 'INSERT INTO stlusers.repart_parameter_tmp_2 ' ||
                             'SELECT ACCT_MONTH, ' ||
                                   'EC_CODE, ' ||
                                   'ORDER_MODE, ' ||
                                   'OFFER_CODE, ' ||
                                   'PRODUCT_CODE, ' ||
                                   'OFFER_ORDER_ID, ' ||
                                   'PRODUCT_ORDER_ID, ''' ||
                                    iv_prov_cd||''', ' ||
                                   'sum(nvl(ACCU_VOLUME, 0) + nvl(ACCU_DURATION, 0)) prov_amount ' ||
                              'FROM stludr.UR_CDN_' || inMonth || '_T ' ||
                             'where rate_back_id = ''1'' and PRODUCT_CODE !=''****************'' and sett_prov='''||iv_prov_cd||''' ' ||
                             'GROUP BY ACCT_MONTH, EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
end loop;
close cur_prov;*/


 ----含网内流量订购的比例计算
  --省间结算部分
  set @vSql := 'INSERT INTO stlusers.repart_parameter_tmp_2 ' ||
               'SELECT ACCT_MONTH, ' ||
                     'EC_CODE, ' ||
                     'ORDER_MODE, ' ||
                     'OFFER_CODE, ' ||
                     'PRODUCT_CODE, ' ||
                     'OFFER_ORDER_ID, ' ||
                     'PRODUCT_ORDER_ID, ' ||
                     'SETT_PROV, ' ||
                     'sum(prov_quantity) prov_amount ' ||
                'FROM  stlusers.repart_parameter_tmp ' ||
               'where rate_back_id = ''1'' and PRODUCT_CODE !=''****************'' and acct_month='''||inMonth ||
               ''' GROUP BY ACCT_MONTH, EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID,SETT_PROV';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT; 

set @vSql := 'INSERT INTO stlusers.STL_CDN_RULE ' ||
                         'SELECT a.ACCT_MONTH, a.EC_CODE,  a.ORDER_MODE,  a.OFFER_CODE, a.PRODUCT_CODE, a.OFFER_ORDER_ID, a.PRODUCT_ORDER_ID, a.SETT_PROV, a.prov_amount, b.total_amount, ' ||

                                  /*'case when (ssbc.istop55 = ''0'' or ssbc.istop55 is null) and a.order_mode = ''3'' then round(a.prov_amount / b.total_amount * 100 * 0.8, 7) '
                                'else round(a.prov_amount / b.total_amount * 100, 12) end ratio ,*/
                           ' round(a.prov_amount / b.total_amount * 100, 12), 0 dest_source, ssbc.istop55 ' ||
                           'FROM repart_parameter_tmp_2 a ' ||
                          'inner join(SELECT ACCT_MONTH, ' ||
                                        'EC_CODE, ' ||
                                        'ORDER_MODE, ' ||
                                        'OFFER_CODE, ' ||
                                        'PRODUCT_CODE, ' ||
                                        'OFFER_ORDER_ID, ' ||
                                        'PRODUCT_ORDER_ID, ' ||
                                        'sum(nvl(prov_amount, 0)) total_amount ' ||
                                   'FROM repart_parameter_tmp_2 ' ||
                                  'GROUP BY ACCT_MONTH, EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID) b ' ||
                          '   on a.ACCT_MONTH = b.ACCT_MONTH AND a.ORDER_MODE = b.ORDER_MODE AND a.OFFER_ORDER_ID = b.OFFER_ORDER_ID ' ||
                            '    AND a.PRODUCT_ORDER_ID = b.PRODUCT_ORDER_ID AND b.total_amount <> 0 '
                           'left join stl_serv_biz_code ssbc on a.product_order_id=ssbc.order_id and ''' || inMonth || ''' between to_char(ssbc.effective_date, ''yyyymm'') and to_char(ssbc.expiry_date, ''yyyymm'')';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


---- 省间结算部分超低时延
set @vSql := 'INSERT INTO stlusers.STL_CDN_RULE ' ||
       'SELECT a.ACCT_MONTH, ' ||
             'a.EC_CODE, ' ||
             'a.ORDER_MODE, ' ||
             'a.OFFER_CODE, ' ||
             'a.PRODUCT_CODE, ' ||
             'a.OFFER_ORDER_ID, ' ||
             'a.PRODUCT_ORDER_ID, ' ||
             'a.SETT_PROV, ' ||
             'a.prov_amount, ' ||
             'b.total_amount, ' ||
             'round(a.prov_amount / b.total_amount * 100, 12) ratio, ' ||
             '0 DEST_SOURCE, ' ||
             ''''' ' ||
        'FROM (SELECT ACCT_MONTH, ' ||
                     'EC_CODE, ' ||
                     'ORDER_MODE, ' ||
                     'OFFER_CODE, ' ||
                     'PRODUCT_CODE, ' ||
                     'OFFER_ORDER_ID, ' ||
                     'PRODUCT_ORDER_ID, ' ||
                     'SETT_PROV, ' ||
                     'sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0)) prov_amount ' ||
                'FROM stludr.UR_CDNAPPEND_' || inMonth || '_T ' ||
               'where FLOW_TYPE = ''1'' and service_type = ''3'' and PRODUCT_CODE !=''****************''' ||  -- 排除海外CDN
               'GROUP BY ACCT_MONTH, EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID, SETT_PROV) a, ' ||
             '(SELECT ACCT_MONTH, ' ||
                     'EC_CODE, ' ||
                     'ORDER_MODE, ' ||
                     'OFFER_CODE, ' ||
                     'PRODUCT_CODE, ' ||
                     'OFFER_ORDER_ID, ' ||
                     'PRODUCT_ORDER_ID, ' ||
                     'sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0)) total_amount ' ||
                'FROM stludr.UR_CDNAPPEND_' || inMonth || '_T ' ||
               'where FLOW_TYPE = ''1'' and SERVICE_TYPE = ''3'' and PRODUCT_CODE !=''****************''' ||  -- 排除海外CDN
               'GROUP BY ACCT_MONTH, EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID) b ' ||
       'WHERE a.ACCT_MONTH = b.ACCT_MONTH ' ||
         'AND a.ORDER_MODE = b.ORDER_MODE ' ||
         'AND a.OFFER_ORDER_ID = b.OFFER_ORDER_ID ' ||
         'AND a.PRODUCT_ORDER_ID = b.PRODUCT_ORDER_ID ' ||
         'AND b.total_amount <> 0 ' ||
       'ORDER BY ACCT_MONTH, PRODUCT_ORDER_ID';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

-- 省间结算部分全站加速
set @vSql :=  'INSERT INTO stlusers.STL_CDN_RULE ' ||
                 'SELECT a.ACCT_MONTH, ' ||
                 'a.EC_CODE, ' ||
                 'a.ORDER_MODE, ' ||
                 'a.OFFER_CODE, ' ||
                 'a.PRODUCT_CODE, ' ||
                 'a.OFFER_ORDER_ID, ' ||
                 'a.PRODUCT_ORDER_ID, ' ||
                 'a.SETT_PROV, ' ||
                 'a.prov_amount, ' ||
                 'b.total_amount, ' ||
                 'round(a.prov_amount / b.total_amount * 100, 12) ratio, ' ||
                 '0 DEST_SOURCE, ' ||
                 ''''' ' ||
                 'FROM (SELECT ACCT_MONTH, ' ||
                 'EC_CODE, ' ||
                 'ORDER_MODE, ' ||
                 'OFFER_CODE, ' ||
                 'PRODUCT_CODE, ' ||
                 'OFFER_ORDER_ID, ' ||
                 'PRODUCT_ORDER_ID, ' ||
                 'SETT_PROV, ' ||
                 'sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0) + nvl(UP_FLOW,0)) prov_amount ' ||
                 'FROM stludr.UR_CDNAPPEND_' || inMonth || '_T ' ||
                 'where FLOW_TYPE = ''1'' and service_type in(''1'',''2'') and PRODUCT_CODE !=''****************''' ||  -- 排除海外CDN
                 'GROUP BY ACCT_MONTH, EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID, SETT_PROV) a, ' ||
                 '(SELECT ACCT_MONTH, ' ||
                 'EC_CODE, ' ||
                 'ORDER_MODE, ' ||
                 'OFFER_CODE, ' ||
                 'PRODUCT_CODE, ' ||
                 'OFFER_ORDER_ID, ' ||
                 'PRODUCT_ORDER_ID, ' ||
                 'sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0) + nvl(UP_FLOW,0)) total_amount ' ||
                 'FROM stludr.UR_CDNAPPEND_' || inMonth || '_T ' ||
                 'where FLOW_TYPE = ''1'' and SERVICE_TYPE in(''1'',''2'') and PRODUCT_CODE !=''****************''' ||  -- 排除海外CDN
                 'GROUP BY ACCT_MONTH, EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID) b ' ||
                 'WHERE a.ACCT_MONTH = b.ACCT_MONTH ' ||
                 'AND a.ORDER_MODE = b.ORDER_MODE ' ||
                 'AND a.OFFER_ORDER_ID = b.OFFER_ORDER_ID ' ||
                 'AND a.PRODUCT_ORDER_ID = b.PRODUCT_ORDER_ID ' ||
                 'AND b.total_amount <> 0 ' ||
                 'ORDER BY ACCT_MONTH, PRODUCT_ORDER_ID';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


outReturn := 0;
            outSysError := 'OK';

select concat('39_', now());

truncate table repart_parameter_tmp_2;
/*open cur_prov;
loop
fetch cur_prov into iv_prov_cd;
                exit when cur_prov%NOTFOUND;
                ----含网内流量订购的比例计算

                set @vSql := 'INSERT INTO stlusers.repart_parameter_tmp_2 ' ||chr(10)||
                             'SELECT ''' || inMonth || ''' ACCT_MONTH, EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, ' ||chr(10)||
                                        'PRODUCT_ORDER_ID,'''||iv_prov_cd ||''', sum(nvl(ACCU_VOLUME, 0) + nvl(ACCU_DURATION, 0)) prov_amount ' ||chr(10)||
                             '  FROM stludr.ur_cdn_' || inMonth || '_t '||chr(10)||
                             ' where rate_back_id = ''1'' and PRODUCT_CODE !=''****************'' and sett_prov='''||iv_prov_cd||''' and sub_group_num = ''1'' and dup_time like ''' || inMonth || '%'' ' ||chr(10)||
                             ' GROUP BY EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID'||chr(10)||
                             ' union all SELECT ''' || inMonth || ''' ACCT_MONTH, EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, ' ||chr(10)||
                                        'PRODUCT_ORDER_ID,'''|| iv_prov_cd ||''', sum(nvl(ACCU_VOLUME, 0) + nvl(ACCU_DURATION, 0)) prov_amount ' ||chr(10)||
                             '  FROM stludr.ur_cdn_' || ivNextMonth || '_t '||chr(10)||
                             ' where rate_back_id = ''1''  and PRODUCT_CODE !=''****************'' and sett_prov='''||iv_prov_cd||''' and sub_group_num = ''1'' and dup_time like ''' || inMonth || '%'' ' ||chr(10)||
                             ' GROUP BY EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
end loop;
close cur_prov;*/

 ----含网内流量订购的比例计算

  set @vSql := 'INSERT INTO stlusers.repart_parameter_tmp_2 ' ||
               'SELECT ''' || inMonth || ''' ACCT_MONTH, EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, ' ||
                          'PRODUCT_ORDER_ID,SETT_PROV, sum(PROV_QUANTITY) prov_amount ' ||
               '  FROM stlusers.repart_parameter_tmp '||
               ' where rate_back_id = ''1'' and PRODUCT_CODE !=''****************''  and sub_group_num = ''1'' and dup_time = ''' || inMonth || ''' '||
               ' GROUP BY EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID,SETT_PROV';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT; 

--华为结算部分
set @vSql := 'INSERT INTO stlusers.STL_CDN_HW_RULE ' ||
                         'SELECT a.ACCT_MONTH, a.EC_CODE, a.ORDER_MODE, a.OFFER_CODE, a.PRODUCT_CODE, a.OFFER_ORDER_ID, a.PRODUCT_ORDER_ID, ' ||
                                'a.SETT_PROV, a.prov_amount, b.total_amount, ' ||

                                /*'case when (ssbc.istop55 = ''0'' or ssbc.istop55 is null) and a.order_mode = ''3'' then round(a.prov_amount / b.total_amount * 100 * 0.8, 7) '
                                'else round(a.prov_amount / b.total_amount * 100, 12) end ratio, */
                                'round(a.prov_amount / b.total_amount * 100, 12), 98 DEST_SOURCE, ssbc.istop55 ' ||
                           'FROM (SELECT ACCT_MONTH, ' ||
                                        'EC_CODE, ' ||
                                        'ORDER_MODE, ' ||
                                        'OFFER_CODE, ' ||
                                        'PRODUCT_CODE, ' ||
                                        'OFFER_ORDER_ID, ' ||
                                        'PRODUCT_ORDER_ID, ' ||
                                        'SETT_PROV, ' ||
                                        'prov_amount ' ||
                                   'FROM stlusers.repart_parameter_tmp_2 ) a ' ||
                           'INNER JOIN (SELECT ACCT_MONTH, ' ||
                                        'EC_CODE, ' ||
                                        'ORDER_MODE, ' ||
                                        'OFFER_CODE, ' ||
                                        'PRODUCT_CODE, ' ||
                                        'OFFER_ORDER_ID, ' ||
                                        'PRODUCT_ORDER_ID, ' ||
                                        'sum(prov_amount) total_amount ' ||
                                   'FROM stlusers.repart_parameter_tmp_2 ' ||
                                  'GROUP BY ACCT_MONTH, EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID) b ' ||
                          '  ON a.ACCT_MONTH = b.ACCT_MONTH  AND a.ORDER_MODE = b.ORDER_MODE  AND a.OFFER_ORDER_ID = b.OFFER_ORDER_ID ' ||
                          '     AND a.PRODUCT_ORDER_ID = b.PRODUCT_ORDER_ID AND b.total_amount <> 0 '||
                          'left join stl_serv_biz_code ssbc on a.product_order_id=ssbc.order_id and ''' || inMonth || ''' between to_char(ssbc.effective_date, ''yyyymm'') and to_char(ssbc.expiry_date, ''yyyymm'')';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

--华为结算部分超低时延
set @vSql := 'INSERT INTO stlusers.STL_CDN_HW_RULE ' ||
      'SELECT a.ACCT_MONTH, ' ||
             'a.EC_CODE, ' ||
             'a.ORDER_MODE, ' ||
             'a.OFFER_CODE, ' ||
             'a.PRODUCT_CODE, ' ||
             'a.OFFER_ORDER_ID, ' ||
             'a.PRODUCT_ORDER_ID, ' ||
             'a.SETT_PROV, ' ||
             'a.prov_amount, ' ||
             'b.total_amount, ' ||
             'round(a.prov_amount / b.total_amount * 100, 12) ratio, ' ||
             '98 DEST_SOURCE, ' ||
             ''''' ' ||
        'FROM (SELECT ''' || inMonth || ''' ACCT_MONTH, ' ||
                     'EC_CODE, ' ||
                     'ORDER_MODE, ' ||
                     'OFFER_CODE, ' ||
                     'PRODUCT_CODE, ' ||
                     'OFFER_ORDER_ID, ' ||
                     'PRODUCT_ORDER_ID, ' ||
                     'SETT_PROV, ' ||
                     'sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0)) prov_amount ' ||
                'FROM (select * from stludr.ur_cdnappend_' || inMonth || '_t ' ||
                       'union all ' ||
                      'select * from stludr.ur_cdnappend_' || ivNextMonth || '_t) ' ||
               'where FLOW_TYPE = ''1'' and service_type = ''3'' and DISTRIBUTION_PLANE = ''1'' ' ||
                 ' and PRODUCT_CODE !=''****************'' ' ||
                 ' and substr(dup_time, 1, 6) = ''' || inMonth || ''' ' ||
               'GROUP BY EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID, SETT_PROV) a, ' ||
             '(SELECT ''' || inMonth || ''' ACCT_MONTH, ' ||
                     'EC_CODE, ' ||
                     'ORDER_MODE, ' ||
                     'OFFER_CODE, ' ||
                     'PRODUCT_CODE, ' ||
                     'OFFER_ORDER_ID, ' ||
                     'PRODUCT_ORDER_ID, ' ||
                     'sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0)) total_amount ' ||
                'FROM (select * from stludr.ur_cdnappend_' || inMonth || '_t ' ||
                       'union all ' ||
                      'select * from stludr.ur_cdnappend_' || ivNextMonth || '_t) ' ||
               'where FLOW_TYPE = ''1'' and service_type = ''3'' and DISTRIBUTION_PLANE = ''1'' ' ||
               	 ' and PRODUCT_CODE !=''****************'' ' ||
                 ' and substr(dup_time, 1, 6) = ''' || inMonth || ''' ' ||
               'GROUP BY EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID) b ' ||
       'WHERE a.ACCT_MONTH = b.ACCT_MONTH ' ||
         'AND a.ORDER_MODE = b.ORDER_MODE ' ||
         'AND a.OFFER_ORDER_ID = b.OFFER_ORDER_ID ' ||
         'AND a.PRODUCT_ORDER_ID = b.PRODUCT_ORDER_ID ' ||
         'AND b.total_amount <> 0 ' ||
       'ORDER BY ACCT_MONTH, PRODUCT_ORDER_ID';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


-- 华为结算部分全站加速

set @vSql := 'INSERT INTO stlusers.STL_CDN_HW_RULE ' ||
                 'SELECT a.ACCT_MONTH, ' ||
                 'a.EC_CODE, ' ||
                 'a.ORDER_MODE, ' ||
                 'a.OFFER_CODE, ' ||
                 'a.PRODUCT_CODE, ' ||
                 'a.OFFER_ORDER_ID, ' ||
                 'a.PRODUCT_ORDER_ID, ' ||
                 'a.SETT_PROV, ' ||
                 'a.prov_amount, ' ||
                 'b.total_amount, ' ||
                 'round(a.prov_amount / b.total_amount * 100, 12) ratio, ' ||
                 '98 DEST_SOURCE, ' ||
                 ''''' ' ||
                 'FROM (SELECT ''' || inMonth || ''' ACCT_MONTH, ' ||
                 'EC_CODE, ' ||
                 'ORDER_MODE, ' ||
                 'OFFER_CODE, ' ||
                 'PRODUCT_CODE, ' ||
                 'OFFER_ORDER_ID, ' ||
                 'PRODUCT_ORDER_ID, ' ||
                 'SETT_PROV, ' ||
                 'sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0) + nvl(UP_FLOW,0)) prov_amount ' ||
                 'FROM (select * from stludr.ur_cdnappend_' || inMonth || '_t ' ||
                 'union all ' ||
                 'select * from stludr.ur_cdnappend_' || ivNextMonth || '_t) ' ||
                 'where FLOW_TYPE = ''1'' and service_type in(''1'',''2'') and DISTRIBUTION_PLANE = ''1'' ' ||
                 ' and PRODUCT_CODE !=''****************'' ' ||
                 ' and substr(dup_time, 1, 6) = ''' || inMonth || ''' ' ||
                 'GROUP BY EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID, SETT_PROV) a, ' ||
                 '(SELECT ''' || inMonth || ''' ACCT_MONTH, ' ||
                 'EC_CODE, ' ||
                 'ORDER_MODE, ' ||
                 'OFFER_CODE, ' ||
                 'PRODUCT_CODE, ' ||
                 'OFFER_ORDER_ID, ' ||
                 'PRODUCT_ORDER_ID, ' ||
                 'sum(nvl(DOWN_FLOW, 0) + nvl(CONTENT_FLOW,0) + nvl(UP_FLOW,0)) total_amount ' ||
                 'FROM (select * from stludr.ur_cdnappend_' || inMonth || '_t ' ||
                 'union all ' ||
                 'select * from stludr.ur_cdnappend_' || ivNextMonth || '_t) ' ||
                 'where FLOW_TYPE = ''1'' and service_type in(''1'',''2'') and DISTRIBUTION_PLANE = ''1'' ' ||
                 ' and PRODUCT_CODE !=''****************'' ' ||
                 ' and substr(dup_time, 1, 6) = ''' || inMonth || ''' ' ||
                 'GROUP BY EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID) b ' ||
                 'WHERE a.ACCT_MONTH = b.ACCT_MONTH ' ||
                 'AND a.ORDER_MODE = b.ORDER_MODE ' ||
                 'AND a.OFFER_ORDER_ID = b.OFFER_ORDER_ID ' ||
                 'AND a.PRODUCT_ORDER_ID = b.PRODUCT_ORDER_ID ' ||
                 'AND b.total_amount <> 0 ' ||
                 'ORDER BY ACCT_MONTH, PRODUCT_ORDER_ID';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
            outSysError := 'OK';

select concat('40_', now());

/*CREATE TABLE repart_parameter_tmp_3 (

  ORDER_MODE decimal(2,0) , EC_CODE varchar(32) , OFFER_CODE varchar(64) , PRODUCT_CODE varchar(64) ,
  OFFER_ORDER_ID decimal(15,0) , PRODUCT_ORDER_ID decimal(15,0) ) comment 'shard=shard2';
*/
--repart_parameter_tmp_5 此临时表废弃
----仅含网外流量订购的比例计算
--省间结算部分

truncate table repart_parameter_tmp_3;
--truncate table repart_parameter_tmp_5;   

/*open cur_prov;
loop
fetch cur_prov into iv_prov_cd;
                exit when cur_prov%NOTFOUND;

                set @vSql := 'insert into repart_parameter_tmp_5 ' ||chr(10)||
                '(select distinct rate_back_id,order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||chr(10)||
                  'from stludr.ur_cdn_' || inMonth || '_t where rate_back_id in (''1'', ''2'') and PRODUCT_CODE !=''****************'' and sett_prov='''||iv_prov_cd||''' and accu_volume > 0 '||chr(10)||
                  'union select distinct rate_back_id, order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||chr(10)||
                  'from stludr.ur_cdn_' || inMonth || '_t where rate_back_id in (''1'', ''2'') and PRODUCT_CODE !=''****************'' and sett_prov='''||iv_prov_cd||''' and accu_volume < 0 ' ||chr(10)||
                  'union select distinct rate_back_id, order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||chr(10)||
                  'from stludr.ur_cdn_' || inMonth || '_t where rate_back_id in (''1'', ''2'') and PRODUCT_CODE !=''****************'' and sett_prov='''||iv_prov_cd||''' and accu_duration > 0 ' ||chr(10)||
                  'union select distinct rate_back_id, order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||chr(10)||
                  'from stludr.ur_cdn_' || inMonth || '_t where rate_back_id in (''1'', ''2'') and PRODUCT_CODE !=''****************'' and sett_prov='''||iv_prov_cd||''' and accu_duration < 0) ' ;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


end loop;
close cur_prov;
outReturn := 0;
            outSysError := 'OK';

set @vSql := 'insert into repart_parameter_tmp_3 ' ||chr(10)||
              '(select distinct order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||chr(10)||
                'from repart_parameter_tmp where rate_back_id = ''2'' and PRODUCT_CODE !=''****************'' )'||chr(10)||
               'minus ' ||chr(10)||
              '(select distinct order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||chr(10)||
                'from repart_parameter_tmp where rate_back_id = ''1'') ';*/

  set @vSql := 'insert into repart_parameter_tmp_3 ' ||
    '(select distinct order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||
      'from repart_parameter_tmp where rate_back_id = ''2'' and PRODUCT_CODE !=''****************'' ) ' ||
     'minus ' ||
    '(select distinct order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||
      'from repart_parameter_tmp where rate_back_id = ''1'' and PRODUCT_CODE !=''****************'' ) ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


set @vSql := 'insert into stl_cdn_rule ' ||chr(10)||
            'select ''' || inMonth || ''', a.ec_code, a.order_mode, a.offer_code, a.product_code, a.offer_order_id, a.product_order_id, b.prov_cd sett_prov, ' ||chr(10)||
                 'b.prov_quantity prov_amount, b.total_quantity total_amount, '||chr(10)||

                /*'case when (ssbc.istop55 = ''0'' or ssbc.istop55 is null) and a.order_mode = ''3'' then round(b.rate * 0.8, 12) else b.rate end ratio, */
			 'b.rate,0, ssbc.istop55 ' ||chr(10)||
             'from repart_parameter_tmp_3 a ' ||chr(10)||
             'cross join (select prov_cd, prov_quantity, total_quantity, rate from stl_national_rate where acct_month = ''' || inMonth || ''' and biz_type = ''CDN'' and SERVICE_TYPE = ''jcyw'') b'||chr(10)||
             'left join stl_serv_biz_code ssbc on a.product_order_id=ssbc.order_id and ''' || inMonth || ''' between to_char(ssbc.effective_date, ''yyyymm'') and to_char(ssbc.expiry_date, ''yyyymm'')';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

-- 省间结算部分超低时延
set @vSql := 'insert into stl_cdn_rule '||chr(10)||
      'select ''' || inMonth || ''', a.ec_code, a.order_mode, a.offer_code, a.product_code, a.offer_order_id, a.product_order_id, b.prov_cd sett_prov, ' ||chr(10)||
             'b.prov_quantity prov_amount, b.total_quantity total_amount, b.rate ratio, ''0'', '''' '||chr(10)||
        'from (select distinct order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id '||chr(10)||
                'from stludr.ur_cdnappend_' || inMonth || '_t ' ||chr(10)||
               'where FLOW_TYPE = ''2'' and service_type = ''3'' and PRODUCT_CODE !=''****************'' and (DOWN_FLOW <> 0 or CONTENT_FLOW <> 0 )' ||chr(10)||
                  'minus ' ||chr(10)||
              'select distinct order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id '||chr(10)||
                'from stludr.ur_cdnappend_' || inMonth || '_t ' ||chr(10)||
               'where FLOW_TYPE in(''1'',''3'') and service_type = ''3'' and PRODUCT_CODE !=''****************'' and (DOWN_FLOW <> 0 or CONTENT_FLOW <> 0 )) a ' ||chr(10)||
               ' cross join ' ||chr(10)||
             '(select prov_cd, prov_quantity, total_quantity, rate ' ||chr(10)||
                'from stl_national_rate ' ||chr(10)||
               'where acct_month = ''' || inMonth || ''' ' ||chr(10)||
                 'and biz_type = ''CDN'' and SERVICE_TYPE = ''cdsy'') b';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


-- 省间结算部分全站加速
set @vSql := 'insert into stl_cdn_rule '||chr(10)||
                 'select ''' || inMonth ||
                 ''', a.ec_code, a.order_mode, a.offer_code, a.product_code, a.offer_order_id, a.product_order_id, b.prov_cd sett_prov, '||chr(10)||
                 'b.prov_quantity prov_amount, b.total_quantity total_amount, b.rate ratio, ''0'', '''' '||chr(10)||
                 'from (select distinct order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||chr(10)||
                 'from stludr.ur_cdnappend_' || inMonth || '_t ' ||
                 'where FLOW_TYPE = ''2'' and service_type in(''1'',''2'') and (DOWN_FLOW <> 0 or CONTENT_FLOW <> 0  or UP_FLOW<> 0) '||chr(10)||
                 ' minus ' ||
                 'select distinct order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||chr(10)||
                 'from stludr.ur_cdnappend_' || inMonth || '_t  ' ||chr(10)||
                 ' where FLOW_TYPE in(''1'',''3'') and service_type in(''1'',''2'') and PRODUCT_CODE !=''****************'' and (DOWN_FLOW <> 0 or CONTENT_FLOW <> 0 or UP_FLOW<> 0)) a ' ||chr(10)||
                 ' cross join ' ||chr(10)||
                 '(select prov_cd, prov_quantity, total_quantity, rate ' ||chr(10)||
                 'from stl_national_rate ' ||chr(10)||
                 'where acct_month = ''' || inMonth || ''' ' ||chr(10)||
                 'and biz_type = ''CDN'' and SERVICE_TYPE = ''qzjs'') b';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
select concat('41_', now());

truncate table repart_parameter_tmp_3;
/*truncate table repart_parameter_tmp_5;
open cur_prov;
loop
fetch cur_prov into iv_prov_cd;
                exit when cur_prov%NOTFOUND;

                --华为结算部分
                set @vSql := 'insert into repart_parameter_tmp_5 ' ||chr(10)||
                ' (select distinct rate_back_id,order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||chr(10)||
                '  from stludr.ur_cdn_' || inMonth || '_t '||chr(10)||
                ' where rate_back_id in (''1'', ''2'') and PRODUCT_CODE !=''****************'' and sub_group_num = ''1'' and sett_prov='''||iv_prov_cd||''' and dup_time like ''' || inMonth || '%'' and accu_volume > 0'||chr(10)||
                ' union select distinct rate_back_id, order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||chr(10)||
                '  from stludr.ur_cdn_' || inMonth || '_t '||chr(10)||
                ' where rate_back_id in (''1'', ''2'') and PRODUCT_CODE !=''****************'' and sub_group_num = ''1'' and sett_prov='''||iv_prov_cd||''' and dup_time like ''' || inMonth || '%'' and accu_volume < 0' ||chr(10)||
                ' union select distinct rate_back_id, order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||chr(10)||
                '  from stludr.ur_cdn_' || inMonth || '_t '||chr(10)||
                ' where rate_back_id in (''1'', ''2'') and PRODUCT_CODE !=''****************'' and sub_group_num = ''1'' and sett_prov='''||iv_prov_cd||''' and dup_time like ''' || inMonth || '%'' and accu_duration > 0' ||chr(10)||
                ' union select distinct rate_back_id, order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||chr(10)||
                ' from stludr.ur_cdn_' || inMonth || '_t '||chr(10)||
                ' where rate_back_id in (''1'', ''2'') and PRODUCT_CODE !=''****************'' and sub_group_num = ''1'' and sett_prov='''||iv_prov_cd||''' and dup_time like ''' || inMonth || '%'' and accu_duration < 0' ||chr(10)||
                ' union select distinct rate_back_id, order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||chr(10)||
                '  from stludr.ur_cdn_' || ivNextMonth || '_t '||chr(10)||
                ' where rate_back_id in (''1'', ''2'') and PRODUCT_CODE !=''****************'' and sub_group_num = ''1'' and sett_prov='''||iv_prov_cd||''' and dup_time like ''' || inMonth || '%'' and accu_volume > 0'||chr(10)||
                ' union select distinct rate_back_id, order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||chr(10)||
                '  from stludr.ur_cdn_' || ivNextMonth || '_t '||chr(10)||
                ' where rate_back_id in (''1'', ''2'') and PRODUCT_CODE !=''****************'' and sub_group_num = ''1'' and sett_prov='''||iv_prov_cd||''' and dup_time like ''' || inMonth || '%'' and accu_volume < 0' ||chr(10)||
                ' union select distinct rate_back_id, order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||chr(10)||
                '  from stludr.ur_cdn_' || ivNextMonth || '_t '||chr(10)||
                ' where rate_back_id in (''1'', ''2'') and PRODUCT_CODE !=''****************'' and sub_group_num = ''1'' and sett_prov='''||iv_prov_cd||''' and dup_time like ''' || inMonth || '%'' and accu_duration > 0' ||chr(10)||
                ' union select distinct rate_back_id, order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||chr(10)||
                ' from stludr.ur_cdn_' || ivNextMonth || '_t '||chr(10)||
                ' where rate_back_id in (''1'', ''2'') and PRODUCT_CODE !=''****************'' and sub_group_num = ''1'' and sett_prov='''||iv_prov_cd||''' and dup_time like ''' || inMonth || '%'' and accu_duration < 0)' ;

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

end loop;
close cur_prov;

set @vSql := 'insert into repart_parameter_tmp_3 ' ||chr(10)||
              '(select distinct order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||chr(10)||
                'from repart_parameter_tmp_5 where rate_back_id = ''2'' )'||chr(10)||
               'minus ' ||chr(10)||
              '(select distinct order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||chr(10)||
                'from repart_parameter_tmp_5 where rate_back_id = ''1'') ';*/

set @vSql := 'insert into repart_parameter_tmp_3 ' ||
              '(select distinct order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||
                'from repart_parameter_tmp where rate_back_id = ''2'' and PRODUCT_CODE !=''****************'' and sub_group_num = ''1'' and dup_time = ''' || inMonth || ''' )'||
               'minus ' ||
              '(select distinct order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||
                'from repart_parameter_tmp where rate_back_id = ''1'' and PRODUCT_CODE !=''****************'' and sub_group_num = ''1'' and dup_time = ''' || inMonth || ''') ';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := 'insert into stl_cdn_hw_rule ' ||chr(10)||
            'select ''' || inMonth || ''', a.ec_code, a.order_mode, a.offer_code, a.product_code, a.offer_order_id, a.product_order_id, b.prov_cd sett_prov, ' ||chr(10)||
                 'b.prov_quantity prov_amount, b.total_quantity total_amount, '||chr(10)||

                 /*'case when (ssbc.istop55 = ''0'' or ssbc.istop55 is null) and a.order_mode = ''3'' then round(b.rate * 0.8, 12) else b.rate end ratio ,*/
 				 'b.rate,''98'', ssbc.istop55 ' ||chr(10)||
            'from repart_parameter_tmp_3 a ' ||chr(10)||
            'cross join (select prov_cd, prov_quantity, total_quantity, rate from stl_national_rate where acct_month = ''' || inMonth || ''' and biz_type = ''CDN-HW'' and SERVICE_TYPE = ''jcyw'') b'||chr(10)||
            'left join stl_serv_biz_code ssbc on a.product_order_id=ssbc.order_id and ''' || inMonth || ''' between to_char(ssbc.effective_date, ''yyyymm'') and to_char(ssbc.expiry_date, ''yyyymm'')';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


--  华为结算部分超低时延
set @vSql :=  'insert into stl_cdn_hw_rule ' ||
      'select ''' || inMonth || ''', a.ec_code, a.order_mode, a.offer_code, a.product_code, a.offer_order_id, a.product_order_id, b.prov_cd sett_prov, ' ||
             'b.prov_quantity prov_amount, b.total_quantity total_amount, b.rate ratio, ''98'', '''' ' ||
        'from (select distinct order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||
                'from (select * from stludr.ur_cdnappend_' || inMonth || '_t ' ||
                       'union all ' ||
                      'select * from stludr.ur_cdnappend_' || ivNextMonth || '_t) ' ||
               'where FLOW_TYPE = ''2'' and service_type = ''3'' and PRODUCT_CODE !=''****************'' and DISTRIBUTION_PLANE = ''1'' and (DOWN_FLOW <> 0 or CONTENT_FLOW <> 0) ' ||
                 'and substr(dup_time, 1, 6) = ''' || inMonth || '''' ||
                  ' minus ' ||
              'select distinct order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||
                'from (select * from stludr.ur_cdnappend_' || inMonth || '_t ' ||
                       'union all ' ||
                      'select * from stludr.ur_cdnappend_' || ivNextMonth || '_t) ' ||
               'where FLOW_TYPE in(''1'',''3'') and service_type = ''3'' and PRODUCT_CODE !=''****************'' and DISTRIBUTION_PLANE = ''1'' and (DOWN_FLOW <> 0 or CONTENT_FLOW <> 0) ' ||
                 'and substr(dup_time, 1, 6) = ''' || inMonth || ''') a ' ||
                 'cross join ' ||
             '(select prov_cd, prov_quantity, total_quantity, rate ' ||
                'from stl_national_rate ' ||
               'where acct_month = ''' || inMonth || ''' ' ||
                 'and biz_type = ''CDN-HW'') b';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;


-- 华为结算部分全站加速
set @vSql :=  'insert into stl_cdn_hw_rule ' ||
                 'select ''' || inMonth ||
                 ''', a.ec_code, a.order_mode, a.offer_code, a.product_code, a.offer_order_id, a.product_order_id, b.prov_cd sett_prov, ' ||
                 'b.prov_quantity prov_amount, b.total_quantity total_amount, b.rate ratio, ''98'', '''' ' ||
                 'from (select distinct order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||
                 'from (select * from stludr.ur_cdnappend_' || inMonth || '_t ' ||
                 'union all ' ||
                 'select * from stludr.ur_cdnappend_' || ivNextMonth || '_t) ' ||
                 'where FLOW_TYPE = ''2'' and PRODUCT_CODE !=''****************'' and service_type in(''1'',''2'') and DISTRIBUTION_PLANE = ''1'' and (DOWN_FLOW <> 0 or CONTENT_FLOW <> 0 or UP_FLOW<> 0) ' ||
                 'and substr(dup_time, 1, 6) = ''' || inMonth || '''' ||
                 ' minus ' ||
                 'select distinct order_mode, ec_code, offer_code, product_code, offer_order_id, product_order_id ' ||
                 'from (select * from stludr.ur_cdnappend_' || inMonth || '_t ' ||
                 'union all ' ||
                 'select * from stludr.ur_cdnappend_' || ivNextMonth || '_t) ' ||
                 'where FLOW_TYPE in( ''1'',''3'') and PRODUCT_CODE !=''****************'' and service_type in(''1'',''2'') and DISTRIBUTION_PLANE = ''1'' and (DOWN_FLOW <> 0 or CONTENT_FLOW <> 0 or UP_FLOW<> 0) ' ||
                 'and substr(dup_time, 1, 6) = ''' || inMonth || ''') a ' ||
                 'cross join ' ||
                 '(select prov_cd, prov_quantity, total_quantity, rate ' ||
                 'from stl_national_rate ' ||
                 'where acct_month = ''' || inMonth || ''' ' ||
                 'and biz_type = ''CDN-HW'' and SERVICE_TYPE = ''qzjs'') b';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
            outSysError := 'OK';

select concat('42_', now());


/*CREATE TABLE repart_parameter_tmp_4 (
 ORDER_MODE decimal(2,0), OFFER_CODE varchar(64), PRODUCT_CODE varchar(64), OFFER_ORDER_ID decimal(15,0), PRODUCT_ORDER_ID decimal(15,0)) comment 'shard=shard2';*/

----无流量订购的比例计算
--省间结算部分
truncate table repart_parameter_tmp_4;
/*open cur_prov;
loop
fetch cur_prov into iv_prov_cd;
                exit when cur_prov%NOTFOUND;
                set @vSql := 'insert into repart_parameter_tmp_4 ' ||
                'select distinct order_mode, offer_code, product_code, offer_order_id, product_order_id from stludr.ur_cdn_' || inMonth || '_t '||
                ' where accu_volume < 0 AND product_code not in (''****************'',''****************'') and sett_prov='''||iv_prov_cd||''''||
                ' union select distinct order_mode, offer_code, product_code, offer_order_id, product_order_id from stludr.ur_cdn_' || inMonth || '_t '||
                ' where accu_volume > 0 AND product_code not in (''****************'',''****************'') and sett_prov='''||iv_prov_cd||''''||
                ' union select distinct order_mode, offer_code, product_code, offer_order_id, product_order_id from stludr.ur_cdn_' || inMonth || '_t '||
                ' where accu_duration < 0 AND product_code not in (''****************'',''****************'') and sett_prov='''||iv_prov_cd||''''||
                ' union select distinct order_mode, offer_code, product_code, offer_order_id, product_order_id from stludr.ur_cdn_' || inMonth || '_t '||
                ' where accu_duration > 0 AND product_code not in (''****************'',''****************'') and sett_prov='''||iv_prov_cd||'''';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
end loop;
close cur_prov;*/

 set @vSql := 'insert into repart_parameter_tmp_4 ' ||
                'select distinct order_mode, offer_code, product_code, offer_order_id, product_order_id from stlusers.repart_parameter_tmp '||
                ' where  product_code not in (''****************'',''****************'') and acct_month=''' || inMonth || '''';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := 'insert into stl_cdn_rule ' ||chr(10)||
            'select ''' || inMonth || ''', '''', a.order_mode, a.offer_code, a.product_code, a.offer_order_id, a.product_order_id, b.prov_cd sett_prov, ' ||chr(10)||
                 'b.prov_quantity prov_amount, b.total_quantity total_amount, '||chr(10)||

                 /*'case when (ssbc.istop55 = ''0'' or ssbc.istop55 is null) and a.order_mode = ''3'' then round(b.rate * 0.8, 12) else b.rate end ratio,*/
                 'b.rate,0, ssbc.istop55 ' ||chr(10)||
            'from
            (((select distinct ordermode order_mode, pospecnumber offer_code, sospecnumber product_code, ' ||chr(10)||
                         'poid offer_order_id, soid product_order_id,to_char(accountid)    ACCOUNTID ' ||chr(10)||
                    'from stludr.sync_interface_bl_' || inMonth || ' ' ||chr(10)||
                   'where pospecnumber = ''50004'' and SOSPECNUMBER not in (''****************'',''****************'') ' ||chr(10)||
                   'union all ' ||chr(10)||
                  'select distinct decode(a.prov_code, ''000'', ''1'', ''3'') order_mode, a.product_code offer_code, ' ||chr(10)||
                         'a.service_code product_code, a.prod_order_id offer_order_id, a.order_id product_order_id, ''1'' ACCOUNTID ' ||chr(10)||
                    'from stludr.cust_prod_info a ' ||chr(10)||
                   'where a.product_code = ''9200397'' ' ||chr(10)||
                     'and a.service_code = ''9200397'' ' ||chr(10)||
                     'and a.acct_month = ''' || inMonth || ''') ' ||chr(10)||
                   'minus ' ||chr(10)||
                  'select distinct to_char(order_mode), offer_code, product_code, to_char(offer_order_id), to_char(product_order_id),to_char(s.ACCOUNTID) ' ||chr(10)||
                    'from repart_parameter_tmp_4 u,stludr.sync_interface_bl_' || inMonth || ' s  where  u.PRODUCT_ORDER_ID =s.SOID ' ||' ) ' ||chr(10)||
                     '  minus ' ||
               ' select distinct to_char(order_mode), offer_code, u.product_code, to_char(offer_order_id), to_char(product_order_id),''1'' '||chr(10)||
               '  from stludr.ur_cdn_' || inMonth || '_t u,stludr.cust_prod_info s ' ||chr(10)||
               ' where u.PRODUCT_ORDER_ID =s.prod_order_id and s.acct_month = '|| inMonth || ' and (accu_volume <> 0 or accu_duration <> 0) ' ||chr(10)||
                     'minus ' ||
              'select distinct to_char(order_mode), offer_code, product_code, to_char(offer_order_id), to_char(product_order_id),to_char(accountid) ACCOUNTID' ||chr(10)||
                'from stludr.ur_cdnappend_' || inMonth || '_t u,stludr.sync_interface_bl_' || inMonth || ' s ' ||chr(10)||
               ' where u.PRODUCT_ORDER_ID =s.SOID and (DOWN_FLOW <> 0  or CONTENT_FLOW <> 0 or UP_FLOW <> 0 )) a ' ||chr(10)||
                     'inner join ' ||chr(10)||
                 '(select prov_cd, prov_quantity, total_quantity, rate,SERVICE_TYPE
      from stlusers.stl_national_rate
      where acct_month = ''' || inMonth || ''' and biz_type = ''CDN''  ) b  ON  b.SERVICE_TYPE  = (case a.ACCOUNTID when ''1'' THEN ''jcyw''
                                 WHEN ''5'' THEN ( CASE a.product_code
                                                   when ''****************'' then ''qzjs''
                                                   ELSE  ''cdsy'' END)
                                 ELSE ''jcyw''
                                 END)  '||chr(10)||
                 'left join stl_serv_biz_code ssbc on a.product_order_id=ssbc.order_id and ''' || inMonth || ''' between to_char(ssbc.effective_date, ''yyyymm'') and to_char(ssbc.expiry_date, ''yyyymm'')';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
            outSysError := 'OK';

select concat('43_', now());

truncate table repart_parameter_tmp_4;
/*open cur_prov;
loop
fetch cur_prov into iv_prov_cd;
                exit when cur_prov%NOTFOUND;
                --华为结算部分
                set @vSql := 'insert into repart_parameter_tmp_4 ' ||chr(10)||
                      'select distinct order_mode, offer_code, product_code, offer_order_id, product_order_id from stludr.ur_cdn_' || inMonth || '_t ' ||chr(10)||
                      ' where accu_volume > 0 AND product_code not in (''****************'',''****************'') and sub_group_num = ''1'' and dup_time like ''' || inMonth || '%'' and sett_prov='''||iv_prov_cd||''' ' ||chr(10)||
                      'union select distinct order_mode, offer_code, product_code, offer_order_id, product_order_id from stludr.ur_cdn_' || inMonth || '_t ' ||chr(10)||
                      ' where accu_volume < 0 AND product_code not in (''****************'',''****************'') and sub_group_num = ''1'' and dup_time like ''' || inMonth || '%'' and sett_prov='''||iv_prov_cd||''' ' ||chr(10)||
                      'union select distinct order_mode, offer_code, product_code, offer_order_id, product_order_id from stludr.ur_cdn_' || inMonth || '_t ' ||chr(10)||
                      ' where accu_duration > 0 AND product_code not in (''****************'',''****************'') and sub_group_num = ''1'' and dup_time like ''' || inMonth || '%'' and sett_prov='''||iv_prov_cd||''' ' ||chr(10)||
                      'union select distinct order_mode, offer_code, product_code, offer_order_id, product_order_id from stludr.ur_cdn_' || inMonth || '_t ' ||chr(10)||
                      ' where accu_duration < 0 AND product_code not in (''****************'',''****************'') and sub_group_num = ''1'' and dup_time like ''' || inMonth || '%'' and sett_prov='''||iv_prov_cd||''' ' ||chr(10)||
                      'union select distinct order_mode, offer_code, product_code, offer_order_id, product_order_id from stludr.ur_cdn_' || ivNextMonth || '_t ' ||chr(10)||
                      ' where accu_volume > 0 AND product_code not in (''****************'',''****************'') and sub_group_num = ''1'' and dup_time like ''' || inMonth || '%'' and sett_prov='''||iv_prov_cd||''' ' ||chr(10)||
                      'union select distinct order_mode, offer_code, product_code, offer_order_id, product_order_id from stludr.ur_cdn_' || ivNextMonth || '_t ' ||chr(10)||
                      ' where accu_volume < 0 AND product_code not in (''****************'',''****************'') and sub_group_num = ''1'' and dup_time like ''' || inMonth || '%'' and sett_prov='''||iv_prov_cd||''' ' ||chr(10)||
                      'union select distinct order_mode, offer_code, product_code, offer_order_id, product_order_id from stludr.ur_cdn_' || ivNextMonth || '_t ' ||chr(10)||
                      ' where accu_duration > 0 AND product_code not in (''****************'',''****************'') and sub_group_num = ''1'' and dup_time like ''' || inMonth || '%'' and sett_prov='''||iv_prov_cd||''' ' ||chr(10)||
                      'union select distinct order_mode, offer_code, product_code, offer_order_id, product_order_id from stludr.ur_cdn_' || ivNextMonth || '_t ' ||chr(10)||
                      ' where accu_duration < 0 AND product_code not in (''****************'',''****************'') and sub_group_num = ''1'' and dup_time like ''' || inMonth || '%'' and sett_prov='''||iv_prov_cd||''' ';

SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

end loop;
close cur_prov;*/

  --华为结算部分
  set @vSql := 'insert into repart_parameter_tmp_4 ' ||chr(10)||
        ' select distinct order_mode, offer_code, product_code, offer_order_id, product_order_id from stlusers.repart_parameter_tmp '||
        ' where product_code not in (''****************'',''****************'') and sub_group_num = ''1'' and dup_time = ''' || inMonth ||''''; 
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

set @vSql := 'insert into stl_cdn_hw_rule ' ||chr(10)||
            'select ''' || inMonth || ''', '''', a.order_mode, a.offer_code, a.product_code, a.offer_order_id, a.product_order_id, b.prov_cd sett_prov, ' ||chr(10)||
                 'b.prov_quantity prov_amount, b.total_quantity total_amount, '||chr(10)||

                /*'case when (ssbc.istop55 = ''0'' or ssbc.istop55 is null) and a.order_mode = ''3'' then round(b.rate * 0.8, 12) else b.rate end ratio,*/
 				'b.rate,''98'', ssbc.istop55 ' ||chr(10)||
            'from (((select distinct ordermode order_mode, pospecnumber offer_code, sospecnumber product_code, ' ||chr(10)||
                         'poid offer_order_id, soid product_order_id ,to_char(accountid)    ACCOUNTID' ||chr(10)||
                    'from stludr.sync_interface_bl_' || inMonth || ' ' ||chr(10)||
                   'where pospecnumber = ''50004'' and SOSPECNUMBER not in (''****************'',''****************'') ' ||chr(10)||
                   'union all ' ||chr(10)||
                  'select distinct decode(a.prov_code, ''000'', ''1'', ''3'') order_mode, a.product_code offer_code, ' ||chr(10)||
                         'a.service_code product_code, a.prod_order_id offer_order_id, a.order_id product_order_id ,''1'' ACCOUNTID ' ||chr(10)||
                    'from stludr.cust_prod_info a ' ||chr(10)||
                   'where a.product_code = ''9200397'' ' ||chr(10)||
                     'and a.service_code = ''9200397'' ' ||chr(10)||
                     'and a.acct_month = ''' || inMonth || ''') ' ||chr(10)||
                   'minus ' ||chr(10)||
                  'select distinct to_char(order_mode), offer_code, product_code, to_char(offer_order_id), to_char(product_order_id),to_char(accountid)    ACCOUNTID  ' ||chr(10)||
                    'from repart_parameter_tmp_4 u,stludr.sync_interface_bl_' || inMonth || ' s where u.PRODUCT_ORDER_ID =s.SOID ) ' ||chr(10)||
                    '  minus ' ||chr(10)||
               ' select distinct to_char(order_mode), offer_code, u.product_code, to_char(offer_order_id), to_char(product_order_id),''1'' '||chr(10)||
               '  from stludr.ur_cdn_' || inMonth || '_t u,stludr.cust_prod_info s ' ||chr(10)||
                 ' where u.PRODUCT_ORDER_ID =s.prod_order_id and s.acct_month = '|| inMonth || ' and (accu_volume <> 0 or accu_duration <> 0) ' ||chr(10)||
                    'minus '  ||chr(10)||
              'select distinct to_char(order_mode), offer_code, product_code, to_char(offer_order_id), to_char(product_order_id),to_char(accountid)    ACCOUNTID   ' ||chr(10)||
                'from (select * from stludr.ur_cdnappend_' || inMonth || '_t u,stludr.sync_interface_bl_' || inMonth || ' s  ' ||chr(10)||
               'where u.PRODUCT_ORDER_ID =s.SOID '  ||chr(10)||
                       'union all  ' ||chr(10)||
                      'select * from stludr.ur_cdnappend_' || ivNextMonth || '_t u,stludr.sync_interface_bl_' || inMonth || ' s   ' ||chr(10)||
               'where u.PRODUCT_ORDER_ID =s.SOID '|| ')  ' ||chr(10)||
               'where (DOWN_FLOW <> 0 or CONTENT_FLOW <> 0 or UP_FLOW <> 0) and DISTRIBUTION_PLANE = ''1'' ' ||
                 'and substr(dup_time, 1, 6) = ''' || inMonth || ''') a ' ||
                     'inner join ' ||chr(10)||
                 '(select prov_cd, prov_quantity, total_quantity, rate,SERVICE_TYPE '||
     ' from stlusers.stl_national_rate '||
     ' where acct_month = ''' || inMonth || ''' and biz_type = ''CDN-HW'' ) b  ON  b.SERVICE_TYPE  = (case a.ACCOUNTID when ''1'' THEN ''jcyw'' '||
                                ' WHEN ''5'' THEN ( CASE a.product_code
                                                   when ''****************'' then ''qzjs''
                                                   ELSE  ''cdsy'' END)
                                 ELSE ''jcyw''
                                 END) '||chr(10)||
                 ' left join stl_serv_biz_code ssbc on a.product_order_id=ssbc.order_id and ''' || inMonth || ''' between to_char(ssbc.effective_date, ''yyyymm'') and to_char(ssbc.expiry_date, ''yyyymm'')';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
            outSysError := 'OK';
select concat('44_', now());


----stl_cdn_rule和stl_cdn_hw_rule表更新istop55字段

update STL_CDN_RULE a
set istop55 = (select istop55
               from stl_serv_biz_code b
               where a.product_order_id = b.order_id
                 and inMonth between to_char(b.effective_date, 'yyyymm') and to_char(b.expiry_date, 'yyyymm'))
where a.acct_month = inMonth;
commit;
select concat('45_', now());
update STL_CDN_HW_RULE a
set istop55 = (select istop55
               from stl_serv_biz_code b
               where a.product_order_id = b.order_id
                 and inMonth between to_char(b.effective_date, 'yyyymm') and to_char(b.expiry_date, 'yyyymm'))
where a.acct_month = inMonth;
commit;
select concat('46_', now());


----更新受理模式3 top55订购的结算比例（*80%）
update STL_CDN_RULE
set ratio = round(ratio * 0.8, 12)
where order_mode = '3'
  and (istop55 = '0' or istop55 is null)
  and acct_month = inMonth;
commit;
select concat('47_', now());
update STL_CDN_HW_RULE
set ratio = round(ratio * 0.8, 12)
where order_mode = '3'
  and (istop55 = '0' or istop55 is null)
  and acct_month = inMonth;
commit;
select concat('48_', now());


----插入20%部分
insert into STL_CDN_RULE
select distinct inMonth, ec_code, order_mode, offer_code, product_code, offer_order_id, product_order_id, '${EC_PROV_CODE_07}', NULL, NULL, '20', '98', '0'
from STL_CDN_RULE
where order_mode = '3' and (istop55 = '0' or istop55 is null)
  and acct_month = inMonth;

COMMIT;

insert into STL_CDN_HW_RULE
select distinct inMonth, ec_code, order_mode, offer_code, product_code, offer_order_id, product_order_id, '${EC_PROV_CODE_07}', NULL, NULL, '20', '98', '0'
from STL_CDN_HW_RULE
where order_mode = '3' and (istop55 = '0' or istop55 is null)
  and acct_month = inMonth;

COMMIT;
select concat('50_', now());

--CDN 1000W 导入数据到  "STLUSERS"."STL_CDN_RULE_1000"


set @vSql := 'INSERT INTO STL_CDN_RULE_1000  '  ||
                   'SELECT k.* FROM STLUSERS.STL_CDN_RULE_1000 w RIGHT JOIN ( '||
                        'SELECT '  ||
                        '      a.ordermode order_mode,    '  ||
                        '      a.ORGMONTH, '  ||
                        '      a.pospecnumber offer_code, '  ||
                        '      a.sospecnumber product_code, '  ||
                        '      a.poid offer_order_id, '  ||
                        '      a.soid product_order_id, '  ||
                        '      a.NOTAXFEE, '  ||
                        '      b.EFFECTIVE_DATE, '  ||
                        '      b.EXPIRY_DATE, '  ||
                        '      b.ISTOP55 '  ||
                        '  FROM '  ||
                        '      STLUSERS.STL_SERV_BIZ_CODE b  '  ||
                        '  JOIN '  ||
                        '      stludr.sync_interface_bl_' ||inMonth|| ' a  '  ||
                        '  ON  '  ||
                        '      a.ORDERMODE = b.PROD_ORDER_MODE  '  ||
                        '  AND  '  ||
                        '      a.POID = b.PROD_ORDER_ID  '  ||
                        '  AND  '  ||
                        '      a.SOID = b.ORDER_ID  '  ||
                        '  WHERE '  ||
                        '      a.pospecnumber = ''50004''  '  ||
                        ' AND a.SOSPECNUMBER not in (''****************'',''****************'' )'  ||
                        '      AND a.ordermode = ''3''  '  ||
                        '      AND b.ISTOP55 = ''1''  '  ||
                   ' ) k  ' ||
                   ' ON w.order_mode = k.order_mode ' ||
                   ' AND w.ORGMONTH = k.ORGMONTH  ' ||
                   ' AND w.offer_code = k.offer_code  ' ||
                   ' AND w.product_code = k.product_code  ' ||
                   ' AND w.offer_order_id = k.offer_order_id ' ||
                   ' AND w.product_order_id = k.product_order_id ' ||
                   ' AND w.NOTAXFEE = k.NOTAXFEE ' ||
                   ' AND w.EFFECTIVE_DATE = k.EFFECTIVE_DATE ' ||
                   ' AND w.EXPIRY_DATE = k.EXPIRY_DATE ' ||
                   ' AND w.ISTOP55 = k.ISTOP55 ' ||
                   ' WHERE  ' ||
                   '  w.order_mode IS NULL  ' ||
                   '  AND w.ORGMONTH IS NULL  ' ||
                   '  AND w.offer_code IS NULL  ' ||
                   '  AND w.product_code IS NULL  ' ||
                   '  AND w.offer_order_id IS NULL  ' ||
                   '  AND w.product_order_id IS NULL  ' ||
                   '  AND w.NOTAXFEE IS NULL  ' ||
                   '  AND w.EFFECTIVE_DATE IS NULL  ' ||
                   '  AND w.EXPIRY_DATE IS NULL  ' ||
                   '  AND w.ISTOP55 IS NULL  ';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;



--cdn_upper_limit NUMBER(20) := 20000000000;


--受理模式3   top55订购1000w以内的结算比例（*95%） 非华为   31省
MERGE INTO STL_CDN_RULE A
    USING(
        SELECT
            X.OFFER_ORDER_ID,
            X.PRODUCT_ORDER_ID
        FROM
            STL_CDN_RULE_1000 X
                JOIN
            (
                SELECT
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID,
                    MAX( ORGMONTH ) END_DATE,
                    CASE
                        WHEN MAX( EFFECTIVE_DATE ) = LAST_DAY(MAX( EFFECTIVE_DATE )) THEN
                            TRUNC(
                                    ADD_MONTHS(  MAX( EFFECTIVE_DATE ),
                                                 12 * TRUNC( ( MONTHS_BETWEEN(
                                                         TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))
                                                                 ) / 12, 0  ) + 1
                                    )
                                , 'MONTH')
                        ELSE
                            ADD_MONTHS(
                                    MAX( EFFECTIVE_DATE ),
                                    12 * TRUNC((MONTHS_BETWEEN( TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))  )/12,0)
                            )
                        END as START_DATE
                FROM
                    STL_CDN_RULE_1000
                GROUP BY
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID
            ) T
            ON  X.OFFER_ORDER_ID = T.OFFER_ORDER_ID
                AND X.PRODUCT_ORDER_ID = T.PRODUCT_ORDER_ID
                AND X.PRODUCT_CODE not in ('****************','****************' )
        WHERE
                X.ORGMONTH >= to_char(T.START_DATE, 'yyyymm' )
          AND X.ORGMONTH <= T.END_DATE
        GROUP BY
            X.OFFER_ORDER_ID,X.PRODUCT_ORDER_ID
        HAVING SUM(NOTAXFEE) <= 20000000000
    ) W
    ON (A.OFFER_ORDER_ID = W.OFFER_ORDER_ID AND A.PRODUCT_ORDER_ID = W.PRODUCT_ORDER_ID)
    WHEN MATCHED THEN
        UPDATE SET A.RATIO = ROUND(A.RATIO * 0.95, 12)
    WHERE A.ORDER_MODE = '3'
            AND A.ISTOP55 = '1'
            AND A.ACCT_MONTH = INMONTH;


--受理模式3 top55订购1000w以内的结算比例（*95%） 华为  31省
MERGE INTO STL_CDN_HW_RULE A
    USING(
        WITH HW_1000 AS (
            SELECT
                b.ORDER_MODE,
                b.ACCT_MONTH ORGMONTH,
                b.OFFER_CODE,
                b.PRODUCT_CODE,
                b.OFFER_ORDER_ID,
                b.PRODUCT_ORDER_ID,
                (b.CMCC_FEE + b.OTH_FEE) as NOTAXFEE,
                a.EFFECTIVE_DATE,
                a.ISTOP55
            FROM
                STLUSERS.STL_SERV_BIZ_CODE a
                    JOIN stludr.STL_CDN_ORDER_FEE b
                         ON a.PROD_ORDER_MODE = b.ORDER_MODE
                             AND a.PROD_ORDER_ID = b.OFFER_ORDER_ID
                             AND a.ORDER_ID = b.PRODUCT_ORDER_ID
            WHERE
                    b.OFFER_CODE = '50004'
              AND b.product_code not in('****************','****************')
              AND b.ORDER_MODE = '3'
              AND a.ISTOP55 = '1'
        )
        SELECT
            X.OFFER_ORDER_ID,
            X.PRODUCT_ORDER_ID
        FROM
            HW_1000 X
                JOIN
            (
                SELECT
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID,
                    MAX( ORGMONTH ) END_DATE,
                    CASE
                        WHEN MAX( EFFECTIVE_DATE ) = LAST_DAY(MAX( EFFECTIVE_DATE )) THEN
                            TRUNC(
                                    ADD_MONTHS(  MAX( EFFECTIVE_DATE ),
                                                 12 * TRUNC( ( MONTHS_BETWEEN(
                                                         TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))
                                                                 ) / 12, 0  ) + 1
                                    )
                                , 'MONTH')
                        ELSE
                            ADD_MONTHS(
                                    MAX( EFFECTIVE_DATE ),
                                    12 * TRUNC((MONTHS_BETWEEN( TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE ))) )/12,0)
                            )
                        END as START_DATE
                FROM
                    HW_1000
                GROUP BY
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID
            ) T
            ON  X.OFFER_ORDER_ID = T.OFFER_ORDER_ID
                AND X.PRODUCT_ORDER_ID = T.PRODUCT_ORDER_ID
        WHERE
                X.ORGMONTH >= to_char(T.START_DATE, 'yyyymm' )
          AND X.ORGMONTH <= T.END_DATE
        GROUP BY
            X.OFFER_ORDER_ID,X.PRODUCT_ORDER_ID
        HAVING SUM(NOTAXFEE) <= 20000000000
    ) W
    ON (A.OFFER_ORDER_ID = W.OFFER_ORDER_ID AND A.PRODUCT_ORDER_ID = W.PRODUCT_ORDER_ID)
    WHEN MATCHED THEN
        UPDATE SET A.RATIO = ROUND(A.RATIO * 0.95, 12)
    WHERE A.ORDER_MODE = '3'
            AND A.ISTOP55 = '1'
            AND A.ACCT_MONTH = INMONTH;


--受理模式3 top55订购1000w以内的结算比例（*5%） 非华为  签约省  国产decimal不能插‘’ oracle是''; 改为0
INSERT INTO STL_CDN_RULE
SELECT DISTINCT
    inMonth,
    x.ec_code,
    x.order_mode,
    x.offer_code,
    x.product_code,
    x.offer_order_id,
    x.product_order_id,
    '${EC_PROV_CODE_07}',
    0,
    0,
    '5',
    '98',
    '1'
FROM
    STL_CDN_RULE x join
    (
        SELECT
            X.OFFER_ORDER_ID,
            X.PRODUCT_ORDER_ID
        FROM
            STL_CDN_RULE_1000 X
                JOIN
            (
                SELECT
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID,
                    MAX( ORGMONTH ) END_DATE,
                    CASE
                        WHEN MAX( EFFECTIVE_DATE ) = LAST_DAY(MAX( EFFECTIVE_DATE )) THEN
                            TRUNC(
                                    ADD_MONTHS(  MAX( EFFECTIVE_DATE ),
                                                 12 * TRUNC( ( MONTHS_BETWEEN(
                                                         TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))
                                                                 ) / 12, 0  ) + 1
                                    )
                                , 'MONTH')
                        ELSE
                            ADD_MONTHS(
                                    MAX( EFFECTIVE_DATE ),
                                    12 * TRUNC((MONTHS_BETWEEN( TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE ))) )/12,0)
                            )
                        END as START_DATE
                FROM
                    STL_CDN_RULE_1000
                GROUP BY
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID
            ) T
            ON  X.OFFER_ORDER_ID = T.OFFER_ORDER_ID
                AND X.PRODUCT_ORDER_ID = T.PRODUCT_ORDER_ID
                AND X.PRODUCT_CODE not in ('****************','****************' )
        WHERE
                X.ORGMONTH >= to_char(T.START_DATE, 'yyyymm' )
          AND X.ORGMONTH <= T.END_DATE
        GROUP BY
            X.OFFER_ORDER_ID,X.PRODUCT_ORDER_ID
        HAVING SUM(NOTAXFEE) <= 20000000000
    ) t
    ON  x.OFFER_ORDER_ID = t.OFFER_ORDER_ID AND x.PRODUCT_ORDER_ID = t.PRODUCT_ORDER_ID
WHERE
        x.order_mode = '3'
  AND x.istop55 = '1'
  AND x.acct_month = inMonth;


--受理模式3 top55订购1000w以内的结算比例（*5%） 华为  签约省    国产decimal不能插‘’ oracle是''; 改为0
INSERT INTO STL_CDN_HW_RULE
SELECT DISTINCT
    inMonth,
    x.ec_code,
    x.order_mode,
    x.offer_code,
    x.product_code,
    x.offer_order_id,
    x.product_order_id,
    '${EC_PROV_CODE_07}',
    0,
    0,
    '5',
    '98',
    '1'
FROM
    STL_CDN_HW_RULE x join
    (

        WITH HW_1000 AS (
            SELECT
                b.ORDER_MODE,
                b.ACCT_MONTH ORGMONTH,
                b.OFFER_CODE,
                b.PRODUCT_CODE,
                b.OFFER_ORDER_ID,
                b.PRODUCT_ORDER_ID,
                (b.CMCC_FEE + b.OTH_FEE) as NOTAXFEE,
                a.EFFECTIVE_DATE,
                a.ISTOP55
            FROM
                STLUSERS.STL_SERV_BIZ_CODE a
                    JOIN stludr.STL_CDN_ORDER_FEE b
                         ON a.PROD_ORDER_MODE = b.ORDER_MODE
                             AND a.PROD_ORDER_ID = b.OFFER_ORDER_ID
                             AND a.ORDER_ID = b.PRODUCT_ORDER_ID
            WHERE
                    b.OFFER_CODE = '50004'
              AND b.product_code not in('****************','****************')
              AND b.ORDER_MODE = '3'
              AND a.ISTOP55 = '1'
        )
        SELECT
            X.OFFER_ORDER_ID,
            X.PRODUCT_ORDER_ID
        FROM
            HW_1000 X
                JOIN
            (
                SELECT
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID,
                    MAX( ORGMONTH ) END_DATE,
                    CASE
                        WHEN MAX( EFFECTIVE_DATE ) = LAST_DAY(MAX( EFFECTIVE_DATE )) THEN
                            TRUNC(
                                    ADD_MONTHS(  MAX( EFFECTIVE_DATE ),
                                                 12 * TRUNC( ( MONTHS_BETWEEN(
                                                         TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))
                                                                 ) / 12, 0  ) + 1
                                    )
                                , 'MONTH')
                        ELSE
                            ADD_MONTHS(
                                    MAX( EFFECTIVE_DATE ),
                                    12 * TRUNC((MONTHS_BETWEEN( TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))  )/12,0)
                            )
                        END as START_DATE
                FROM
                    HW_1000
                GROUP BY
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID
            ) T
            ON  X.OFFER_ORDER_ID = T.OFFER_ORDER_ID
                AND X.PRODUCT_ORDER_ID = T.PRODUCT_ORDER_ID
        WHERE
                X.ORGMONTH >= to_char(T.START_DATE, 'yyyymm' )
          AND X.ORGMONTH <= T.END_DATE
        GROUP BY
            X.OFFER_ORDER_ID,X.PRODUCT_ORDER_ID
        HAVING SUM(NOTAXFEE) <= 20000000000
    ) t
    ON  x.OFFER_ORDER_ID = t.OFFER_ORDER_ID AND x.PRODUCT_ORDER_ID = t.PRODUCT_ORDER_ID
WHERE
        x.order_mode = '3'
  AND x.istop55 = '1'
  AND x.acct_month = inMonth;




--cdn 1000w金额分割
--受理模式3 top55订购超过1000w的结算比例 非华为  31省
MERGE INTO STL_CDN_RULE A
    USING(
        SELECT
            X.OFFER_ORDER_ID,
            X.PRODUCT_ORDER_ID,

            -- 1-(((SUM(X.NOTAXFEE)/20000000000) - 1) * 0.05) as PROVINCE31_RATIO
            CASE
                WHEN (SUM(X.NOTAXFEE)-20000000000) < SUM(CASE WHEN X.ORGMONTH = inMonth THEN X.NOTAXFEE ELSE 0 END) THEN
                        (
                                ((SUM(X.NOTAXFEE)-20000000000)/SUM(CASE WHEN X.ORGMONTH = inMonth THEN X.NOTAXFEE ELSE 1 END)) * 1
                            ) + (
                                (1 - (SUM(X.NOTAXFEE)-20000000000)/SUM(CASE WHEN X.ORGMONTH = inMonth THEN X.NOTAXFEE ELSE 1 END)) * 0.95
                            )
                --第一次超出
                ELSE
                    1
                --除第一次超出外的第n次超出
                END  AS PROVINCE31_RATIO
        FROM
            STL_CDN_RULE_1000 X
                JOIN
            (
                SELECT
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID,
                    MAX( ORGMONTH ) END_DATE,
                    CASE
                        WHEN MAX( EFFECTIVE_DATE ) = LAST_DAY(MAX( EFFECTIVE_DATE )) THEN
                            TRUNC(
                                    ADD_MONTHS(  MAX( EFFECTIVE_DATE ),
                                                 12 * TRUNC( ( MONTHS_BETWEEN(
                                                         TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))
                                                                 ) / 12, 0  ) + 1
                                    )
                                , 'MONTH')
                        ELSE
                            ADD_MONTHS(
                                    MAX( EFFECTIVE_DATE ),
                                    12 * TRUNC((MONTHS_BETWEEN( TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE ))) )/12,0)
                            )
                        END as START_DATE
                FROM
                    STL_CDN_RULE_1000
                GROUP BY
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID
            ) T
            ON  X.OFFER_ORDER_ID = T.OFFER_ORDER_ID
                AND X.PRODUCT_ORDER_ID = T.PRODUCT_ORDER_ID
                AND X.PRODUCT_CODE not in ('****************','****************' )
        WHERE
                X.ORGMONTH >= to_char(T.START_DATE, 'yyyymm' )
          AND X.ORGMONTH <= T.END_DATE
        GROUP BY
            X.OFFER_ORDER_ID,X.PRODUCT_ORDER_ID
        HAVING SUM(NOTAXFEE)  > 20000000000
    ) W
    ON (A.OFFER_ORDER_ID = W.OFFER_ORDER_ID AND A.PRODUCT_ORDER_ID = W.PRODUCT_ORDER_ID)
    WHEN MATCHED THEN
        UPDATE SET A.RATIO = ROUND(A.RATIO * W.PROVINCE31_RATIO, 12)
    WHERE A.ORDER_MODE = '3'
            AND A.ISTOP55 = '1'
            AND A.ACCT_MONTH = INMONTH;


--受理模式3 top55订购超过1000w的结算比例 华为  31省
MERGE INTO STL_CDN_HW_RULE A
    USING(
        WITH HW_1000 AS (
            SELECT
                b.ORDER_MODE,
                b.ACCT_MONTH ORGMONTH,
                b.OFFER_CODE,
                b.PRODUCT_CODE,
                b.OFFER_ORDER_ID,
                b.PRODUCT_ORDER_ID,
                (b.CMCC_FEE + b.OTH_FEE) as NOTAXFEE,
                a.EFFECTIVE_DATE,
                a.ISTOP55
            FROM
                STLUSERS.STL_SERV_BIZ_CODE a
                    JOIN stludr.STL_CDN_ORDER_FEE b
                         ON a.PROD_ORDER_MODE = b.ORDER_MODE
                             AND a.PROD_ORDER_ID = b.OFFER_ORDER_ID
                             AND a.ORDER_ID = b.PRODUCT_ORDER_ID
            WHERE
                    b.OFFER_CODE = '50004'
              AND b.product_code not in('****************','****************')
              AND b.ORDER_MODE = '3'
              AND a.ISTOP55 = '1'
        )
        SELECT
            X.OFFER_ORDER_ID,
            X.PRODUCT_ORDER_ID,

            -- 1-(((SUM(X.NOTAXFEE)/20000000000) - 1) * 0.05) as PROVINCE31_RATIO
            CASE
                WHEN (SUM(X.NOTAXFEE)-20000000000) < SUM(CASE WHEN X.ORGMONTH = inMonth THEN X.NOTAXFEE ELSE 0 END) THEN
                        (
                                ((SUM(X.NOTAXFEE)-20000000000)/SUM(CASE WHEN X.ORGMONTH = inMonth THEN X.NOTAXFEE ELSE 1 END)) * 1
                            ) + (
                                (1 - (SUM(X.NOTAXFEE)-20000000000)/SUM(CASE WHEN X.ORGMONTH = inMonth THEN X.NOTAXFEE ELSE 1 END)) * 0.95
                            )
                --第一次超出
                ELSE
                    1
                --除第一次超出外的第n次超出
                END  AS PROVINCE31_RATIO
        FROM
            HW_1000 X
                JOIN
            (
                SELECT
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID,
                    MAX( ORGMONTH ) END_DATE,
                    CASE
                        WHEN MAX( EFFECTIVE_DATE ) = LAST_DAY(MAX( EFFECTIVE_DATE )) THEN
                            TRUNC(
                                    ADD_MONTHS(  MAX( EFFECTIVE_DATE ),
                                                 12 * TRUNC( ( MONTHS_BETWEEN(
                                                         TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))
                                                                 ) / 12, 0  ) + 1
                                    )
                                , 'MONTH')
                        ELSE
                            ADD_MONTHS(
                                    MAX( EFFECTIVE_DATE ),
                                    12 * TRUNC((MONTHS_BETWEEN( TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))  )/12,0)
                            )
                        END as START_DATE
                FROM
                    HW_1000
                GROUP BY
                    OFFER_ORDER_ID,
                    PRODUCT_ORDER_ID
            ) T
            ON  X.OFFER_ORDER_ID = T.OFFER_ORDER_ID
                AND X.PRODUCT_ORDER_ID = T.PRODUCT_ORDER_ID
        WHERE
                X.ORGMONTH >= to_char(T.START_DATE, 'yyyymm' )
          AND X.ORGMONTH <= T.END_DATE
        GROUP BY
            X.OFFER_ORDER_ID,X.PRODUCT_ORDER_ID
        HAVING SUM(NOTAXFEE) > 20000000000
    ) W
    ON (A.OFFER_ORDER_ID = W.OFFER_ORDER_ID AND A.PRODUCT_ORDER_ID = W.PRODUCT_ORDER_ID)
    WHEN MATCHED THEN
        UPDATE SET A.RATIO = ROUND(A.RATIO * W.PROVINCE31_RATIO, 12)
    WHERE A.ORDER_MODE = '3'
            AND A.ISTOP55 = '1'
            AND A.ACCT_MONTH = INMONTH;


--受理模式3 top55订购超过1000w的结算比例 非华为  签约省
INSERT INTO STL_CDN_RULE
SELECT DISTINCT
    inMonth,
    x.ec_code,
    x.order_mode,
    x.offer_code,
    x.product_code,
    x.offer_order_id,
    x.product_order_id,
    '${EC_PROV_CODE_07}',
    0,
    0,
    ROUND(t.sign_ratio* 100, 12),
    '98',
    '1'
FROM STL_CDN_RULE x
         JOIN (
    SELECT
        X.OFFER_ORDER_ID,
        X.PRODUCT_ORDER_ID,

        -- ((sum( NOTAXFEE )/20000000000) - 1) * 0.05  as SIGN_RATIO
        (1 - (SUM(X.NOTAXFEE)-20000000000)/SUM(CASE WHEN X.ORGMONTH = inMonth THEN X.NOTAXFEE ELSE 1 END)) * 0.05 as SIGN_RATIO
    FROM
        STL_CDN_RULE_1000 X
            JOIN
        (
            SELECT
                OFFER_ORDER_ID,
                PRODUCT_ORDER_ID,
                MAX( ORGMONTH ) END_DATE,
                CASE
                    WHEN MAX( EFFECTIVE_DATE ) = LAST_DAY(MAX( EFFECTIVE_DATE )) THEN
                        TRUNC(
                                ADD_MONTHS(  MAX( EFFECTIVE_DATE ),
                                             12 * TRUNC( ( MONTHS_BETWEEN(
                                                     TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))
                                                             ) / 12, 0  ) + 1
                                )
                            , 'MONTH')
                    ELSE
                        ADD_MONTHS(
                                MAX( EFFECTIVE_DATE ),
                                12 * TRUNC((MONTHS_BETWEEN( TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))  )/12,0)
                        )
                    END as START_DATE
            FROM
                STL_CDN_RULE_1000
            GROUP BY
                OFFER_ORDER_ID,
                PRODUCT_ORDER_ID
        ) T
        ON  X.OFFER_ORDER_ID = T.OFFER_ORDER_ID
            AND X.PRODUCT_ORDER_ID = T.PRODUCT_ORDER_ID
            AND X.PRODUCT_CODE not in ('****************','****************' )
    WHERE
            X.ORGMONTH >= to_char(T.START_DATE, 'yyyymm' )
      AND X.ORGMONTH <= T.END_DATE
    GROUP BY
        X.OFFER_ORDER_ID,X.PRODUCT_ORDER_ID
    HAVING SUM(X.NOTAXFEE) > 20000000000
       AND  (SUM(X.NOTAXFEE)-20000000000) < SUM(CASE WHEN X.ORGMONTH = inMonth THEN X.NOTAXFEE ELSE 0 END)
) t
              ON x.OFFER_ORDER_ID = t.OFFER_ORDER_ID
                  AND x.PRODUCT_ORDER_ID = t.PRODUCT_ORDER_ID
WHERE
        x.order_mode = '3'
  AND x.istop55 = '1'
  AND x.acct_month = inMonth;


--受理模式3 top55订购超过1000w的结算比例 华为  签约省
INSERT INTO STL_CDN_HW_RULE
SELECT DISTINCT
    inMonth,
    x.ec_code,
    x.order_mode,
    x.offer_code,
    x.product_code,
    x.offer_order_id,
    x.product_order_id,
    '${EC_PROV_CODE_07}',
    0,
    0,
    ROUND(t.sign_ratio* 100, 12),
    '98',
    '1'
FROM STL_CDN_HW_RULE x
         JOIN (
    WITH HW_1000 AS (
        SELECT
            b.ORDER_MODE,
            b.ACCT_MONTH ORGMONTH,
            b.OFFER_CODE,
            b.PRODUCT_CODE,
            b.OFFER_ORDER_ID,
            b.PRODUCT_ORDER_ID,
            (b.CMCC_FEE + b.OTH_FEE) as NOTAXFEE,
            a.EFFECTIVE_DATE,
            a.ISTOP55
        FROM
            STLUSERS.STL_SERV_BIZ_CODE a
                JOIN stludr.STL_CDN_ORDER_FEE b
                     ON a.PROD_ORDER_MODE = b.ORDER_MODE
                         AND a.PROD_ORDER_ID = b.OFFER_ORDER_ID
                         AND a.ORDER_ID = b.PRODUCT_ORDER_ID
        WHERE
                b.OFFER_CODE = '50004'
          AND b.product_code not in('****************','****************')
          AND b.ORDER_MODE = '3'
          AND a.ISTOP55 = '1'
    )
    SELECT
        X.OFFER_ORDER_ID,
        X.PRODUCT_ORDER_ID,

        -- ((sum( NOTAXFEE )/20000000000) - 1) * 0.05  as SIGN_RATIO
        (1 - (SUM(X.NOTAXFEE)-20000000000)/SUM(CASE WHEN X.ORGMONTH = inMonth THEN X.NOTAXFEE ELSE 1 END)) * 0.05 as SIGN_RATIO
    FROM
        HW_1000 X
            JOIN
        (
            SELECT
                OFFER_ORDER_ID,
                PRODUCT_ORDER_ID,
                MAX( ORGMONTH ) END_DATE,
                CASE
                    WHEN MAX( EFFECTIVE_DATE ) = LAST_DAY(MAX( EFFECTIVE_DATE )) THEN
                        TRUNC(
                                ADD_MONTHS(  MAX( EFFECTIVE_DATE ),
                                             12 * TRUNC( ( MONTHS_BETWEEN(
                                                     TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))) / 12, 0  ) + 1
                                )
                            , 'MONTH')
                    ELSE
                        ADD_MONTHS(
                                MAX( EFFECTIVE_DATE ),
                                12 * TRUNC((MONTHS_BETWEEN( TO_DATE( MAX( ORGMONTH ), 'yyyymm' ), TRUNC( MAX( EFFECTIVE_DATE )))) / 12,0)
                        )
                    END as START_DATE
            FROM
                HW_1000
            GROUP BY
                OFFER_ORDER_ID,
                PRODUCT_ORDER_ID
        ) T
        ON  X.OFFER_ORDER_ID = T.OFFER_ORDER_ID
            AND X.PRODUCT_ORDER_ID = T.PRODUCT_ORDER_ID
    WHERE
            X.ORGMONTH >= to_char(T.START_DATE, 'yyyymm' )
      AND X.ORGMONTH <= T.END_DATE
    GROUP BY
        X.OFFER_ORDER_ID,X.PRODUCT_ORDER_ID
    HAVING SUM(X.NOTAXFEE) > 20000000000
       AND  (SUM(X.NOTAXFEE)-20000000000) < SUM(CASE WHEN X.ORGMONTH = inMonth THEN X.NOTAXFEE ELSE 0 END)
) t
              ON  x.OFFER_ORDER_ID = t.OFFER_ORDER_ID
                  AND x.PRODUCT_ORDER_ID = t.PRODUCT_ORDER_ID
WHERE
        x.order_mode = '3'
  AND x.istop55 = '1'
  AND x.acct_month = inMonth;

commit;

----BBOSS出账的CDN
--省间结算部分
INSERT INTO STL_REPART_PARAMETER_T(ID,OFFER_CODE,PRODUCT_CODE,PROD_INST_ID,SVC_INST_ID,
                                   ORDER_MODE,RULE_ID,RATE_ID,CALC_PRIORITY,
                                   OBJECT_VALUE,TARIFF_TYPE,RATE_VALUE,EFF_DATE,
                                   EXP_DATE,ACCT_MONTH,DEST_SOURCE,CHARGE_ITEM,ROUTE_FLAG)
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*, ft.FEETYPE, 1 route_flag
FROM
    (SELECT a.OFFER_CODE offer_code, a.PRODUCT_CODE product_code,
            a.OFFER_ORDER_ID poid_inst_id, a.PRODUCT_ORDER_ID svc_inst_id, a.ORDER_MODE order_mode, b.RULE_ID rule_id,
            c.RATE_ID rate_id, row_number() over(PARTITION BY a.PRODUCT_ORDER_ID ORDER BY to_number(a.RATIO)) - 1
                 calc_priority, a.SETT_PROV object_value, '1' tariff_type,
            a.RATIO / 100 rate_value, to_date(a.ACCT_MONTH, 'yyyymm') eff_date, add_months(to_date(
                                                                                                   a.ACCT_MONTH, 'yyyymm'), 1) - 1 exp_date, a.ACCT_MONTH acct_month,a.dest_source
     FROM STL_CDN_RULE a,
          STL_OFFER_T b,
          STL_RATE_T c
     WHERE a.OFFER_CODE = b.OFFER_CODE
       AND a.ORDER_MODE = b.ORDER_MODE
       AND b.PRODUCT_CODE = -1
       AND b.RULE_ID = c.RULE_ID
       AND b.DATA_SOURCE = 1
       and a.offer_code = '50004'
       and b.dest_source = '0'
       AND a.ACCT_MONTH = inMonth
       and to_date(inMonth, 'YYYYMM') between b.eff_date and b.exp_date) data,
    (SELECT '16' feetype FROM DUAL UNION SELECT '1063' FROM dual
     UNION SELECT '1101' FROM DUAL UNION SELECT '1102' FROM DUAL UNION SELECT '1467' feetype FROM dual UNION SELECT '4018' feetype FROM DUAL
     UNION SELECT '1468' FROM DUAL UNION SELECT '1469' FROM DUAL UNION SELECT '1470' feetype FROM DUAL) ft;
select concat('51_', now());
--华为结算部分
INSERT INTO STL_REPART_PARAMETER_T(ID,OFFER_CODE,PRODUCT_CODE,PROD_INST_ID,SVC_INST_ID,
                                   ORDER_MODE,RULE_ID,RATE_ID,CALC_PRIORITY,
                                   OBJECT_VALUE,TARIFF_TYPE,RATE_VALUE,EFF_DATE,
                                   EXP_DATE,ACCT_MONTH,DEST_SOURCE,CHARGE_ITEM,ROUTE_FLAG)
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*, ft.FEETYPE, 1 route_flag
FROM
    (SELECT a.OFFER_CODE offer_code, a.PRODUCT_CODE product_code,
            a.OFFER_ORDER_ID poid_inst_id, a.PRODUCT_ORDER_ID svc_inst_id, a.ORDER_MODE order_mode, b.RULE_ID rule_id,
            c.RATE_ID rate_id, row_number() over(PARTITION BY a.PRODUCT_ORDER_ID ORDER BY to_number(a.RATIO)) - 1
                 calc_priority, a.SETT_PROV object_value, '1' tariff_type,
            a.RATIO / 100 rate_value, to_date(a.ACCT_MONTH, 'yyyymm') eff_date, add_months(to_date(
                                                                                                   a.ACCT_MONTH, 'yyyymm'), 1) - 1 exp_date, a.ACCT_MONTH acct_month,a.dest_source
     FROM STL_CDN_HW_RULE a,
          STL_OFFER_T b,
          STL_RATE_T c
     WHERE a.OFFER_CODE = b.OFFER_CODE
       AND a.ORDER_MODE = b.ORDER_MODE
       AND b.PRODUCT_CODE = -1
       AND b.RULE_ID = c.RULE_ID
       AND b.DATA_SOURCE = 1
       and a.offer_code = '50004'
       and b.dest_source = '98'
       AND a.ACCT_MONTH = inMonth
       and to_date(inMonth, 'YYYYMM') between b.eff_date and b.exp_date) data,
    (SELECT '116' feetype FROM DUAL
     union all
     select resource_specode From stludr.cdn_vas_dict) ft;
select concat('52_', now());
---- EBOSS出账的CDN
-- 省间结算部分（互联网客户、苏研直签客户）

INSERT INTO STL_REPART_PARAMETER_T(ID,OFFER_CODE,PRODUCT_CODE,PROD_INST_ID,SVC_INST_ID,
                                   ORDER_MODE,RULE_ID,RATE_ID,CALC_PRIORITY,
                                   OBJECT_VALUE,TARIFF_TYPE,RATE_VALUE,EFF_DATE,
                                   EXP_DATE,ACCT_MONTH,DEST_SOURCE,CHARGE_ITEM,ROUTE_FLAG)
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*, ft.FEETYPE, 1 route_flag
FROM
    (SELECT a.OFFER_CODE offer_code, a.PRODUCT_CODE product_code,
            a.OFFER_ORDER_ID poid_inst_id, a.PRODUCT_ORDER_ID svc_inst_id, a.ORDER_MODE order_mode, b.RULE_ID rule_id,
            c.RATE_ID rate_id, row_number() over(PARTITION BY a.PRODUCT_ORDER_ID ORDER BY to_number(a.RATIO)) - 1
                 calc_priority, a.SETT_PROV object_value, '1' tariff_type,
            a.RATIO / 100 rate_value, to_date(a.ACCT_MONTH, 'yyyymm') eff_date, add_months(to_date(
                                                                                                   a.ACCT_MONTH, 'yyyymm'), 1) - 1 exp_date, a.ACCT_MONTH acct_month,'98' dest_source
     FROM STL_CDN_RULE a,
          STL_OFFER_T b,
          STL_RATE_T c
     WHERE a.OFFER_CODE = b.OFFER_CODE
       AND a.ORDER_MODE = b.ORDER_MODE
       AND b.PRODUCT_CODE = '9200397'
       AND b.RULE_ID = c.RULE_ID
       AND b.DATA_SOURCE = 1
       and a.offer_code = '9200397'
       and b.route_code = '0'
       AND a.ACCT_MONTH = inMonth
       and to_date(inMonth, 'YYYYMM') between b.eff_date and b.exp_date) data,
    (SELECT '16' feetype FROM DUAL) ft;
commit;
select concat('53_', now());
--省间结算部分（后付费政企客户）
INSERT INTO STL_REPART_PARAMETER_T(ID,OFFER_CODE,PRODUCT_CODE,PROD_INST_ID,SVC_INST_ID,
                                   ORDER_MODE,RULE_ID,RATE_ID,CALC_PRIORITY,
                                   OBJECT_VALUE,TARIFF_TYPE,RATE_VALUE,EFF_DATE,
                                   EXP_DATE,ACCT_MONTH,DEST_SOURCE,CHARGE_ITEM,ROUTE_FLAG)
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*, ft.FEETYPE, 1 route_flag
FROM
    (SELECT a.OFFER_CODE offer_code, a.PRODUCT_CODE product_code,
            a.OFFER_ORDER_ID poid_inst_id, a.PRODUCT_ORDER_ID svc_inst_id, a.ORDER_MODE order_mode, b.RULE_ID rule_id,
            c.RATE_ID rate_id, row_number() over(PARTITION BY a.PRODUCT_ORDER_ID ORDER BY to_number(a.RATIO)) - 1
                   calc_priority, a.SETT_PROV object_value, '1' tariff_type,
            a.RATIO / 100 rate_value, to_date(a.ACCT_MONTH, 'yyyymm') eff_date, add_months(to_date(
                                                                                                   a.ACCT_MONTH, 'yyyymm'), 1) - 1 exp_date, a.ACCT_MONTH acct_month, '0' dest_source
     FROM STL_CDN_RULE a,
          STL_OFFER_T b,
          STL_RATE_T c
     WHERE a.OFFER_CODE = b.OFFER_CODE
       AND a.ORDER_MODE = b.ORDER_MODE
       AND b.PRODUCT_CODE = '9200397'
       AND b.RULE_ID = c.RULE_ID
       AND b.DATA_SOURCE = 1
       and a.offer_code = '9200397'
       and b.route_code = '0'
       AND a.ACCT_MONTH = inMonth
       and to_date(inMonth, 'YYYYMM') between b.eff_date and b.exp_date) data,
    (SELECT '17' feetype FROM DUAL) ft;
select concat('54_', now());
--华为结算部分
INSERT INTO STL_REPART_PARAMETER_T(ID,OFFER_CODE,PRODUCT_CODE,PROD_INST_ID,SVC_INST_ID,
                                   ORDER_MODE,RULE_ID,RATE_ID,CALC_PRIORITY,
                                   OBJECT_VALUE,TARIFF_TYPE,RATE_VALUE,EFF_DATE,
                                   EXP_DATE,ACCT_MONTH,DEST_SOURCE,CHARGE_ITEM,ROUTE_FLAG)
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*, '98', ft.FEETYPE, 1 route_flag
FROM
    (SELECT a.OFFER_CODE offer_code, a.PRODUCT_CODE product_code,
            a.OFFER_ORDER_ID poid_inst_id, a.PRODUCT_ORDER_ID svc_inst_id, a.ORDER_MODE order_mode, b.RULE_ID rule_id,
            c.RATE_ID rate_id, row_number() over(PARTITION BY a.PRODUCT_ORDER_ID ORDER BY to_number(a.RATIO)) - 1
                 calc_priority, a.SETT_PROV object_value, '1' tariff_type,
            a.RATIO / 100 rate_value, to_date(a.ACCT_MONTH, 'yyyymm') eff_date, add_months(to_date(
                                                                                                   a.ACCT_MONTH, 'yyyymm'), 1) - 1 exp_date, a.ACCT_MONTH acct_month
     FROM STL_CDN_HW_RULE a,
          STL_OFFER_T b,
          STL_RATE_T c
     WHERE a.OFFER_CODE = b.OFFER_CODE
       AND a.ORDER_MODE = b.ORDER_MODE
       AND b.PRODUCT_CODE = '9200397'
       AND b.RULE_ID = c.RULE_ID
       AND b.DATA_SOURCE = 1
       and a.offer_code = '9200397'
       and b.route_code = '3'
       AND a.ACCT_MONTH = inMonth
       and to_date(inMonth, 'YYYYMM') between b.eff_date and b.exp_date) data,
    (SELECT '116' feetype FROM DUAL union all
     SELECT '117' feetype FROM DUAL union all
     select resource_specode From stludr.cdn_vas_dict) ft;
commit;

select('开始处理CDN特殊订购结算规则');
call STL_CDN_SPECIAL_SETT(inMonth,outReturn,outSysError);


-- CDN 直播源站类产品结算规则
BEGIN

      -- 先删除直播源产品的规则
delete from STL_REPART_PARAMETER_T where offer_code = '50004' and product_code = '****************';
INSERT INTO STL_REPART_PARAMETER_T(ID,OFFER_CODE,PRODUCT_CODE,PROD_INST_ID,SVC_INST_ID,
                                   ORDER_MODE,RULE_ID,RATE_ID,CALC_PRIORITY,OBJECT_VALUE,TARIFF_TYPE,RATE_VALUE,EFF_DATE,
                                   EXP_DATE,ACCT_MONTH,CHARGE_ITEM,DEST_SOURCE,ROUTE_FLAG)
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, t.* ,1 ROUTE_FLAG
FROM
    (
        SELECT * FROM (
                          WITH tmp AS (
                              SELECT
                                  a.POID,
                                  a.SOID,
                                  b.CALC_PRIORITY,
                                  b.OBJECT_VALUE ,
                                  b.RATE_VALUE ,
                                  b.ACCT_MONTH ,
                                  b.ORDER_MODE
                              FROM
                                  stl_sync_attr a,
                                  (
                                      SELECT b.OFFER_CODE,b.SVC_INST_ID,b.CALC_PRIORITY,b.OBJECT_VALUE,b.RATE_VALUE,b.ACCT_MONTH,b.ORDER_MODE,b.CHARGE_ITEM
                                      FROM stl_sync_attr a,STL_REPART_PARAMETER_T b WHERE
                                              a.ATTR_VALUE = b.SVC_INST_ID
                                                                                      AND b.TARIFF_TYPE = 1
                                                                                      AND a.POSPECNUMBER = b.OFFER_CODE
                                                                                      AND a.POSPECNUMBER = '50004'
                                                                                      AND a.SOSPECNUMBER = '****************'
                                                                                      AND b.CHARGE_ITEM NOT IN (
                                              SELECT '116' feetype FROM DUAL union all
                                              SELECT '117' feetype FROM DUAL union all
                                              SELECT '4039' feetype FROM DUAL union all
                                              SELECT '501' feetype FROM DUAL union all
                                              select resource_specode FROM stludr.cdn_vas_dict)
                                  ) b
                              WHERE
                                      a.ATTR_VALUE = b.SVC_INST_ID
                                AND a.POSPECNUMBER = b.OFFER_CODE
                                AND a.POSPECNUMBER = '50004'
                                AND a.SOSPECNUMBER = '****************'
                              GROUP BY
                                  a.POID,
                                  a.SOID,
                                  b.CALC_PRIORITY,
                                  b.OBJECT_VALUE ,
                                  b.RATE_VALUE ,
                                  b.ACCT_MONTH,
                                  b.ORDER_MODE
                          )
                          SELECT a.OFFER_CODE,a.PRODUCT_CODE ,tmp.POID,tmp.SOID ,a.ORDER_MODE,a.RULE_ID , '1' RATE_ID ,
                                 tmp.CALC_PRIORITY,tmp.OBJECT_VALUE ,'1' TARIFF_TYPE,tmp.RATE_VALUE,
                                 to_date(tmp.ACCT_MONTH, 'yyyymm') eff_date, add_months(to_date(tmp.ACCT_MONTH, 'yyyymm'), 1) - 1 exp_date,
                                 tmp.ACCT_MONTH,b.CHARGE_ITEM,a.DEST_SOURCE
                          FROM  STL_OFFER_T a,STL_RULE_ITEM_T b ,tmp
                          WHERE
                                  a.RULE_ID =b.RULE_ID
                            AND a.OFFER_CODE ='50004'
                            AND a.PRODUCT_CODE ='****************'
                            AND a.ORDER_MODE =tmp.ORDER_MODE
                            AND b.CHARGE_ITEM IN ('3970','7970')
                          ORDER BY tmp.SOID,b.CHARGE_ITEM
                      ) A
        UNION ALL
        SELECT * FROM (

                          -- 华为结算部分
                          WITH HW AS (
                              SELECT
                                  a.POID,
                                  a.SOID,
                                  b.CALC_PRIORITY,
                                  b.OBJECT_VALUE ,
                                  b.RATE_VALUE ,
                                  b.ACCT_MONTH ,
                                  b.ORDER_MODE
                              FROM
                                  stl_sync_attr a,
                                  (
                                      SELECT b.OFFER_CODE,b.SVC_INST_ID,b.CALC_PRIORITY,b.OBJECT_VALUE,b.RATE_VALUE,b.ACCT_MONTH,b.ORDER_MODE,b.CHARGE_ITEM
                                      FROM stl_sync_attr a,STL_REPART_PARAMETER_T b WHERE
                                              a.ATTR_VALUE = b.SVC_INST_ID
                                                                                      AND b.TARIFF_TYPE = 1
                                                                                      AND a.POSPECNUMBER = b.OFFER_CODE
                                                                                      AND a.POSPECNUMBER = '50004'
                                                                                      AND a.SOSPECNUMBER = '****************'
                                                                                      AND b.CHARGE_ITEM IN (
                                              SELECT '116' feetype FROM DUAL union all
                                              SELECT '117' feetype FROM DUAL union all
                                              select resource_specode FROM stludr.cdn_vas_dict)
                                  ) b
                              WHERE
                                      a.ATTR_VALUE = b.SVC_INST_ID
                                AND a.POSPECNUMBER = b.OFFER_CODE
                                AND a.POSPECNUMBER = '50004'
                                AND a.SOSPECNUMBER = '****************'
                              GROUP BY
                                  a.POID,
                                  a.SOID,
                                  b.CALC_PRIORITY,
                                  b.OBJECT_VALUE ,
                                  b.RATE_VALUE ,
                                  b.ACCT_MONTH,
                                  b.ORDER_MODE
                              ORDER BY a.SOID
                          )
                          SELECT a.OFFER_CODE,a.PRODUCT_CODE ,HW.POID,HW.SOID ,a.ORDER_MODE,a.RULE_ID , '1' RATE_ID ,
                                 HW.CALC_PRIORITY,HW.OBJECT_VALUE ,'1' TARIFF_TYPE,HW.RATE_VALUE,
                                 to_date(HW.ACCT_MONTH, 'yyyymm') eff_date, add_months(to_date(HW.ACCT_MONTH, 'yyyymm'), 1) - 1 exp_date,
                                 HW.ACCT_MONTH,b.CHARGE_ITEM,a.DEST_SOURCE
                          FROM  STL_OFFER_T a ,STL_RULE_ITEM_T b,HW
                          WHERE
                                  a.RULE_ID =b.RULE_ID
                            AND a.OFFER_CODE ='50004'
                            AND a.PRODUCT_CODE ='****************'
                            AND a.RULE_ID IN ('666','667','668')
                            AND a.ORDER_MODE =HW.ORDER_MODE
                          ORDER BY HW.SOID ,b.CHARGE_ITEM
                      ) B
    ) t;
COMMIT;

END;


select concat('55_', now());
--插入能力开放结算规则

set @vSql := 'TRUNCATE TABLE STL_NLKF_RULE';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
            outSysError := 'OK';


            set @vSql := 'INSERT INTO STL_NLKF_RULE ' ||
                           '(ACCT_MONTH, ORDER_MODE, EC_CODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID, FEETYPE, SETT_PROV, ' ||
                           'PROV_AMOUNT, TOTAL_AMOUNT, RATIO, DEST_SOURCE, ROUTE_FLAG) ' ||
                         'SELECT a.orgmonth, ' ||
                           'a.ordermode,  ' ||
                           'a.customernumber, ' ||
                           'a.pospecnumber, ' ||
                           'a.sospecnumber, ' ||
                           'a.poid, ' ||
                           'a.soid, ' ||
                           'a.feetype, ' ||
                           'a.prov_cd, ' ||
                           'a.orgfee, ' ||
                           'b.total_fee, ' ||
                           'decode(a.sospecnumber,''5001606'',round(a.orgfee / decode(b.total_fee, 0, 1, b.total_fee) * 100 * 0.3, 7),  '||
                           ' ''5001602'',round(a.orgfee / decode(b.total_fee, 0, 1, b.total_fee) * 100 * 0.3, 7),   '||
                           ' ''5001603'',round(a.orgfee / decode(b.total_fee, 0, 1, b.total_fee) * 100 * 0.3, 7),   '||
                           ' ''5001604'',round(a.orgfee / decode(b.total_fee, 0, 1, b.total_fee) * 100 * 0.3, 7),   '||
               ' ''20009'',round(a.orgfee / decode(b.total_fee, 0, 1, b.total_fee) * 100 * 0.3, 7),   '||
                           'round(a.orgfee / decode(b.total_fee, 0, 1, b.total_fee) * 100 * 0.7, 7))  ratio, ' ||
                           '''0'' dest_source, ' ||
                           '''1'' route_flag ' ||
                      'FROM ' ||
                          '(SELECT ORGMONTH, ' ||
                                 'ORDERMODE, ' ||
                                 'CUSTOMERNUMBER, ' ||
                                 'POSPECNUMBER, ' ||
                                 'SOSPECNUMBER, ' ||
                                 'POID, ' ||
                                 'SOID, ' ||
                                 'FEETYPE, ' ||
                                 'PROV_CD, ' ||
                                 'sum(AMOUNT) orgfee ' ||
                            'FROM stludr.SYNC_BL_RULE_' || inMonth || ' ' ||
                           'WHERE STATUS = ''0'' ' ||
                             'AND (POSPECNUMBER in (''50016'',''0102001'') AND SOSPECNUMBER <> ''5001613'' ) or  (POSPECNUMBER =''50121'' AND SOSPECNUMBER = ''5001612'' ) '||
                             'AND DATASOURCE = ''BL'' ' ||
                             'AND ORGMONTH = ' || inMonth || ' ' ||
                           'GROUP BY ORGMONTH, ORDERMODE, CUSTOMERNUMBER, POSPECNUMBER, SOSPECNUMBER, POID, SOID, FEETYPE, PROV_CD) a, ' ||
                          '(SELECT ORGMONTH, ' ||
                                 'ORDERMODE, ' ||
                                 'CUSTOMERNUMBER, ' ||
                                 'SOSPECNUMBER, ' ||
                                 'POID, ' ||
                                 'SOID, ' ||
                                 'FEETYPE, ' ||
                                 'sum(AMOUNT) total_fee ' ||
                            'FROM stludr.SYNC_BL_RULE_' || inMonth || ' ' ||
                           'WHERE STATUS = ''0'' ' ||
                             'AND (POSPECNUMBER in (''50016'',''0102001'') AND SOSPECNUMBER <> ''5001613'' ) or  (POSPECNUMBER =''50121'' AND SOSPECNUMBER = ''5001612'' ) '||
                             'AND DATASOURCE = ''BL'' ' ||
                             'AND ORGMONTH = ' || inMonth || ' ' ||
                           'GROUP BY ORGMONTH, ORDERMODE, CUSTOMERNUMBER, SOSPECNUMBER, POID, SOID, FEETYPE) b ' ||
                     'WHERE a.poid = b.poid ' ||
                       'AND a.soid = b.soid ' ||
                       'and a.feetype = b.feetype';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
select concat('56_', now());
outReturn := 0;
            outSysError := 'OK';

INSERT INTO STL_NLKF_RULE
SELECT ACCT_MONTH, EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID,PRODUCT_ORDER_ID, '${EC_PROV_CODE_07}' SETT_PROV, FEETYPE,
       NULL, NULL, decode(product_code,'20009',70,'5001606',70,'5001602',70,'5001603',70,'5001604',70,30), '0', decode(product_code, '5001606', '0', '1')
FROM STL_NLKF_RULE
WHERE ACCT_MONTH = inMonth
GROUP BY ACCT_MONTH, EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID,PRODUCT_ORDER_ID, FEETYPE;

select concat('57_', now());
INSERT INTO STL_REPART_PARAMETER_T(ID,OFFER_CODE,PRODUCT_CODE,PROD_INST_ID,SVC_INST_ID,
                                   ORDER_MODE,RULE_ID,RATE_ID,CALC_PRIORITY,
                                   OBJECT_VALUE,TARIFF_TYPE,RATE_VALUE,EFF_DATE,
                                   EXP_DATE,ACCT_MONTH,DEST_SOURCE,CHARGE_ITEM,ROUTE_FLAG)
SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.*
FROM
    (SELECT a.OFFER_CODE offer_code, a.PRODUCT_CODE product_code,
            a.OFFER_ORDER_ID poid_inst_id, a.PRODUCT_ORDER_ID svc_inst_id, a.ORDER_MODE order_mode, b.RULE_ID rule_id,
            c.RATE_ID rate_id, row_number() over(PARTITION BY a.PRODUCT_ORDER_ID, a.feetype ORDER BY to_number(a.RATIO)) - 1
                 calc_priority, a.SETT_PROV object_value, '1' tariff_type,
            a.RATIO / 100 rate_value, to_date(a.ACCT_MONTH, 'yyyymm') eff_date, add_months(to_date(
                                                                                                   a.ACCT_MONTH, 'yyyymm'), 1) - 1 exp_date, a.ACCT_MONTH acct_month,a.dest_source, d.charge_item, a.route_flag
     FROM STL_NLKF_RULE a,
          STL_OFFER_T b,
          STL_RATE_T c,
          stl_rule_item_t d
     WHERE a.OFFER_CODE = b.OFFER_CODE
       AND a.PRODUCT_CODE = b.PRODUCT_CODE
       AND a.ORDER_MODE = b.ORDER_MODE
       AND b.RULE_ID = c.RULE_ID
       and c.rule_id = d.rule_id
       and c.rate_type = 3
       and a.feetype = d.charge_item
       AND b.DATA_SOURCE = 1
       AND a.ratio <> 0
       AND a.ACCT_MONTH = inMonth
       and to_date(inMonth, 'YYYYMM') between b.eff_date and b.exp_date
       and to_date(inMonth, 'YYYYMM') between c.eff_date and c.exp_date
       and to_date(inMonth, 'YYYYMM') between d.eff_date and d.exp_date) data;


select concat('58_', now());

--插入企业和多号全网产品规则

set @vSql := 'INSERT INTO STL_REPART_PARAMETER_T ' ||
                 'select SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.* from ' ||
                 '(select a.pospecnumber offer_code, ' ||
                 'a.sospecnumber product_code, poid poid_inst_id, soid svc_inst_id, a.ordermode order_mode, ' ||
                 'b.rule_id, c.rate_id, decode(a.remark, ''M'', 1, 0) calc_priority, a.prov_cd object_value, ''2'' tariff_type, ' ||
                 'decode(a.remark, ''M'', ''$'', a.amount) rate_value, to_date(a.orgmonth, ''yyyymm'') eff_date, add_months(to_date( ' ||
                 'a.orgmonth, ''yyyymm''), 1) - 1 exp_date, a.orgmonth acct_month, a.feetype, 0 dest_source, 1 route_flag ' ||
            'from stludr.sync_bl_rule_' || inMonth || ' a, ' ||
                 'stl_offer_t b, ' ||
                 'stl_rate_t c, ' ||
                 'stl_rule_item_t d ' ||
           'where a.pospecnumber = b.OFFER_CODE ' ||
             'and a.sospecnumber = b.product_code ' ||
             'and a.pospecnumber = ''1101010'' ' ||
             'and a.sospecnumber = ''9101002'' ' ||
             'and b.data_source = 1 ' ||
             'AND a.ORDERMODE = b.ORDER_MODE ' ||
             'AND b.RULE_ID = c.RULE_ID ' ||
             'and b.rule_id = d.rule_id ' ||
             'and d.charge_item = a.feetype ' ||
             'and a.orgmonth = ' || inMonth || ' ' ||
             'order by order_mode, svc_inst_id, feetype, object_value) data';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
            outSysError := 'OK';

select concat('59_', now());
--插入EBOSS云网融合产品规则

set @vSql := 'INSERT INTO STL_REPART_PARAMETER_T ' ||
                 'select SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.* from ' ||
                 '(select a.product_spec_number offer_code, ' ||
                 '''-1'' product_code, a.product_id poid_inst_id, '''' svc_inst_id, a.order_mode order_mode, ' ||
                 'b.rule_id, c.rate_id, row_number() over(PARTITION BY a.product_id, a.prod_charge_code ORDER BY to_number(a.settle_rate)) calc_priority, a.settle_in_prov object_value, ''1'' tariff_type, ' ||
                 'round(to_number(a.settle_rate / 100), 7)  rate_value, to_date(a.bill_month, ''yyyymm'') eff_date, add_months(to_date( ' ||
                 'a.bill_month, ''yyyymm''), 1) - 1 exp_date, a.bill_month acct_month, a.prod_charge_code feetype, 0 dest_source, 1 route_flag ' ||
            'from stl_sync_eboss a, ' ||
                 'stl_offer_t b, ' ||
                 'stl_rate_t c, ' ||
                 'stl_rule_item_t d ' ||
           'where a.product_spec_number = b.OFFER_CODE ' ||
             'and a.status = ''0'' '  ||
             'and b.product_code = ''-1'' ' ||
             'and a.product_spec_number in (''9200374'', ''9200371'') ' ||
             'and b.data_source = 1 ' ||
             'AND a.ORDER_MODE = b.ORDER_MODE ' ||
             'AND b.RULE_ID = c.RULE_ID ' ||
             'and b.rule_id = d.rule_id ' ||
             'and a.bill_month = ' || inMonth || ' ' ||
             'order by order_mode, poid_inst_id, feetype, object_value) data';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;

outReturn := 0;
            outSysError := 'OK';

select concat('60_', now());
--插入智能路由规则

set @vSql := 'INSERT INTO STLUSERS.STL_REPART_PARAMETER_T ' ||
          'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.* FROM ' ||
           '(SELECT DISTINCT a.pospecnumber, a.sospecnumber, ' ||
                 'a.poid poid_inst_id, a.soid svc_inst_id, a.ORDERMODE, b.RULE_ID rule_id, ' ||
                 'c.RATE_ID rate_id, 0 calc_priority, a.prov_cd object_value, ' ||
                 'e.tariff_type tariff_type, a.amount rate_value, ' ||
                 'to_date(a.orgmonth, ''yyyymm'') eff_date, add_months(to_date( ' ||
                 'a.orgmonth, ''yyyymm''), 1) - 1 exp_date, a.orgmonth, a.feetype charge_item, ' ||
                 '0 dest_source, 0 route_flag ' ||
            'FROM stludr.SYNC_BL_SETTLE_' || inMonth || ' a, ' ||
                 'STL_OFFER_T b, ' ||
                 'STL_RATE_T c, ' ||
                 'stl_rule_item_t d, ' ||
                 'STL_REPART_RATE_T e ' ||
           'WHERE a.pospecnumber = b.OFFER_CODE ' ||
             'AND (a.sospecnumber = b.PRODUCT_CODE OR a.sospecnumber IS NULL) ' ||
             'AND a.soid IS NOT NULL ' ||
             'and a.ordermode = b.order_mode ' ||
             'and b.rule_id = d.rule_id ' ||
             'and a.feetype = d.charge_item ' ||
             'AND b.RULE_ID = c.RULE_ID ' ||
             'AND c.RATE_ID = e.RATE_ID ' ||
             'AND e.MATCH_MODE = 2 ' ||
             'AND b.DATA_SOURCE = 1 ' ||
             'AND c.RATE_TYPE = 3 ' ||
             'and a.ordermode = b.order_mode ' ||
             'AND ' || inMonth || ' BETWEEN to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') ' ||
             'and ' || inMonth || ' BETWEEN to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') ' ||
             'AND a.orgmonth = ' || inMonth || ' ' ||
             'AND a.pospecnumber = ''50021'' ' ||
           'GROUP BY a.pospecnumber, a.sospecnumber, a.poid, a.soid, a.ORDERMODE, b.RULE_ID, ' ||
                 'c.RATE_ID, e.tariff_type, a.orgmonth, a.prov_cd, a.feetype, a.amount) data';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
select concat('61_', now());
outReturn := 0;
            outSysError := 'OK';



        --插入梧桐风控收入还原规则

            set @vSql := 'INSERT INTO STLUSERS.STL_REPART_PARAMETER_T ' ||
          'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.* FROM  ' ||
          '(select distinct a.pospecnumber, a.sospecnumber, a.poid, a.soid, a.ordermode, b.rule_id, c.rate_id, 0 calc_priority, a.prov_cd object_values, ' ||
                 'd.tariff_type, sum(a.amount) rate_value, to_date(a.orgmonth, ''yyyymm'') eff_date, add_months(to_date(a.orgmonth, ''yyyymm''), 1) - 1 exp_date, ' ||
                 'a.orgmonth, ''5'' || a.feetype charge_item, ''99'' dest_source, 1 route_flag ' ||
            'from stludr.sync_bl_settle_' || inMonth || ' a,  ' ||
                 'stl_offer_t b, ' ||
                 'stl_rate_t c, ' ||
                 'stl_repart_rate_t d ' ||
           'where pospecnumber = ''50024'' and sospecnumber = ''2021999400052091'' and feetype like ''1%'' ' ||
             'and b.data_source = 1 and c.rate_type = 3 and d.match_mode = 2 ' ||
             'and a.pospecnumber = b.offer_code and a.sospecnumber = b.product_code and a.ordermode = b.order_mode ' ||
             'and b.rule_id = c.rule_id ' ||
             'and c.rate_id = d.rate_id ' ||
             'and ''' || inMonth || ''' between to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') ' ||
             'and ''' || inMonth || ''' between to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') ' ||
           'group by a.pospecnumber, a.sospecnumber, a.poid, a.soid, a.ordermode, b.rule_id, c.rate_id, a.prov_cd, ' ||
                    'd.tariff_type, a.orgmonth, a.feetype) data';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
select concat('62_', now());
outReturn := 0;
            outSysError := 'OK';


          set @vSql := 'INSERT INTO STLUSERS.STL_REPART_PARAMETER_T ' ||
          'SELECT SEQ_REPART_PARAMETER_ID.NEXTVAL id, data.* FROM  ' ||
          '(select distinct a.pospecnumber, a.sospecnumber, a.poid, a.soid, a.ordermode, b.rule_id, c.rate_id, 0 calc_priority, ''${EC_PROV_CODE_07}'' object_values, ' ||
                 'd.tariff_type, ''$'' rate_value, to_date(a.orgmonth, ''yyyymm'') eff_date, add_months(to_date(a.orgmonth, ''yyyymm''), 1) - 1 exp_date, ' ||
                 'a.orgmonth, a.feetype charge_item, ''0'' dest_source, 1 route_flag ' ||
            'from stludr.sync_interface_bl_' || inMonth || ' a,  ' ||
                 'stl_offer_t b, ' ||
                 'stl_rate_t c, ' ||
                 'stl_repart_rate_t d ' ||
           'where pospecnumber = ''50024'' and sospecnumber = ''2021999400052091'' ' ||
             'and b.data_source = 1 and c.rate_type = 3 and d.match_mode = 2 ' ||
             'and a.pospecnumber = b.offer_code and a.sospecnumber = b.product_code and a.ordermode = b.order_mode ' ||
             'and b.rule_id = c.rule_id ' ||
             'and c.rate_id = d.rate_id ' ||
             'and ''' || inMonth || ''' between to_char(b.EFF_DATE, ''yyyymm'') AND to_char(b.EXP_DATE, ''yyyymm'') ' ||
             'and ''' || inMonth || ''' between to_char(c.EFF_DATE, ''yyyymm'') AND to_char(c.EXP_DATE, ''yyyymm'') ' ||
           'group by a.pospecnumber, a.sospecnumber, a.poid, a.soid, a.ordermode, b.rule_id, c.rate_id, ' ||
                    'd.tariff_type, a.orgmonth, a.feetype) data';
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
select concat('63_', now());
outReturn := 0;
            outSysError := 'OK';


COMMIT;

SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
END;

END//

DELIMITER;