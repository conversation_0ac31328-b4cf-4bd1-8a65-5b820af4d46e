ALTER TABLE `stludr`.`BILL_12`
    CHANGE COLUMN `PAID_MONTH` `PAYMONTH` char(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL ;

ALTER TABLE `stludr`.`bill_16`
    <PERSON>AN<PERSON> COLUMN `PAID_MONTH` `PAYMONTH` char(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL ;

ALTER TABLE `stludr`.`bill_17`
    CHANGE COLUMN `PAID_MONTH` `PAYMONTH` char(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL ;

ALTER TABLE `stludr`.`bill_18`
    CHANGE COLUMN `PAID_MONTH` `PAYMONTH` char(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL ;

<PERSON>TER TABLE `stludr`.`bill_22`
    <PERSON>AN<PERSON> COLUMN `PAID_MONTH` `PAYMONTH` char(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL ;

ALTER TABLE `stludr`.`bill_24`
    CHANGE COLUMN `PAID_MONTH` `PAYMONTH` char(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL ;

ALTER TABLE `stludr`.`bill_29`
    CHANGE COLUMN `PAID_MONTH` `PAYMONTH` char(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL ;

ALTER TABLE `stludr`.`bill_31`
    CHANGE COLUMN `PAID_MONTH` `PAYMONTH` char(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL ;

ALTER TABLE `stludr`.`bill_32`
    CHANGE COLUMN `PAID_MONTH` `PAYMONTH` char(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL ;

ALTER TABLE `stludr`.`bill_33`
    CHANGE COLUMN `PAID_MONTH` `PAYMONTH` char(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NULL DEFAULT NULL ;


----12
drop table IF EXISTS stludr.BILL_12;
CREATE TABLE stludr.BILL_12( IN_OBJECT VARCHAR(20),
                          SETTMODE VARCHAR(10),
                          CUSTOMER_CODE VARCHAR(32),
                          OFFER_ORDER_ID DECIMAL(20,0),
                          ORG_MONTH  CHAR(6) ,
                          PAYMONTH CHAR(6) ,
                          PRODUCT_ORDER_ID DECIMAL(20,0),
                          MEMBER_CODE  VARCHAR(32),
                          MEMBER_PROV VARCHAR(3),
                          CHARGE_CODE VARCHAR(20),
                          SETTLEFEE  DECIMAL(20,2),
                          CDR_CHARGECODE VARCHAR(20),
                          CDR_CHARGENAME  VARCHAR(20),
                          TAXRATE   VARCHAR(6),
                          SETTLE_TAXFEE VARCHAR(6),
                          SETTLE_NOTAXFEE VARCHAR(6),
                          DD VARCHAR(8),
                          ADJMONTH CHAR(6),
                          FEETYPE CHAR(1)
) COMMENT='shard=shard1';

---- 16
drop table IF EXISTS stludr.BILL_16;
CREATE TABLE stludr.BILL_16( IN_OBJECT VARCHAR(20),
                          SETTMODE VARCHAR(10),
                          CUSTOMER_CODE VARCHAR(32),
                          OFFER_ORDER_ID DECIMAL(20,0),
                          ORG_MONTH  CHAR(6) ,
                          PAYMONTH CHAR(6) ,
                          PRODUCT_ORDER_ID DECIMAL(20,0),
                          MEMBER_CODE  VARCHAR(32),
                          MEMBER_PROV VARCHAR(3),
                          CHARGE_CODE VARCHAR(20),
                          SETTLEFEE  DECIMAL(20,2),
                          CDR_CHARGECODE VARCHAR(20),
                          CDR_CHARGENAME  VARCHAR(20),
                          TAXRATE   VARCHAR(6),
                          SETTLE_TAXFEE VARCHAR(6),
                          SETTLE_NOTAXFEE VARCHAR(6),
                          DD VARCHAR(8),
                          ADJMONTH CHAR(6),
                          FEETYPE CHAR(1)
) COMMENT='shard=shard1';

----17
drop table IF EXISTS stludr.BILL_17;
CREATE TABLE stludr.BILL_17( OUT_OBJECT VARCHAR(20),
                          SETTMODE VARCHAR(10),
                          CUSTOMER_CODE VARCHAR(32),
                          OFFER_ORDER_ID DECIMAL(20,0),
                          ORG_MONTH  CHAR(6) ,
                          PAYMONTH CHAR(6) ,
                          PRODUCT_ORDER_ID DECIMAL(20,0),
                          MEMBER_CODE  VARCHAR(32),
                          MEMBER_PROV VARCHAR(3),
                          CHARGE_CODE VARCHAR(20),
                          SETTLEFEE  DECIMAL(20,2),
                          CDR_CHARGECODE VARCHAR(20),
                          CDR_CHARGENAME  VARCHAR(20),
                          TAXRATE   VARCHAR(6),
                          SETTLE_TAXFEE VARCHAR(6),
                          SETTLE_NOTAXFEE VARCHAR(6),
                          DD VARCHAR(8),
                          ADJMONTH CHAR(6),
                          FEETYPE CHAR(1)
) COMMENT='shard=shard1';

----18
drop table IF EXISTS stludr.BILL_18;
CREATE TABLE stludr.BILL_18( OUT_OBJECT VARCHAR(20),
                          SETTMODE VARCHAR(10),
                          CUSTOMER_CODE VARCHAR(32),
                          OFFER_ORDER_ID DECIMAL(20,0),
                          ORG_MONTH  CHAR(6) ,
                          PAYMONTH CHAR(6) ,
                          PRODUCT_ORDER_ID DECIMAL(20,0),
                          MEMBER_CODE  VARCHAR(32),
                          MEMBER_PROV VARCHAR(3),
                          CHARGE_CODE VARCHAR(20),
                          SETTLEFEE  DECIMAL(20,2),
                          CDR_CHARGECODE VARCHAR(20),
                          CDR_CHARGENAME  VARCHAR(20),
                          TAXRATE   VARCHAR(6),
                          SETTLE_TAXFEE VARCHAR(6),
                          SETTLE_NOTAXFEE VARCHAR(6),
                          DD VARCHAR(8),
                          ADJMONTH CHAR(6),
                          FEETYPE CHAR(1)
) COMMENT='shard=shard1';

----22
drop table IF EXISTS stludr.BILL_22;
CREATE TABLE stludr.BILL_22( IN_OBJECT VARCHAR(20),
                          SETTMODE VARCHAR(10),
                          CUSTOMER_CODE VARCHAR(32),
                          OFFER_ORDER_ID DECIMAL(20,0),
                          ORG_MONTH  CHAR(6) ,
                          PAYMONTH CHAR(6) ,
                          PRODUCT_ORDER_ID DECIMAL(20,0),
                          MEMBER_CODE  VARCHAR(32),
                          MEMBER_PROV VARCHAR(3),
                          CHARGE_CODE VARCHAR(20),
                          SETTLEFEE  DECIMAL(20,2),
                          CDR_CHARGECODE VARCHAR(20),
                          CDR_CHARGENAME  VARCHAR(20),
                          TAXRATE   VARCHAR(6),
                          SETTLE_TAXFEE VARCHAR(6),
                          SETTLE_NOTAXFEE VARCHAR(6),
                          CHARGE decimal(16,0),
                          DD VARCHAR(8),
                          PRODATTR1 VARCHAR(1500),
                          PRODATTR2 VARCHAR(20),
                          ADJMONTH CHAR(6),
                          FEETYPE CHAR(1)
) COMMENT='shard=shard1';

----24
drop table IF EXISTS stludr.BILL_24;
CREATE TABLE stludr.BILL_24( OUT_OBJECT VARCHAR(20),
                          SETTMODE VARCHAR(10),
                          CUSTOMER_CODE VARCHAR(32),
                          OFFER_ORDER_ID DECIMAL(20,0),
                          ORG_MONTH  CHAR(6) ,
                          PAYMONTH CHAR(6) ,
                          PRODUCT_ORDER_ID DECIMAL(20,0),
                          MEMBER_CODE  VARCHAR(32),
                          MEMBER_PROV VARCHAR(3),
                          CHARGE_CODE VARCHAR(20),
                          SETTLEFEE  DECIMAL(20,2),
                          CDR_CHARGECODE VARCHAR(20),
                          CDR_CHARGENAME  VARCHAR(20),
                          TAXRATE   VARCHAR(6),
                          SETTLE_TAXFEE VARCHAR(6),
                          SETTLE_NOTAXFEE VARCHAR(6),
                          DD VARCHAR(8),
                          ADJMONTH CHAR(6),
                          FEETYPE CHAR(1)
) COMMENT='shard=shard1';

----29
drop table IF EXISTS stludr.BILL_29;
CREATE TABLE stludr.BILL_29( IN_OBJECT VARCHAR(20),
                          SETTMODE VARCHAR(10),
                          CUSTOMER_CODE VARCHAR(32),
                          OFFER_ORDER_ID DECIMAL(20,0),
                          ORG_MONTH  CHAR(6) ,
                          PAYMONTH CHAR(6) ,
                          PRODUCT_ORDER_ID DECIMAL(20,0),
                          MEMBER_CODE  VARCHAR(32),
                          MEMBER_PROV VARCHAR(3),
                          CHARGE_CODE VARCHAR(20),
                          SETTLEFEE  DECIMAL(20,2),
                          CDR_CHARGECODE VARCHAR(20),
                          CDR_CHARGENAME  VARCHAR(20),
                          TAXRATE   VARCHAR(6),
                          SETTLE_TAXFEE VARCHAR(6),
                          SETTLE_NOTAXFEE VARCHAR(6),
                          DD VARCHAR(8),
                          OUT_OBJECT VARCHAR(20),
                          ORDER_MODE VARCHAR(6),
                          OFFER_CODE VARCHAR(64) ,
                          PRODUCT_CODE VARCHAR(64),
                          ADJMONTH CHAR(6),
                          FEETYPE CHAR(1)
) COMMENT='shard=shard1';

----31
drop table IF EXISTS stludr.BILL_31;
CREATE TABLE stludr.BILL_31( IN_OBJECT VARCHAR(20),
                          SETTMODE VARCHAR(10),
                          CUSTOMER_CODE VARCHAR(32),
                          OFFER_ORDER_ID DECIMAL(20,0),
                          ORG_MONTH  CHAR(6) ,
                          PAYMONTH CHAR(6) ,
                          PRODUCT_ORDER_ID DECIMAL(20,0),
                          MEMBER_CODE  VARCHAR(32),
                          MEMBER_PROV VARCHAR(3),
                          CHARGE_CODE VARCHAR(20),
                          SETTLEFEE  DECIMAL(20,2),
                          CDR_CHARGECODE VARCHAR(20),
                          CDR_CHARGENAME  VARCHAR(20),
                          TAXRATE   VARCHAR(6),
                          SETTLE_TAXFEE VARCHAR(6),
                          SETTLE_NOTAXFEE VARCHAR(6),
                          DD VARCHAR(8),
                          OUT_OBJECT VARCHAR(20),
                          ORDER_MODE VARCHAR(6),
                          OFFER_CODE VARCHAR(64),
                          PRODUCT_CODE VARCHAR(64),
                          ADJMONTH CHAR(6),
                          FEETYPE CHAR(1)
) COMMENT='shard=shard1';

----32
drop table IF EXISTS stludr.BILL_32;
CREATE TABLE stludr.BILL_32( OUT_OBJECT VARCHAR(20),
                          SETTMODE VARCHAR(10),
                          CUSTOMER_CODE VARCHAR(32),
                          OFFER_ORDER_ID DECIMAL(20,0),
                          ORG_MONTH  CHAR(6) ,
                          PAYMONTH CHAR(6) ,
                          PRODUCT_ORDER_ID DECIMAL(20,0),
                          MEMBER_CODE  VARCHAR(32),
                          MEMBER_PROV VARCHAR(3),
                          CHARGE_CODE VARCHAR(20),
                          SETTLEFEE  DECIMAL(20,2),
                          CDR_CHARGECODE VARCHAR(20),
                          CDR_CHARGENAME  VARCHAR(20),
                          TAXRATE   VARCHAR(6),
                          SETTLE_TAXFEE VARCHAR(6),
                          SETTLE_NOTAXFEE VARCHAR(6),
                          DD VARCHAR(8),
                          IN_OBJECT VARCHAR(20),
                          ORDER_MODE VARCHAR(6),
                          OFFER_CODE VARCHAR(64),
                          PRODUCT_CODE VARCHAR(64),
                          ADJMONTH CHAR(6),
                          FEETYPE CHAR(1)
) COMMENT='shard=shard1';

----33
drop table IF EXISTS stludr.BILL_33;
CREATE TABLE stludr.BILL_33( OUT_OBJECT VARCHAR(20),
                          SETTMODE VARCHAR(10),
                          CUSTOMER_CODE VARCHAR(32),
                          OFFER_ORDER_ID DECIMAL(20,0),
                          ORG_MONTH  CHAR(6) ,
                          PAYMONTH CHAR(6) ,
                          PRODUCT_ORDER_ID DECIMAL(20,0),
                          MEMBER_CODE  VARCHAR(32),
                          MEMBER_PROV VARCHAR(3),
                          CHARGE_CODE VARCHAR(20),
                          SETTLEFEE  DECIMAL(20,2),
                          CDR_CHARGECODE VARCHAR(20),
                          CDR_CHARGENAME  VARCHAR(20),
                          TAXRATE   VARCHAR(6),
                          SETTLE_TAXFEE VARCHAR(6),
                          SETTLE_NOTAXFEE VARCHAR(6),
                          DD VARCHAR(8),
                          IN_OBJECT VARCHAR(20),
                          ORDER_MODE VARCHAR(6),
                          OFFER_CODE VARCHAR(64),
                          PRODUCT_CODE VARCHAR(64),
                          ADJMONTH CHAR(6),
                          FEETYPE CHAR(1)
) COMMENT='shard=shard1';