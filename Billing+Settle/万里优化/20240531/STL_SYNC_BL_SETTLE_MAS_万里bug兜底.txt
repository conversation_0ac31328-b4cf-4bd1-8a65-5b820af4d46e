CREATE DEFINER="stludr"@"10.%" PROCEDURE "STL_SYNC_BL_SETTLE_MAS"(
    inMonth          IN   VARCHAR2,
    inBatch          IN   VARCHAR2, 
    flag_version     IN   VARCHAR2,
    reserve1         IN   VARCHAR2,
    reserve2         IN   VARCHAR2,
    proc_out         OUT  VARCHAR2,
    outSysError      OUT  VARCHAR2,
    outReturn        OUT  NUMBER,
    outBL            OUT  VARCHAR2,
    outAR            OUT  VARCHAR2
)
AS

    v_proc_name       VARCHAR2(30) := 'STL_SYNC_BL_SETTLE_MAS';
    iv_Sql_Insert    VARCHAR2(3072);
    iv_Sql_Insert1   VARCHAR2(1024);
    iv_Sql_Insert2   VARCHAR2(2048);
    iv_Sql_Insert3   VARCHAR2(2048);
    iv_Sql_Update    VARCHAR2(1024);


BEGIN

    outSysError := '';
    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        outSysError := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;
        
        
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg ;
    END;

    BEGIN
        if ( length(inMonth) < 6 )  then
            SELECT 'inMonth length less than 6. inMonth=[' || inMonth || ']' FROM dual;
            outReturn := -1;
            ROLLBACK;
            RETURN;
        end if;
        
        SELECT 'inMonth=' ||  inMonth FROM dual;

    

        --清空当月话单表
           SET @vSql := 'truncate table SYNC_BL_SETTLE_' || inMonth;
     SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @vSql := 'truncate table SYNC_BL_RULE_' || inMonth;
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
     --流量统付（个人流量包）部分  
        --step 1 统计SYNC_INTERFACE_BL_<month>的数据
        SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_RULE_SWAP ' ||
               'SELECT ''BL'' datasource, ' ||
                       'ordermode, ' ||
                       'orgmonth, ' ||
                       'customernumber, ' ||
                       'pospecnumber, ' ||
                       'sospecnumber, ' ||
                       'poid, ' ||
                       'soid, ' ||
                       'dn, ' ||
                       'sum(amount) amount, ' ||
                       '''0'' amount_type, ' ||
                       'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime, ' ||
                       '''0'' status, ' ||
                       'NULL errmesg, ' ||
                       'remark, ' ||
                       'NULL feetype ' ||
                  'FROM (SELECT a.ORDERMODE, ' ||
                               'a.ORGMONTH, ' ||
                               'a.CUSTOMERNUMBER, ' ||
                               'a.POSPECNUMBER, ' ||
                               'a.SOSPECNUMBER, ' ||
                               'a.POID, ' ||
                               'a.SOID, ' ||
                               'a.NOTAXFEE amount, ' ||
                               'a.REMARK, substr(a.DN, 1, 7) DN ' ||
                          'FROM SYNC_INTERFACE_BL_' || inMonth || ' a ' ||
                         'WHERE a.DN IS NOT NULL ' ||
                           'AND a.SOSPECNUMBER IN (SELECT PRODUCT_CODE FROM stlusers.STL_BUSINESS_TYPE ' ||
                                                   'WHERE BIZ_TYPE = ''GPRS'') ' ||
                           'AND a.STATUS = ''0'' ' ||
                           'AND a.FEETYPE IN (''18'', ''04'')) ' ||
                 'GROUP BY ordermode, orgmonth, customernumber, pospecnumber, sospecnumber, ' ||
                          'poid, soid, remark, dn';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --step 2 生成最终表的数据
        SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_RULE_' || inMonth || ' ' ||
               'SELECT ''BL'' datasource, ' ||
                       'ordermode, ' ||
                       'orgmonth, ' ||
                       'customernumber, ' ||
                       'pospecnumber, ' ||
                       'sospecnumber, ' ||
                       'poid, ' ||
                       'soid, ' ||
                       'NULL dn, ' ||
                       'amount, ' ||
                       '''0'' amount_type, ' ||
                       'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime, ' ||
                       '''0'' status, ' ||
                       'NULL errmesg, ' ||
                       'prov_cd, ' ||
                       'remark, ' ||
                       'NULL feetype ' ||
                  'FROM ( SELECT a.ORDERMODE, ' ||
                               'a.ORGMONTH, ' ||
                               'a.CUSTOMERNUMBER, ' ||
                               'a.POSPECNUMBER, ' ||
                               'a.SOSPECNUMBER, ' ||
                               'a.POID, ' ||
                               'a.SOID, ' ||
                               'a.amount, ' ||
                               'd.PROV_CD, ' ||
                               'a.REMARK ' ||
                          'FROM SYNC_BL_RULE_SWAP a, ' ||
                               'stlusers.STL_IMSI_LD_CD c, ' ||
                               'stlusers.STL_DOM_LD_AREA_CD_PROV d ' ||
                         'WHERE a.DN IS NOT NULL ' ||
                           'AND c.LD_AREA_CD = d.LD_AREA_CD ' ||
                           'AND C.MSISDN_AREA_ID = a.DN ' ||
                           'AND to_date(' || inMonth || ', ''YYYYMM'') BETWEEN trunc(c.EFF_TM, ''MM'') AND c.EXP_TM )';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
    

        --云MAS部分

        ----更新 OTH_SP字段的空值
        SET @iv_Sql_Insert := 'truncate table cmas_swap_01';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT; 

        SET @iv_Sql_Insert := 'truncate table cmas_swap_02';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT; 

        
        --先对ur_cmas_202311_t group by，再做offer_mode和product_code进行过滤。因为分组字段中包含这两列，所以先分组再过滤是等价的。
        truncate table cmas_mid1;
        SET @iv_Sql_Insert := 'INSERT INTO cmas_mid1 ' ||
             'SELECT a.ORDER_MODE, ' ||
                     'a.ACCT_MONTH, ' || 
                     'a.EC_CODE,  ' ||
                     'a.OFFER_CODE,  ' ||
                     'a.PRODUCT_CODE,  ' ||
                     'a.OFFER_ORDER_ID,  ' ||
                     'a.PRODUCT_ORDER_ID,  ' ||
                     'round(SUM(a.CHARGE2 / 100), 0) fee, ' ||
                     
                     'nvl(oth_sp_orig, ''00'') oth_sp,  ' ||
                     'a.ec_code_prov, ' ||
                     'a.charge_code1 ' ||
                'FROM UR_CMAS_' || inMonth || '_T a  ' ||
                'WHERE a.oth_sp_orig <> ''03'' ' ||
                  'and a.order_mode = ''1'' ' ||
               'GROUP BY ACCT_MONTH, EC_CODE, EC_CODE_PROV, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, ' ||
                     'OFFER_ORDER_ID, PRODUCT_ORDER_ID, OTH_SP_ORIG, charge_code1 ' ;
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
        -- 万里bug兜底ur话单入库时decimal字段入成0，正确应该为null 不用20240530，这里cmas_mid1->SYNC_BL_RULE_->cmas_swap_01->cmas_swap_02表，导致最终P_SETTLE_RULE_PROC_REPART_PARAMETER中计算云MAS关联cmas_swap_02表计算少了规则（stlusers.STL_REPART_PARAMETER_T表010101012业务）
				update cmas_mid1 set PRODUCT_ORDER_ID=null where PRODUCT_ORDER_ID=0;
				commit;
        

        SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_RULE_' || inMonth || ' ' ||
             'SELECT ''BL'' datasource, ' ||
                     'order_mode ordermode,  ' ||
                     'acct_month orgmonth, ' ||
                     'ec_code customernumber, ' ||
                     'offer_code pospecnumber,  ' ||
                     'product_code sospecnumber, ' ||
                     'offer_order_id poid, ' ||
                     'product_order_id soid, ' ||
                     'NULL dn, ' ||
                     'fee amount,  ' ||
                     '''1'' amount_type, ' ||
                     'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime, ' ||
                     '''0'' status, ' ||
                     'NULL errmesg,  ' ||
                     'decode(oth_sp, ''00'', ''ZQ'', ''01'', ''ZW'', ''02'', ''ZYJC'') prov_cd, ' ||
                     '''2'' remark, ' ||
                     'decode(charge_code1, ''Comm_Fee_3870'', ''3870'', ''1040'') feetype ' ||
                'FROM (SELECT a.ORDER_MODE, ' ||
                     'a.ACCT_MONTH, ' ||

                     'a.EC_CODE,  ' ||
                     'a.OFFER_CODE,  ' ||
                     'a.PRODUCT_CODE,  ' ||
                     'a.OFFER_ORDER_ID,  ' ||
                     'a.PRODUCT_ORDER_ID,  ' ||
                     'a.fee, ' ||
                     'a.oth_sp,  ' ||



                     'a.ec_code_prov, ' ||
                     'a.charge_code1 ' ||
                'FROM  cmas_mid1 a  ' ||

                'WHERE (a.OFFER_CODE IN (SELECT DISTINCT OFFER_CODE FROM stlusers.STL_BUSINESS_TYPE  ' ||
                                        'WHERE BIZ_TYPE in (''CMAS1'', ''CMAS5'')) or a.product_code = ''110151'')  ' ||




                ' ) GROUP BY order_mode, acct_month, ec_code, ec_code_prov, offer_code, product_code, offer_order_id, product_order_id, ' ||
                      'fee, oth_sp, charge_code1';

        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT; 
        
        
        
        --先对ur_cmas_202311_t group by，再做offer_mode和product_code进行过滤。因为分组字段中包含这两列，所以先分组再过滤是等价的。
        truncate table cmas_mid2;
        
        SET @iv_Sql_Insert := 'INSERT INTO cmas_mid2  ' ||
                         'SELECT MAX(a.TICKET_ID) ticket_id,  '||
                                 'a.ACCT_MONTH, '||
                                 'a.EC_CODE,    '||
                                 'a.ORDER_MODE,  '||
                                 'a.OFFER_CODE,   '||
                                 'a.PRODUCT_CODE, '||
                                 'a.OFFER_ORDER_ID,  '||
                                 'a.PRODUCT_ORDER_ID,  '||
                                 'round(SUM(a.CHARGE2 / 100), 0) fee,  '||
                                 'nvl(oth_sp_orig, ''00'') oth_sp,  ' ||
                                 'a.charge_code1 '||
                     'FROM UR_CMAS_' || inMonth || '_T a  '||
                       'WHERE  a.oth_sp_orig <> ''03'' ' ||
                  'and a.order_mode in (''3'', ''5'') ' ||
               'GROUP BY ACCT_MONTH, EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, ' ||
                     'OFFER_ORDER_ID, PRODUCT_ORDER_ID, OTH_SP_ORIG, charge_code1 ';
         
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT; 
        
        -- 万里bug兜底ur话单入库时decimal字段入成0，正确应该为null 不用20240530 （为防止万一有影响这里一起修正数据）
				update cmas_mid2 set PRODUCT_ORDER_ID=null where PRODUCT_ORDER_ID=0;
				commit;


        SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_SETTLE_' || inMonth || ' ' ||
                         'SELECT ticket_id stream_id,  '||
                         '''BL'' datasource,  '||
                         't.order_mode,  '||
                         't.acct_month orgmonth,   '||
                         't.ec_code customernumber,   '||
                         't.offer_code pospecnumber,    '||
                         't.product_code sospecnumber,  '||
                         't.offer_order_id poid,    '||
                         't.product_order_id soid,  '||
                         'decode(charge_code1, ''Comm_Fee_3870'', ''3870'', ''1040'') feetype,  '||
                         'NULL dn,   '||
                         't.fee amount,   '||
                         '''2'' amount_type,   '||
                         'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime,   '||
                         '''0'' status, '||
                         'NULL errmesg,   '||
                         'decode(oth_sp, ''00'', ''ZQ'', ''01'', ''ZW'', ''02'', ''ZYJC'') prov_cd,    '||
                         'NULL accountid,    '||
                         '''2'' remark     '||
                    'FROM (SELECT a.ticket_id,  '||
                                 'a.ACCT_MONTH, '||
                                 'a.EC_CODE,    '||
                                 'a.ORDER_MODE,  '||
                                 'a.OFFER_CODE,   '||
                                 'a.PRODUCT_CODE, '||
                                 'a.OFFER_ORDER_ID,  '||
                                 'a.PRODUCT_ORDER_ID,  '||
                                 'a.fee,  '||
                                 'a.oth_sp,  ' ||


                                 'a.charge_code1 '||
                     'FROM cmas_mid2 a  '||

                       'WHERE (a.OFFER_CODE IN (SELECT DISTINCT OFFER_CODE FROM stlusers.STL_BUSINESS_TYPE  ' ||
                                        'WHERE BIZ_TYPE in (''CMAS1'', ''CMAS5'')) or a.product_code = ''110151'')) t ';
         





        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT; 
                     

      
        ----话单汇总数据关联计费出账数据，写入中间表
        SET @iv_Sql_Insert := 'insert into cmas_swap_01(offer_order_id, product_order_id, notaxfee, half_notaxfee, oth_sp, oth_fee, ' ||
                       ' pospecnumber, sospecnumber, ordermode, orgmonth, charge_code) ' ||
      'select a.poid, a.soid, nvl(b.notaxfee, 0) notaxfee, nvl(floor(b.notaxfee / 2), 0) half_notaxfee, a.prov_cd oth_sp, a.amount, ' ||
             'a.pospecnumber, a.sospecnumber, a.ordermode, a.orgmonth, a.feetype ' ||
        'from (select t.poid, t.soid, sum(t.amount) amount, t.prov_cd, t.pospecnumber, t.sospecnumber, t.ordermode, t.orgmonth, t.feetype ' ||
                'from sync_bl_rule_' || inMonth || ' t ' ||
               'where (t.pospecnumber in (select distinct offer_code ' ||
                                           'from stlusers.stl_business_type ' ||
                                          'where biz_type = ''CMAS1'') ' ||
                  'or t.sospecnumber = ''110151'') ' ||
                 'and t.ordermode = ''1'' ' ||
                 'and t.soid is null ' ||
               'group by t.orgmonth, t.ordermode, t.pospecnumber, t.sospecnumber, t.poid, t.soid, t.prov_cd, t.feetype) a ' ||
        'left join (select t.poid, t.soid, sum(t.notaxfee) notaxfee ' ||
                     'from sync_interface_bl_' || inMonth || ' t ' ||
                    'where t.ordermode = ''1'' ' ||
                      'and t.soid is null ' ||
                      'and (t.pospecnumber in (select distinct offer_code ' ||
                                               'from stlusers.stl_business_type ' ||
                                              'where biz_type = ''CMAS1'') ' ||
                       'or t.sospecnumber = ''110151'') ' ||
                    'group by t.poid, t.soid) b ' ||
          'on a.poid = b.poid and a.soid is null and b.soid is null ' ||
       'union all ' ||
      'select a.poid, a.soid, b.notaxfee, floor(b.notaxfee / 2) half_notaxfee, a.prov_cd oth_sp, a.amount, ' ||
             'a.pospecnumber, a.sospecnumber, a.ordermode, a.orgmonth, a.feetype ' ||
        'from (select t.poid, t.soid, sum(t.amount) amount, t.prov_cd, t.pospecnumber, t.sospecnumber, t.ordermode, t.orgmonth, t.feetype ' ||
                'from sync_bl_rule_' || inMonth || ' t ' ||
               'where (t.pospecnumber in (select distinct offer_code ' ||
                                           'from stlusers.stl_business_type ' ||
                                          'where biz_type = ''CMAS1'') ' ||
                  'or t.sospecnumber = ''110151'') ' ||
                 'and t.ordermode = ''1'' ' ||
                 'and t.soid is not null ' ||
               'group by t.orgmonth, t.ordermode, t.pospecnumber, t.sospecnumber, t.poid, t.soid, t.prov_cd, t.feetype) a ' ||
        'left join (select t.poid, t.soid, sum(t.notaxfee) notaxfee ' ||
                     'from sync_interface_bl_' || inMonth || ' t ' ||
                    'where t.ordermode = ''1'' ' ||
                      'and t.soid is not null ' ||
                      'and (t.pospecnumber in (select distinct offer_code ' ||
                                               'from stlusers.stl_business_type ' ||
                                              'where biz_type = ''CMAS1'') ' ||
                       'or t.sospecnumber = ''110151'') ' ||
                    'group by t.poid, t.soid) b ' ||
          'on a.poid = b.poid and a.soid = b.soid';
        
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT; 
        
        ----计算异网服务商数量和异网结算总金额
        merge into cmas_swap_01 a
         using (select offer_order_id, product_order_id, sum(oth_fee) oth_total_fee, count(*) sp_count
                  from cmas_swap_01 t
                 where product_order_id is null
                 group by offer_order_id, product_order_id) b
            on (a.offer_order_id = b.offer_order_id)
          when matched then
        update set a.sp_count = b.sp_count, a.oth_total_fee = b.oth_total_fee;
        commit;

        merge into cmas_swap_01 a
         using (select offer_order_id, product_order_id, sum(oth_fee) oth_total_fee, count(*) sp_count
                  from cmas_swap_01 t
                 where product_order_id is not null
                 group by offer_order_id, product_order_id) b
            on (a.offer_order_id = b.offer_order_id and a.product_order_id = b.product_order_id)
          when matched then
        update set a.sp_count = b.sp_count, a.oth_total_fee = b.oth_total_fee;
        commit;
        
        ----标异网服务商数为2且异网大于出账费用的
        update cmas_swap_01
           set flag = 1
         where sp_count >= 2
           and oth_total_fee > notaxfee;
        commit;
        
        ----符合要求的话单汇总数据写入中间表2
        insert into CMAS_SWAP_02(offer_order_id, product_order_id, notaxfee, half_notaxfee, oth_sp, oth_fee, sp_count, oth_total_fee,
                    pospecnumber, sospecnumber, ordermode, orgmonth, rate, charge_code)
        select offer_order_id, product_order_id, notaxfee, half_notaxfee, oth_sp, oth_fee, sp_count, oth_total_fee,
               pospecnumber, sospecnumber, ordermode, orgmonth, round(oth_fee / oth_total_fee, 27) rate, charge_code
          from CMAS_SWAP_01
         where flag = '1';
        
        
        
        commit;
       
        ----更新sync_bl_rule_YYYYMM，存在于cmas_swap_02中的数据，状态更新为2
        SET @iv_Sql_Update := 'merge into sync_bl_rule_' || inMonth || ' a ' ||
          'using (select distinct offer_order_id ' ||
                  'from cmas_swap_02 t ' ||
                 'where product_order_id is null) b ' ||
            'on (a.poid = b.offer_order_id) ' ||
          'when matched then ' ||
          'update set a.status = ''2'' ' ||
          'where a.soid is null';
      
        SELECT @iv_Sql_Update;
        PREPARE STMT FROM @iv_Sql_Update;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT; 

        set @iv_Sql_Update := 'merge into sync_bl_rule_' || inMonth || ' a ' ||
      'using (select distinct offer_order_id, product_order_id ' ||
              'from cmas_swap_02 t ' ||
             'where product_order_id is not null) b ' ||
        'on (a.poid = b.offer_order_id and a.soid = b.product_order_id) ' ||
      'when matched then ' ||
      'update set a.status = ''2'' ' ||
      'where a.soid is not null';
      
        SELECT @iv_Sql_Update;
        PREPARE STMT FROM @iv_Sql_Update;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT; 

        commit;


        outSysError := 'OK';
        outReturn := 0;
        
        SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
    END;
END