delimiter ;;
CREATE or replace DEFINER="stludr"@"10.%" PROCEDURE "STL_UPD_TAXRATE"(
    inMonth          IN   VARCHAR2,
    outSysError      OUT  VARCHAR2,
    outReturn        OUT  NUMBER
)
AS

    v_proc_name       VARCHAR2(30) := 'STL_UPD_TAXRATE';
    vSql varchar2(9999);


BEGIN


    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        outSysError := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;

        --  日志写表
        select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg ;
    END;

    BEGIN

        outSysError := '';
        outReturn := 0;

        if ( length(inMonth) < 6 )  then
            SELECT 'inMonth length less than 6. inMonth=[' || inMonth || ']' FROM dual;
            outReturn := -1;
            ROLLBACK;
            RETURN;
        end if;

        SELECT 'inMonth=' ||  inMonth FROM dual;

        ----更新税率
        SET @vSql := 'UPDATE SYNC_INTERFACE_BL_' || inMonth || ' a ' ||
             'SET a.TAXRATE = ''6'', ' ||
                 'a.NOTAXFEE = a.ORGFEE, ' ||
                 'a.TAXFEE = 0 ' ||
           'WHERE a.TAXRATE IS NULL';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @vSql := 'UPDATE SYNC_INTERFACE_AR_' || inMonth || ' a ' ||
             'SET a.TAXRATE = ''6'', ' ||
                 'a.NOTAXFEE = a.ORGFEE, ' ||
                 'a.TAXFEE = 0 ' ||
           'WHERE a.TAXRATE IS NULL';

        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        COMMIT;

        ----更新应收接口表中往月账期
        SET @vSql := 'UPDATE SYNC_INTERFACE_BL_' || inMonth || ' a ' ||
             'SET a.ORGMONTH = ' || inMonth || ' ' ||
           'WHERE a.ORGMONTH != ''' || inMonth || ''' ';

        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        COMMIT;

        ----移动云测试客户排除（所有应收和11月以后的实收）
        SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
             'set a.status = ''1'' ' ||
           'where a.pospecnumber = ''1010402'' ' ||
             'and a.poid in (select poid from stlusers.stl_csmp_poid where settlemonth = ''' || inMonth || ''')';

        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
             'set a.status = ''1'' ' ||
           'where a.pospecnumber = ''1010402'' ' ||
             'and a.poid in (select poid from stlusers.stl_csmp_poid where settlemonth = ''201810'') ' ||
             'and a.orgmonth <= ''201810''';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
             'set a.status = ''1'' ' ||
           'where a.pospecnumber = ''1010402'' ' ||
             'and a.poid in (select poid from stlusers.stl_csmp_poid b where b.settlemonth = a.orgmonth) ' ||
             'and a.orgmonth > ''201810''';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        commit;

        ----政企内部客户排除
        SET @vSql := 'update sync_interface_bl_' || inMonth || ' t ' ||
         'set t.status = 2 ' ||
        'where t.status = 0 and exists (select 1 from zq_customer_config z ' ||
             'where z.customer_code = t.customernumber and z.type = ''C'')';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @vSql := 'update sync_interface_ar_' || inMonth || ' t ' ||
         'set t.status = 2 ' ||
        'where t.status = 0 and exists (select 1 from zq_customer_config z ' ||
             'where z.customer_code = t.customernumber and z.type = ''C'')';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @vSql := 'update sync_bl_settle_' || inMonth || ' t ' ||
         'set t.status = 2 ' ||
        'where t.status = 0 and exists (select 1 from zq_customer_config z ' ||
             'where z.customer_code = t.customernumber and z.type = ''C'')';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @vSql := 'update stlusers.interface_sub_member_' || inMonth || '_t t ' ||
         'set t.status = 2 ' ||
        'where t.status = 0 and exists (select 1 from zq_customer_config z ' ||
             'where z.customer_code = t.customer_code and z.type = ''C'')';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----标准化产品订购数据排除
        SET @vSql := 'update sync_interface_bl_' || inMonth || ' t ' ||
         'set t.status = 3 ' ||
        'where t.status = 0 and exists (select 1 from zq_customer_config z ' ||
             'where z.customer_code = t.customernumber and z.offer_code = t.pospecnumber and z.type = ''CO'')';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @vSql := 'update sync_interface_ar_' || inMonth || ' t ' ||
         'set t.status = 3 ' ||
        'where t.status = 0 and exists (select 1 from zq_customer_config z ' ||
             'where z.customer_code = t.customernumber and z.offer_code = t.pospecnumber and z.type = ''CO'')';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @vSql := 'update sync_bl_settle_' || inMonth || ' t ' ||
         'set t.status = 3 ' ||
        'where t.status = 0 and exists (select 1 from zq_customer_config z ' ||
             'where z.customer_code = t.customernumber and z.offer_code = t.pospecnumber and z.type = ''CO'')';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @vSql := 'update stlusers.interface_sub_member_' || inMonth || '_t t ' ||
         'set t.status = 3 ' ||
        'where t.status = 0 and exists (select 1 from zq_customer_config z ' ||
             'where z.customer_code = t.customer_code and z.offer_code = t.product_code and z.type = ''CO'')';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        commit;


              --更新 srv6 bl接口表数据状态
       SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
         'set a.status = 4 ' ||
       'where a.pospecnumber = ''50120'' ' ||
         'and a.sospecnumber = ''2023999400073791'' '||
         'and a.feetype in(''3967'',''1665'',''1424'',''1663'',''7967'',''5665'',''5424'',''5663'') '||
         'and a.ordermode=''3'' ';
       SELECT @vSql;
       PREPARE STMT FROM @vSql;
       EXECUTE STMT;
       DEALLOCATE PREPARE STMT;

            --更新 srv6 ar接口表数据状态
       SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
        'set a.status = 4 ' ||
       'where a.pospecnumber = ''50120'' ' ||
         'and a.sospecnumber = ''2023999400073791'' '||
         'and a.feetype in(''3967'',''1665'',''1424'',''1663'',''7967'',''5665'',''5424'',''5663'') '||
         'and a.ordermode=''3'' ';
       SELECT @vSql;
       PREPARE STMT FROM @vSql;
       EXECUTE STMT;
    DEALLOCATE PREPARE STMT;
    commit;

    ---- 云MAS受理模式3、5接口表数据排除
        SET @vSql := 'update sync_interface_bl_' || inMonth || ' t ' ||
         'set t.status = 1 ' ||
        'where t.status = 0 and ordermode <> ''1'' and (pospecnumber between ''010101012'' and ''010101017'' or pospecnumber = ''60000'' or sospecnumber = ''110151'')';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
        
        SET @vSql := 'update sync_interface_ar_' || inMonth || ' t ' ||
         'set t.status = 1 ' ||
        'where t.status = 0 and ordermode <> ''1'' and (pospecnumber between ''010101012'' and ''010101017'' or pospecnumber = ''60000'' or sospecnumber = ''110151'')';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
        commit;
        -- onePark  2024999480000942, 2024999480000943, 2024999480000944
        SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
                      'set a.status = 4 ' ||
                    'where a.sospecnumber in (2024999480000942, 2024999480000943, 2024999480000944) and a.status = ''0''';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
        commit;

        --onePark 受理模式3 接口表数据排除
        SET @vSql := 'update sync_interface_bl_' || inMonth || ' t ' ||
                          'set t.status = 1 ' ||
                          'where t.status = 0 and ordermode <> ''1'' and sospecnumber in( ''2024999480000942'',''2024999480000943'',''2024999480000944'' ) ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        -- 智能路由语音受理模式3通信费从应收接口表排除
        SET @vSql := 'update sync_interface_bl_' || inMonth || ' t ' ||
            'set t.status = 1 ' ||
            'where t.status = ''0'' and t.ordermode = ''3'' and sospecnumber in (''5002101'',''5002105'') and feetype = ''01''';
       
       SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;    

        -- 数智协同
        SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
                     'set a.status = 4 ' ||
                     'where a.sospecnumber in (''2024999480001535'', ''2024999480001490'', ''2024999480001491'',''2024999480002026'',''2024999480002149'') and a.status = ''0'' ';

        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @vSql :='update sync_interface_bl_' || inMonth || ' t ' ||
                    'set t.status = 1 ' ||
                    'where t.status = 0 and ordermode <> ''1'' and sospecnumber in(''2024999480001535'', ''2024999480001490'', ''2024999480001491'',''2024999480002026'',''2024999480002149'') ';

        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --oneSoc 接口表数据排除
        SET @vSql :=  'update sync_interface_bl_' || inMonth || ' t ' ||
                'set t.status = 1 ' ||
                'where t.status = 0 and sospecnumber =''2024999480001612'' ';

        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
        commit;


        ----MPLS-VPN商品-境内发起境外CE产品 将含税金额改为不含税金额
        SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
           'set a.notaxfee = a.notaxfee / (1 + a.taxrate / 100), ' ||
               'a.orgfee = a.orgfee / (1 + a.taxrate / 100), ' ||
               'a.taxrate = 0 ' ||
         'where a.pospecnumber in (''01011309'', ''50042'') ' ||
           'and a.sospecnumber in (''111253'', ''5004203'') ' ||
           'and a.feetype in (''1425'', ''5425'', ''1428'', ''5428'', ''1429'', ''5429'', ''1664'', ''5664'', ''1666'', ''5666'')';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
           'set a.notaxfee = a.notaxfee / (1 + a.taxrate / 100), ' ||
               'a.orgfee = a.orgfee / (1 + a.taxrate / 100), ' ||
               'a.taxrate = 0 ' ||
         'where a.pospecnumber in (''01011309'', ''50042'') ' ||
           'and a.sospecnumber in (''111253'', ''5004203'') ' ||
           'and a.feetype in (''1425'', ''5425'', ''1428'', ''5428'', ''1429'', ''5429'', ''1664'', ''5664'', ''1666'', ''5666'')';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        commit;


        ----和医疗原始费用修改为CRM归档金额
        SET @vSql := 'update stludr.sync_interface_bl_' || inMonth || ' a ' ||
                         'set status = ''6'' ' ||
                         'where pospecnumber = ''50074'' ' ||
                         'and feetype in(''1990'',''5990'',''1991'',''5991'',''2000'',''6000'')' ||
                           'and not exists (select * from stlusers.stl_sync_rule b ' ||
                               'where b.pospecnumber = ''50074'' ' ||
                                 'and a.orgmonth between b.rulestartmonth and b.ruleendmonth ' ||
                                 'and a.poid = b.poid and a.soid = b.soid and get_normal_feetype(a.feetype) = b.feetype)';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @vSql := 'update stludr.sync_interface_ar_' || inMonth || ' a ' ||
                          'set status = ''6'' ' ||
                         'where pospecnumber = ''50074'' ' ||
                         'and feetype in(''1990'',''5990'',''1991'',''5991'',''2000'',''6000'')' ||
                           'and not exists (select * from stlusers.stl_sync_rule b ' ||
                               'where b.pospecnumber = ''50074'' ' ||
                                 'and a.orgmonth between b.rulestartmonth and b.ruleendmonth ' ||
                                 'and a.poid = b.poid and a.soid = b.soid and get_normal_feetype(a.feetype) = b.feetype)';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @vSql := 'update stludr.sync_interface_bl_' || inMonth || ' a ' ||
                           'set notaxfee = (select b.settlefee from stlusers.stl_sync_rule b ' ||
                               'where b.pospecnumber = ''50074'' ' ||
                                 'and a.orgmonth between b.rulestartmonth and b.ruleendmonth ' ||
                                 'and a.poid = b.poid and a.soid = b.soid and get_normal_feetype(a.feetype) = b.feetype) ' ||
                         'where pospecnumber = ''50074'' and status = ''0'' and feetype in(''1990'',''5990'',''1991'',''5991'',''2000'',''6000'')';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @vSql := 'update stludr.sync_interface_ar_' || inMonth || ' a ' ||
                           'set notaxfee = (select b.settlefee from stlusers.stl_sync_rule b ' ||
                               'where b.pospecnumber = ''50074'' ' ||
                                 'and a.orgmonth between b.rulestartmonth and b.ruleendmonth ' ||
                                 'and a.poid = b.poid and a.soid = b.soid and get_normal_feetype(a.feetype) = b.feetype) ' ||
                         'where pospecnumber = ''50074'' and status = ''0'' and feetype in(''1990'',''5990'',''1991'',''5991'',''2000'',''6000'')';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        commit;


        ----能力开放特服号
        SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
           'set a.status = 4 ' ||
         'where a.pospecnumber = ''50016'' ' ||
           'and a.sospecnumber <> ''5001606'' ' ||
           'and a.dn like ''12539%'' ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
           'set a.status = 4 ' ||
         'where a.pospecnumber = ''50016'' ' ||
           'and a.sospecnumber <> ''5001606'' ' ||
           'and a.dn like ''12539%'' ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

             ----受理模式3短信通知类不进行出账费用结算
        SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
           'set a.status = 4 ' ||
         'where a.pospecnumber = ''50016'' ' ||
           'and a.sospecnumber between ''5001608'' and ''5001611'' ' ||
           'and a.ordermode = ''3'' ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
           'set a.status = 4 ' ||
         'where a.pospecnumber = ''50016'' ' ||
           'and a.sospecnumber between ''5001608'' and ''5001611'' ' ||
           'and a.ordermode = ''3'' ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

             --更新工作号冗余费项的状态
        SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
          'set a.status = 7 ' ||
        'where a.pospecnumber = ''50016'' ' ||
          'and a.sospecnumber = ''2022999400004820'' '||
          'and (a.ordermode= ''3'' or (a.ordermode = ''1'' and a.feetype not in (''2318'',''2326'',''6318'',''6326''))) ' ||
          'and a.status=0 ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

              --更新 工作号 bl接口表数据状态
        SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
          'set a.status = 4 ' ||
        'where a.pospecnumber = ''50016'' ' ||
          'and a.sospecnumber = ''2022999400004820'' '||
          'and a.feetype in(''2318'',''2326'',''6318'',''6326'') '||
          'and a.ordermode=''1'' ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

             --更新 工作号  ar接口表数据状态
        SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
          'set a.status = 4 ' ||
        'where a.pospecnumber = ''50016'' ' ||
          'and a.sospecnumber = ''2022999400004820'' ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --云游戏QOS
            --产品5001612 -》原商品50016 换了新商品  50121
        SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
                     'set a.status = 4 ' ||
                     'where a.pospecnumber = ''50121'' ' ||
                     'and a.sospecnumber = ''5001612'' ' ||
                     'and a.dn like ''12539%'' ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
                     'set a.status = 4 ' ||
                     'where a.pospecnumber = ''50121'' ' ||
                     'and a.sospecnumber = ''5001612'' ' ||
                     'and a.dn like ''12539%'' ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
                     'set a.status = 4 ' ||
                     'where a.pospecnumber = ''50121'' ' ||
                     'and a.sospecnumber = ''2023999400018530'' ' ;
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
                     'set a.status = 4 ' ||
                     'where a.pospecnumber = ''50121'' ' ||
                     'and a.sospecnumber = ''2023999400018530'' ' ;
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

             --5G-阅信   更新bl接口表数据
        SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
          'set a.status = 4 ' ||
        'where a.pospecnumber = ''50034'' ' ||
          'and a.sospecnumber = ''5003401'' ' ||
         'and a.feetype not in(''3908'',''7908'') ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

             --5G-阅信   更新ar接口表数据
        SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
          'set a.status = 4 ' ||
        'where a.pospecnumber = ''50034'' ' ||
          'and a.sospecnumber = ''5003401''  '||
         'and a.feetype not in(''3908'',''7908'') ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

             --和盾安全产品   更新bl接口表数据
        SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
          'set a.status = 4 ' ||
        'where a.pospecnumber = ''50086'' ' ||
          'and a.sospecnumber in (''2022999400016609'',''2022999400016610'',''2023999400059470'',''2023999400059469'') ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


          --和盾安全产品   更新ar接口表数据
        SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
          'set a.status = 4 ' ||
        'where a.pospecnumber = ''50086'' ' ||
          'and a.sospecnumber in (''2022999400016609'',''2022999400016610'',''2023999400059470'',''2023999400059469'') ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----中移凌云
        SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
          'set a.status = 4 ' ||
        'where a.pospecnumber = ''50087'' ' ||
          'and a.sospecnumber = ''2022999400021419'' ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


          --和盾安全融合产品   更新bl接口表数据
        SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
          'set a.status = 4 ' ||
        'where a.pospecnumber = ''50088'' ' ||
          'and a.sospecnumber = ''2022999400016609'' ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


          --和盾安全融合产品   更新ar接口表数据
        SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
          'set a.status = 4 ' ||
        'where a.pospecnumber = ''50088'' ' ||
          'and a.sospecnumber = ''2022999400016609'' ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


          --热线彩印   更新bl接口表数据
        SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
          'set a.status = 4 ' ||
        'where a.pospecnumber = ''50022'' ' ||
          'and a.sospecnumber = ''5002202'' ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


          --热线彩印   更新ar接口表数据
        SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
          'set a.status = 4 ' ||
        'where a.pospecnumber = ''50022'' ' ||
          'and a.sospecnumber = ''5002202'' ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

       --阅信(增值能力)、onepower互联网工业、跨省专线卫士
       SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
         'set a.status = 4 ' ||
         'where a.pospecnumber in(''50020'',''50097'',''50118'')  ' ||
         'and a.status = ''0'' ';
       SELECT @vSql;
       PREPARE STMT FROM @vSql;
       EXECUTE STMT;
       DEALLOCATE PREPARE STMT;


       --阅信(增值能力)、onepower互联网工业、移动办公DICT集成服务包
       SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
         'set a.status = 4 ' ||
         'where (a.pospecnumber in(''50020'',''50097'') or a.sospecnumber=''2023999400036743'') ' ||
         'and a.status = ''0'' ';
       SELECT @vSql;
       PREPARE STMT FROM @vSql;
       EXECUTE STMT;
       DEALLOCATE PREPARE STMT;

   --更新星火党建应收接口表数据状态
       SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
         'set a.status = 4 ' ||
         'where a.pospecnumber = ''50096'' ' ||
           'and a.sospecnumber = ''2022999400077568'' ' ||
           'and a.feetype in (''3305'', ''3306'', ''3307'')';
       SELECT @vSql;
       PREPARE STMT FROM @vSql;
       EXECUTE STMT;
       DEALLOCATE PREPARE STMT;
       
       --更新星火党建实收接口表数据状态
       SET @vSql := 'update sync_interface_ar_' || inMonth || ' a ' ||
         'set a.status = 4 ' ||
         'where a.pospecnumber = ''50096'' ' ||
           'and a.sospecnumber = ''2022999400077568'' ' ||
           'and a.feetype in (''3305'', ''3306'', ''3307'')';
       SELECT @vSql;
       PREPARE STMT FROM @vSql;
       EXECUTE STMT;
       DEALLOCATE PREPARE STMT;


             --名片彩印   更新amount接口表个付数据
        SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
          'set a.status = 4 ' ||
        'where a.pospecnumber = ''50022'' ' ||
          'and a.sospecnumber = ''5002201'' '||
          'and a.cdr_chargename=''2'' ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


          --名片彩印   更新amount接口表统付数据
        SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
          'set a.status = 5 ' ||
        'where a.pospecnumber = ''50022'' ' ||
          'and a.sospecnumber = ''5002201'' '||
          'and a.status = ''0'' ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --能开-中间号
        SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
           'set a.status = 4 ' ||
         'where a.pospecnumber = ''50016'' ' ||
           'and a.sospecnumber = ''5001606'' '||
           'and a.status = ''0'' ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


         --移动办公-快通知、移动办公-三公产品
        SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
           'set a.status = 4 ' ||
         'where a.pospecnumber = ''50038'' ' ||
           'and a.sospecnumber in(''2022999400045330'',''2023999400007532'',''2023999400007535'',''2023999400007533'') '||
           'and a.status = ''0'' ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --OneTrip基础产品、 OneGame-游戏云直播、 OneCity产品、e企收银产品
        SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
           'set a.status = 4 ' ||
         'where a.pospecnumber in(''50078'',''50100'',''50107'',''50068'',''50099'')' ||
         'and sospecnumber <> ''2023999400052876'' '||
           'and a.status = ''0'' ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --云游戏实例
        SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
                   'set a.status = 4 ' ||
                 'where a.pospecnumber =''50121'''||
                 'and sospecnumber = ''2023999400018530'' '||
                   'and a.status = ''0'' ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
        /*
         --OneCity产品
        execute immediate 'update sync_interface_amount_' || inMonth || ' a ' ||
           'set a.status = 4 ' ||
         'where a.pospecnumber  = ''50068''  ' ||
           'and a.status = ''0'' ';


          --e企收银产品
        execute immediate 'update sync_interface_amount_' || inMonth || ' a ' ||
           'set a.status = 4 ' ||
         'where a.pospecnumber  = ''50099''  ' ||
           'and a.status = ''0'' '; */

         --sd-wan组网业务、e企组网、千里眼标准产品增值服务、OneNET标准产品增值服务包、移动办公DICT集成服务包
   --行车卫士标准产品
        SET @vSql := 'update sync_interface_sdwan_' || inMonth || ' a ' ||
           'set a.status = 4 ' ||
         'where (a.pospecnumber in(''50077'',''50110'',''50117'',''50119'')   ' ||
         'or a.sospecnumber=''2022999400072067''  '||
         'or a.sospecnumber=''2023999400010056''   '||
         'or a.sospecnumber=''2023999400048574''    '||
        ' or a.sospecnumber=''2023999400036743'')   '||
           'and a.status = ''0'' ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


             --企业视频彩铃产品 【融合50036产品】
        SET @vSql := 'update /*+ AUTOCOMMIT_DURING_DML() */ sync_interface_amount_' || inMonth || ' a ' ||
           'set a.status = 4 ' ||
         'where a.pospecnumber in (''010113001'',''50036'')   ' ||
           'and a.sospecnumber = ''910401''   '||
           'and a.feetype <> ''3681''  '||
           'and a.status = ''0'' ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

           -- OneVillage 商品
        SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
           'set a.status = 4 ' ||
         'where a.pospecnumber  = ''50106''  ' ||
           'and a.status = ''0'' ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        ----移动办公个付（含联想权益）
        SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
           'set a.status = 4 ' ||
         'where a.pospecnumber = ''50038'' ' ||
           'and a.cdr_chargename in (''2'', ''3'') ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----联想权益、和易报统付  移动办公智慧流程基础服务  移动办公数字人
        SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
           'set a.status = 5 ' ||
           'where a.pospecnumber = ''50038'' and (a.sospecnumber between ''5003810'' and ''5003822'' or a.sospecnumber in (''2024999480000641'',''2021999400048964'',''2022999400013561'',''2022999400013562'',''2023999400041622'',''2023999400041623'',''2023999400092024'')) and a.status = ''0'' ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----和教育
        --去除bl表受理模式3收入还原部分
        SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
                'set a.status = 7 ' ||
              'where a.pospecnumber = ''50049'' and a.ordermode = ''3'' ' ||
                'and (a.sospecnumber in (''5004901'', ''5004902'', ''5004903'', ''5004909'', ''5004915'', ''5004929'', ''5004935'', ' ||
                    '''5004936'', ''5004938'', ''5004948'', ''5004951'', ''5004952'', ''5004953'', ''5004965'', ''5004966'', ''5004970'', ' ||
                    '''5004971'', ''5004927'',''2022999400066446'',''2022999400066447'',''2022999400066448'',''2022999400066449'') or (a.sospecnumber = ''5004964'' and a.cdr_chargecode = ''2737''))' ;
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @vSql := 'update sync_interface_amount_' || inMonth || ' a ' ||
                'set a.status = 4 ' ||
              'where a.pospecnumber = ''50049'' and a.status = ''0''';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
                'set a.status = 4 ' ||
              'where a.pospecnumber = ''50049'' ' ||
                'and (a.sospecnumber = ''5004957'' and a.cdr_chargecode = ''2724'' or a.sospecnumber = ''5004964'' and a.cdr_chargecode = ''2738'') ' ||
                'and a.feetype = ''02'' and a.status = ''0''';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----梧桐风控受理模式3
        --去除bl表受理模式3收入还原部分
        SET @vSql := 'update sync_interface_bl_' || inMonth || ' a ' ||
                'set a.status = 7 ' ||
              'where a.pospecnumber = ''50024'' and a.sospecnumber = ''2021999400052091'' and a.ordermode = ''3'' ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --和对讲 bl接口表数据状态
        SET @vSql :='update sync_interface_bl_' || inMonth || ' a ' ||
                    'set a.status = 4 ' ||
                    'WHERE EXISTS ( ' ||
                    'SELECT 1 FROM stl_mnp_record_poc b ' ||
                    'where a.pospecnumber = ''50025'' ' ||
                    'and a.sospecnumber = ''5002501'' ' ||
                    'and a.ordermode in(''1'',''3'') ' ||
                    'AND a.DN =b.MEMBER_CODE ' ||
                    'AND b.OTH_NETWORK_FLAG=''1'' ' ||
                    'AND a.soid = b.prod_order_id ' ||
                    'AND b.settlemonth = ' || inMonth || ' ' ||
                    'AND b.partid = substr(''' || inMonth || ''', 5, 2)) ' ||
                    'AND a.cdr_chargecode != ''4739'' ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --更新 和对讲  ar接口表数据状态
        SET @vSql :='update sync_interface_ar_' || inMonth || ' a ' ||
                    'set a.status = 4 ' ||
                    'WHERE EXISTS ( ' ||
                    'SELECT 1 FROM stl_mnp_record_poc b ' ||
                    'where a.pospecnumber = ''50025'' ' ||
                    'and a.sospecnumber = ''5002501'' ' ||
                    'and a.ordermode in(''1'',''3'') ' ||
                    'AND a.DN =b.MEMBER_CODE ' ||
                    'AND b.OTH_NETWORK_FLAG=''1'' ' ||
                    'AND a.soid = b.prod_order_id ' ||
                    'AND b.settlemonth = ' || inMonth || ' ' ||
                    'AND b.partid = substr(''' || inMonth || ''', 5, 2)) ' ||
                    'AND a.cdr_chargecode != ''4739'' ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --和对讲 bl接口表数据状态
        SET @vSql :='update sync_interface_bl_' || inMonth || ' a ' ||
                    'set a.status = 1 ' ||
                    'where a.pospecnumber = ''50025'' ' ||
                    'and a.sospecnumber = ''5002501'' ' ||
                    'and a.ordermode in(''3'') ' ||
                    'and a.cdr_chargecode = ''4739'' ';

        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --和对讲 ar接口表数据状态
        SET @vSql :='update sync_interface_ar_' || inMonth || ' a ' ||
                    'set a.status = 1 ' ||
                    'where a.pospecnumber = ''50025'' ' ||
                    'and a.sospecnumber = ''5002501'' ' ||
                    'and a.ordermode in(''3'') ' ||
                    'and  a.cdr_chargecode = ''4739'' ';

        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --和对讲 bl接口表数据状态
        SET @vSql :='update sync_interface_bl_' || inMonth || ' a ' ||
                    'set a.status = 4 ' ||
                    'where a.pospecnumber = ''50025'' ' ||
                    'and a.sospecnumber = ''5002501'' ' ||
                    'and a.ordermode in(''1'') ' ||
                    'and a.cdr_chargecode = ''4739'' ';

        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --和对讲 ar接口表数据状态
        SET @vSql :='update sync_interface_ar_' || inMonth || ' a ' ||
                    'set a.status = 4 ' ||
                    'where a.pospecnumber = ''50025'' ' ||
                    'and a.sospecnumber = ''5002501'' ' ||
                    'and a.ordermode in(''1'') ' ||
                    'and  a.cdr_chargecode = ''4739'' ';

        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --和对讲 amount接口表数据状态 受理模式3的4103费项的异网号码不结算
        SET @vSql :='update sync_interface_amount_' || inMonth || ' a ' ||
                    'set a.status = 1 ' ||
                    'WHERE EXISTS ( ' ||
                    'SELECT 1 FROM stl_mnp_record_poc b ' ||
                    'where a.pospecnumber = ''50025'' ' ||
                    'and a.sospecnumber = ''5002501'' ' ||
                    'and a.ordermode in(''3'') ' ||
                    'AND a.DN =b.MEMBER_CODE ' ||
                    'AND b.OTH_NETWORK_FLAG=''1'' ' ||
                    'AND a.soid = b.prod_order_id ' ||
                    'AND b.settlemonth = ' || inMonth || ' ' ||
                    'AND b.partid = substr(''' || inMonth || ''', 5, 2)) and a.feetype=''4103'' ';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        -- 高精度定位受理模式1 bl结构表状态修改
        SET @vSql :='update sync_interface_bl_' || inMonth || ' a ' ||
                    'set a.status = 4 ' ||
                    'where a.pospecnumber = ''50091'' ' ||
                    'and a.ordermode = ''1''';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
        
        ----调用用量接口结算价格计算存储过程
        call STL_SETTLEFEE_CALCULATE(inMonth, @outSysError, @outReturn);
        IF ( @outReturn != 0) THEN
            SELECT 'call STL_SETTLEFEE_CALCULATE error, return_code=' || @outReturn FROM dual;
            ROLLBACK;
            RETURN;
        END IF;
        commit;

        ----调用费项映射存储过程
        call STL_CHARGECODE_MAPPING(inMonth, @outSysError, @outReturn);
        IF ( @outReturn != 0) THEN
            SELECT 'call STL_CHARGECODE_MAPPING error, return_code=' || @outReturn FROM dual;
            ROLLBACK;
            RETURN;
        END IF;
        commit;

        -- CDN 95峰计算
        call PROC_CDN_RATE(inMonth, @outReturn, @outSysError);
        IF ( @outReturn != 0) THEN
           SELECT 'call PROC_CDN_RATE error, return_code=' || @outReturn FROM dual;
           ROLLBACK;
           RETURN;
        END IF;

        -- CDN CDN_VAS计算
    call PROC_CDN_VAS(inMonth, @outReturn, @outSysError);
        IF ( @outReturn != 0) THEN
          SELECT 'call PROC_CDN_VAS error, return_code=' || @outReturn FROM dual;
          ROLLBACK;
          RETURN;
        END IF;

         -- PROC_CDN_RATE_RIF计算
    call PROC_CDN_RATE_RIF(inMonth, @nReturn, @outSysError);
        IF ( @nReturn != 0) THEN
          SELECT 'call PROC_CDN_RATE_RIF error, return_code=' || @nReturn FROM dual;
          ROLLBACK;
          RETURN;
        END IF;

        -- CDN全站加速计算
        call PROC_CDNAPPEND_RATE(inMonth, @nReturn, @outSysError);
        IF ( @nReturn != 0) THEN
        SELECT 'call PROC_CDNAPPEND_RATE error, return_code=' || @nReturn FROM dual;
        ROLLBACK;
        RETURN;
        END IF;

        -- 海外CDN 数据源结算
        call PROC_HWCDN_RATE(inMonth, @nReturn, @outSysError);
        IF ( @nReturn != 0) THEN
        SELECT 'call PROC_HWCDN_RATE error, return_code=' || @nReturn FROM dual;
        ROLLBACK;
        RETURN;
        END IF;

        SET @vSql := 'TRUNCATE TABLE stludr.stl_cb_mode';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        insert into stl_cb_mode
        select a.bal_month, a.ecid, a.product_code, b.order_mode, ''
        from (select distinct bal_month, ecid, t.product_code
                from stludr.sync_cb_interface t
               where t.bal_month = inMonth
                 and t.product_code in ('010101001', '010105003')) a
        left join (select distinct ec_code,
                                   product_code,
                                   decode(prod_order_mode, 1, '1', '3/5') order_mode
                     from stlusers.stl_serv_biz_code
                    where inMonth between to_char(effective_date, 'yyyymm') and
                          to_char(expiry_date, 'yyyymm')
                      and product_code in ('010101001', '010105003')
                      and ec_code in (select distinct ecid
                                        from stludr.sync_cb_interface
                                       where bal_month = inMonth)) b on a.ecid =
                                                                         b.ec_code
                                                                     and a.product_code =
                                                                         b.product_code;

        COMMIT;

        outSysError := 'OK';
        outReturn := 0;

        SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || to_number(outReturn);
    END;

END;;
delimiter ;
