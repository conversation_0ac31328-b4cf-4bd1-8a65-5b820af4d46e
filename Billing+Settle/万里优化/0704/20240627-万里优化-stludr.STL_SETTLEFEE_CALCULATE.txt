delimiter ;;
CREATE or replace DEFINER="stludr"@"10.%" PROCEDURE "STL_SETTLEFEE_CALCULATE"(    
    inMonth          IN   VARCHAR2,
    outSysError      OUT  VARCHAR2,
    outReturn        OUT  NUMBER
)
AS
    v_proc_name VARCHAR2(30) := 'STL_SETTLEFEE_CALCULATE';
    vSql varchar2(9999);

BEGIN

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        outSysError := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;
        
        

        select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg ;
    END;

    BEGIN

        outSysError := '';
        outReturn := 0;

        if ( length(inMonth) < 6 )  then
            SELECT 'inMonth length less than 6. inMonth=[' || inMonth || ']' FROM dual;
            outReturn := -1;
            ROLLBACK;
            RETURN;
        end if;

        SELECT 'inMonth=' ||  inMonth FROM dual;

        SELECT 'mark1';
        

        SET @vSql := 'update /*+ index(a idx_com1)*/ sync_interface_amount_' || inMonth || ' a ' ||
                     ' inner join stl_amount_rule b '||
                     ' on b.order_mode = a.ordermode ' ||
                     ' and b.offer_code = a.pospecnumber ' ||
                     ' and b.product_code = a.sospecnumber '||
                     ' and b.rateplan_id = a.cdr_chargecode ' ||
                     ' and b.charge_code = a.feetype ' ||
                     ' and b.match_type= ''OPRF'' ' ||
                     ' and ''' || inMonth || ''' between b.start_month and b.end_month ' ||
                     ' set a.notaxfee = round(a.amount * b.unit_price) ';


        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
        SELECT 'mark2';


        SET @vSql := 'update /*+ AUTOCOMMIT_DURING_DML() set_var(gdb_enable_xplan=off) */ sync_interface_amount_' || inMonth || ' a ' ||
                     ' inner join stl_amount_rule b '||
                     'on b.order_mode = a.ordermode ' ||
                     'and b.offer_code = a.pospecnumber ' ||
                     'and b.product_code = a.sospecnumber '||
                     'and b.charge_code = a.feetype ' ||
                     'and b.match_type= ''OPF'' ' ||
                     'and ''' || inMonth || ''' between b.start_month and b.end_month ' ||
                     'set a.notaxfee = round(a.amount * b.unit_price) ';


        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
      
        
        SELECT 'mark3';

        --和教育-智慧校园叠加包资费

        SET @vSql := 'update /*+ AUTOCOMMIT_DURING_DML() */ sync_interface_amount_' || inMonth || ' a '||
             'join (select b.ordertype, b.pospecnumber, b.sospecnumber, b.poid, b.soid, b.feetype, sum(settlefee) settlefee '||
                 'from stlusers.stl_sync_rule b '||
                 'where b.pospecnumber = ''50049'' '||
                  'and b.sospecnumber = ''5004927'' '||
                  'and b.feetype in (''2280'', ''2281'', ''2282'', ''2283'', ''2284'') '|| 
                  'and ''' || inMonth || ''' between b.rulestartmonth and  b.ruleendmonth '||
                 'group by b.ordertype,b.pospecnumber,b.sospecnumber, b.poid,b.soid,b.feetype) b '||
                 'on b.ordertype = a.ordermode and b.pospecnumber = a.pospecnumber and b.sospecnumber = a.sospecnumber and b.poid = a.poid and b.soid = a.soid and b.feetype = a.feetype '||
             'set a.notaxfee = round(a.amount * b.settlefee) '||
             'where a.pospecnumber = ''50049'' '||
             ' and a.sospecnumber = ''5004927'' '|| 
             ' and a.feetype in (''2280'', ''2281'', ''2282'', ''2283'', ''2284'')'; 

        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
          
        SELECT 'mark4';
 
         --成员视频彩铃-服务资费

        SET @vSql := 'update /*+ AUTOCOMMIT_DURING_DML() */ sync_interface_amount_' || inMonth || ' a '||
                     'join (select b.ordertype, b.pospecnumber, b.sospecnumber, b.poid, b.soid, b.feetype, sum(settlefee) settlefee '||
                             'from stlusers.stl_sync_rule b '||
                             'where b.pospecnumber = ''*********'' '||
                             'and b.sospecnumber = ''910401'' '||
                             'and b.feetype =''1656'' '|| 
                             'and ''' || inMonth || ''' between b.rulestartmonth and  b.ruleendmonth '||
                             'group by b.ordertype,b.pospecnumber,b.sospecnumber, b.poid,b.soid,b.feetype) b '||
                             'on b.ordertype = a.ordermode and b.pospecnumber = a.pospecnumber and b.sospecnumber = a.sospecnumber and b.poid = a.poid and b.soid = a.soid and b.feetype = a.feetype '||
                         'set a.notaxfee = round(a.amount * b.settlefee) '||
                     'where a.pospecnumber = ''*********'' '||
                         'and a.sospecnumber = ''910401'' '|| 
                         ' and a.feetype =''1656'' '; 
        
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT; 
        SELECT 'mark5';
  
         --能开-工作号  成员数 >= 100000

        SET @vSql := 'update /*+ AUTOCOMMIT_DURING_DML() */ sync_interface_amount_' || inMonth || ' a '||
                     'join  stl_amount_rule b  on b.offer_code = a.pospecnumber '|| 
                     'and b.product_code = a.sospecnumber '||   
                     'and b.price_tag = ''G_E_100000'' '|| 
                     'and b.match_type = ''OP'' '||   
                     'and ''' || inMonth || ''' between b.start_month and b.end_month '|| 
                     'set a.notaxfee = round(a.amount * b.unit_price) '|| 
                     'where a.amount >= 100000 '; 

        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT; 
        
        SELECT 'mark6';
 
        

        SET @vSql := 'update /*+ AUTOCOMMIT_DURING_DML() */ sync_interface_amount_' || inMonth || ' a '||
                     'join  stl_amount_rule b  on b.offer_code = a.pospecnumber '|| 
                     'and b.product_code = a.sospecnumber '||   
                     'and b.price_tag = ''L_100000'' '|| 
                     'and b.match_type = ''OP'' '||   
                     'and ''' || inMonth || ''' between b.start_month and b.end_month '|| 
                     'set a.notaxfee = round(a.amount * b.unit_price) '|| 
                     'where a.amount < 100000 ';

        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT; 



        
        SET @vSql := 'update /*+ AUTOCOMMIT_DURING_DML() */ sync_interface_amount_' || inMonth || ' a ' ||
                     'set a.notaxfee = round(a.amount * (select b.unit_price from stl_amount_rule b ' ||
                                           'where b.order_mode = a.ordermode ' ||
                                           'and b.offer_code = a.pospecnumber ' ||
                                           'and b.product_code = a.sospecnumber '||
                                           'and b.rateplan_id = a.cdr_chargecode ' ||
                                           'and b.charge_code = a.feetype ' ||
                                           'and b.match_type= ''OPRF'' ' ||
                                           'and ''' || inMonth || ''' between b.start_month and b.end_month) * ' ||
                                           '(select NVL(c.eff_days, 0) from stludr.sync_onepower_' || inMonth || ' c ' ||
                                           'where c.ordermode = a.ordermode ' ||
                                           'and c.pospecnumber = a.pospecnumber ' ||
                                           'and c.sospecnumber = a.sospecnumber '||
                                           'and c.poid = a.poid ' ||
                                           'and c.soid = a.soid ' ||
                                           'and c.feetype = a.feetype ' ||
                                           'and c.rateplan_id = a.cdr_chargecode   '||
                                           'and ''' || inMonth || ''' between c.orgmonth and c.orgmonth) / ' ||   
                                           '(SELECT EXTRACT(DAY FROM LAST_DAY(TO_DATE(''' || inMonth || ''', ''yyyymm''))) AS days_in_month FROM DUAL)) ' ||
                'where a.pospecnumber IN (''50121'',''50107'') ' ||
                'and a.sospecnumber = ''2023999400018530'' ' ||
                'and a.feetype in (''3334'', ''3335'')'; 
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
        COMMIT;
                
        outSysError := 'OK';
        outReturn := 0;
        
        SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || to_number(outReturn);
    END;
          
END;;
delimiter ;
