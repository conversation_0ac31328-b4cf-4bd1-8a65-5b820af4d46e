
DROP FUNCTION IF EXISTS stludr.GET_PRODUCT_NAME;
DELIMITER ;;
CREATE DEFINER="stludr"@"10.%" FUNCTION "GET_PRODUCT_NAME"(F_OFFER_CODE   VARCHAR2,
                                            F_PRODUCT_CODE VARCHAR2,
                                            F_MONTH        VARCHAR2,
                                            F_FLAG         VARCHAR2) RETURN varchar(16383) CHARSET utf8mb4 COLLATE utf8mb4_0900_bin
AS
  PRODUCT_NAME VARCHAR2(256); -- 产品 名称
  OFFER_NAME   VARCHAR2(256); -- 商品 名称
  F_RES        VARCHAR2(512); -- 返回值
BEGIN
  IF F_FLAG = '1' THEN
    -- 获取商品名称
    SELECT DISTINCT NVL(T.NAME, '')
      INTO OFFER_NAME
      FROM STL_PRODUCT T
     WHERE T.PRODUCT_CODE = F_OFFER_CODE
       AND T.ACCT_MONTH = F_MONTH;
    set F_RES = OFFER_NAME;
  END IF;
  IF F_FLAG = '2' THEN
    -- 获取产品名称
    SELECT DISTINCT NVL(T.NAME, '')
    INTO PRODUCT_NAME
    FROM STL_SERVICE T
    WHERE T.SERVICE_CODE = F_PRODUCT_CODE
    AND T.ACCT_MONTH = F_MONTH;
    F_RES := PRODUCT_NAME;
  end if;
  IF F_FLAG = '3' THEN
    -- 获取商品名称
    SELECT DISTINCT NVL(T.NAME, '')
    INTO OFFER_NAME
    FROM STL_PRODUCT T
    WHERE T.PRODUCT_CODE = F_OFFER_CODE
    AND T.ACCT_MONTH = F_MONTH;
    -- 获取产品名称
    SELECT DISTINCT NVL(T.NAME, '')
    INTO PRODUCT_NAME
    FROM STL_SERVICE T
    WHERE T.SERVICE_CODE = F_PRODUCT_CODE
    AND T.ACCT_MONTH = F_MONTH;
    -- 判断产品名称是否为空，为空则不拼接
    IF PRODUCT_NAME IS NOT NULL AND PRODUCT_NAME != '' THEN
       F_RES := OFFER_NAME || '_' || PRODUCT_NAME;
    ELSE
       F_RES := OFFER_NAME;
    END IF;
  END IF;
  RETURN F_RES;
END ;;
DELIMITER ;