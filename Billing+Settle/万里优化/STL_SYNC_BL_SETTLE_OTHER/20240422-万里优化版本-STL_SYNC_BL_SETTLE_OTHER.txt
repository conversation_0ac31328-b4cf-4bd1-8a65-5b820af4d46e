delimiter ;;
CREATE OR REPLACE DEFINER="stludr"@"%"  PROCEDURE "STL_SYNC_BL_SETTLE_OTHER"(
    inMonth          IN   VARCHAR2,
    --inBatch          IN   VARCHAR2,
    --flag_version     IN   VARCHAR2,
    --reserve1         IN   VARCHAR2,
    --reserve2         IN   VARCHAR2,
    --proc_out         OUT  VARCHAR2,
    outSysError      OUT  VARCHAR2,
    outReturn        OUT  NUMBER
    --outBL            OUT  VARCHAR2,
    --outAR            OUT  VARCHAR2

)
IS

    v_proc_name       VARCHAR2(30) := 'STL_SYNC_BL_SETTLE_OTHER';
    iv_Sql_Insert    VARCHAR2(3072);
    iv_Sql_Insert1   VARCHAR2(1024);
    iv_Sql_Insert2   VARCHAR2(2048);
    iv_Sql_Insert3   VARCHAR2(2048);
    iv_Sql_Update    VARCHAR2(1024);

    cursor maapmma_1587 is select subscriber_id, mmm_count from BIZ_MSG_MMM_STL where acct_month = inMonth;
    inSoid           number(15);
    inCount          integer;

BEGIN

    outSysError := '';
    outReturn := 0;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 @p1 = RETURNED_SQLSTATE, @p2 = MESSAGE_TEXT;
        outSysError := substr(@p2, 1, 1000);
        outReturn  := -1;
        ROLLBACK;


        


        select ('exception: ' || outReturn || '|'  || @p1 || '|' || outSysError ) AS error_msg ;
    END;

    BEGIN
        if ( length(inMonth) < 6 )  then
            SELECT 'inMonth length less than 6. inMonth=[' || inMonth || ']' FROM dual;
            outReturn := -1;
            ROLLBACK;
            RETURN;
        end if;


        SELECT 'inMonth=' ||  inMonth FROM dual;

    --清空当月话单中间表  最终在SYNC_BL_SETTLE_MERGE过程中汇入         SYNC_BL_SETTLE        SYNC_BL_RULE表中
        --清空当月话单表
        SET @vSql := 'truncate table SYNC_BL_SETTLE_MID';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @vSql := 'truncate table SYNC_BL_RULE_MID';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @vSql := 'truncate table sync_bl_rule_swap';
        SELECT @vSql;
        PREPARE STMT FROM @vSql;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        --爱流量部分
        SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_SETTLE_MID ' ||
                           'SELECT a.TICKET_ID stream_id, ' ||
                           '''BL'' DATASOURCE, ' ||
                           'a.ORDER_MODE ORDERMODE, ' ||
                           'a.ACCT_MONTH ORGMONTH, ' ||
                           'a.EC_CODE CUSTOMERNUMBER, ' ||
                           'a.OFFER_CODE POSPECNUMBER, ' ||
                           'a.PRODUCT_CODE SOSPECNUMBER, ' ||
                           'a.OFFER_ORDER_ID POID, ' ||
                           'a.PRODUCT_ORDER_ID SOID, ' ||
                           'c.CHARGE_ITEM_REF feetype, ' ||
                           'a.CHARGED_MSISDN dn, ' ||
                           'round(a.CHARGE/10,0) AMOUNT, ' ||
                           '''3'' AMOUNT_TYPE, ' ||
                           'to_char(SYSDATE, ''yyyymmddhh24miss'') SYNCTIME, ' ||
                           '''0'' STATUS, ' ||
                           'DECODE(lower(a.CHARGE_CODE), ''gprs_fee'', NULL, CHARGE_CODE) ERRMESG, ' ||
                           'a.HOT_PROV PROV_CD, ' ||
                           'null,  ' ||
                           '''2'' REMARK ' ||
                     'FROM UR_CHRGFLOW_' || inMonth || '_T a, stlusers.STL_CHARGE_ITEM_DEF c ' ||
                    'WHERE lower(a.CHARGE_CODE) = lower(c.CHARGE_ITEM(+)) ';


        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        --双跨部分
        --分为多个阶梯执行，分别为50万，50万，50万，50万，100万，1亿
        --万里：分批执行去掉了。20240322

        SET @iv_Sql_Insert1 := 'INSERT INTO SYNC_BL_SETTLE_MID ' ||
                              'SELECT a.TICKET_ID stream_id, ' ||
                              '''BL'' DATASOURCE, ' ||
                              'a.ORDER_MODE ORDERMODE, ' ||
                              'a.ACCT_MONTH ORGMONTH, ' ||
                              'a.EC_CODE CUSTOMERNUMBER, ' ||
                              'a.OFFER_CODE POSPECNUMBER, ' ||
                              'decode(a.PRODUCT_ORDER_ID, null, null, a.PRODUCT_CODE) SOSPECNUMBER, ' ||
                              'a.OFFER_ORDER_ID POID, ' ||
                              'a.PRODUCT_ORDER_ID SOID, ' ||
                              'decode(c.CHARGE_ITEM_REF, ''01'', ''1080'', c.CHARGE_ITEM_REF) feetype, ' ||
                              'a.CHARGE_NUMBER dn, ' ||
                              'round(a.CHARGE/10,0) AMOUNT, ' ||
                              '''3'' AMOUNT_TYPE, ' ||
                              'to_char(SYSDATE, ''yyyymmddhh24miss'') SYNCTIME, ' ||
                              '''0'' STATUS, ' ||
                              'DECODE(lower(a.CHARGE_CODE), ''comm_fee'', NULL, ''icomm_fee'', NULL, ''recy_fee'', NULL, CHARGE_CODE) ERRMESG, ' ||
                              'a.HOT_PROV PROV_CD, ' ||
                              'null,  ' ||
                              '''2'' REMARK ' ||
                        'FROM  UR_MVP_' || inMonth || '_T  a, stlusers.STL_CHARGE_ITEM_DEF c ' ||
                       'WHERE lower(a.CHARGE_CODE) = lower(c.CHARGE_ITEM(+)) ';

        SELECT @iv_Sql_Insert1;
        PREPARE STMT FROM @iv_Sql_Insert1;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        SET @iv_Sql_Insert2 := 'INSERT INTO SYNC_BL_SETTLE_MID ' ||
                              'SELECT a.TICKET_ID stream_id, ' ||
                              '''BL'' DATASOURCE, ' ||
                              'a.ORDER_MODE ORDERMODE, ' ||
                              'a.ACCT_MONTH ORGMONTH, ' ||
                              'a.EC_CODE CUSTOMERNUMBER, ' ||
                              'a.OFFER_CODE POSPECNUMBER, ' ||
                              'decode(a.PRODUCT_ORDER_ID, null, null, a.PRODUCT_CODE) SOSPECNUMBER, ' ||
                              'a.OFFER_ORDER_ID POID, ' ||
                              'a.PRODUCT_ORDER_ID SOID, ' ||
                              'decode(c.CHARGE_ITEM_REF, ''01'', ''1080'', c.CHARGE_ITEM_REF) feetype, ' ||
                              'a.CHARGEDPARTY dn, ' ||
                              'round(a.CHARGE/10,0) AMOUNT, ' ||
                              '''3'' AMOUNT_TYPE, ' ||
                              'to_char(SYSDATE, ''yyyymmddhh24miss'') SYNCTIME, ' ||
                              '''0'' STATUS, ' ||
                              'DECODE(lower(a.CHARGE_CODE), ''comm_fee'', NULL, ''icomm_fee'', NULL, ''recy_fee'', NULL, CHARGE_CODE) ERRMESG, ' ||
                              'a.HOT_PROV PROV_CD, ' ||
                              'null,  ' ||
                              '''2'' REMARK ' ||
                         'FROM UR_CTD_' || inMonth || '_T a, stlusers.STL_CHARGE_ITEM_DEF c ' ||
                        'WHERE lower(a.CHARGE_CODE) = lower(c.CHARGE_ITEM(+)) ';
        SELECT @iv_Sql_Insert2;
        PREPARE STMT FROM @iv_Sql_Insert2;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        SET @iv_Sql_Insert3 := 'INSERT INTO SYNC_BL_SETTLE_MID ' ||
                              'SELECT a.TICKET_ID stream_id, ' ||
                              '''BL'' DATASOURCE, ' ||
                              'a.ORDER_MODE ORDERMODE, ' ||
                              'a.ACCT_MONTH ORGMONTH, ' ||
                              'a.EC_CODE CUSTOMERNUMBER, ' ||
                              'a.OFFER_CODE POSPECNUMBER, ' ||
                              'decode(a.PRODUCT_ORDER_ID, null, null, a.PRODUCT_CODE) SOSPECNUMBER, ' ||
                              'a.OFFER_ORDER_ID POID, ' ||
                              'a.PRODUCT_ORDER_ID SOID, ' ||
                              'decode(c.CHARGE_ITEM_REF, ''01'', ''1080'', c.CHARGE_ITEM_REF) feetype, ' ||
                              'a.CREATOR dn, ' ||
                              'round(a.CHARGE/10,0) AMOUNT, ' ||
                              '''3'' AMOUNT_TYPE, ' ||
                              'to_char(SYSDATE, ''yyyymmddhh24miss'') SYNCTIME, ' ||
                              '''0'' STATUS, ' ||
                              'DECODE(lower(a.CHARGE_CODE), ''comm_fee'', NULL, ''icomm_fee'', NULL, ''recy_fee'', NULL, CHARGE_CODE) ERRMESG, ' ||
                              'a.HOT_PROV PROV_CD, ' ||
                              'null,  ' ||
                              '''2'' REMARK ' ||
                        'FROM UR_CAS_' || inMonth || '_T a, stlusers.STL_CHARGE_ITEM_DEF c ' ||
                       'WHERE lower(a.CHARGE_CODE) = lower(c.CHARGE_ITEM(+)) ';
        SELECT @iv_Sql_Insert3;
        PREPARE STMT FROM @iv_Sql_Insert3;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --流量卡部分
        SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_SETTLE_MID ' ||
                       'Select a.TICKET_ID stream_id, ' ||
                       '''BL'' DATASOURCE, ' ||
                       'a.prod_order_mode, ' ||
                       'a.acct_month, ' ||
                       'a.ec_code, ' ||
                       'a.offer_code, ' ||
                       'a.product_code, ' ||
                       'a.offer_order_id, ' ||
                       'a.product_order_id, ' ||
                       'c.CHARGE_ITEM_REF feetype, ' ||
                       'a.MSISDN, ' ||
                       'round(a.charge1 / 10, 0), ' ||
                       '''3'' AMOUNT_TYPE, ' ||
                       'to_char(SYSDATE, ''yyyymmddhh24miss'') SYNCTIME, ' ||
                       '''0'' STATUS, ' ||
                       'NULL ERRMESG, ' ||
                       'NULL PROV_CD, ' ||
                       'null,  ' ||
                       '''2'' REMARK ' ||
                 'From UR_ENCARD_BIND_' || inMonth || '_T a, stlusers.STL_CHARGE_ITEM_DEF c ' ||
                'WHERE lower(a.CHARGE_CODE1) = lower(c.CHARGE_ITEM(+)) ';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        --WLAN统付部分


        SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_RULE_MID ' ||
               'SELECT ''BL'' datasource, ' ||
                       't.order_mode ordermode, ' ||
                       't.acct_month orgmonth, ' ||
                       't.ec_code customernumber, ' ||
                       't.offer_code pospecnumber, ' ||
                       't.product_code sospecnumber, ' ||
                       't.offer_order_id poid, ' ||
                       't.product_order_id soid, ' ||
                       'NULL dn, ' ||
                       't.amount, ' ||
                       'decode(t.charge_code, ''GPRS_Fee'', 2, ''Comm_Fee'', 1, ''Adjust_GPRS_Fee'', 2, ''Adjust_Comm_Fee'', 1, 0) amount_type, ' ||
                       'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime, ' ||
                       '''0'' status, ' ||
                       'NULL errmesg, ' ||
                       't.hot_prov prov_cd, ' ||
                       '2 remark, ' ||
                       'c.charge_item_ref feetype ' ||
                  'FROM (SELECT a.ORDER_MODE, ' ||
                               'a.ACCT_MONTH, ' ||
                               'a.EC_CODE, ' ||
                               'a.OFFER_CODE, ' ||
                               'a.PRODUCT_CODE, ' ||
                               'a.OFFER_ORDER_ID, ' ||
                               'a.PRODUCT_ORDER_ID, ' ||
                               'a.CHARGE_CODE, ' ||
                               'sum(decode(a.charge_code, ''GPRS_Fee'', nvl(a.accu_volume, 0), ''Comm_Fee'', nvl(accu_duration, 0), ''Adjust_GPRS_Fee'', nvl(a.accu_volume, 0), ''Adjust_Comm_Fee'', nvl(accu_duration, 0),0)) amount, ' ||
                               'a.HOT_PROV ' ||
                          'FROM UR_WLANTF_' || inMonth || '_T a ' ||
                         'GROUP BY order_mode, acct_month, ec_code, offer_code, product_code, ' ||
                          'offer_order_id, product_order_id, charge_code, hot_prov' ||
                         ') t, stlusers.STL_CHARGE_ITEM_DEF c ' ||
                   'WHERE lower(t.CHARGE_CODE) = lower(c.CHARGE_ITEM(+))';

        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --能力开放部分
        ----语音通知、语音验证码、点击拨号 
        --step 0 临时表数据清理
        SET @iv_Sql_Insert := 'truncate table sync_bl_rule_swap_04';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --step 1 临时表数据插入
        SET @iv_Sql_Insert := 'insert into sync_bl_rule_swap_04 ' ||
            ' SELECT a.PROD_ORDER_MODE, a.ACCT_MONTH, a.EC_CODE,  a.OFFER_CODE,  a.PRODUCT_CODE,  a.OFFER_ORDER_ID,  a.PRODUCT_ORDER_ID,  a.CHARGE_CODE, sum(a.charge) amount,  a.call_prov  ' ||
            ' FROM UR_YJTXKF_' || inMonth || '_T a  ' ||
            ' GROUP BY prod_order_mode, acct_month, ec_code, offer_code, product_code, offer_order_id, product_order_id, charge_code, call_prov ';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --step 2  根据临时表数据生成最终数据
        SET @iv_Sql_Insert :=  'INSERT INTO SYNC_BL_RULE_MID ' ||
               'SELECT ''BL'' datasource,  ' ||
                       't.prod_order_mode ordermode, ' ||
                       't.acct_month orgmonth,  ' ||
                       't.ec_code customernumber,  ' ||
                       't.offer_code pospecnumber,  ' ||
                       't.product_code sospecnumber,  ' ||
                       't.offer_order_id poid,  ' ||
                       't.product_order_id soid,  ' ||
                       'NULL dn,  ' ||
                       't.amount,  ' ||
                       '1 amount_type,  ' ||
                       'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime, ' ||
                       '''0'' status,  ' ||
                       'NULL errmesg, ' ||
                       't.call_prov prov_cd, ' ||
                       '2 remark, ' ||
                       'c.charge_item_ref feetype  ' ||
                  'FROM sync_bl_rule_swap_04 t, stlusers.STL_CHARGE_ITEM_DEF c  ' ||
                   'WHERE lower(t.CHARGE_CODE) = lower(c.CHARGE_ITEM(+))';

        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----拨打验证
      --step 0 临时表数据清理
        SET @iv_Sql_Insert := 'truncate table sync_bl_rule_swap_04';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --step 1 临时表数据插入
        SET @iv_Sql_Insert := 'insert into sync_bl_rule_swap_04 ' ||
            ' SELECT a.PROD_ORDER_MODE, a.ACCT_MONTH, a.EC_CODE,  a.OFFER_CODE,  a.PRODUCT_CODE,  a.OFFER_ORDER_ID,  a.PRODUCT_ORDER_ID,  a.CHARGE_CODE, count(*) amount,  a.called_prov  ' ||
            ' FROM UR_YJTXIC_' || inMonth || '_T a  ' ||
            ' GROUP BY prod_order_mode, acct_month, ec_code, offer_code, product_code, offer_order_id, product_order_id, charge_code, called_prov ';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --step 2  根据临时表数据生成最终数据
        SET @iv_Sql_Insert :=  'INSERT INTO SYNC_BL_RULE_MID ' ||
               'SELECT ''BL'' datasource,  ' ||
                       't.prod_order_mode ordermode, ' ||
                       't.acct_month orgmonth,  ' ||
                       't.ec_code customernumber,  ' ||
                       't.offer_code pospecnumber,  ' ||
                       't.product_code sospecnumber,  ' ||
                       't.offer_order_id poid,  ' ||
                       't.product_order_id soid,  ' ||
                       'NULL dn,  ' ||
                       't.amount,  ' ||
                       '2 amount_type,  ' ||
                       'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime, ' ||
                       '''0'' status,  ' ||
                       'NULL errmesg, ' ||
                       't.call_prov prov_cd, ' ||
                       '2 remark, ' ||
                       'c.charge_item_ref feetype  ' ||
                  'FROM sync_bl_rule_swap_04 t, stlusers.STL_CHARGE_ITEM_DEF c  ' ||
                   'WHERE lower(t.CHARGE_CODE) = lower(c.CHARGE_ITEM(+))';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----中间号
       
        --此处ur_bosk_yyyymm_t大表被查询了3次，分组很相似，通过一个中间表sync_bl_rule_swap_05_1进行合并
        SET @iv_Sql_Insert := 'truncate table sync_bl_rule_swap_05_1';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
        
        --按条数统计  count(a.charge) amount where  a.charge_code=''Mcn_Comm_Fee''
				--按时长占比  sum(a.charge) amount where   a.charge_code in(''OTM_Comm_Fee'',''Comm_Fee_3914'')
        SET @iv_Sql_Insert := 'insert into sync_bl_rule_swap_05_1 ' ||
            ' SELECT MAX(a.TICKET_ID) ticket_id, a.ACCT_MONTH, a.EC_CODE,  a.PROD_ORDER_MODE, a.OFFER_CODE,  a.PRODUCT_CODE,  a.OFFER_ORDER_ID,  a.PRODUCT_ORDER_ID, ' ||
            ' SUM(decode(recording_sign,''0'',a.CHARGE/10 * 500,''1'', a.CHARGE/10 * 600, a.CHARGE/10)) fee, a.CHARGE_CODE, a.call_prov,  decode(charge_code,''Mcn_Comm_Fee'',count(a.charge),''OTM_Comm_Fee'' or ''Comm_Fee_3914'', sum(a.charge)) as amount,ec_code_prov ' ||
            ' FROM UR_BOSK_' || inMonth || '_T a   ' ||
            ' GROUP BY ACCT_MONTH, EC_CODE, PROD_ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID,charge_code, call_prov,ec_code_prov ';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;
        



        --step 0 临时表数据清理
        SET @iv_Sql_Insert := 'truncate table sync_bl_rule_swap_04';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --step 1 临时表数据插入
        SET @iv_Sql_Insert := 'insert into sync_bl_rule_swap_04 ' ||
            ' SELECT a.PROD_ORDER_MODE, a.ACCT_MONTH, a.EC_CODE,  a.OFFER_CODE,  a.PRODUCT_CODE,  a.OFFER_ORDER_ID,  a.PRODUCT_ORDER_ID,  a.CHARGE_CODE, sum(a.amount) amount,  a.call_prov  ' ||
            ' from sync_bl_rule_swap_05_1 a' ||

            ' GROUP BY prod_order_mode, acct_month, ec_code, offer_code, product_code, offer_order_id, product_order_id, charge_code, call_prov ';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        --step 2  根据临时表数据生成最终数据
        SET @iv_Sql_Insert :=  'INSERT INTO SYNC_BL_RULE_MID ' ||
               'SELECT ''BL'' datasource,  ' ||
                       't.prod_order_mode ordermode, ' ||
                       't.acct_month orgmonth,  ' ||
                       't.ec_code customernumber,  ' ||
                       't.offer_code pospecnumber,  ' ||
                       't.product_code sospecnumber,  ' ||
                       't.offer_order_id poid,  ' ||
                       't.product_order_id soid,  ' ||
                       'NULL dn,  ' ||
                       't.amount,  ' ||
                       'decode(charge_code,''Mcn_Comm_Fee'',2,''OTM_Comm_Fee'',1, ''Comm_Fee_3914'', 1),  ' ||
                       'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime, ' ||
                       '''0'' status,  ' ||
                       'NULL errmesg, ' ||
                       't.call_prov prov_cd, ' ||
                       '2 remark, ' ||
                       'c.charge_item_ref feetype  ' ||
                  'FROM sync_bl_rule_swap_04 t, stlusers.STL_CHARGE_ITEM_DEF c  ' ||
                   'WHERE lower(t.CHARGE_CODE) = lower(c.CHARGE_ITEM(+))';

        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        --step 0 临时表数据清理
        SET @iv_Sql_Insert := 'truncate table sync_bl_rule_swap_05';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --step 1 临时表数据插入
        SET @iv_Sql_Insert := 'insert into sync_bl_rule_swap_05 ' ||
            ' SELECT MAX(a.TICKET_ID) ticket_id, a.ACCT_MONTH, a.EC_CODE,  a.PROD_ORDER_MODE, a.OFFER_CODE,  a.PRODUCT_CODE,  a.OFFER_ORDER_ID,  a.PRODUCT_ORDER_ID, ' ||
            ' round(SUM(a.fee)* 0.3) fee, a.CHARGE_CODE, a.call_prov  ' ||
            ' FROM sync_bl_rule_swap_05_1 a  where a.offer_code = ''50016'' and a.product_code =''5001606''  ' ||
            ' GROUP BY ACCT_MONTH, EC_CODE, PROD_ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID,charge_code, call_prov  ';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        SET @iv_Sql_Insert2 := 'INSERT INTO SYNC_BL_SETTLE_MID ' ||
                         'SELECT ticket_id stream_id,  '||
                         '''BL'' datasource,  '||
                         't.prod_order_mode ordermode,  '||
                         't.acct_month orgmonth,   '||
                         't.ec_code customernumber,   '||
                         't.offer_code pospecnumber,    '||
                         't.product_code sospecnumber,  '||
                         't.offer_order_id poid,    '||
                         't.product_order_id soid,  '||
                         'decode(c.charge_item_ref,''1485'',145,''1975'',195, ''3914'', 394) feetype,  '||
                         'NULL dn,   '||
                         't.fee amount,   '||
                         'decode(t.charge_code, ''Mcn_Comm_Fee'', 2, ''OTM_Comm_Fee'', 1,''Comm_Fee_3914'', 1) amount_type,   '||
                         'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime,   '||
                         '''0'' status, '||
                         'NULL errmesg,   '||
                         't.call_prov prov_cd,    '||
                         'NULL accountid,    '||
                         '''2'' remark     '||
                    'FROM sync_bl_rule_swap_05 t, stlusers.STL_CHARGE_ITEM_DEF c WHERE t.charge_code = c.charge_item ';

        SELECT @iv_Sql_Insert2;
        PREPARE STMT FROM @iv_Sql_Insert2;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

       --step 0 临时表数据清理
        SET @iv_Sql_Insert := 'truncate table sync_bl_rule_swap_05';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --step 1 临时表数据插入
        SET @iv_Sql_Insert := 'insert into sync_bl_rule_swap_05 ' ||
            ' SELECT MAX(a.TICKET_ID) ticket_id, a.ACCT_MONTH, a.EC_CODE,  a.PROD_ORDER_MODE, a.OFFER_CODE,  a.PRODUCT_CODE,  a.OFFER_ORDER_ID,  a.PRODUCT_ORDER_ID, ' ||
            ' round(SUM(a.fee)* 0.7) fee, a.CHARGE_CODE, a.ec_code_prov  ' ||
            ' FROM sync_bl_rule_swap_05_1 a  where a.offer_code = ''50016'' and a.product_code =''5001606''  ' ||
            ' GROUP BY ACCT_MONTH, EC_CODE, PROD_ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID,charge_code, ec_code_prov  ';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        SET @iv_Sql_Insert3 := 'INSERT INTO SYNC_BL_SETTLE_MID ' ||
                         'SELECT ticket_id stream_id,  '||
                         '''BL'' datasource,  '||
                         't.prod_order_mode ordermode,  '||
                         't.acct_month orgmonth,   '||
                         't.ec_code customernumber,   '||
                         't.offer_code pospecnumber,    '||
                         't.product_code sospecnumber,  '||
                         't.offer_order_id poid,    '||
                         't.product_order_id soid,  '||
                         'decode(c.charge_item_ref,''1485'',145,''1975'',195, ''3914'', 394) feetype,  '||
                         'NULL dn,   '||
                         't.fee amount,   '||
                         'decode(t.charge_code, ''Mcn_Comm_Fee'', 2, ''OTM_Comm_Fee'', 1,''Comm_Fee_3914'', 1) amount_type,   '||
                         'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime,   '||
                         '''0'' status, '||
                         'NULL errmesg,   '||
                         't.call_prov prov_cd,    '||
                         'NULL accountid,    '||
                         '''2'' remark     '||
                    'FROM sync_bl_rule_swap_05 t, stlusers.STL_CHARGE_ITEM_DEF c WHERE t.charge_code = c.charge_item ';


        SELECT @iv_Sql_Insert3;
        PREPARE STMT FROM @iv_Sql_Insert3;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----工作号
        --step 0 临时表数据清理
        SET @iv_Sql_Insert := 'truncate table sync_bl_rule_swap_05';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --step 1 临时表数据插入
        SET @iv_Sql_Insert := 'insert into sync_bl_rule_swap_05 ' ||
            ' SELECT MAX(a.TICKET_ID) ticket_id, a.ACCT_MONTH, a.EC_CODE,  a.PROD_ORDER_MODE, a.OFFER_CODE,  a.PRODUCT_CODE,  a.OFFER_ORDER_ID,  a.PRODUCT_ORDER_ID, ' ||
            ' round(SUM(a.CHARGE/10)) fee, a.CHARGE_CODE,null  ' ||
            ' FROM UR_BOSGSMS_' || inMonth || '_T a  where a.offer_code = ''50016'' and a.product_code =''2022999400004820''  ' ||
            ' GROUP BY ACCT_MONTH, EC_CODE, PROD_ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID,charge_code  ';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_SETTLE_MID ' ||
       'SELECT ticket_id stream_id, '||
       '''BL'' datasource,  '||
       't.prod_order_mode ordermode, '||
       't.acct_month orgmonth,    '||
       't.ec_code customernumber,  '||
       't.offer_code pospecnumber,   '||
       't.product_code sospecnumber,    '||
       't.offer_order_id poid,        '||
       't.product_order_id soid,   '||
       'c.charge_item_ref feetype,     '||
       'NULL dn,      '||
       't.fee amount,    '||
       '''2'' amount_type,  '||
       'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime,   '||
       '''0'' status, '||
       'NULL errmesg,   '||
       'NULL prov_cd,   '||
       'NULL accountid,   '||
       '''2'' remark      '||
        'FROM sync_bl_rule_swap_05 t, stlusers.STL_CHARGE_ITEM_DEF c WHERE t.charge_code = c.charge_item';

        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----5G-阅信

        --step 0 临时表数据清理
        SET @iv_Sql_Insert := 'truncate table sync_bl_rule_swap_05';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --step 1 临时表数据插入
        SET @iv_Sql_Insert := 'insert into sync_bl_rule_swap_05 ' ||
            ' SELECT MAX(a.TICKET_ID) ticket_id, a.ACCT_MONTH, a.EC_CODE,  a.PROD_ORDER_MODE, a.OFFER_CODE,  a.PRODUCT_CODE,  a.OFFER_ORDER_ID,  a.PRODUCT_ORDER_ID, ' ||
            ' round(SUM(a.CHARGE/10)) fee, a.CHARGE_CODE,null  ' ||
            ' FROM UR_RCSSMS_' || inMonth || '_T a  where a.offer_code = ''50034'' and a.product_code =''5003401''  ' ||
            ' GROUP BY ACCT_MONTH, EC_CODE, PROD_ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID,charge_code  ';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_SETTLE_MID ' ||
       'SELECT ticket_id stream_id, '||
       '''BL'' datasource,  '||
       't.prod_order_mode ordermode, '||
       't.acct_month orgmonth,    '||
       't.ec_code customernumber,  '||
       't.offer_code pospecnumber,   '||
       't.product_code sospecnumber,    '||
       't.offer_order_id poid,        '||
       't.product_order_id soid,   '||
       'c.charge_item_ref feetype,     '||
       'NULL dn,      '||
       't.fee amount,    '||
       '''2'' amount_type,  '||
       'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime,   '||
       '''0'' status, '||
       'NULL errmesg,   '||
       'NULL prov_cd,   '||
       'NULL accountid,   '||
       '''2'' remark      '||
        'FROM sync_bl_rule_swap_05 t, stlusers.STL_CHARGE_ITEM_DEF c WHERE t.charge_code = c.charge_item';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        ----OneGame-游戏云直播
        --step 0 临时表数据清理
        SET @iv_Sql_Insert := 'truncate table sync_bl_rule_swap_05';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --step 1 临时表数据插入
        SET @iv_Sql_Insert := 'insert into sync_bl_rule_swap_05 ' ||
            ' SELECT MAX(a.TICKET_ID) ticket_id, a.ACCT_MONTH, a.EC_CODE,  a.PROD_ORDER_MODE, a.OFFER_CODE,  a.PRODUCT_CODE,  a.OFFER_ORDER_ID,  a.PRODUCT_ORDER_ID, ' ||
            ' round(SUM(a.CHARGE/10)) fee, a.CHARGE_CODE,null  ' ||
            ' FROM UR_ONEGAME_' || inMonth || '_T a  where a.offer_code = ''50107'' and a.product_code =''2023999400018547''  ' ||
            ' GROUP BY ACCT_MONTH, EC_CODE, PROD_ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID,charge_code  ';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_SETTLE_MID ' ||
       'SELECT ticket_id stream_id, '||
       '''BL'' datasource,  '||
       't.prod_order_mode ordermode, '||
       't.acct_month orgmonth,    '||
       't.ec_code customernumber,  '||
       't.offer_code pospecnumber,   '||
       't.product_code sospecnumber,    '||
       't.offer_order_id poid,        '||
       't.product_order_id soid,   '||
       'decode(c.charge_item_ref,''3339'',339,''3340'',340,''3341'',341) feetype,     '||
       'NULL dn,      '||
       't.fee amount,    '||
       '''2'' amount_type,  '||
       'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime,   '||
       '''0'' status, '||
       'NULL errmesg,   '||
       'NULL prov_cd,   '||
       'NULL accountid,   '||
       '''2'' remark      '||
        'FROM sync_bl_rule_swap_05 t, stlusers.STL_CHARGE_ITEM_DEF c WHERE t.charge_code = c.charge_item';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;




        ----成员视频彩铃-中间号

        --step 0 临时表数据清理
        SET @iv_Sql_Insert := 'truncate table sync_bl_rule_swap_05';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @iv_Sql_Insert := 'insert into sync_bl_rule_swap_05 ' ||
            ' SELECT MAX(a.TICKET_ID) ticket_id, a.ACCT_MONTH, a.EC_CODE,  a.PROD_ORDER_MODE, a.OFFER_CODE,  a.PRODUCT_CODE,  a.OFFER_ORDER_ID,  a.PRODUCT_ORDER_ID, ' ||
            ' round(SUM(a.CHARGE * 300 /10)) fee, a.CHARGE_CODE,null  ' ||
            ' FROM UR_MRBP_' || inMonth || '_T a  where a.offer_code = ''*********'' and a.product_code =''910401''  ' ||
            ' GROUP BY ACCT_MONTH, EC_CODE, PROD_ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID,charge_code  ';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_SETTLE_MID ' ||
       'SELECT ticket_id stream_id, '||
       '''BL'' datasource,  '||
       't.prod_order_mode ordermode, '||
       't.acct_month orgmonth,    '||
       't.ec_code customernumber,  '||
       't.offer_code pospecnumber,   '||
       't.product_code sospecnumber,    '||
       't.offer_order_id poid,        '||
       't.product_order_id soid,   '||
       'decode(c.charge_item_ref,''3742'',742) feetype,     '||
       'NULL dn,      '||
       't.fee amount,    '||
       '''2'' amount_type,  '||
       'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime,   '||
       '''0'' status, '||
       'NULL errmesg,   '||
       'NULL prov_cd,   '||
       'NULL accountid,   '||
       '''2'' remark      '||
        'FROM sync_bl_rule_swap_05 t, stlusers.STL_CHARGE_ITEM_DEF c WHERE t.charge_code = c.charge_item';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @iv_Sql_Insert := 'truncate table sync_bl_rule_swap_05';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @iv_Sql_Insert := 'insert into sync_bl_rule_swap_05 ' ||
            ' SELECT MAX(a.TICKET_ID) ticket_id, a.ACCT_MONTH, a.EC_CODE,  a.PROD_ORDER_MODE, a.OFFER_CODE,  a.PRODUCT_CODE,  a.OFFER_ORDER_ID,  a.PRODUCT_ORDER_ID, ' ||
            ' round(SUM(a.CHARGE * 200 / 10)) fee, a.CHARGE_CODE,null  ' ||
            ' FROM UR_MRBP_' || inMonth || '_T a  where a.offer_code = ''*********'' and a.product_code =''910401''  ' ||
            ' GROUP BY ACCT_MONTH, EC_CODE, PROD_ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID,charge_code  ';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_SETTLE_MID ' ||
       'SELECT ticket_id stream_id, '||
       '''BL'' datasource,  '||
       't.prod_order_mode ordermode, '||
       't.acct_month orgmonth,    '||
       't.ec_code customernumber,  '||
       't.offer_code pospecnumber,   '||
       't.product_code sospecnumber,    '||
       't.offer_order_id poid,        '||
       't.product_order_id soid,   '||
       'decode(c.charge_item_ref,''3742'',247) feetype,     '||
       'NULL dn,      '||
       't.fee amount,    '||
       '''2'' amount_type,  '||
       'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime,   '||
       '''0'' status, '||
       'NULL errmesg,   '||
       'NULL prov_cd,   '||
       'NULL accountid,   '||
       '''2'' remark      '||
        'FROM sync_bl_rule_swap_05 t, stlusers.STL_CHARGE_ITEM_DEF c WHERE t.charge_code = c.charge_item';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;



        ----成员视频彩铃-工作号

        SET @iv_Sql_Insert := 'truncate table sync_bl_rule_swap_05';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @iv_Sql_Insert := 'insert into sync_bl_rule_swap_05 ' ||
            ' SELECT MAX(a.TICKET_ID) ticket_id, a.ACCT_MONTH, a.EC_CODE,  a.PROD_ORDER_MODE, a.OFFER_CODE,  a.PRODUCT_CODE,  a.OFFER_ORDER_ID,  a.PRODUCT_ORDER_ID, ' ||
            ' round(SUM(a.CHARGE * 60000 /10)) fee, a.CHARGE_CODE,null  ' ||
            ' FROM UR_MRBPGZH_' || inMonth || '_T a  where a.offer_code = ''*********'' and a.product_code =''910401''  ' ||
            ' GROUP BY ACCT_MONTH, EC_CODE, PROD_ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID,charge_code  ';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_SETTLE_MID ' ||
       'SELECT ticket_id stream_id, '||
       '''BL'' datasource,  '||
       't.prod_order_mode ordermode, '||
       't.acct_month orgmonth,    '||
       't.ec_code customernumber,  '||
       't.offer_code pospecnumber,   '||
       't.product_code sospecnumber,    '||
       't.offer_order_id poid,        '||
       't.product_order_id soid,   '||
       'decode(c.charge_item_ref,''3681'',681) feetype,     '||
       'NULL dn,      '||
       't.fee amount,    '||
       '''2'' amount_type,  '||
       'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime,   '||
       '''0'' status, '||
       'NULL errmesg,   '||
       'NULL prov_cd,   '||
       'NULL accountid,   '||
       '''2'' remark      '||
        'FROM sync_bl_rule_swap_05 t, stlusers.STL_CHARGE_ITEM_DEF c WHERE t.charge_code = c.charge_item';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @iv_Sql_Insert := 'truncate table sync_bl_rule_swap_05';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @iv_Sql_Insert := 'insert into sync_bl_rule_swap_05 ' ||
            ' SELECT MAX(a.TICKET_ID) ticket_id, a.ACCT_MONTH, a.EC_CODE,  a.PROD_ORDER_MODE, a.OFFER_CODE,  a.PRODUCT_CODE,  a.OFFER_ORDER_ID,  a.PRODUCT_ORDER_ID, ' ||
            ' round(SUM(a.CHARGE * 26000 / 10)) fee, a.CHARGE_CODE,null  ' ||
            ' FROM UR_MRBPGZH_' || inMonth || '_T a  where a.offer_code = ''*********'' and a.product_code =''910401''  ' ||
            ' GROUP BY ACCT_MONTH, EC_CODE, PROD_ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID,charge_code  ';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_SETTLE_MID ' ||
       'SELECT ticket_id stream_id, '||
       '''BL'' datasource,  '||
       't.prod_order_mode ordermode, '||
       't.acct_month orgmonth,    '||
       't.ec_code customernumber,  '||
       't.offer_code pospecnumber,   '||
       't.product_code sospecnumber,    '||
       't.offer_order_id poid,        '||
       't.product_order_id soid,   '||
       'decode(c.charge_item_ref,''3681'',186) feetype,     '||
       'NULL dn,      '||
       't.fee amount,    '||
       '''2'' amount_type,  '||
       'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime,   '||
       '''0'' status, '||
       'NULL errmesg,   '||
       'NULL prov_cd,   '||
       'NULL accountid,   '||
       '''2'' remark      '||
        'FROM sync_bl_rule_swap_05 t, stlusers.STL_CHARGE_ITEM_DEF c WHERE t.charge_code = c.charge_item';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        --5G-异网短信通信费
        SET @iv_Sql_Insert := 'truncate table sync_bl_rule_swap_06';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @iv_Sql_Insert := 'insert into sync_bl_rule_swap_06 ' ||
            ' SELECT MAX(a.TICKET_ID) ticket_id, a.ACCT_MONTH, a.EC_CODE,  a.PROD_ORDER_MODE, a.OFFER_CODE,  a.PRODUCT_CODE,  a.OFFER_ORDER_ID,  a.PRODUCT_ORDER_ID, ' ||
            ' round(SUM(a.CHARGE /10)) fee, a.CHARGE_CODE,a.oth_sp_orig  ' ||
            ' FROM UR_GCSMS_' || inMonth || '_T a  where a.offer_code = ''50034'' and a.product_code =''5003401''  and a.oth_sp_orig=''01'' ' ||
            ' GROUP BY ACCT_MONTH, EC_CODE, PROD_ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID,charge_code,oth_sp_orig  ';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_SETTLE_MID ' ||
       'SELECT ticket_id stream_id, '||
       '''BL'' datasource,  '||
       't.prod_order_mode ordermode, '||
       't.acct_month orgmonth,    '||
       't.ec_code customernumber,  '||
       't.offer_code pospecnumber,   '||
       't.product_code sospecnumber,    '||
       't.offer_order_id poid,        '||
       't.product_order_id soid,   '||
       'decode(c.charge_item_ref,''3908'',''908'')  feetype,     '||
       'NULL dn,      '||
       't.fee amount,    '||
       '''2'' amount_type,  '||
       'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime,   '||
       '''0'' status, '||
       'NULL errmesg,   '||
       'decode(t.oth_sp_orig,''01'',''ZW'') prov_cd,   '||
       'NULL accountid,   '||
       '''2'' remark      '||
        'FROM sync_bl_rule_swap_06 t, stlusers.STL_CHARGE_ITEM_DEF c WHERE t.charge_code = c.charge_item';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @iv_Sql_Insert := 'truncate table sync_bl_rule_swap_06';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @iv_Sql_Insert := 'insert into sync_bl_rule_swap_06 ' ||
            ' SELECT MAX(a.TICKET_ID) ticket_id, a.ACCT_MONTH, a.EC_CODE,  a.PROD_ORDER_MODE, a.OFFER_CODE,  a.PRODUCT_CODE,  a.OFFER_ORDER_ID,  a.PRODUCT_ORDER_ID, ' ||
            ' round(SUM(a.CHARGE /10)) fee, a.CHARGE_CODE,a.oth_sp_orig  ' ||
            ' FROM UR_GCSMS_' || inMonth || '_T a  where a.offer_code = ''50034'' and a.product_code =''5003401''  and a.oth_sp_orig=''02'' ' ||
            ' GROUP BY ACCT_MONTH, EC_CODE, PROD_ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID,charge_code,oth_sp_orig  ';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_SETTLE_MID ' ||
       'SELECT ticket_id stream_id, '||
       '''BL'' datasource,  '||
       't.prod_order_mode ordermode, '||
       't.acct_month orgmonth,    '||
       't.ec_code customernumber,  '||
       't.offer_code pospecnumber,   '||
       't.product_code sospecnumber,    '||
       't.offer_order_id poid,        '||
       't.product_order_id soid,   '||
       'decode(c.charge_item_ref,''3908'',''809'')  feetype,     '||
       'NULL dn,      '||
       't.fee amount,    '||
       '''2'' amount_type,  '||
       'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime,   '||
       '''0'' status, '||
       'NULL errmesg,   '||
       'decode(t.oth_sp_orig,''02'',''ZYJC'') prov_cd,   '||
       'NULL accountid,   '||
       '''2'' remark      '||
        'FROM sync_bl_rule_swap_06 t, stlusers.STL_CHARGE_ITEM_DEF c WHERE t.charge_code = c.charge_item';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;



        ----阅信(增值能力)
        --step 0 临时表数据清理
        SET @iv_Sql_Insert := 'truncate table sync_bl_rule_swap_05';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --step 1 临时表数据插入
        SET @iv_Sql_Insert := 'insert into sync_bl_rule_swap_05 ' ||
            ' SELECT MAX(a.TICKET_ID) ticket_id, a.ACCT_MONTH, a.EC_CODE,  a.PROD_ORDER_MODE, a.OFFER_CODE,  a.PRODUCT_CODE,  a.OFFER_ORDER_ID,  a.PRODUCT_ORDER_ID, ' ||
            ' round(SUM(a.CHARGE/10)) fee, a.CHARGE_CODE,null  ' ||
            ' FROM UR_NEWRCS_' || inMonth || '_T a  where a.offer_code = ''50020'' and a.product_code =''5002001''  ' ||
            ' GROUP BY ACCT_MONTH, EC_CODE, PROD_ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID,charge_code  ';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_SETTLE_MID ' ||
       'SELECT ticket_id stream_id, '||
       '''BL'' datasource,  '||
       't.prod_order_mode ordermode, '||
       't.acct_month orgmonth,    '||
       't.ec_code customernumber,  '||
       't.offer_code pospecnumber,   '||
       't.product_code sospecnumber,    '||
       't.offer_order_id poid,        '||
       't.product_order_id soid,   '||
       'c.charge_item_ref feetype,     '||
       'NULL dn,      '||
       't.fee amount,    '||
       '''2'' amount_type,  '||
       'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime,   '||
       '''0'' status, '||
       'NULL errmesg,   '||
       'NULL prov_cd,   '||
       'NULL accountid,   '||
       '''2'' remark      '||
        'FROM sync_bl_rule_swap_05 t, stlusers.STL_CHARGE_ITEM_DEF c WHERE t.charge_code = c.charge_item';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;



        ----和盾-电子签章
        --step 0 临时表数据清理
        SET @iv_Sql_Insert := 'truncate table sync_bl_rule_swap_05';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --step 1 临时表数据插入
        SET @iv_Sql_Insert := 'insert into sync_bl_rule_swap_05 ' ||
            ' SELECT MAX(a.TICKET_ID) ticket_id, a.ACCT_MONTH, a.EC_CODE,  a.PROD_ORDER_MODE, a.OFFER_CODE,  a.PRODUCT_CODE,  a.OFFER_ORDER_ID,  a.PRODUCT_ORDER_ID, ' ||
            ' round(SUM(a.CHARGE*a.VISA_NUMBER/10)) fee, a.CHARGE_CODE, null  ' ||
            ' FROM UR_CA_' || inMonth || '_T a  where a.offer_code = ''50086'' and a.product_code =''2022999400016610''  ' ||
            ' GROUP BY ACCT_MONTH, EC_CODE, PROD_ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID,charge_code  ';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_SETTLE_MID ' ||
       'SELECT ticket_id stream_id,  '||
       '''BL'' datasource,  '||
       't.prod_order_mode ordermode,  '||
       't.acct_month orgmonth,  '||
       't.ec_code customernumber,  '||
       't.offer_code pospecnumber,   '||
       't.product_code sospecnumber,  '||
       't.offer_order_id poid,   '||
       't.product_order_id soid,   '||
       'c.charge_item_ref feetype,   '||
       'NULL dn,   '||
       't.fee amount,  '||
       '''2'' amount_type,  '||
       'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime,   '||
       '''0'' status,  '||
       'NULL errmesg,  '||
       'NULL prov_cd,   '||
       'NULL accountid,   '||
       '''2'' remark    '||
        'FROM sync_bl_rule_swap_05 t, stlusers.STL_CHARGE_ITEM_DEF c WHERE t.charge_code = c.charge_item';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;




        ----5G-会话消息
        --step 0 临时表数据清理
        SET @iv_Sql_Insert := 'truncate table sync_bl_rule_swap_05';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --step 1 临时表数据插入
        SET @iv_Sql_Insert := 'insert into sync_bl_rule_swap_05 ' ||
            ' SELECT MAX(a.TICKET_ID) ticket_id, a.ACCT_MONTH, a.EC_CODE,  a.PROD_ORDER_MODE, a.OFFER_CODE,  a.PRODUCT_CODE,  a.OFFER_ORDER_ID,  a.PRODUCT_ORDER_ID, ' ||
            ' round(SUM(a.CHARGE/10)) fee, a.CHARGE_CODE, null  ' ||
            ' FROM UR_MAAPCSN_' || inMonth || '_T a  where a.offer_code = ''50034'' and a.product_code =''5003401''  ' ||
            ' GROUP BY ACCT_MONTH, EC_CODE, PROD_ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID,charge_code  ';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

      SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_SETTLE_MID ' ||
       'SELECT ticket_id stream_id,  '||
       '''BL'' datasource,  '||
       't.prod_order_mode ordermode,  '||
       't.acct_month orgmonth,  '||
       't.ec_code customernumber,  '||
       't.offer_code pospecnumber,   '||
       't.product_code sospecnumber,  '||
       't.offer_order_id poid,   '||
       't.product_order_id soid,   '||
       'c.charge_item_ref feetype,   '||
       'NULL dn,   '||
       't.fee amount,  '||
       '''2'' amount_type,  '||
       'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime,   '||
       '''0'' status,  '||
       'NULL errmesg,  '||
       'NULL prov_cd,   '||
       'NULL accountid,   '||
       '''2'' remark    '||
        'FROM sync_bl_rule_swap_05 t, stlusers.STL_CHARGE_ITEM_DEF c WHERE t.charge_code = c.charge_item';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        --终端分成-叠加包更新
        begin
          open maapmma_1587;
          loop
            fetch maapmma_1587 into inSoid, inCount;
            exit when maapmma_1587%NOTFOUND;


            --随机取订购中的部分话单归入叠加包，rate_back_id更新为1，万里优化ticket_id做索引，跟晓菲确认过ticket_id是唯一的，
           
             execute immediate 'update ur_maapmma_' || inMonth || '_t as t ' ||

              'set t.rate_back_id = ''1'' where t.product_order_id = ' || inSoid || ' limit ' || inCount ;
              
          end loop;
          commit;
        end;


        --终端分成-互联网公司
        --step 0 临时表数据清理
        SET @iv_Sql_Insert := 'truncate table sync_bl_rule_swap_05';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --step 1 临时表数据插入
        SET @iv_Sql_Insert := 'insert into sync_bl_rule_swap_05 ' ||
            ' SELECT MAX(a.TICKET_ID) ticket_id, a.ACCT_MONTH, a.EC_CODE,  a.PROD_ORDER_MODE, a.OFFER_CODE,  a.PRODUCT_CODE,  a.OFFER_ORDER_ID,  a.PRODUCT_ORDER_ID, ' ||
            ' round(SUM(a.CHARGE/10)) fee, a.CHARGE_CODE,null  ' ||
            ' FROM UR_MAAPMMA_' || inMonth || '_T a  where a.offer_code = ''50034'' and a.product_code =''5003401''  ' ||
            ' GROUP BY ACCT_MONTH, EC_CODE, PROD_ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID,charge_code  ';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_SETTLE_MID ' ||
        'SELECT ticket_id stream_id,  '||
       '''BL'' datasource,    '||
       't.prod_order_mode ordermode,   '||
       't.acct_month orgmonth,    '||
       't.ec_code customernumber,    '||
       't.offer_code pospecnumber,    '||
       't.product_code sospecnumber,    '||
       't.offer_order_id poid,   '||
       't.product_order_id soid,   '||
       'decode(c.charge_item_ref,''1587'',''587'',c.charge_item_ref) feetype,   '||
       'NULL dn,   '||
       't.fee amount,    '||
       '''2'' amount_type,   '||
       'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime,   '||
       '''0'' status,  '||
       'NULL errmesg,    '||
       'NULL prov_cd,    '||
       'NULL accountid,   '||
       '''2'' remark      '||
        'FROM sync_bl_rule_swap_05 t, stlusers.STL_CHARGE_ITEM_DEF c WHERE t.charge_code = c.charge_item ';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;



        --终端分成-终端厂商  ------------------------待确定是否需要优化
        SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_SETTLE_MID ' ||
         'SELECT ticket_id stream_id, ' ||
         '''BL'' datasource, ' ||
         't.prod_order_mode ordermode, ' ||
         't.acct_month orgmonth, ' ||
         't.ec_code customernumber, ' ||
         't.offer_code pospecnumber, ' ||
         't.product_code sospecnumber, ' ||
         't.offer_order_id poid, ' ||
         't.product_order_id soid, ' ||
         'decode(c.charge_item_ref, ''1587'', ''157'', c.charge_item_ref) feetype, ' ||
         ''''' dn, ' ||
         't.fee amount, ' ||
         '''2'' amount_type, ' ||
         'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime, ' ||
         '''0'' status, ' ||
         ''''' errmesg, ' ||
         't.terminalvendor prov_cd, ' ||
         'null accountid, ' ||
         '''2'' remark ' ||
    'FROM (SELECT MAX(a.TICKET_ID) ticket_id, ' ||
                 'a.ACCT_MONTH, ' ||
                 'a.EC_CODE, ' ||
                 'a.PROD_ORDER_MODE, ' ||
                 'a.OFFER_CODE, ' ||
                 'a.PRODUCT_CODE, ' ||
                 'a.OFFER_ORDER_ID, ' ||
                 'a.PRODUCT_ORDER_ID, ' ||
                 
                 






                 'round(decode(a.rate_back_id, ''1'', nvl(f.cdr_rate,1), a.bill_charge) * 10 * count(a.cerca_mark) * 0.3 * d.incentivecoefficient * c.evaluationscore / 10) fee, ' ||
                 'a.charge_code, ' ||
                 'd.terminalvendor ' ||
            'FROM stludr.UR_MAAPMMA_' || inMonth || '_T a, ' ||
                 '(select x.terminalvendor, x.evaluationscore ' ||
                    'from stlusers.MAAP_TERMINAL_INFO x, ' ||
                         '(select max(createdate) createdate, terminalvendor ' ||
                            'from stlusers.MAAP_TERMINAL_INFO ' ||
                           'where ''' || inMonth || ''' between startdate and enddate ' ||
                             'and incentivecoefficient is null ' ||
                           'group by terminalvendor) y ' ||
                   'where x.createdate = y.createdate ' ||
                     'and x.terminalvendor = y.terminalvendor ' ||
                     'and x.incentivecoefficient is null ' ||
                     'and ''' || inMonth || ''' between x.startdate and x.enddate) c, ' ||
                 '(select x.terminalvendor, x.incentivecoefficient ' ||
                    'from stlusers.MAAP_TERMINAL_INFO x, ' ||
                         '(select max(createdate) createdate, terminalvendor ' ||
                            'from stlusers.MAAP_TERMINAL_INFO ' ||
                           'where ''' || inMonth || ''' between startdate and enddate ' ||
                             'and evaluationscore is null ' ||
                           'group by terminalvendor) y ' ||
                   'where x.createdate = y.createdate ' ||
                     'and x.terminalvendor = y.terminalvendor ' ||
                     'and x.evaluationscore is null ' ||
                     'and ''' || inMonth || ''' between x.startdate and x.enddate) d, ' ||
                 'stludr.MAAPMMA_VENDOR_CONF e, ' ||
          '(select * from stludr.BIZ_MSG_MMM_STL where acct_month = ''' || inMonth || ''') f ' ||
           'where a.offer_code = ''50034'' ' ||
             'and a.product_code = ''5003401'' ' ||
             'and a.cerca_mark = ''1'' ' ||
             'and a.manufacturer_code = e.terminal_brand ' ||
             'and e.terminal_vendor = c.terminalvendor ' ||
             'and e.terminal_vendor = d.terminalvendor ' ||
        'and a.product_order_id = f.subscriber_id(+) ' ||
           'GROUP BY ACCT_MONTH, ' ||
                    'EC_CODE, ' ||
                    'a.PROD_ORDER_MODE, ' ||
                    'OFFER_CODE, ' ||
                    'a.PRODUCT_CODE, ' ||
                    'OFFER_ORDER_ID, ' ||
                    'PRODUCT_ORDER_ID, ' ||
                    'rate_back_id, ' ||
          'f.cdr_rate, ' ||
                    'bill_charge, ' ||
                    'charge_code, ' ||
                    'cerca_mark, ' ||
                    'd.incentivecoefficient, ' ||
                    'c.evaluationscore, ' ||
                    'd.terminalvendor) t, ' ||
         'stlusers.STL_CHARGE_ITEM_DEF c ' ||
   'WHERE t.charge_code = c.charge_item';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        ----QoS --130秒，待优化
        --step 0 临时表数据清理
        SET @iv_Sql_Insert := 'truncate table sync_bl_rule_swap_04';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --step 1 临时表数据插入
        SET @iv_Sql_Insert := 'insert into sync_bl_rule_swap_04 ' ||
            ' SELECT a.PROD_ORDER_MODE, a.ACCT_MONTH, a.EC_CODE, a.OFFER_CODE,  a.PRODUCT_CODE,  a.OFFER_ORDER_ID,  a.PRODUCT_ORDER_ID, ' ||
            ' a.CHARGE_CODE, sum(a.billing_unit) amount,  a.visit_prov ' ||
            ' FROM UR_YJTXPT_' || inMonth || '_T a ' ||
            ' GROUP BY prod_order_mode, acct_month, ec_code, offer_code, product_code, offer_order_id, product_order_id, charge_code, visit_prov ';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @iv_Sql_Insert :=  'INSERT INTO SYNC_BL_RULE_MID ' ||
               'SELECT ''BL'' datasource,  ' ||
                       't.prod_order_mode ordermode, ' ||
                       't.acct_month orgmonth,  ' ||
                       't.ec_code customernumber,  ' ||
                       't.offer_code pospecnumber,  ' ||
                       't.product_code sospecnumber,  ' ||
                       't.offer_order_id poid,  ' ||
                       't.product_order_id soid,  ' ||
                       'NULL dn,  ' ||
                       't.amount,  ' ||
                       '2 amount_type,  ' ||
                       'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime, ' ||
                       '''0'' status,  ' ||
                       'NULL errmesg, ' ||
                       't.call_prov  prov_cd, ' ||
                       '2 remark, ' ||
                       'c.charge_item_ref feetype  ' ||
                  'FROM sync_bl_rule_swap_04 t, stlusers.STL_CHARGE_ITEM_DEF c  ' ||
                   'WHERE lower(t.CHARGE_CODE) = lower(c.CHARGE_ITEM(+))';

        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----短信通知、短信验证码、企业挂机彩信、来去电身份提示 
        SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_SETTLE_MID ' ||
               'SELECT ticket_id stream_id, ' ||
                     '''BL'' datasource, ' ||
                     'order_mode ordermode, ' ||
                     'acct_month orgmonth, ' ||
                     'ec_code customernumber, ' ||
                     'offer_code pospecnumber, ' ||
                     'product_code sospecnumber, ' ||
                     'offer_order_id poid, ' ||
                     'product_order_id soid, ' ||
                     '''1041'' feetype, ' ||
                     'NULL dn, ' ||
                     'fee amount, ' ||
                     '''2'' amount_type, ' ||
                     'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime, ' ||
                     '''0'' status, ' ||
                     'NULL errmesg, ' ||
                     'NULL prov_cd, ' ||
                     'NULL accountid, ' ||
                     '''2'' remark ' ||
                'FROM(SELECT MAX(a.TICKET_ID) ticket_id, ' ||
                     'a.ACCT_MONTH, ' ||
                     'a.EC_CODE, ' ||
                     'a.ORDER_MODE, ' ||
                     'a.OFFER_CODE, ' ||
                     'a.PRODUCT_CODE, ' ||
                     'a.OFFER_ORDER_ID, ' ||
                     'a.PRODUCT_ORDER_ID, ' ||
                     'round(SUM(a.CHARGE2 / 100), 0) fee ' ||
                'FROM UR_CMAS_' || inMonth || '_T a ' ||
               'where a.offer_code = ''50016'' ' ||
               'GROUP BY ACCT_MONTH, EC_CODE, ORDER_MODE, OFFER_CODE, PRODUCT_CODE, ' ||
                     'OFFER_ORDER_ID, PRODUCT_ORDER_ID)';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;



        --企业和多号全网产品
        SET @iv_Sql_Insert :=  'INSERT INTO SYNC_BL_RULE_MID ' ||
                       'select ''ST'' datasource, ' ||
                       'order_mode ordermode, ' ||
                       'acct_month orgmonth, ' ||
                       'ec_code customernumber, ' ||
                       'offer_code pospecnumber, ' ||
                       'product_code sospecnumber, ' ||
                       'offer_order_id poid, ' ||
                       'product_order_id soid, ' ||
                       'NULL dn, ' ||
                       'round(sum(charge / 10)) amount, ' ||
                       '''0'' amount_type, ' ||
                       'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime, ' ||
                       '''0'' status, ' ||
                       'NULL errmsg, ' ||
                       'host_prov prov_cd, ' ||
                       'NULL remark, ' ||
                       'c.charge_item_ref feetype ' ||
                  'from ur_bosp_' || inMonth || '_t a, stlusers.STL_CHARGE_ITEM_DEF c ' ||
                 'where offer_code = ''1101010'' ' ||
                   'and product_code = ''9101002'' ' ||
           

                  

                   'and charge_code is not null ' ||
                   'and host_prov is not null ' ||
                   'and lower(a.CHARGE_CODE) = lower(c.CHARGE_ITEM(+)) ' ||
                 'group by acct_month, order_mode, ec_code, offer_code, product_code,  ' ||
                       'offer_order_id, product_order_id, c.charge_item_ref, host_prov';

        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @iv_Sql_Update := 'update sync_bl_rule_MID a ' ||
            'set remark = ''X'' ' ||
            'where amount = (select max(amount) from (select * from sync_bl_rule_' || inMonth || ' ) b ' ||
             'where b.orgmonth = a.orgmonth ' ||
               'and b.poid = a.poid ' ||
               'and b.soid = a.soid ' ||
               'and b.feetype = a.feetype ' ||
               'and b.pospecnumber = ''1101010'' ' ||
               'and b.sospecnumber = ''9101002'' ' ||
             'group by orgmonth, ordermode, customernumber, pospecnumber, sospecnumber, poid, soid, feetype)';
        SELECT @iv_Sql_Update;
        PREPARE STMT FROM @iv_Sql_Update;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @iv_Sql_Update := 'update sync_bl_rule_MID a ' ||
        ' set remark = ''M'' ' ||
        ' where remark = ''X'' and  (orgmonth,ordermode,customernumber,pospecnumber,sospecnumber,poid,soid,feetype) in ' ||
        ' (select t.* from   (  select orgmonth, ordermode,customernumber,pospecnumber, sospecnumber, poid,soid, feetype ' ||
        ' from sync_bl_rule_' || inMonth || ' where remark = ''X'' and pospecnumber = ''1101010'' and sospecnumber = ''9101002'' ' ||
        ' group by orgmonth,ordermode, customernumber,pospecnumber,sospecnumber,poid, soid, feetype ) t )  limit 1 ';
        SELECT @iv_Sql_Update;
        PREPARE STMT FROM @iv_Sql_Update;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----热线彩印产品
        ----根据携号转网记录表，更新热线彩印话单的异网运营商代码，将异网数据更新为23厘每条
        SET @iv_Sql_Update := 'update ur_cpm_' || inMonth || '_t t1 ' ||
                           ' set t1.carrier_type2 = (select decode(t2.net_id, ''001'', ''3'', ''002'', ''1'', ''003'', ''2'') ' ||
                                                     ' from stl_mnp_record t2 ' ||
                                                    ' where t2.member_code = t1.mem_code ' ||
                                                      ' and t2.dupkey = t1.dup_key) ' ||
                         ' where exists (select 1 ' ||
                                         ' from stl_mnp_record t2 ' ||
                                        ' where t2.member_code = t1.mem_code ' ||
                                          ' and t2.dupkey = t1.dup_key)';
        SELECT @iv_Sql_Update;
        PREPARE STMT FROM @iv_Sql_Update;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @iv_Sql_Update := 'update ur_cpm_' || inMonth || '_t t1 ' ||
                          ' set t1.carrier_type2 = decode(t1.carrier_type, ''YD'', ''1'', ''LT'', ''2'', ''DX'', ''3'', t1.carrier_type) ' ||
                        ' where carrier_type2 is null';
        SELECT @iv_Sql_Update;
        PREPARE STMT FROM @iv_Sql_Update;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        ----将异网数据更新为30厘每条 万里优化把这里的计算放到下面的decode中
          


        ----汇总至话单汇总表，待结算
        SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_SETTLE_MID ' ||
                       'select max(a.ticket_id) stream_id, ' ||
                       '''BL'' datasource, ' ||
                       'a.prod_order_mode ordermode, ' ||
                       'a.acct_month orgmonth, ' ||
                       'a.ec_code customernumber, ' ||
                       'a.offer_code pospecnumber, ' ||
                       'a.product_code sospecnumber, ' ||
                       'a.offer_order_id poid, ' ||
                       'a.product_order_id soid, ' ||
                       'c.charge_item_ref feetype, ' ||
                       'NULL dn, ' ||
                       'round(sum(decode(a.carrier_type2, ''1'', 6, 30)) / 10, 0) amount, ' ||
                       '''1'' amount_type, ' ||
                       'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime, ' ||
                       '''0'' status, ' ||
                       'NULL errmesg, ' ||
                       'NULL prov_cd, ' ||
                       'null,  ' ||
                       'NULL remark ' ||
                  'from ur_cpm_' || inMonth || '_t a, stlusers.stl_charge_item_def c ' ||
                 'where lower(a.CHARGE_CODE) = lower(c.CHARGE_ITEM(+)) ' ||
                 'group by  a.prod_order_mode, a.acct_month, a.ec_code, a.offer_code, a.product_code, a.offer_order_id, ' ||
                           'a.product_order_id, c.charge_item_ref';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        --SIP音视频部分
        --step 1. truncate swap table
        SET @iv_Sql_Insert := 'truncate table sync_bl_rule_swap_02';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --step 2. 统计UR_VCP详单表
        SET @iv_Sql_Insert := 'INSERT INTO sync_bl_rule_swap_02 ' ||
             ' SELECT a.PROD_ORDER_MODE, ' ||
             '   a.ACCT_MONTH, ' ||
             '    a.EC_CODE, ' ||
             '    a.OFFER_CODE, ' ||
             '    a.PRODUCT_CODE, ' ||
             '    a.OFFER_ORDER_ID, ' ||
             '    a.PRODUCT_ORDER_ID, ' ||
             '    a.CHARGE_CODE, ' ||
             '    sum(a.charge) amount, ' ||
             '    a.call_prov ' ||
             ' FROM UR_VCP_' || inMonth || '_T a  where a.member_type = ''0'' and offer_code=''50016'' and product_code=''5001613'' ' ||
             ' GROUP BY prod_order_mode, ' ||
             '       acct_month, ' ||
             '       ec_code, ' ||
             '       offer_code, ' ||
             '       product_code, ' ||
             '       offer_order_id, ' ||
             '       product_order_id, ' ||
             '       charge_code, ' ||
             '       call_prov ';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        --step 3. 生成最终数据

        SET @iv_Sql_Insert :=  'INSERT INTO SYNC_BL_RULE_MID ' ||
               'SELECT ''BL'' datasource,  ' ||
                       't.prod_order_mode ordermode, ' ||
                       't.acct_month orgmonth,  ' ||
                       't.ec_code customernumber,  ' ||
                       't.offer_code pospecnumber,  ' ||
                       't.product_code sospecnumber,  ' ||
                       't.offer_order_id poid,  ' ||
                       't.product_order_id soid,  ' ||
                       'NULL dn,  ' ||
                       't.amount,  ' ||
                       '1 amount_type,  ' ||
                       'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime, ' ||
                       '''0'' status,  ' ||
                       'NULL errmesg, ' ||
                       't.prov_cd , ' ||
                       '2 remark, ' ||
                       'c.charge_item_ref feetype  ' ||
                  'FROM sync_bl_rule_swap_02 t, stlusers.STL_CHARGE_ITEM_DEF c  ' ||
                   'WHERE lower(t.CHARGE_CODE) = lower(c.CHARGE_ITEM(+))';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        --双跨融合通信-智能呼管部分
        --智能呼管 受理模式3
        --step 1. truncate swap table
        SET @iv_Sql_Insert := 'truncate table sync_bl_rule_swap_07';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --step 2. 统计UR_VCP详单表
        SET @iv_Sql_Insert := 'INSERT INTO sync_bl_rule_swap_07 ' ||
             ' SELECT a.PROD_ORDER_MODE, ' ||
             '   a.ACCT_MONTH, ' ||
             '    a.EC_CODE, ' ||
             '    a.OFFER_CODE, ' ||
             '    a.PRODUCT_CODE, ' ||
             '    a.OFFER_ORDER_ID, ' ||
             '    a.PRODUCT_ORDER_ID, ' ||
             '    a.CHARGE_CODE, ' ||
             '    sum(a.charge) amount, ' ||
             '    a.call_prov ' ||
             ' FROM UR_VCP_' || inMonth || '_T a  where offer_code=''0102001'' and product_code=''20009'' and prod_order_mode=''3'' ' ||
             ' GROUP BY prod_order_mode, ' ||
             '       acct_month, ' ||
             '       ec_code, ' ||
             '       offer_code, ' ||
             '       product_code, ' ||
             '       offer_order_id, ' ||
             '       product_order_id, ' ||
             '       charge_code, ' ||
             '       call_prov ';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        --step 3. 生成最终数据

        SET @iv_Sql_Insert :=  'INSERT INTO SYNC_BL_RULE_MID ' ||
                'SELECT  '||
                      '''BL'' datasource,  '||
                      't.prod_order_mode ordermode, '||
                      't.acct_month orgmonth,    '||
                      't.ec_code customernumber,  '||
                      't.offer_code pospecnumber,   '||
                      't.product_code sospecnumber,    '||
                      't.offer_order_id poid,        '||
                      't.product_order_id soid,   '||


                      'NULL dn,      '||
                      't.amount,    '||
                      '''1'' amount_type,  '||
                      'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime,   '||
                      '''0'' status, '||
                      'NULL errmesg,   '||
                      't.prov_cd prov_cd,   '||

                      '''2'' remark,      '||
                      'c.charge_item_ref feetype     '||
                  'FROM sync_bl_rule_swap_07 t, stlusers.STL_CHARGE_ITEM_DEF c  ' ||
                   'WHERE lower(t.CHARGE_CODE) = lower(c.CHARGE_ITEM(+))';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --智能路由部分
        --智能路由语音 受理模式3

        --step 0 临时表数据清理
        SET @iv_Sql_Insert := 'truncate table sync_bl_rule_swap_03' ;
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --step 1. 将统计数据写入临时表
        SET @iv_Sql_Insert := ' insert into sync_bl_rule_swap_03 ' ||
            ' SELECT MAX(a.TICKET_ID) ticket_id, a.ACCT_MONTH,a.EC_CODE,a.PROD_ORDER_MODE, a.OFFER_CODE, a.PRODUCT_CODE, a.OFFER_ORDER_ID,a.PRODUCT_ORDER_ID,a.dest_prov,a.charge_code,round(SUM(a.CHARGE3 / 10), 0) fee ' ||
            '          FROM UR_IR95_' || inMonth || '_T a ' ||
            '          WHERE a.product_code = ''5002101''  and a.prod_order_mode = ''3'' ' ||
            '          GROUP BY ACCT_MONTH, EC_CODE, PROD_ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID, dest_prov, charge_code' ;
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --step 2. 使用临时表生成最终数据
        SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_SETTLE_MID ' ||
               'SELECT t.stream_id, ' ||
                     '''BL'' datasource, ' ||
                     't.ordermode, ' ||
                     't.orgmonth, ' ||
                     't.customernumber, ' ||
                     't.pospecnumber, ' ||
                     't.sospecnumber, ' ||
                     't.poid, ' ||
                     't.soid, ' ||
                     'c.charge_item_ref feetype, ' ||
                     'NULL dn, ' ||
                     't.amount, ' ||
                     '''1'' amount_type, ' ||
                     'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime, ' ||
                     '''0'' status, ' ||
                     'NULL errmesg, ' ||
                     't.prov_cd, ' ||
                     'NULL accountid, ' ||
                     '''2'' remark ' ||
                'FROM sync_bl_rule_swap_03 t, stlusers.STL_CHARGE_ITEM_DEF c ' ||
              'WHERE lower(t.CHARGE_CODE) = lower(c.CHARGE_ITEM)';

        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --智能路由语音 受理模式1、智能路由外呼
        --step 0 临时表数据清理
        SET @iv_Sql_Insert := 'truncate table sync_bl_rule_swap_03' ;
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --step 1. 将统计数据写入临时表
        SET @iv_Sql_Insert := ' insert into sync_bl_rule_swap_03 ' ||
            ' SELECT MAX(a.TICKET_ID) ticket_id, a.ACCT_MONTH,a.EC_CODE,a.PROD_ORDER_MODE, a.OFFER_CODE, a.PRODUCT_CODE, a.OFFER_ORDER_ID,a.PRODUCT_ORDER_ID,a.dest_prov,a.charge_code,round(SUM(a.CHARGE1 / 10), 0) fee ' ||
            '          FROM UR_IR95_' || inMonth || '_T a ' ||
            '          WHERE a.product_code = ''5002101''  and a.prod_order_mode = ''1'' ' ||
            '          GROUP BY ACCT_MONTH, EC_CODE, PROD_ORDER_MODE, OFFER_CODE, PRODUCT_CODE, OFFER_ORDER_ID, PRODUCT_ORDER_ID, dest_prov, charge_code' ;
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        --step 2. 使用临时表生成最终数据
        SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_SETTLE_MID ' ||
               'SELECT t.stream_id, ' ||
                     '''BL'' datasource, ' ||
                     't.ordermode, ' ||
                     't.orgmonth, ' ||
                     't.customernumber, ' ||
                     't.pospecnumber, ' ||
                     't.sospecnumber, ' ||
                     't.poid, ' ||
                     't.soid, ' ||
                     'c.charge_item_ref feetype, ' ||
                     'NULL dn, ' ||
                     't.amount, ' ||
                     '''1'' amount_type, ' ||
                     'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime, ' ||
                     '''0'' status, ' ||
                     'NULL errmesg, ' ||
                     't.prov_cd, ' ||
                     'NULL accountid, ' ||
                     '''2'' remark ' ||
                'FROM sync_bl_rule_swap_03 t, stlusers.STL_CHARGE_ITEM_DEF c ' ||
              'WHERE lower(t.CHARGE_CODE) = lower(c.CHARGE_ITEM)';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        ----支付卫士
        --清空临时表和年套餐累计表，话单结果表更新字段清空
        SET @iv_Sql_Insert := 'truncate table zfws_rp_month_swap';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        SET @iv_Sql_Insert := 'truncate table zfws_rp_year_swap';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        delete from stl_zfws_usage where settle_month = inMonth;

        SET @iv_Sql_Insert := 'update ur_zfws_' || inMonth || '_t set rateplan_id = null, upd_charge_code = null';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        --获取当月未超套的月套餐话单写入中间表
        SET @iv_Sql_Insert := 'insert into zfws_rp_month_swap ' ||
          'select record_row_number, offer_order_id, product_order_id, rateplan_id, charge_code, dup_key, start_time, type mark ' ||
            'from (select row_number() over(partition by a.offer_order_id, a.product_order_id order by a.start_time) record_row_number, ' ||
                         'a.offer_order_id, a.product_order_id, b.rateplan_id, c.charge_code, a.dup_key, a.start_time, c.quantity, c.type ' ||
                    'from ur_zfws_' || inMonth || '_t a, stlusers.stl_order_rateplan b, stl_zfws_rateplan c ' ||
                   'where a.offer_order_id = b.prod_order_id ' ||
                     'and a.product_order_id = b.order_id ' ||
                     'and b.rateplan_id = c.rateplan_id ' ||
                     'and c.type = ''M'') t ' ||
           'where quantity >= record_row_number';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        --支付卫士话单结果表更新（月套餐）
        
        



        SET @iv_Sql_Update := 'update ur_zfws_' || inMonth || '_t a ' ||
                     'join (select dup_key, rateplan_id, charge_code from zfws_rp_month_swap) b ' ||
                        'on (a.dup_key = b.dup_key) ' ||

                    'set a.rateplan_id = b.rateplan_id, a.upd_charge_code = b.charge_code';
        SELECT @iv_Sql_Update;
        PREPARE STMT FROM @iv_Sql_Update;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        --更新年套餐起始用量
        ----新的年套餐周期
        SET @iv_Sql_Insert := 'insert into stl_zfws_usage ' ||
                'select ''' || inMonth || ''', a.prod_order_id, a.order_id, a.rateplan_id, b.quantity, null, null ' ||
                  'from stlusers.stl_order_rateplan a, stl_zfws_rateplan b ' ||
                 'where a.rateplan_id = b.rateplan_id ' ||
                   'and b.type = ''Y'' ' ||
                   'and substr(a.eff_month, 5, 2) = substr(''' || inMonth || ''', 5, 2)';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        ----没用完的年套餐周期
        SET @iv_Sql_Insert := 'insert into stl_zfws_usage ' ||
                'select ''' || inMonth || ''', offer_order_id, product_order_id, rateplan_id, end_point start_point, null, null ' ||
                  'from stl_zfws_usage ' ||
                 'where settle_month = to_char(add_months(to_date(''' || inMonth || ''', ''yyyymm''), -1), ''yyyymm'')';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        --获取当月的年套餐话单写入中间表
        SET @iv_Sql_Insert := 'insert into zfws_rp_year_swap ' ||
              'select record_row_number, offer_order_id, product_order_id, rateplan_id, charge_code, dup_key, start_time, type mark ' ||
                'from (select row_number() over(partition by a.offer_order_id, a.product_order_id order by a.start_time) record_row_number, ' ||
                             'a.offer_order_id, a.product_order_id, b.rateplan_id, c.charge_code, a.dup_key, a.start_time, b.start_point, c.type ' ||
                        'from ur_zfws_' || inMonth || '_t a, stl_zfws_usage b, stl_zfws_rateplan c ' ||
                       'where a.rateplan_id is null ' ||
                         'and a.offer_order_id = b.offer_order_id ' ||
                         'and b.settle_month = ''' || inMonth || ''''||
                         'and a.product_order_id = b.product_order_id ' ||
                         'and b.rateplan_id = c.rateplan_id) t ' ||
               'where start_point >= record_row_number';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        --支付卫士话单结果表更新（年套餐）

        --merge改为update join语句


        SET @iv_Sql_Update := 'update ur_zfws_' || inMonth || '_t a ' ||
                 'join (select dup_key, rateplan_id, charge_code from zfws_rp_year_swap) b ' ||
                    'on (a.dup_key = b.dup_key) ' ||

                'set a.rateplan_id = b.rateplan_id, a.upd_charge_code = b.charge_code ' ||
                 'where a.rateplan_id is null';
        SELECT @iv_Sql_Update;
        PREPARE STMT FROM @iv_Sql_Update;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        --更新年套餐
        SET @iv_Sql_Update := 'update stl_zfws_usage a ' ||
               'set (monthly_usage, end_point) = (select count(*), a.start_point - count(*) ' ||
                                      'from zfws_rp_year_swap b ' ||
                                     'where b.offer_order_id = a.offer_order_id ' ||
                                       'and b.product_order_id = a.product_order_id) ' ||
             'where settle_month = ''' || inMonth || '''';
        SELECT @iv_Sql_Update;
        PREPARE STMT FROM @iv_Sql_Update;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;




        --数据源省金额计算
          ----套内部分
        SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_SETTLE_MID ' ||
        'select stream_id, ' ||
               '''BL'' datasource, ' ||
               'order_mode, ' ||
               'org_month, ' ||
               'customernumber, ' ||
               'pospecnumber, ' ||
               'sospecnumber, ' ||
               'poid, ' ||
               'soid, ' ||
               'feetype, ' ||
               'NULL dn, ' ||
               't.n * (b.sale_price * 0.8 * 0.4 / b.quantity) amount, ' ||
               '''2'' amount_type, ' ||
               'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime, ' ||
               '''0'' status, ' ||
               'NULL errmesg, ' ||
               'prov_cd, ' ||
               'NULL accountid, ' ||
               '''2'' remark ' ||
          'from (select max(ticket_id) stream_id, ' ||
               'order_mode, ' ||
               'acct_month org_month, ' ||
               'ec_code customernumber, ' ||
               'offer_code pospecnumber, ' ||
               'product_code sospecnumber, ' ||
               'offer_order_id poid, ' ||
               'product_order_id soid, ' ||
               'substr(upd_charge_code, 2, 3) feetype, ' ||
               'datasource_prov prov_cd, ' ||
               'rateplan_id, ' ||
               'upd_charge_code, ' ||
               'count(*) n ' ||
          'from ur_zfws_' || inMonth || '_t ' ||
         'where rateplan_id is not null ' ||
         'group by order_mode, acct_month, ec_code, offer_code, product_code, offer_order_id, product_order_id, upd_charge_code, datasource_prov, ' ||
                  'rateplan_id, upd_charge_code) t, stl_zfws_rateplan b ' ||
         'where t.rateplan_id = b.rateplan_id';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        ----超套/次包部分
        SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_SETTLE_MID ' ||
              'select stream_id, ' ||
                     '''BL'' datasource, ' ||
                     'order_mode, ' ||
                     'org_month, ' ||
                     'customernumber, ' ||
                     'pospecnumber, ' ||
                     'sospecnumber, ' ||
                     'poid, ' ||
                     'soid, ' ||
                     'substr(feetype, 2, 3) feetype, ' ||
                     'NULL dn, ' ||
                     't.n * (b.sale_price * 0.4) amount, ' ||
                     '''2'' amount_type, ' ||
                     'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime, ' ||
                     '''0'' status, ' ||
                     'NULL errmesg, ' ||
                     'prov_cd, ' ||
                     'NULL accountid, ' ||
                     '''2'' remark ' ||
                'from (select max(ticket_id) stream_id, ' ||
                     'order_mode, ' ||
                     'acct_month org_month, ' ||
                     'ec_code customernumber, ' ||
                     'offer_code pospecnumber, ' ||
                     'product_code sospecnumber, ' ||
                     'offer_order_id poid, ' ||
                     'product_order_id soid, ' ||
                     'charge_code feetype, ' ||
                     'datasource_prov prov_cd, ' ||
                     'count(*) n ' ||
                'from ur_zfws_' || inMonth || '_t ' ||
               'where rateplan_id is null ' ||
               'group by order_mode, acct_month, ec_code, offer_code, product_code, offer_order_id, product_order_id, charge_code, datasource_prov) t, stl_zfws_chargecode b ' ||
               'where t.feetype = b.charge_code';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;


        --省专结算金额计算
        SET @iv_Sql_Insert := 'INSERT INTO SYNC_BL_SETTLE_MID ' ||
               'select max(ticket_id) stream_id, ' ||
               '''BL'' datasource, ' ||
               'order_mode, ' ||
               'acct_month org_month, ' ||
               'ec_code customernumber, ' ||
               'offer_code pospecnumber, ' ||
               'product_code sospecnumber, ' ||
               'offer_order_id poid, ' ||
               'product_order_id soid, ' ||
               '''5'' || substr(nvl(upd_charge_code, charge_code), 3, 2) feetype, ' ||
               'NULL dn, ' ||
               'sum(sz_unit) amount, ' ||
               '''4'' amount_type, ' ||
               'to_char(SYSDATE, ''yyyymmddhh24miss'') synctime, ' ||
               '''0'' status, ' ||
               'NULL errmesg, ' ||
               'datasource_prov prov_cd, ' ||
               'NULL accountid, ' ||
               '''2'' remark ' ||
          'from ur_zfws_' || inMonth || '_t ' ||
         'group by order_mode, acct_month, ec_code, offer_code, product_code, offer_order_id, product_order_id, upd_charge_code, charge_code, datasource_prov';
        SELECT @iv_Sql_Insert;
        PREPARE STMT FROM @iv_Sql_Insert;
        EXECUTE STMT;
        DEALLOCATE PREPARE STMT;

        commit;


        outSysError := 'OK';
        outReturn := 0;


        SELECT 'procedure ' || v_proc_name || ' completed successfully. outReturn=' || outReturn;
    END;
END;;