select prov_cd, outprov_name, prov_wl,
       nvl(-round(sum(A_fee + B_fee) / 1000, 2), 0) total_fee, -- 含税金额
       nvl(-round(round(sum(A_fee + B_fee) / 1000, 2) / (1 + taxrate), 2), 0) total_notaxfee, -- 不含税金额
       cast(nvl(-round(sum(A_fee + B_fee) / 1000, 2) + round(round(sum(A_fee + B_fee) / 1000, 2) / (1 + taxrate), 2), 0) as decimal (20,2)) total_taxfee, -- 税额
       cast(nvl(-round(sum(A_fee) / 1000, 2) + round(round(sum(A_fee) / 1000, 2) / (1 + taxrate), 2), 0) as decimal (20,2)) A_taxfee, -- 税额
       nvl(-round(round(sum(A_fee) / 1000, 2) / (1 + taxrate), 2), 0) A_notaxfee, -- 不含税金额
       cast(nvl(-round(sum(B_fee) / 1000, 2) + round(round(sum(B_fee) / 1000, 2) / (1 + taxrate), 2), 0) as decimal (20,2)) B_taxfee, -- 税额
       nvl(-round(round(sum(B_fee) / 1000, 2) / (1 + taxrate), 2), 0) B_notaxfee -- 不含税金额
  from
(select p.prov_cd, p.prov_nm outprov_name, '30' || p.prov_wl prov_wl, t.taxrate,
       nvl(decode(t.col_name, '音乐宽带', t.total_fee, 0), 0) A_fee,
       nvl(decode(t.col_name, '和对讲', t.total_fee, 0), 0) B_fee
  from
(select a.outprov_code, to_number(a.taxrate) taxrate, a.product_class, sum(a.settlement_amount) total_fee, b.col_name
  from rpt_p2c a, rvl_p2c_bus_config b
 where a.settlemonth = ? and a.taxrate * 100 = ? and (a.fee_flag = ? or ? = '0')
   and ? between b.start_month and b.end_month
   and a.inprov_code = 'ZQ'
   and b.rep_num = 'D335'
   and (b.type = 'OP' and a.offer_code = b.offer_code and a.product_code = b.product_code
    or b.type = 'O' and a.offer_code = b.offer_code
    or b.type = 'P' and a.product_code = b.product_code)
 group by a.outprov_code, a.taxrate, a.product_class, b.col_name
   ) t right join stl_province_cd p on p.prov_cd = t.outprov_code
 where p.prov_type = 0)
 group by prov_cd, outprov_name, prov_wl, taxrate
 order by prov_cd