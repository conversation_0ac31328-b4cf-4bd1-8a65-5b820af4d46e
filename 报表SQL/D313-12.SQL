 select 
 prov_cd, outprov_name, prov_wl,
 sum(A_taxfee+B_taxfee+C_taxfee+D_taxfee+E_taxfee+F_taxfee+G_taxfee+H_taxfee+I_taxfee+A_notaxfee+B_notaxfee+C_notaxfee+D_notaxfee+E_notaxfee+F_notaxfee+G_notaxfee+H_notaxfee+I_notaxfee) total_fee,
 sum(A_notaxfee+B_notaxfee+C_notaxfee+D_notaxfee+E_notaxfee+F_notaxfee+G_notaxfee+H_notaxfee+I_notaxfee) total_notaxfee,
 sum(A_taxfee+B_taxfee+C_taxfee+D_taxfee+E_taxfee+F_taxfee+G_taxfee+H_taxfee+I_taxfee) total_taxfee,
 A_taxfee,B_taxfee,C_taxfee,D_taxfee,E_taxfee,F_taxfee,G_taxfee,H_taxfee,I_taxfee,
A_notaxfee,B_notaxfe<PERSON>,C_notaxfee,D_notaxfee,E_notaxfee,F_notaxfee,G_notaxfee,H_notaxfee,I_notaxfee
 from (
 select prov_cd, outprov_name, prov_wl,taxrate,
       nvl(-round(sum(to_number(A_fee)) / 1000, 2) + round(round(sum(to_number(A_fee)) / 1000, 2) / (1 + taxrate), 2), 0) A_taxfee, -- 税额
       nvl(-round(round(sum(to_number(A_fee)) / 1000, 2) / (1 + taxrate), 2), 0) A_notaxfee, -- 不含税金额
       nvl(-round(sum(to_number(B_fee)) / 1000, 2) + round(round(sum(to_number(B_fee)) / 1000, 2) / (1 + taxrate), 2), 0) B_taxfee, -- 税额
       nvl(-round(round(sum(to_number(B_fee)) / 1000, 2) / (1 + taxrate), 2), 0) B_notaxfee, -- 不含税金额
       nvl(-round(sum(to_number(C_fee)) / 1000, 2) + round(round(sum(to_number(C_fee)) / 1000, 2) / (1 + taxrate), 2), 0) C_taxfee, -- 税额
       nvl(-round(round(sum(to_number(C_fee)) / 1000, 2) / (1 + taxrate), 2), 0) C_notaxfee, -- 不含税金额
       nvl(-round(sum(to_number(D_fee)) / 1000, 2) + round(round(sum(to_number(D_fee)) / 1000, 2) / (1 + taxrate), 2), 0) D_taxfee, -- 税额
       nvl(-round(round(sum(to_number(D_fee)) / 1000, 2) / (1 + taxrate), 2), 0) D_notaxfee, -- 不含税金额
       nvl(-round(sum(to_number(E_fee)) / 1000, 2) + round(round(sum(to_number(E_fee)) / 1000, 2) / (1 + taxrate), 2), 0) E_taxfee, -- 税额
       nvl(-round(round(sum(to_number(E_fee)) / 1000, 2) / (1 + taxrate), 2), 0) E_notaxfee, -- 不含税金额
       nvl(-round(sum(to_number(F_fee)) / 1000, 2) + round(round(sum(to_number(F_fee)) / 1000, 2) / (1 + taxrate), 2), 0) F_taxfee, -- 税额
       nvl(-round(round(sum(to_number(F_fee)) / 1000, 2) / (1 + taxrate), 2), 0) F_notaxfee, -- 不含税金额
       nvl(-round(sum(to_number(G_fee)) / 1000, 2) + round(round(sum(to_number(G_fee)) / 1000, 2) / (1 + taxrate), 2), 0) G_taxfee, -- 税额
       nvl(-round(round(sum(to_number(G_fee)) / 1000, 2) / (1 + taxrate), 2), 0) G_notaxfee, -- 不含税金额
       nvl(-round(sum(to_number(H_fee)) / 1000, 2) + round(round(sum(to_number(H_fee)) / 1000, 2) / (1 + taxrate), 2), 0) H_taxfee, -- 税额
       nvl(-round(round(sum(to_number(H_fee)) / 1000, 2) / (1 + taxrate), 2), 0) H_notaxfee, -- 不含税金额
       nvl(-round(sum(to_number(I_fee)) / 1000, 2) + round(round(sum(to_number(I_fee)) / 1000, 2) / (1 + taxrate), 2), 0) I_taxfee, -- 税额
       nvl(-round(round(sum(to_number(I_fee)) / 1000, 2) / (1 + taxrate), 2), 0) I_notaxfee -- 不含税金额
  from
(select p.prov_cd, p.prov_full_nm outprov_name, '30' || p.prov_wl prov_wl, t.taxrate,
       nvl(decode(t.col_name, '千里眼', t.total_fee, 0), 0) A_fee,
       nvl(decode(t.col_name, 'OneNET', t.total_fee, 0), 0) B_fee,
       nvl(decode(t.col_name, 'OnePark（智慧园区）', t.total_fee, 0), 0) C_fee,
       nvl(decode(t.col_name, 'M2M芯片产品', t.total_fee, 0), 0) D_fee,
       nvl(decode(t.col_name, '行车卫士', t.total_fee, 0), 0) E_fee,
       nvl(decode(t.col_name, '千里眼增值服务', t.total_fee, 0), 0) F_fee,
       nvl(decode(t.col_name, 'OneNET增值服务', t.total_fee, 0), 0) G_fee,
       nvl(decode(t.col_name, 'OnePark增值服务', t.total_fee, 0), 0) H_fee,
       nvl(decode(t.col_name, 'e企组网', t.total_fee, 0), 0) I_fee
  from
(select a.outprov_code, to_number(a.taxrate) taxrate, a.product_class, sum(a.settlement_amount) total_fee, b.col_name
  from rpt_p2c a, rvl_p2c_bus_config b
 where a.settlemonth = ? and a.taxrate * 100 = ? and (a.fee_flag = ? or ? = '0')
   and ? between b.start_month and b.end_month
   and a.inprov_code = 'ZYWLW'
   and b.rep_num = 'D313-12'
   and b.report_source = '1'
   and (b.type = 'OP' and a.offer_code = b.offer_code and a.product_code = b.product_code
    or b.type = 'O' and a.offer_code = b.offer_code
    or b.type = 'OPF' and a.offer_code = b.offer_code and a.product_code = b.product_code and a.charge_code = b.charge_item)
 group by a.outprov_code, a.taxrate, a.product_class, b.col_name
 union all
 select c.prov_cd, c.taxrate / 100, '', c.report_fee total_fee, c.col_name
   from rpt_p2c_limited c
  where c.settlemonth = ? and c.taxrate = ? and (c.fee_flag = ? or ? = '0')
   and c.rep_num = 'D313-12'
   ) t right join stl_province_cd p on p.prov_cd = t.outprov_code
 where p.prov_type = 0)
 group by prov_cd, outprov_name, prov_wl, taxrate
 )group by prov_cd, outprov_name, prov_wl, taxrate
 order by prov_cd