-- Step 1: 公共聚合部分
with aggregated_data as (
    select
        a.outprov_code,
        a.taxrate,
        sum(a.settlement_amount) as total_fee
    from
        rpt_p2c_nmg a
    where
        a.settlemonth = ?
         and a.taxrate * 100 = ? 
        and (a.fee_flag =? or ? = '0')
        and a.settle_out_type = '2'
    group by
        a.outprov_code,
        a.taxrate
),
-- Step 2: 拼接省份表和字典表的数据
base_data as (
    select
        p.prov_full_nm,
        '30'|| p.prov_wl as prov_wl,
        nvl(to_number(t.taxrate), ?/100) as taxrate,
        nvl(round(t.total_fee / 1000, 2), 0) as fee
    from  stl_province_cd p
    left join aggregated_data t  on '30'||p.prov_wl  = t.outprov_code
    where p.prov_type = 0
    union all
	select
        n.DICTDESC as prov_full_nm,
        n.DICTVALUE as prov_wl,
        nvl(TO_NUMBER(t.taxrate),?/100) taxrate,
        nvl(round(t.total_fee / 1000, 2), 0) as fee
    from stl_conf_dict n
    left join aggregated_data t on n.DICTVALUE = t.outprov_code
    where n.ITEM ='PROFESSIONAL_COMPANY' 
),
-- Step 3: 汇总计算每个省的金额 + 所有省和专业公司总金额
prov_out_data as (
    select
        prov_full_nm,
        prov_wl,
        taxrate,
        round(sum(fee), 2) as total_fee,
        round(sum(fee) / (1 + taxrate), 2) as total_notaxfee
    from base_data
    group by prov_full_nm, prov_wl, taxrate
),
-- 步骤4：计算所有省份应结出的不含税总额（作为内蒙古的应结入）
nmg_fee as (
    select
        round(sum(total_fee), 2) as all_out_withtax,-- 含税金额
        round(sum(total_notaxfee), 2) as all_out_notaxfee  
    from prov_out_data
)
-- Step 4: 计算结出/结入/税额
-- 步骤5：最终结果合并
select
    p.prov_full_nm as PROV_NM,
    p.prov_wl,
    -- 应结出（非内蒙古304210）
    p.total_fee as out_total_fee,
    p.total_notaxfee as out_total_notaxfee,
    round(p.total_fee - p.total_notaxfee, 2) as out_taxfee,
    
    -- 应结入（304210）
    case when p.prov_wl = '304210' then cast(t.all_out_withtax as decimal(20, 2)) else 0 end as in_total_fee,
    case when p.prov_wl = '304210' then cast(t.all_out_notaxfee as decimal(20, 2)) else 0 end as in_total_notaxfee,
    case when p.prov_wl = '304210' then cast(round(t.all_out_withtax - t.all_out_notaxfee, 2)as decimal(20, 2)) else 0 end as in_taxfee,
    
    -- 结算额（304210收，其它省支出）
    case 
        when p.prov_wl != '304210' then -p.total_fee
        else t.all_out_withtax
    end as settle_total_fee
from prov_out_data p
cross join nmg_fee t