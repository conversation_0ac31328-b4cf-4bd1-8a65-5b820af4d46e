select t2.prov_wl, t2.prov_full_name outprov_name,
       round(sum(total_fee / 1000), 2) total_fee, 
    round(round(sum(total_fee / 1000), 2) / (1 + taxrate), 2) total_notaxfee, 
    round(sum(total_fee / 1000), 2) - round(round(sum(total_fee / 1000), 2) / (1 + taxrate), 2) total_taxfee from
 (select prov_wl, 0.06 taxrate, decode(?, '6', decode(?, '2', 0, sum(a_fee + c_fee)), 0) total_fee
    from stl_pvs_z002
   where acct_month = ?
  group by prov_wl) t1 right join stl_wl_cd t2
    on t1.prov_wl = t2.prov_wl
 where t2.prov_wl <> '391110'
 group by prov_wl, taxrate
 order by t2.order_rule1;