select t2.prov_cd, '30' || t2.prov_wl prov_wl, t2.prov_full_nm outprov_name,
       round(sum(total_fee / 1000), 2) total_fee, 
			 round(round(sum(total_fee / 1000), 2) / (1 + taxrate), 2) total_notaxfee, 
			 round(sum(total_fee / 1000), 2) - round(round(sum(total_fee / 1000), 2) / (1 + taxrate), 2) total_taxfee from
 (select prov_cd, taxrate / 100 taxrate, sum(report_fee) total_fee
   from rpt_p2c_limited
  where rep_num = 'D313-5'
    and settlemonth = ? and taxrate = ? and (decode(fee_flag, '3', '2', fee_flag) = ? or ? = '0')
	group by prov_cd, taxrate
	union all
	select prov_cd, 0.06 taxrate, decode(?, '6', decode(?, '2', 0, sum(a_fee + c_fee)), 0) total_fee
    from stl_pvs
   where acct_month = ? and (joint_flag = '1' or inner_customer_flag = '1' or ydn_flag = '1')
  group by prov_cd) t1 right join stludr.stl_province_cd t2
	   on t1.prov_cd = t2.prov_cd
 where t2.prov_type = '0'
 group by prov_cd, taxrate
 order by prov_cd, taxrate