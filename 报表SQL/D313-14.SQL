select prov_cd, outprov_name, prov_wl,
       nvl(-round(sum(A_fee + B_fee + C_fee), 2) / 1, 0) total_fee, -- 含税金额
       nvl(-round(sum(A_fee + B_fee + C_fee) / (1 + taxrate), 2), 0) total_notaxfee, -- 不含税金额
       nvl(-round(sum(A_fee + B_fee + C_fee), 2) + round(sum(A_fee + B_fee + C_fee) / (1 + taxrate), 2), 0) total_taxfee, -- 税额
       nvl(-round(sum(A_fee), 2) + round(sum(A_fee) / (1 + taxrate), 2), 0) A_fee, -- 税额
       nvl(-round(sum(A_fee) / (1 + taxrate), 2), 0) A_notaxfee, -- 不含税金额
       nvl(-round(sum(B_fee), 2) + round(sum(B_fee) / (1 + taxrate), 2), 0) B_fee, -- 税额
       nvl(-round(sum(B_fee) / (1 + taxrate), 2), 0) B_notaxfee, -- 不含税金额
       nvl(-round(sum(C_fee), 2) + round(sum(C_fee) / (1 + taxrate), 2), 0) C_fee, -- 税额
       nvl(-round(sum(C_fee) / (1 + taxrate), 2), 0) C_notaxfee -- 不含税金额
  from
(select p.prov_cd, p.prov_nm outprov_name, '30' || p.prov_wl prov_wl, TO_NUMBER(t.taxrate) taxrate,
       nvl(round(decode(t.col_name, '数智协同产品族（5G随e签、你说我记、AI扫描王）', t.total_fee, 0) / 1000, 2),0) A_fee,
       nvl(round(decode(t.col_name, '数智协同产品族（中移链、磐匠数字员工）', t.total_fee, 0) / 1000, 2),0) B_fee,
       nvl(round(decode(t.col_name, '磐基Stack族（磐基PaaS平台、磐舟DevSecOps平台）', t.total_fee, 0) / 1000, 2),0) C_fee
  from
(select a.outprov_code, a.taxrate, a.product_class, to_number(sum(a.settlement_amount)) total_fee, b.col_name
  from rpt_p2c a, rvl_p2c_bus_config b
 where a.settlemonth = ? and a.taxrate * 100 = ? and (a.fee_flag = ? or ? = '0')
   and ? between b.start_month and b.end_month
   and a.inprov_code = 'XXJSZX'
   and b.rep_num = 'D313-14'   AND b.REPORT_SOURCE ='1'
   and (b.type = 'OP' and a.offer_code = b.offer_code and a.product_code = b.product_code
    or b.type = 'O' and a.offer_code = b.offer_code and b.remark is null
    or b.type = 'O' and a.offer_code = b.offer_code and b.remark = '3' and a.settlement_class = b.remark
    or b.type = 'OPF' and a.offer_code = b.offer_code and a.product_code = b.product_code and a.charge_code = b.charge_item)
 group by a.outprov_code, a.taxrate, a.product_class, b.col_name) t right join stl_province_cd p on p.prov_cd = t.outprov_code
 where p.prov_type = 0)
 group by prov_cd, outprov_name, prov_wl, taxrate
 order by prov_cd