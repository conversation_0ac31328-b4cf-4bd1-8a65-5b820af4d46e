select p.prov_nm outprov_name, '30' || p.prov_wl prov_wl,
       nvl(-round(sum(PAAS_fee + IAAS_fee + SAAS_fee), 2), 0) total_fee,  -- 含税金额
       nvl(-round(sum(PAAS_fee + IAAS_fee + SAAS_fee) / (1 + taxrate), 2), 0) total_notaxfee, -- 不含税金额
       cast(nvl(-round(sum(PAAS_fee + IAAS_fee + SAAS_fee), 2) + round(sum(PAAS_fee + IAAS_fee + SAAS_fee) / (1 + taxrate), 2), 0)as decimal (20,2)) total_taxfee, -- 税额
       cast(nvl(-round(sum(PAAS_fee) , 2) + round(sum(PAAS_fee) / (1 + taxrate), 2), 0)as decimal (20,2)) PAAS_fee, -- 税额
       nvl(-round(sum(PAAS_fee) / (1 + taxrate), 2), 0) PAAS_notaxfee, -- 不含税金额
       cast(nvl(-round(sum(IAAS_fee) , 2) + round(sum(IAAS_fee) / (1 + taxrate), 2), 0)as decimal (20,2)) IAAS_fee, -- 税额
       nvl(-round(sum(IAAS_fee) / (1 + taxrate), 2), 0) IAAS_notaxfee, -- 不含税金额
       cast(nvl(-round(sum(SAAS_fee) , 2) + round(sum(SAAS_fee) / (1 + taxrate), 2), 0)as decimal (20,2)) SAAS_fee, -- 税额
       nvl(-round(sum(SAAS_fee) / (1 + taxrate), 2), 0) SAAS_notaxfee -- 不含税金额
from
    (select outprov_code, outprov_name,  prov_wl, to_number(taxrate) taxrate,
            nvl(round(sum(decode(product_class, '1', settlement_amount, 0)) / 1000 , 2),0) SAAS_fee,
            nvl(round(sum(decode(product_class, '2', settlement_amount, 0)) / 1000 , 2),0) PAAS_fee,
            nvl(round(sum(decode(product_class, '3', settlement_amount, 0)) / 1000 , 2),0) IAAS_fee
     from rpt_p2c
     where settlement_class = 1 and outprov_code <> '000' and offer_code in ('1010402','BC2C')
       and settlemonth = ? and taxrate * 100 = ? and (decode(fee_flag, '3', '2', fee_flag) = ? or ? = '0')
     group by outprov_code, outprov_name, prov_wl, taxrate
    ) t right join stl_province_cd p on p.prov_cd = t.outprov_code
where p.prov_type = 0
group by p.prov_cd, p.prov_nm, p.prov_wl,taxrate
order by p.prov_cd