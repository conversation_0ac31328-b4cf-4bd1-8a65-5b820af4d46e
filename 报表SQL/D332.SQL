select prov_cd, outprov_name, prov_wl,
       nvl(-round(sum((SAAS_fee + PAAS_fee + IAAS_fee)) / 1000, 2), 0) total_fee,
       nvl(-round(round(sum((SAAS_fee + PAAS_fee + IAAS_fee)) / 1000, 2) / (1 + taxrate), 2), 0) total_notaxfee,
       cast(nvl(-round(sum((SAAS_fee + PAAS_fee + IAAS_fee)) / 1000, 2) + round(round(sum((SAAS_fee + PAAS_fee + IAAS_fee)) / 1000, 2) / (1 + taxrate), 2), 0) as decimal(20,2)) total_taxfee,
       cast(nvl(-round(sum(IAAS_fee_a) / 1000, 2) + round(round(sum(IAAS_fee_a) / 1000, 2) / (1 + taxrate), 2), 0)as decimal(20,2)) A_taxfee, -- 税额
       nvl(-round(round(sum(IAAS_fee_a) / 1000, 2) / (1 + taxrate), 2), 0) A_notaxfee, -- 不含税金额
       cast(nvl(-round(sum(IAAS_fee_b) / 1000, 2) + round(round(sum(IAAS_fee_b) / 1000, 2) / (1 + taxrate), 2), 0)as decimal(20,2)) B_taxfee, -- 税额
       nvl(-round(round(sum(IAAS_fee_b) / 1000, 2) / (1 + taxrate), 2), 0) B_notaxfee, -- 不含税金额
       cast(nvl(-round(sum((IAAS_fee - IAAS_fee_a - IAAS_fee_b)) / 1000, 2) + round(round(sum((IAAS_fee - IAAS_fee_a - IAAS_fee_b)) / 1000, 2) / (1 + taxrate), 2), 0)as decimal(20,2)) C_taxfee, -- 税额
       nvl(-round(round(sum((IAAS_fee - IAAS_fee_a - IAAS_fee_b)) / 1000, 2) / (1 + taxrate), 2), 0) C_notaxfee, -- 不含税金额
       cast(nvl(-round(sum(PAAS_fee) / 1000, 2) + round(round(sum(PAAS_fee) / 1000, 2) / (1 + taxrate), 2), 0)as decimal(20,2)) D_taxfee, -- 税额
       nvl(-round(round(sum(PAAS_fee) / 1000, 2) / (1 + taxrate), 2), 0) D_notaxfee, -- 不含税金额
       cast(nvl(-round(sum(SAAS_fee) / 1000, 2) + round(round(sum(SAAS_fee) / 1000, 2) / (1 + taxrate), 2), 0)as decimal(20,2)) E_taxfee, -- 税额
       nvl(-round(round(sum(SAAS_fee) / 1000, 2) / (1 + taxrate), 2), 0) E_notaxfee -- 不含税金额
 from
(select p.prov_cd, p.prov_nm outprov_name, '30' || p.prov_wl prov_wl, t.taxrate,
   SAAS_fee,
   PAAS_fee,
   IAAS_fee,
   IAAS_fee_a,
   IAAS_fee_b
from
(select outprov_code, prov_wl, to_number(taxrate) taxrate,
        sum(decode(product_class, '1', settlement_amount, 0)) SAAS_fee,
        sum(decode(product_class, '2', settlement_amount, 0)) PAAS_fee,
        sum(decode(product_class, '3', settlement_amount, 0)) IAAS_fee,
        sum(decode(product_class, '3', decode(gh_code, '130202', settlement_amount, 0), 0)) IAAS_fee_a, -- 存储
        sum(decode(product_class, '3', decode(gh_code, '130201', settlement_amount, 0), 0)) IAAS_fee_b  -- 计算
  from rpt_p2c
 where settlement_class = 2 and outprov_code <> '000'  and offer_code = '1010402'
   and settlemonth = ? and taxrate * 100 = ? and (decode(fee_flag, '3', '2', fee_flag) = ? or ? = '0')
 group by outprov_code, prov_wl, taxrate, gh_code) t right join stl_province_cd p on p.prov_cd = t.outprov_code
 where p.prov_type = 0)
    group by prov_cd, outprov_name, prov_wl, taxrate
    order by prov_cd