select p.prov_cd, p.prov_nm outprov_name, '30' || p.prov_wl prov_wl,
       nvl(-round(sum(total_fee) / 1000, 2), 0) total_fee, -- 含税金额
       nvl(-round(round(sum(total_fee) / 1000, 2) / (1 + taxrate), 2), 0) total_notaxfee, -- 不含税金额
       cast(nvl(-round(sum(total_fee) / 1000, 2) + round(round(sum(total_fee) / 1000, 2) / (1 + taxrate), 2), 0)as decimal (20,2)) total_taxfee, -- 税额
       cast(nvl(-round(sum(total_fee) / 1000, 2) + round(round(sum(total_fee) / 1000, 2) / (1 + taxrate), 2), 0)as decimal (20,2)) A_taxfee, -- 税额
       nvl(-round(round(sum(total_fee) / 1000, 2) / (1 + taxrate), 2), 0) A_notaxfee -- 不含税金额 
from (
select a.outprov_code, to_number(a.taxrate) taxrate, b.col_name, sum(a.settlement_amount) total_fee
  from rpt_p2c a, rvl_p2c_bus_config b
 where a.settlemonth = ? AND taxrate * 100 = ? and (fee_flag = ? or ? = '0')
   and ? between b.start_month and b.end_month
   and a.inprov_code = 'HLW' and a.settlement_class = '2'
   and b.rep_num = 'D335-5'
   and (b.type = 'OP' and a.offer_code = b.offer_code and a.product_code = b.product_code
    or b.type = 'O' and a.offer_code = b.offer_code
    or b.type = 'P' and a.product_code = b.product_code
    or b.type = 'OPF' and a.offer_code = b.offer_code and a.product_code = b.product_code and a.charge_code =b.charge_item )
 group by a.outprov_code, a.taxrate, b.col_name) t right join stl_province_cd p on p.prov_cd = t.outprov_code
 where p.prov_type = 0
 group by p.prov_cd, p.prov_nm, p.prov_wl, t.taxrate
 order by prov_cd