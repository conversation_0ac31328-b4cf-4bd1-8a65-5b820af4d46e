select prov_cd, outprov_name, prov_wl,
       nvl(-round(sum(A_fee + B_fee + C_fee + D_fee + E_fee), 2), 0) total_fee, -- 含税金额
       nvl(-round(sum(A_fee + B_fee + C_fee + D_fee + E_fee) / (1 + taxrate), 2), 0) total_notaxfee, -- 不含税金额
       cast(nvl(-round(sum(A_fee + B_fee + C_fee + D_fee + E_fee), 2) + round(sum(A_fee + B_fee + C_fee + D_fee + E_fee) / (1 + taxrate), 2), 0)as decimal (20,2)) total_taxfee, -- 税额
       cast(nvl(-round(sum(A_fee), 2) + round(sum(A_fee) / (1 + taxrate), 2), 0)as decimal (20,2)) A_taxfee, -- 税额
       nvl(-round(sum(A_fee) / (1 + taxrate), 2), 0) A_notaxfee, -- 不含税金额
       cast(nvl(-round(sum(B_fee), 2) + round(sum(B_fee) / (1 + taxrate), 2), 0)as decimal (20,2)) B_taxfee, -- 税额
       nvl(-round(sum(B_fee) / (1 + taxrate), 2), 0) B_notaxfee, -- 不含税金额
       cast(nvl(-round(sum(C_fee), 2) + round(sum(C_fee) / (1 + taxrate), 2), 0)as decimal (20,2)) C_taxfee, -- 税额
       nvl(-round(sum(C_fee) / (1 + taxrate), 2), 0) C_notaxfee, -- 不含税金额
       cast(nvl(-round(sum(D_fee), 2) + round(sum(D_fee) / (1 + taxrate), 2), 0)as decimal (20,2)) D_taxfee, -- 税额
       nvl(-round(sum(D_fee) / (1 + taxrate), 2), 0) D_notaxfee, -- 不含税金额
       cast(nvl(-round(sum(E_fee), 2) + round(sum(E_fee) / (1 + taxrate), 2), 0)as decimal (20,2)) E_taxfee, -- 税额
       nvl(-round(sum(E_fee) / (1 + taxrate), 2), 0) E_notaxfee -- 不含税金额
  from
(select p.prov_cd, p.prov_nm outprov_name, '30' || p.prov_wl prov_wl, t.taxrate,
       nvl(round(decode(t.col_name, 'OnePower工业互联网', t.total_fee, 0) / 1000, 2),0) A_fee,
       nvl(round(decode(t.col_name, '梧桐大数据-风控（支付位士）', t.total_fee, 0) / 1000, 2),0) B_fee,
       nvl(round(decode(t.col_name, 'OnePower工业互联网（增值服务）', t.total_fee, 0) / 1000, 2),0) C_fee,
       nvl(round(decode(t.col_name, '中移车队', t.total_fee, 0) / 1000, 2),0) D_fee,
       nvl(round(decode(t.col_name, 'OneTraffic智慧交通增值服务', t.total_fee, 0) / 1000, 2),0) E_fee
  from
(select a.outprov_code, to_number(a.taxrate) taxrate, a.product_class, sum(a.settlement_amount) total_fee, b.col_name
  from rpt_p2c a, rvl_p2c_bus_config b
 where a.settlemonth = ? and a.taxrate * 100 = ? and (a.fee_flag = ? or ? = '0')
   and ? between b.start_month and b.end_month
   and a.inprov_code = 'SY'
   and b.rep_num = 'D313-3'
   and b.report_source = '1'
   and (b.type = 'OP' and a.offer_code = b.offer_code and a.product_code = b.product_code
    or b.type = 'O' and a.offer_code = b.offer_code
    or b.type = 'OPF' and a.offer_code = b.offer_code and a.product_code = b.product_code and a.charge_code = b.charge_item)
 group by a.outprov_code, a.taxrate, a.product_class, b.col_name
 union all
 select c.prov_cd, c.taxrate / 100, '', c.report_fee total_fee, c.col_name
   from rpt_p2c_limited c
  where c.settlemonth = ? and c.taxrate = ? and (c.fee_flag = ? or ? = '0')
   and c.rep_num = 'D313-3'
   ) t right join stl_province_cd p on p.prov_cd = t.outprov_code
 where p.prov_type = 0)
 group by prov_cd, outprov_name, prov_wl, taxrate
 order by prov_cd