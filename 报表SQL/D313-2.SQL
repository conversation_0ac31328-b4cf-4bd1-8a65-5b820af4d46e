select prov_cd, outprov_name, prov_wl,
       nvl(-round(sum(A_fee + B_fee + C_fee  + E_fee  /*F_fee*/  + H_fee + V_fee + HS_fee + /*HXY_fee*/ + HBB_fee + EDU_fee + I_fee), 2) / 1, 0) total_fee, -- 含税金额
       nvl(-round(sum(A_fee + B_fee + C_fee  + E_fee  /*F_fee*/  + H_fee + V_fee + HS_fee + /*HXY_fee*/+ HBB_fee + EDU_fee+ I_fee) / (1 + taxrate), 2), 0) total_notaxfee, -- 不含税金额
       nvl(-round(sum(A_fee + B_fee + C_fee  + E_fee  /*F_fee*/ + H_fee + V_fee + HS_fee + /*HXY_fee*/ + HBB_fee + EDU_fee+ I_fee), 2) + round(sum(A_fee + B_fee + C_fee  + E_fee  /*F_fee*/  + H_fee + V_fee + HS_fee + /*HXY_fee*/ + HBB_fee + EDU_fee+ I_fee) / (1 + taxrate), 2), 0) total_taxfee, -- 税额
       nvl(-round(sum(A_fee), 2) + round(sum(A_fee) / (1 + taxrate), 2), 0) A_fee, -- 税额
       nvl(-round(sum(A_fee) / (1 + taxrate), 2), 0) A_notaxfee, -- 不含税金额
       nvl(-round(sum(B_fee), 2) + round(sum(B_fee) / (1 + taxrate), 2), 0) B_fee, -- 税额
       nvl(-round(sum(B_fee) / (1 + taxrate), 2), 0) B_notaxfee, -- 不含税金额
       nvl(-round(sum(C_fee), 2) + round(sum(C_fee) / (1 + taxrate), 2), 0) C_fee, -- 税额
       nvl(-round(sum(C_fee) / (1 + taxrate), 2), 0) C_notaxfee, -- 不含税金额
       nvl(-round(sum(E_fee), 2) + round(sum(E_fee) / (1 + taxrate), 2), 0) E_fee, -- 税额
       nvl(-round(sum(E_fee) / (1 + taxrate), 2), 0) E_notaxfee, -- 不含税金额
       --nvl(-round(sum(F_fee), 2) + round(sum(F_fee) / (1 + taxrate), 2), 0) F_fee, -- 税额
       --nvl(-round(sum(F_fee) / (1 + taxrate), 2), 0) F_notaxfee, -- 不含税金额
       nvl(-round(sum(H_fee), 2) + round(sum(H_fee) / (1 + taxrate), 2), 0) H_fee, -- 税额
       nvl(-round(sum(H_fee) / (1 + taxrate), 2), 0) H_notaxfee,  -- 不含税金额
       nvl(-round(sum(V_fee), 2) + round(sum(V_fee) / (1 + taxrate), 2), 0) V_fee, -- 税额
       nvl(-round(sum(V_fee) / (1 + taxrate), 2), 0) V_notaxfee,  -- 不含税金额
       nvl(-round(sum(HS_fee), 2) + round(sum(HS_fee) / (1 + taxrate), 2), 0) HS_fee, -- 税额
       nvl(-round(sum(HS_fee) / (1 + taxrate), 2), 0) HS_notaxfee, -- 不含税金额
      -- nvl(-round(sum(HXY_fee), 2) + round(sum(HXY_fee) / (1 + taxrate), 2), 0) HXY_fee, -- 税额
      -- nvl(-round(sum(HXY_fee) / (1 + taxrate), 2), 0) HXY_notaxfee, -- 不含税金额
       nvl(-round(sum(HBB_fee), 2) + round(sum(HBB_fee) / (1 + taxrate), 2), 0) HBB_fee, -- 税额
       nvl(-round(sum(HBB_fee) / (1 + taxrate), 2), 0) HBB_notaxfee, -- 不含税金额
       nvl(-round(sum(EDU_fee), 2) + round(sum(EDU_fee) / (1 + taxrate), 2), 0) EDU_fee, -- 税额
       nvl(-round(sum(EDU_fee) / (1 + taxrate), 2), 0) EDU_notaxfee, -- 不含税金额
       nvl(-round(sum(I_fee), 2) + round(sum(I_fee) / (1 + taxrate), 2), 0) I_fee, -- 税额
       nvl(-round(sum(I_fee) / (1 + taxrate), 2), 0) I_notaxfee -- 不含税金额
  from
(select p.prov_cd, p.prov_nm outprov_name, '30' || p.prov_wl prov_wl, t.taxrate,
       nvl(round(decode(t.col_name, '智慧校园云平台', t.total_fee, 0) / 1000, 2),0) A_fee,
       nvl(round(decode(t.col_name, 'OneTrip智慧文旅', t.total_fee, 0) / 1000, 2),0) B_fee,
       nvl(round(decode(t.col_name, 'OneSKY（中移凌云）', t.total_fee, 0) / 1000, 2),0) C_fee,
       nvl(round(decode(t.col_name, 'OneTrip智慧文旅增值服务', t.total_fee, 0) / 1000, 2),0) E_fee,
       --nvl(round(decode(t.col_name, '和教育', t.total_fee, 0) / 1000, 2),0) F_fee,
       nvl(round(decode(t.col_name, 'OneHealth 智慧医疗增值服务', t.total_fee, 0) / 1000, 2),0) H_fee,
       nvl(round(decode(t.col_name, 'OneVillage(乡村振兴平台)增值服务', t.total_fee, 0) / 1000, 2),0) V_fee,
       nvl(round(decode(t.col_name, '和校园（赛马池）', t.total_fee, 0) / 1000, 2),0) HS_fee,
      -- nvl(round(decode(t.col_name, '和校园', t.total_fee, 0) / 1000, 2),0) HXY_fee,
       nvl(round(decode(t.col_name, '和宝贝', t.total_fee, 0) / 1000, 2),0) HBB_fee,
       nvl(round(decode(t.col_name, 'OneEDU', t.total_fee, 0) / 1000, 2),0) EDU_fee,
       nvl(round(decode(t.col_name, '0neHealth智慧医疗-标准产品', t.total_fee, 0) / 1000, 2),0) I_fee
  from
(
select a.outprov_code,  to_number(a.taxrate) taxrate, a.product_class, sum(a.settlement_amount) total_fee, '智慧校园云平台' col_name
      from rpt_p2c a
     where a.settlemonth = ?  and a.taxrate * 100 = ? and (a.fee_flag = ? or ? = '0')
       and a.inprov_code = 'CY'
       and a.product_code ='5004927'
     group by a.outprov_code, a.taxrate, a.product_class, '智慧校园云平台增值服务'
 UNION ALL
 -- OneTrip智慧文旅增值服务
 select a.outprov_code,  to_number(a.taxrate) taxrate, a.product_class, sum(a.settlement_amount) total_fee, 'OneTrip智慧文旅增值服务' col_name
      from rpt_p2c a
     where a.settlemonth = ?  and a.taxrate * 100 = ? and (a.fee_flag = ? or ? = '0')
       and a.inprov_code = 'CY'
       and a.offer_code = '50100'
     group by a.outprov_code, a.taxrate, a.product_class, 'OneTrip智慧文旅增值服务'
 UNION ALL
 -- OneSKY（中移凌云）增值服务
 select a.outprov_code,  to_number(a.taxrate) taxrate, a.product_class, sum(a.settlement_amount) total_fee, 'OneSKY（中移凌云）' col_name
      from rpt_p2c a
     where a.settlemonth = ?  and a.taxrate * 100 = ? and (a.fee_flag = ? or ? = '0')
       and a.inprov_code = 'CY' 
       and a.offer_code ='50087' and a.product_code = '2022999400021419'
     group by a.outprov_code, a.taxrate, a.product_class, 'OneSKY（中移凌云）增值服务'
 UNION ALL
 -- OneHealth 智慧医疗增值服务
 select a.outprov_code,  to_number(a.taxrate) taxrate, a.product_class, sum(a.settlement_amount) total_fee, 'OneHealth 智慧医疗增值服务' col_name
      from rpt_p2c a
     where a.settlemonth = ?  and a.taxrate * 100 = ? and (a.fee_flag = ? or ? = '0')
       and a.inprov_code = 'CY'
       and ((a.offer_code ='50074' and a.product_code ='5007401' and a.CHARGE_CODE not in('3829','3830','3831','3832','7829','7830','7831','7832'))
       	or (a.offer_code ='50104' and a.product_code ='2022999400054889' and a.CHARGE_CODE in ('3120','3121','7120','7121')))
     group by a.outprov_code, a.taxrate, a.product_class, 'OneHealth 智慧医疗增值服务'
 --UNION ALL
 -- 和教育
 --select a.outprov_code,  to_number(a.taxrate) taxrate, a.product_class, sum(a.settlement_amount) total_fee, '和教育' col_name
--from rpt_p2c a
  --   where a.settlemonth = ?  and a.taxrate * 100 = ? and (a.fee_flag = ? or ? = '0')
    --   and a.inprov_code = 'CY'
       -- 排除5004927智慧校园
      -- and a.offer_code = '50049' AND a.product_code !='5004927'
     --group by a.outprov_code, a.taxrate, a.product_class, '和教育'
 UNION ALL
 -- OneVillage(乡村振兴平台)增值服务
 select a.outprov_code,  to_number(a.taxrate) taxrate, a.product_class, sum(a.settlement_amount) total_fee, 'OneVillage(乡村振兴平台)增值服务' col_name
      from rpt_p2c a
     where a.settlemonth = ?  and a.taxrate * 100 = ? and (a.fee_flag = ? or ? = '0')
       and a.inprov_code = 'CY'
       -- 排除5004927智慧校园
       -- 2023//12/14上线 新增2023999400084752
       and a.offer_code = '50106' AND a.product_code in('2022999400055820','2022999400055822','2023999400084752')
     group by a.outprov_code, a.taxrate, a.product_class, 'OneVillage(乡村振兴平台)增值服务'
UNION ALL
-- 资源池赛马模式  和校园
     select a.outprov_code,  to_number(a.taxrate) taxrate, a.product_class, sum(a.settlement_amount) total_fee, '和校园（赛马池）' col_name
      from rpt_p2c a
     where a.settlemonth = ?  and a.taxrate * 100 = ? and (a.fee_flag = ? or ? = '0')
       and a.inprov_code = 'CY'
       and a.offer_code = '50049' AND a.product_code in('5004909','5004903','5004901','5004938','5004902','5004935','5004936','5004915','5004929','5004910','5004904')
       and a.CHARGE_CODE in('02','1725','2313','52','5725','6313')
     group by a.outprov_code, a.taxrate, a.product_class, '和校园（赛马池）'
UNION ALL	 
	 select a.outprov_code,  to_number(a.taxrate) taxrate, a.product_class, sum(a.settlement_amount) total_fee, '和校园（赛马池）' col_name
     from stludr.rpt_p2c a
     where a.settlemonth = ?  and a.taxrate * 100 = ? and (a.fee_flag = ? or ? = '0')
       and a.inprov_code = 'CY'
       and a.SETTLEMENT_CLASS ='4'
     group by a.outprov_code, a.taxrate, a.product_class, '和校园（赛马池）' 	 
--UNION ALL
-- 市场化结算模式  和校园
     --select a.outprov_code,  to_number(a.taxrate) taxrate, a.product_class, sum(a.settlement_amount) total_fee, '和校园' col_name
      --from rpt_p2c a
    -- where a.settlemonth = ?  and a.taxrate * 100 = ? and (a.fee_flag = ? or ? = '0')
       --and a.inprov_code = 'CY'
      -- and a.offer_code = '50049' AND a.product_code in('5004929','5004910','5004904')
    -- group by a.outprov_code, a.taxrate, a.product_class, '和校园'
UNION ALL
-- 市场化结算模式  和宝贝
     select a.outprov_code,  to_number(a.taxrate) taxrate, a.product_class, sum(a.settlement_amount) total_fee, '和宝贝' col_name
      from rpt_p2c a
     where a.settlemonth = ?  and a.taxrate * 100 = ? and (a.fee_flag = ? or ? = '0')
       and a.inprov_code = 'CY'
       and a.offer_code = '50049' AND a.product_code in('5004966','5004951','5004948','5004952','5004949','5004950','5004955','5004956','5004957',
                                                       '5004958','5004960','5004961','5004962','5004921','5004963','5004970','5004971','5004964',
                                                       '5004965','5004937','5004953')
     and a.CHARGE_CODE in('02','1774','3038','3039','3040','3041','3042','3043','3044','3045','3046','3047','3048','3049','3050','3051','3052','1725',
'52','5774','7038','7039','7040','7041','7042','7043','7044','7045','7046','7047','7048','7049','7050','7051','7052','5725','800901','800902','800903','800904','800905','800906',
'800907','800908','800909','800910','900901','900902','900903','900904','900905','900906','900907','900908','900909','900910')
     group by a.outprov_code, a.taxrate, a.product_class, '和宝贝'
UNION ALL
     select a.outprov_code,  to_number(a.taxrate) taxrate, a.product_class, sum(a.settlement_amount) total_fee, '和宝贝' col_name
      from stludr.rpt_p2c a
     where a.settlemonth = ?  and a.taxrate * 100 = ? and (a.fee_flag = ? or ? = '0')
       and a.inprov_code = 'CY'
       and a.SETTLEMENT_CLASS ='5'
     group by a.outprov_code, a.taxrate, a.product_class, '和宝贝'		 
	 
UNION ALL
-- 市场化结算模式  OneEDU
     select a.outprov_code,  to_number(a.taxrate) taxrate, a.product_class, sum(a.settlement_amount) total_fee, 'OneEDU' col_name
      from rpt_p2c a
     where a.settlemonth = ?  and a.taxrate * 100 = ? and (a.fee_flag = ? or ? = '0')
       and a.inprov_code = 'CY'
       and a.offer_code = '50049' AND a.product_code in('2022999400066446','2022999400066447','2022999400066448','2022999400066449','2023999400082369','2024999480007066','2022999400066447')
     and a.CHARGE_CODE in('3144','3145','3146','3150','3151','3152','3154','3155','3156','3157','3158','3159','3160','3161','3162','3163','3168','3169','3170','3171',
'7144','7145','7146','7150','7151','7152','7154','7155','7156','7157','7158','7159','7160','7161','7162','7163','7168','7169','7170','7171','3998','3999','4000','4001','4002','4003','4004','4005','7998','7999','8000','8001','8002','8003','8004','8005',
'4138','8138','4139','8139','4140','8140','800505','900505','800506','900506','800507','900507','800508','900508','800509','900509','800510','900510','800511','900511','4141','8141','4142','8142','4143','8143','4144','8144','4145','8145','4146','8146',
'4147','8147','4148','8148','4149','8149','4150','8150')
     group by a.outprov_code, a.taxrate, a.product_class, 'OneEDU'
     -- OneHealth平台、中移急救
UNION ALL
     select a.outprov_code,  to_number(a.taxrate) taxrate, a.product_class, sum(a.settlement_amount) total_fee, '0neHealth智慧医疗-标准产品' col_name
      from stludr.rpt_p2c a
     where a.settlemonth = ?  and a.taxrate * 100 = ? and (a.fee_flag = ? or ? = '0')
       and a.inprov_code = 'CY'
  	   and a.offer_code in('50104','50074')  AND a.product_code in('2022999400054889','5007401')
       and a.CHARGE_CODE in('3118','3119','7118','7119','3829','3830','3831','3832','7829','7830','7831','7832')
     group by a.outprov_code, a.taxrate, a.product_class, '0neHealth智慧医疗-标准产品'
-- OneTrip智慧文旅
UNION ALL
     select a.outprov_code,  to_number(a.taxrate) taxrate, a.product_class, sum(a.settlement_amount) total_fee, 'OneTrip智慧文旅' col_name
      from stludr.rpt_p2c a
     where a.settlemonth = ?  and a.taxrate * 100 = ? and (a.fee_flag = ? or ? = '0')
       and a.inprov_code = 'CY'
  	   and a.offer_code ='50078'  and product_code in('2022999400048208','2022999400048209','2022999400048210','2022999400048211')
     group by a.outprov_code, a.taxrate, a.product_class, 'OneTrip智慧文旅'
 UNION ALL
 -- 任务销售模式 智慧校园云平台    OneSKY（中移凌云）  和校园  0   和宝贝 0
 select c.prov_cd, c.taxrate / 100, '', c.report_fee total_fee, c.col_name
   from rpt_p2c_limited c
  where c.settlemonth = ? and c.taxrate = ? and (c.fee_flag = ? or ? = '0')
   and c.rep_num = 'D313-2'
   ) t right join stl_province_cd p on p.prov_cd = t.outprov_code
 where p.prov_type = 0)
 group by prov_cd, outprov_name, prov_wl, taxrate
 order by prov_cd