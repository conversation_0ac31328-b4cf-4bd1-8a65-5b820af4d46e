 select prov_cd, outprov_name, prov_wl,
        nvl(-round(sum(A_fee + B_fee + C_fee + D_fee + F_fee + G_fee + H_fee + I_fee + J_fee + K_fee + L_fee + M_fee), 2), 0) total_fee, -- 含税金额
       nvl(-round(sum(A_fee + B_fee + C_fee + D_fee + F_fee + G_fee + H_fee + I_fee + J_fee + K_fee + L_fee + M_fee) / (1 + taxrate), 2), 0) total_notaxfee, -- 不含税金额
       cast(nvl(-round(sum(A_fee + B_fee + C_fee + D_fee + F_fee + G_fee + H_fee + I_fee + J_fee + K_fee + L_fee + M_fee), 2) + round(sum(A_fee + B_fee + C_fee + D_fee + F_fee + G_fee + H_fee + I_fee + J_fee + K_fee + L_fee + M_fee) / (1 + taxrate), 2), 0)as decimal (20,2)) total_taxfee, -- 税额
       -- 5G行业消息
       cast(nvl(-round(sum(C_fee + D_fee + F_fee + G_fee), 2) + round(sum(C_fee + D_fee + F_fee + G_fee) / (1 + taxrate), 2), 0)as decimal (20,2))  A_fee, -- 税额
       nvl(-round(sum(C_fee + D_fee + F_fee + G_fee) / (1 + taxrate), 2), 0)  A_notaxfee, -- 不含税金额
       -- 能力开放平台-中间号
       cast(nvl(-round(sum(A_fee + B_fee), 2) + round(sum(A_fee + B_fee) / (1 + taxrate), 2), 0)as decimal (20,2)) B_fee, -- 税额
       nvl(-round(sum(A_fee + B_fee) / (1 + taxrate), 2), 0) B_notaxfee, -- 不含税金额
       -- 移动办公
       cast(nvl(-round(sum(H_fee + I_fee + J_fee + L_fee + M_fee), 2) + round(sum(H_fee + I_fee + J_fee + L_fee + M_fee) / (1 + taxrate), 2), 0)as decimal (20,2)) C_fee, -- 税额
       nvl(-round(sum(H_fee + I_fee + J_fee + L_fee + M_fee) / (1 + taxrate), 2), 0) C_notaxfee,  -- 不含税金额
       -- 成员视频彩铃
       cast(nvl(-round(sum(K_fee), 2) + round(sum(K_fee) / (1 + taxrate), 2), 0)as decimal (20,2)) D_fee, -- 税额
       nvl(-round(sum(K_fee) / (1 + taxrate), 2), 0) D_notaxfee  -- 不含税金额
  from
(select p.prov_cd, p.prov_nm outprov_name, '30' || p.prov_wl prov_wl, t.taxrate,
       nvl(round(decode(t.col_name, '中间号', t.total_fee, 0) / 1000, 2),0) A_fee,
       nvl(round(decode(t.col_name, '工作号', t.total_fee, 0) / 1000, 2),0) B_fee,
       nvl(round(decode(t.col_name, '5G会话消息', t.total_fee, 0) / 1000, 2),0) C_fee,
       nvl(round(decode(t.col_name, '5G多媒体消息', t.total_fee, 0) / 1000, 2),0) D_fee,
       nvl(round(decode(t.col_name, '5G阅信', t.total_fee, 0) / 1000, 2),0) F_fee,
       nvl(round(decode(t.col_name, '阅信（增值能力）', t.total_fee, 0) / 1000, 2),0) G_fee,
       nvl(round(decode(t.col_name, '公费电话', t.total_fee, 0) / 1000, 2),0) H_fee,
       nvl(round(decode(t.col_name, '公费会议', t.total_fee, 0) / 1000, 2),0) I_fee,
       nvl(round(decode(t.col_name, '公费短信', t.total_fee, 0) / 1000, 2),0) J_fee,
       nvl(round(decode(t.col_name, '成员视频彩铃', t.total_fee, 0) / 1000, 2),0) K_fee,
       nvl(round(decode(t.col_name, '快通知增强通知（个人版）', t.total_fee, 0) / 1000, 2),0) L_fee,
       nvl(round(decode(t.col_name, '快通知增强通知', t.total_fee, 0) / 1000, 2),0) M_fee 
  from
(select a.outprov_code, a.taxrate, b.col_name, sum(a.settlement_amount) total_fee, a.charge_code
  from rpt_p2c a, rvl_p2c_bus_config b
 where a.settlemonth = ? and a.taxrate * 100 = ? and (a.fee_flag = ? or ? = '0')
   and ? between b.start_month and b.end_month 
   and a.inprov_code = 'HLW'
   and b.rep_num = 'D313-8'  AND b.REPORT_SOURCE = '1'
   and (b.type = 'OP' and a.offer_code = b.offer_code and a.product_code = b.product_code
    or b.type = 'O' and a.offer_code = b.offer_code
    or b.type='OPF' and a.offer_code = b.offer_code and a.product_code = b.product_code and a.charge_code = b.charge_item)
 group by a.outprov_code, a.taxrate, b.col_name, a.charge_code) t right join stl_province_cd p on p.prov_cd = t.outprov_code
 where p.prov_type = 0)
 group by prov_cd, outprov_name, prov_wl,taxrate
 order by prov_cd