select prov_cd, outprov_name, prov_wl,
       nvl(-round(sum(total_fee) / 1000, 2), 0) total_fee,
       nvl(-round(round(sum(total_fee) / (1 + taxrate), 2) / 1000, 2), 0) total_notaxfee,
       cast(nvl(-round(sum(total_fee) / 1000, 2) + round(round(sum(total_fee) / (1 + taxrate), 2) / 1000, 2), 0)as decimal (20,2)) total_taxfee,
       cast(nvl(-round(sum(total_fee) / 1000, 2) + round(round(sum(total_fee) / (1 + taxrate), 2) / 1000, 2), 0)as decimal (20,2)) A_taxfee,
       nvl(-round(round(sum(total_fee) / (1 + taxrate), 2) / 1000, 2), 0) A_notaxfee
  from (select p.prov_cd, p.prov_nm outprov_name, '30' || p.prov_wl prov_wl, taxrate, total_fee
          from (select a.outprov_code, to_number(a.taxrate) taxrate, sum(a.settlement_amount) total_fee
                  from rpt_p2c a, (select distinct terminal_vendor from stludr.maapmma_vendor_conf) b
                 where a.settlemonth = ? and a.taxrate * 100 = ? and (fee_flag = ? or ? = '0') and a.inprov_code = b.terminal_vendor
                   and a.product_code = '5003401' and a.inprov_code <> 'HLW'
                 group by a.outprov_code, a.taxrate) t
         right join stl_province_cd p on p.prov_cd = t.outprov_code where p.prov_type = 0)
 group by prov_cd, outprov_name, prov_wl, taxrate
 order by prov_cd