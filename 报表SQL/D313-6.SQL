  select prov_cd, outprov_name, prov_wl,
       nvl(-round(sum(A_fee + B_fee + C_fee+ D_fee ), 2), 0) total_fee, -- 含税金额
       nvl(-round(sum(A_fee + B_fee + C_fee+ D_fee ) / (1 + taxrate), 2), 0) total_notaxfee, -- 不含税金额
       cast(nvl(-round(sum(A_fee + B_fee + C_fee+ D_fee ), 2) + round(sum(A_fee + B_fee + C_fee + D_fee) / (1 + taxrate), 2), 0) as decimal (20,2)) total_taxfee, -- 税额
       cast(nvl(-round(sum(A_fee), 2) + round(sum(A_fee) / (1 + taxrate), 2), 0) as decimal (20,2)) A_fee, -- 税额
       nvl(-round(sum(A_fee) / (1 + taxrate), 2), 0) A_notaxfee, -- 不含税金额
       cast(nvl(-round(sum(B_fee), 2) + round(sum(B_fee) / (1 + taxrate), 2), 0) as decimal (20,2)) B_fee, -- 税额
       nvl(-round(sum(B_fee) / (1 + taxrate), 2), 0) B_notaxfee, -- 不含税金额
       cast(nvl(-round(sum(C_fee), 2) + round(sum(C_fee) / (1 + taxrate), 2), 0) as decimal (20,2)) C_fee, -- 税额
       nvl(-round(sum(C_fee) / (1 + taxrate), 2), 0) C_notaxfee, -- 不含税金额
       cast(nvl(-round(sum(D_fee), 2) + round(sum(D_fee) / (1 + taxrate), 2), 0) as decimal (20,2)) D_fee, -- 税额
       nvl(-round(sum(D_fee) / (1 + taxrate), 2), 0) D_notaxfee -- 不含税金额
  from
(select p.prov_cd, p.prov_nm outprov_name, '30' || p.prov_wl prov_wl, t.taxrate,
       nvl(round(decode(t.col_name, '5G视信', t.total_fee, 0) / 1000, 2),0) A_fee,
       nvl(round(decode(t.col_name, '云MAS', t.total_fee, 0) / 1000, 2),0) B_fee,
       nvl(round(decode(t.col_name, 'OneSOC一体化安全运营中心', t.total_fee, 0) / 1000, 2),0) C_fee,
       nvl(round(decode(t.col_name, '量子密话', t.total_fee, 0) / 1000, 2),0) D_fee
  from
(select a.outprov_code, a.taxrate, b.col_name, sum(a.settlement_amount) total_fee
  from rpt_p2c a, rvl_p2c_bus_config b
 where a.settlemonth = ? and a.taxrate * 100 = ?  and (fee_flag = ? or ? = '0')
   and ? between b.start_month and b.end_month
   and a.inprov_code = 'ZYJC'
   and b.rep_num = 'D313-6'   AND b.REPORT_SOURCE ='1' 
   and (b.type = 'OP' and a.offer_code = b.offer_code and a.product_code = b.product_code
    or b.type = 'O' and a.offer_code = b.offer_code and (a.product_code IS NULL or a.product_code='')
    OR b.type = 'OPF' AND a.CHARGE_CODE = b.CHARGE_ITEM and a.offer_code = b.offer_code and a.product_code = b.product_code)
 group by a.outprov_code, a.taxrate, b.col_name) t right join stl_province_cd p on p.prov_cd = t.outprov_code
 where p.prov_type = 0)
 group by prov_cd, outprov_name, prov_wl,taxrate
 order by prov_cd