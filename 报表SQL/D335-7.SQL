WITH aggregated_data AS (
    SELECT 
        a.outprov_code,
        a.taxrate,
        SUM(a.settlement_amount) AS total_fee
    FROM rpt_p2c_nmg a
    WHERE a.settlemonth = ?
      AND a.taxrate* 100 = ?
      AND (a.fee_flag = ? OR ? = '0')
      AND a.settle_out_type = '1'
    GROUP BY a.outprov_code, a.taxrate
),
province_data AS (
    SELECT
        p.prov_cd,
        p.prov_full_nm,
        '30' || p.prov_wl AS prov_wl,
        NVL(t.taxrate, ?/100) AS taxrate, 
        NVL(-ROUND(t.total_fee / 1000, 2), 0) AS fee
    FROM stl_province_cd p
    LEFT JOIN aggregated_data t ON p.prov_cd = t.outprov_code
    WHERE p.prov_type = 0
),
province_agg AS (
    SELECT
        prov_cd,
        prov_full_nm,
        prov_wl,
        taxrate,
        ROUND(SUM(fee), 2) AS total_fee,
        ROUND(SUM(fee) / (1 + taxrate), 2) AS total_notaxfee
    FROM province_data
    GROUP BY prov_cd, prov_full_nm, prov_wl, taxrate
),
total_agg AS (
    SELECT 
        SUM(total_fee) AS all_total_fee,
        SUM(total_notaxfee) AS all_total_notaxfee
    FROM province_agg
)
SELECT 
    p.prov_wl,
    p.prov_full_nm AS PROV_NM,
    -- 应结出（汇总）
    CASE WHEN p.prov_wl != '304210' THEN CAST(p.total_fee AS DECIMAL(20, 2)) ELSE 0 END AS out_total_fee,
    CASE WHEN p.prov_wl != '304210' THEN CAST(p.total_notaxfee AS DECIMAL(20, 2)) ELSE 0 END AS out_total_notaxfee,
    CASE WHEN p.prov_wl != '304210' THEN CAST(p.total_fee-p.total_notaxfee AS DECIMAL(20, 2)) ELSE 0 END AS out_taxfee,
    -- 应结入（汇总）
    CASE WHEN p.prov_wl = '304210' THEN CAST(t.all_total_fee AS DECIMAL(20, 2)) ELSE 0 END AS in_total_fee,
    CASE WHEN p.prov_wl = '304210' THEN CAST(t.all_total_notaxfee AS DECIMAL(20, 2)) ELSE 0 END AS in_total_notaxfee,
    CASE WHEN p.prov_wl = '304210' THEN CAST(t.all_total_fee-t.all_total_notaxfee AS DECIMAL(20, 2)) ELSE 0 END AS in_taxfee,
    -- 结算额-含税金额
    CASE 
        WHEN p.prov_wl != '304210' THEN CAST(p.total_fee AS DECIMAL(20, 2))  -- 非内蒙古行：0-应结出含税金额
        ELSE -CAST(t.all_total_fee AS DECIMAL(20, 2))  -- 内蒙古行：所有省份应结出含税金额之和
    END AS settle_total_fee
FROM province_agg p
CROSS JOIN total_agg t