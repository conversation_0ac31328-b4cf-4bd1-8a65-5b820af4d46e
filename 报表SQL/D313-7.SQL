select prov_cd, outprov_name, prov_wl,
       nvl(-round(sum(A_fee + B_fee + C_fee + E_fee + F_fee + G_fee + H_fee + I_fee + J_fee+K_fee+L_fee+M_fee+N_fee+O_fee+P_fee+Q_fee+R_fee ), 2) / 1, 0) total_fee, -- 含税金额
       nvl(-round(sum(A_fee + B_fee + C_fee + E_fee + F_fee + G_fee + H_fee + I_fee + J_fee+K_fee+L_fee+M_fee+N_fee+O_fee+P_fee+Q_fee+R_fee ) / (1 + taxrate), 2), 0) total_notaxfee, -- 不含税金额
       nvl(-round(sum(A_fee + B_fee + C_fee + E_fee + F_fee + G_fee + H_fee + I_fee + J_fee+K_fee+L_fee+M_fee+N_fee+O_fee+P_fee+Q_fee+R_fee ), 2) + round(sum(A_fee + B_fee + C_fee  + E_fee + F_fee + G_fee + H_fee + I_fee + J_fee+K_fee+L_fee+M_fee+N_fee+O_fee+P_fee+Q_fee+R_fee ) / (1 + taxrate), 2), 0) total_taxfee, -- 税额
       nvl(-round(sum(A_fee), 2) + round(sum(A_fee) / (1 + taxrate), 2), 0) A_fee, -- 税额
       nvl(-round(sum(A_fee) / (1 + taxrate), 2), 0) A_notaxfee, -- 不含税金额
       nvl(-round(sum(B_fee), 2) + round(sum(B_fee) / (1 + taxrate), 2), 0) B_fee, -- 税额
       nvl(-round(sum(B_fee) / (1 + taxrate), 2), 0) B_notaxfee, -- 不含税金额
       nvl(-round(sum(C_fee), 2) + round(sum(C_fee) / (1 + taxrate), 2), 0) C_fee, -- 税额
       nvl(-round(sum(C_fee) / (1 + taxrate), 2), 0) C_notaxfee, -- 不含税金额
       nvl(-round(sum(E_fee), 2) + round(sum(E_fee) / (1 + taxrate), 2), 0) E_fee, -- 税额
       nvl(-round(sum(E_fee) / (1 + taxrate), 2), 0) E_notaxfee, -- 不含税金额
       nvl(-round(sum(F_fee + G_fee + H_fee + I_fee + J_fee+L_fee+M_fee+N_fee+O_fee+P_fee+Q_fee), 2) + round(sum(F_fee + G_fee + H_fee + I_fee + J_fee+L_fee+M_fee+N_fee+O_fee+P_fee+Q_fee) / (1 + taxrate), 2), 0) F_fee ,-- 税额
       nvl(-round(sum(F_fee + G_fee + H_fee + I_fee + J_fee+L_fee+M_fee+N_fee+O_fee+P_fee+Q_fee) / (1 + taxrate), 2), 0) F_notaxfee, -- 不含税金额
       nvl(-round(sum(K_fee), 2) + round(sum(K_fee) / (1 + taxrate), 2), 0) G_fee, -- 税额
       nvl(-round(sum(K_fee) / (1 + taxrate), 2), 0) G_notaxfee, -- 不含税金额
       nvl(-round(sum(R_fee), 2) + round(sum(R_fee) / (1 + taxrate), 2), 0) H_fee, -- 税额
       nvl(-round(sum(R_fee) / (1 + taxrate), 2), 0) H_notaxfee -- 不含税金额
  from
(select p.prov_cd, p.prov_nm outprov_name, '30' || p.prov_wl prov_wl, TO_NUMBER(t.taxrate) taxrate,
       nvl(round(decode(t.col_name, '企业视频彩铃', t.total_fee, 0) / 1000, 2),0) A_fee,
       nvl(round(decode(t.col_name, '能力开放平台-企业彩印', t.total_fee, 0) / 1000, 2),0) B_fee,
       nvl(round(decode(t.col_name, '和商务TV', t.total_fee, 0) / 1000, 2),0) C_fee,
       nvl(round(decode(t.col_name, '企业阅读', t.total_fee, 0) / 1000, 2),0) E_fee,
       nvl(round(decode(t.col_name, '云游戏实例', t.total_fee, 0) / 1000, 2),0) F_fee,
       nvl(round(decode(t.col_name, '云游戏增值服务', t.total_fee, 0) / 1000, 2),0) G_fee,
       nvl(round(decode(t.col_name, '游戏云直播', t.total_fee, 0) / 1000, 2),0) H_fee,
       nvl(round(decode(t.col_name, 'AR虚实空间', t.total_fee, 0) / 1000, 2),0) I_fee,
       nvl(round(decode(t.col_name, '悦听卡', t.total_fee, 0) / 1000, 2),0) J_fee,
       nvl(round(decode(t.col_name, '5G融媒', t.total_fee, 0) / 1000, 2),0) K_fee,
       nvl(round(decode(t.col_name, '数字空间云渲染运行服务套件', t.total_fee, 0) / 1000, 2),0) L_fee,
       nvl(round(decode(t.col_name, '数字空间云渲染空间定制套件', t.total_fee, 0) / 1000, 2),0) M_fee,
       nvl(round(decode(t.col_name, '智能云渲染增值服务', t.total_fee, 0) / 1000, 2),0) N_fee,
       nvl(round(decode(t.col_name, '智能产品类', t.total_fee, 0) / 1000, 2),0) O_fee,
       nvl(round(decode(t.col_name, '设计制作云渲染运行服务套件', t.total_fee, 0) / 1000, 2),0) P_fee,
       nvl(round(decode(t.col_name, '数智人云渲染', t.total_fee, 0) / 1000, 2),0) Q_fee,
       nvl(round(decode(t.col_name, '人车家内容增值服务', t.total_fee, 0) / 1000, 2),0) R_fee
  from
(select a.outprov_code, a.taxrate, a.product_class, sum(a.settlement_amount) total_fee, b.col_name
  from rpt_p2c a, rvl_p2c_bus_config b
 where a.settlemonth = ? and a.taxrate * 100 = ? and (a.fee_flag = ? or ? = '0')
   and ? between b.start_month and b.end_month
   and a.inprov_code = 'MG'
   and b.rep_num = 'D313-7'   AND b.REPORT_SOURCE ='1'
   and (b.type = 'OP' and a.offer_code = b.offer_code and a.product_code = b.product_code
    or b.type = 'O' and a.offer_code = b.offer_code and b.remark is null
    or b.type = 'O' and a.offer_code = b.offer_code and b.remark = '3' and a.settlement_class = b.remark
    or b.type = 'OPF' and a.offer_code = b.offer_code and a.product_code = b.product_code and a.charge_code = b.charge_item)
 group by a.outprov_code, a.taxrate, a.product_class, b.col_name) t right join stl_province_cd p on p.prov_cd = t.outprov_code
 where p.prov_type = 0)
 group by prov_cd, outprov_name, prov_wl, taxrate
 order by prov_cd