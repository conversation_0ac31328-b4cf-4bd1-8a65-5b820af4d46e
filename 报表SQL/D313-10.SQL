select prov_cd, outprov_name, prov_wl,
       nvl(-round(sum(A1_fee + A2_fee + B_fee + C_fee + D_fee + E_fee + F_fee + G_fee + H_fee + I_fee + J_fee + Q_fee+ K_fee), 2) / 1, 0) total_fee, -- 含税金额
       nvl(-round(sum(A1_fee + A2_fee + B_fee + C_fee + D_fee + E_fee + F_fee + G_fee + H_fee + I_fee + J_fee + Q_fee+ K_fee) / (1 + taxrate), 2), 0) total_notaxfee, -- 不含税金额
       nvl(-round(sum(A1_fee + A2_fee + B_fee + C_fee + D_fee + E_fee + F_fee + G_fee + H_fee + I_fee + J_fee + Q_fee+ K_fee), 2) + round(sum(A1_fee + A2_fee + B_fee + C_fee + D_fee + E_fee + F_fee + G_fee + H_fee + I_fee+ J_fee + Q_fee+ K_fee) / (1 + taxrate), 2), 0) total_taxfee, -- 税额
	   -- 云MAS
       nvl(-round(sum(A1_fee + A2_fee), 2) + round(sum(A1_fee + A2_fee) / (1 + taxrate), 2), 0) A_fee, -- 税额
       nvl(-round(sum(A1_fee + A2_fee) / (1 + taxrate), 2), 0) A_notaxfee, -- 不含税金额
       -- 5G视信
       nvl(-round(sum(B_fee), 2) + round(sum(B_fee) / (1 + taxrate), 2), 0) B_fee, -- 税额
       nvl(-round(sum(B_fee) / (1 + taxrate), 2), 0) B_notaxfee, -- 不含税金额
       -- 移动和盾
       nvl(-round(sum(C_fee + D_fee), 2) + round(sum(C_fee + D_fee) / (1 + taxrate), 2), 0) C_fee, -- 税额
       nvl(-round(sum(C_fee + D_fee) / (1 + taxrate), 2), 0) C_notaxfee,  -- 不含税金额
       -- 专线卫士
       nvl(-round(sum(E_fee + F_fee + G_fee + H_fee + I_fee+ J_fee + K_fee), 2) + round(sum(E_fee + F_fee + G_fee + H_fee + I_fee+ J_fee + K_fee) / (1 + taxrate), 2), 0) D_fee, -- 税额
       nvl(-round(sum(E_fee + F_fee + G_fee + H_fee + I_fee+ J_fee + K_fee) / (1 + taxrate), 2), 0) D_notaxfee,  -- 不含税金额
       -- e企收银
       nvl(-round(sum(Q_fee), 2) + round(sum(Q_fee) / (1 + taxrate), 2), 0) Q_fee, -- 税额
       nvl(-round(sum(Q_fee) / (1 + taxrate), 2), 0) Q_notaxfee -- 不含税金额
  from
(select p.prov_cd, p.prov_nm outprov_name, '30' || p.prov_wl prov_wl, t.taxrate,
       nvl(round(decode(t.col_name, '云MAS短信', t.total_fee, 0) / 1000, 2),0) A1_fee,
       nvl(round(decode(t.col_name, '云MAS彩信', t.total_fee, 0) / 1000, 2),0) A2_fee,
       nvl(round(decode(t.col_name, '5G视信', t.total_fee, 0) / 1000, 2),0) B_fee,
       nvl(round(decode(t.col_name, '和盾抗D', t.total_fee, 0) / 1000, 2),0) C_fee,
       nvl(round(decode(t.col_name, '和盾CMCA', t.total_fee, 0) / 1000, 2),0) D_fee,
       nvl(round(decode(t.col_name, '防火墙', t.total_fee, 0) / 1000, 2),0) E_fee,
       nvl(round(decode(t.col_name, '防火墙增强版', t.total_fee, 0) / 1000, 2),0) F_fee,
       nvl(round(decode(t.col_name, '防火墙优享版', t.total_fee, 0) / 1000, 2),0) G_fee,
       nvl(round(decode(t.col_name, '审计版', t.total_fee, 0) / 1000, 2),0) H_fee,
       nvl(round(decode(t.col_name, '日志上传网监平台服务', t.total_fee, 0) / 1000, 2),0) I_fee ,
       nvl(round(decode(t.col_name, '插件基础版', t.total_fee, 0) / 1000, 2),0) J_fee,
       nvl(round(decode(t.col_name, '专线卫士', t.total_fee, 0) / 1000, 2),0) K_fee,
       nvl(round(decode(t.col_name, 'e企收银', t.total_fee, 0) / 1000, 2),0) Q_fee
  from
(select a.outprov_code, a.taxrate, b.col_name, sum(a.settlement_amount) total_fee
  from rpt_p2c a, rvl_p2c_bus_config b
 where a.settlemonth = ? and a.taxrate * 100 = ? and (a.fee_flag = ? or ? = '0')
   and ? between b.start_month and b.end_month
   and a.inprov_code = 'ZW'
   and b.rep_num = 'D313-10' AND b.REPORT_SOURCE ='1'
   and (b.type = 'OP' and a.offer_code = b.offer_code and a.product_code = b.product_code
    or b.type = 'O' and a.offer_code = b.offer_code and (a.product_code IS NULL or a.product_code='')
    OR b.type = 'OPF' AND a.CHARGE_CODE = b.CHARGE_ITEM and a.offer_code = b.offer_code and a.product_code = b.product_code)
 group by a.outprov_code, a.taxrate, b.col_name) t right join stl_province_cd p on p.prov_cd = t.outprov_code
 where p.prov_type = 0)
 group by prov_cd, outprov_name, prov_wl,taxrate
 order by prov_cd