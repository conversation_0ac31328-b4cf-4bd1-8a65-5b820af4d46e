select prov_cd, outprov_name, prov_wl,
       nvl(-round(sum(A_fee) / 1000, 2), 0) total_fee, -- 含税金额
       nvl(-round(round(sum(A_fee) / 1000, 2) / (1 + taxrate), 2), 0) total_notaxfee, -- 不含税金额
       cast(nvl(-round(sum(A_fee) / 1000, 2) + round(round(sum(A_fee) / 1000, 2) / (1 + taxrate), 2), 0)as decimal (20,2)) total_taxfee, -- 税额
       cast(nvl(-round(sum(A_fee) / 1000, 2) + round(round(sum(A_fee) / 1000, 2) / (1 + taxrate), 2), 0)as decimal (20,2)) A_taxfee, -- 税额
       nvl(-round(round(sum(A_fee) / 1000, 2) / (1 + taxrate), 2), 0) A_notaxfee -- 不含税金额
  from
(select p.prov_cd, p.prov_nm outprov_name, '30' || p.prov_wl prov_wl, taxrate,
       nvl(decode(t.col_name, 'e企收银', t.total_fee, 0), 0) A_fee
  from
(select a.outprov_code, to_number(a.taxrate) taxrate, b.col_name, sum(a.settlement_amount) total_fee
  from rpt_p2c a, rvl_p2c_bus_config b
 where a.settlemonth = ? and a.taxrate * 100 = ? and (fee_flag = ? or ? = '0')
   and ? between b.start_month and b.end_month
   and a.inprov_code = 'JK'
   and b.rep_num = 'D313-11'
   and (b.type = 'OP' and a.offer_code = b.offer_code and a.product_code = b.product_code
    or b.type = 'O' and a.offer_code = b.offer_code
    or b.type = 'OPF' and a.offer_code = b.offer_code and a.product_code = b.product_code and a.charge_code = b.charge_item)
 group by a.outprov_code, a.taxrate, b.col_name) t right join stl_province_cd p on p.prov_cd = t.outprov_code
 where p.prov_type = 0)
 group by prov_cd, outprov_name, prov_wl, taxrate
 order by prov_cd