---
description: 
globs: 
alwaysApply: false
---
# Settlement Service Procedure Project Structure

## Directory Structure

The settlement service procedures are organized under:
```
settle-service-procedure/src/main/resources/stludr/结算前数据准备/话单汇总/
```

Key files:
- [STL_SYNC_BL_SETTLE_NEWRCS.sql](mdc:Billing+Settle/settle-service-procedure/src/main/resources/stludr/结算前数据准备/话单汇总/STL_SYNC_BL_SETTLE_NEWRCS.sql) - 阅信(增值能力) settlement procedure
- [STL_SYNC_BL_SETTLE_MAS.sql](mdc:Billing+Settle/settle-service-procedure/src/main/resources/stludr/结算前数据准备/话单汇总/STL_SYNC_BL_SETTLE_MAS.sql) - MAS settlement procedure

## Procedure Naming Conventions

- `STL_SYNC_BL_SETTLE_*` - Settlement data preparation procedures
- `STL_*_LOG` - Logging procedures (STL_INFO_LOG, STL_ERROR_LOG)
- Table names follow pattern: `UR_*_YYYYMM_T` for monthly partitioned tables

## Standard Procedure Parameters

```sql
PROCEDURE procedure_name(
    inMonth          IN   VARCHAR2,    -- Settlement month (YYYYMM)
    inBatch          IN   VARCHAR2,    -- Batch identifier
    flag_version     IN   VARCHAR2,    -- Version flag
    reserve1         IN   VARCHAR2,    -- Reserved parameter 1
    reserve2         IN   VARCHAR2,    -- Reserved parameter 2
    proc_out         OUT  VARCHAR2,    -- Procedure output
    outSysError      OUT  VARCHAR2,    -- System error message
    outReturn        OUT  NUMBER,      -- Return code (0=success, -1=error)
    outBL            OUT  VARCHAR2,    -- Business logic output
    outAR            OUT  VARCHAR2     -- Additional result
)
```

## Logging Standards

### Info Logging
```sql
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_##', '执行结束时间：' || TO_CHAR(SYSDATE, 'yyyy-MM-dd hh24:mi:ss'));
```

### Error Logging
```sql
call STLUDR.STL_ERROR_LOG(inMonth, P_ERRCODE, outSysError, '', v_proc_name, '');
```

### Performance Logging
```sql
call stludr.STL_INFO_LOG(inMonth, inBatch, v_proc_name||'_99', '执行完成总耗时秒='||TIMESTAMPDIFF(SECOND, @P_TIME, NOW()));
```

## Database Table Patterns

### Settlement Tables
- `SYNC_BL_SETTLE_MID` - Settlement middle table
- `SYNC_BL_RULE_SWAP_*` - Temporary swap tables for rule processing
- `BIZ_MSG_MMM_STL` - Business message settlement table

### Monthly Partitioned Tables
- `UR_NEWRCS_YYYYMM_T` - New RCS monthly data
- `UR_CMAS_YYYYMM_T` - CMAS monthly data  
- `UR_MAAPMMA_YYYYMM_T` - MAAP MMA monthly data

## Best Practices

1. **Always use dynamic table names** with month parameter: `'table_name_' || inMonth`
2. **Include execution timing** in log messages
3. **Use proper step numbering** in log calls (_1, _2, _3, etc.)
4. **Handle monthly partitioned tables** correctly with proper date formatting
5. **Follow the batch processing pattern** for large datasets (e.g., 11 batches for 31 days)

