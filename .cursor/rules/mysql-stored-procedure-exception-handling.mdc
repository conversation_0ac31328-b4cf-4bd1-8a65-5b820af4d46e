---
description: 
globs: 
alwaysApply: false
---
# MySQL Stored Procedure Exception Handling

## Critical Exception Handling Pattern

When working with MySQL stored procedures, especially those using `DECLARE EXIT HANDLER FOR SQLEXCEPTION`, follow this essential pattern:

```sql
DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
    GET DIAGNOSTICS CONDITION 1 P_ERRCODE = RETURNED_SQLSTATE, P_ERRMSG = MESSAGE_TEXT;
    outSysError := substr(P_ERRMSG, 1, 1000);
    outReturn := -1;
    ROLLBACK;
    
    -- Enhanced exception output
    select ('=== EXCEPTION CAUGHT ===') AS error_header;
    select ('SQLSTATE: ' || P_ERRCODE) AS sql_state;
    select ('ERROR_MESSAGE: ' || P_ERRMSG) AS error_message;
    select ('PROC_NAME: ' || v_proc_name) AS proc_name;
    select ('exception: ' || outReturn || '|' || P_ERRCODE || '|' || outSysError) AS error_msg;
    
    call STLUDR.STL_ERROR_LOG(inMonth,P_ERRCODE,outSysError,'',v_proc_name,'');
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = outSysError;
END;

begin  -- CRITICAL: This begin block is essential!
    -- All your main procedure logic goes here
    -- Including PREPARE/EXECUTE statements
    -- Without this begin block, exceptions may not be caught
end;
```

## Key Rules

1. **Scope Matters**: `DECLARE EXIT HANDLER` only catches exceptions within the same scope
2. **Wrap Main Logic**: Always wrap your main procedure logic in a `begin...end` block after the exception handler
3. **Enhanced Error Output**: Include detailed error information for debugging
4. **Use ORACLE Mode**: The stored procedures use `||` for concatenation (MySQL ORACLE mode)

## Common Pitfalls

- ❌ **WRONG**: Writing SQL statements directly after the exception handler without a `begin` block
- ❌ **WRONG**: Using nested `DECLARE` statements (causes syntax errors in MySQL)
- ❌ **WRONG**: Using `CONCAT()` function in ORACLE mode (use `||` instead)
- ✅ **CORRECT**: Wrapping all main logic in a `begin...end` block within the exception handler's scope

## Settlement Procedure Context

For settlement procedures like [STL_SYNC_BL_SETTLE_NEWRCS.sql](mdc:Billing+Settle/settle-service-procedure/src/main/resources/stludr/结算前数据准备/话单汇总/STL_SYNC_BL_SETTLE_NEWRCS.sql):

- Always include the self-increment primary key `null` value in INSERT statements
- Use proper step logging with `STL_INFO_LOG` calls
- Handle dynamic SQL with PREPARE/EXECUTE statements safely within the exception handler scope

