-- 测试存储过程异常处理的脚本
-- 用于验证 P_SETTLE_DB2F 存储过程的异常处理机制是否正常工作

-- 创建一个测试版本的存储过程，包含故意的异常
use stlusers;
DROP PROCEDURE IF EXISTS stlusers.P_SETTLE_DB2F_TEST;
DELIMITER //
CREATE OR REPLACE DEFINER="stlusers"@"10.%" PROCEDURE stlusers.P_SETTLE_DB2F_TEST(
    inMonth          IN   VARCHAR2,
    batch            IN   VARCHAR2,
    flag_version     IN   VARCHAR2,
    reserve1         IN   VARCHAR2,
    reserve2         IN   VARCHAR2,
    proc_out         OUT  VARCHAR2,
    outSysError      OUT  VARCHAR2,
    outReturn        OUT  NUMBER,
    outBL            OUT  NUMBER,
    outAR            OUT  NUMBER
)
AS
    v_proc_name   VARCHAR2(30) := 'P_SETTLE_DB2F_TEST';
    iv_Batch VARCHAR2(1);
    P_ERRCODE   VARCHAR2(16);
    P_ERRMSG    VARCHAR2(1024);
BEGIN
    -- 初始化输出参数
    outSysError := 'OK';
    outReturn := 0;

    -- 参数验证
    if (batch = '0') then
        iv_Batch := '1';
    elsif (batch in ('1', '2', '5')) then
        iv_Batch := batch;
    else
        outSysError := '批次错误。现仅支持预出账-0；一批-1；二批-2。';
        outReturn := '-1';
        return;
    end if;

    -- 声明异常处理器，覆盖整个业务逻辑块
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
        BEGIN
            GET DIAGNOSTICS CONDITION 1
                P_ERRCODE = RETURNED_SQLSTATE, P_ERRMSG = MESSAGE_TEXT;
            outSysError := substr(P_ERRMSG, 1, 1000);
            outReturn  := -1;
            ROLLBACK;
            
            select ('exception caught: ' || outReturn || '|'  || P_ERRCODE || '|' || outSysError ) AS error_msg ;
            call LOG_PROCEDURES('异常被捕获: ' || outSysError, v_proc_name);
        END;
BEGIN
    -- 业务逻辑开始
    call LOG_PROCEDURES('开始测试异常处理', v_proc_name);
    
    -- 故意触发一个异常来测试异常处理
    SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = '这是一个测试异常，验证异常处理是否正常工作';
    
    -- 这行代码不应该被执行，因为上面的异常应该被捕获
    call LOG_PROCEDURES('如果看到这条消息，说明异常处理没有正常工作', v_proc_name);
    
    -- 记录执行完成信息（正常情况下不会到达这里）
    SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || outReturn  as info;
    call LOG_PROCEDURES('procedure ' || v_proc_name || ' completed successfully. nReturn=' || outReturn, v_proc_name);
END;
END//
DELIMITER ;

-- 测试调用
SET @outSysError = '';
SET @outReturn = 0;
SET @outBL = 0;
SET @outAR = 0;
SET @proc_out = '';

CALL stlusers.P_SETTLE_DB2F_TEST('202412', '1', '', '', '', @proc_out, @outSysError, @outReturn, @outBL, @outAR);

-- 显示结果
SELECT 
    @outSysError as 'Error Message',
    @outReturn as 'Return Code',
    CASE 
        WHEN @outReturn = -1 AND @outSysError LIKE '%测试异常%' THEN '异常处理正常工作'
        WHEN @outReturn = 0 THEN '异常处理可能没有正常工作'
        ELSE '未知状态'
    END as 'Test Result';
