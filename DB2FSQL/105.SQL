/*
 * 结算数据提取SQL - BL结算数据处理 (105.SQL)
 * 
 * ======================== 与101-104系列差异 ========================
 * | 项目          | 101-104系列                | 105.SQL               |
 * |---------------|----------------------------|----------------------|
 * | 数据源        | sync_interface_bl_202507   | sync_bl_settle_202507|
 * | 分区处理      | 按partid分区处理           | 按产品代码分类处理    |
 * | 税率处理      | 使用原始taxrate字段        | 固定税率6%/9%        |
 * | 数据源类型    | 接口数据                   | 结算数据             |
 * | 汇总方式      | 明细数据                   | 部分汇总处理         |
 * =================================================================
 * 
 * 业务说明：
 * 1. 第一部分：处理普通BL结算数据
 *    - 使用原始金额字段 (amount)
 *    - 固定税率6%，税费为0
 *    - 排除多种特殊产品代码和业务规则
 *    - 数据源：BL结算表
 * 
 * 2. 第二部分：处理50021产品代码的汇总数据
 *    - 对金额进行SUM汇总
 *    - 固定税率9%，税费为0
 *    - 按业务维度分组汇总
 *    - 只处理50021产品代码
 * 
 * 特殊业务规则过滤：
 * - 排除多个特定产品代码组合
 * - 特定产品的特定费用类型排除
 * - 复杂的条件组合过滤
 */

-- 第一部分：普通BL结算数据处理
SELECT
	'1' sn,
	to_char ( aa.stream_id ) stream_id,
	aa.customernumber,
	aa.ordermode,
	aa.pospecnumber,
	decode( aa.soid, '', aa.soid, NULL, aa.soid, aa.sospecnumber ) sospecnumber,
	to_char ( aa.poid ) poid,
	to_char ( aa.soid ) soid,
	aa.dn,
	aa.accountid,
	aa.feetype,
	aa.amount orgfee,          -- 使用原始金额
	aa.amount notaxfee,        -- 使用原始金额作为不含税费用
	0 taxfee,                  -- 固定税费为0
	'6' taxrate,               -- 固定税率6%
	aa.orgmonth,
	'202507' settlemonth,
	aa.orgmonth || '********' start_time,
	'1' phase,
	'BL' filesource,           -- BL数据源标识
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	'' lcd,
	'' lce 
FROM
	sync_bl_settle_202507 aa 
WHERE
	-- 排除多个特定产品代码，但110151服务代码除外
	( aa.pospecnumber NOT IN ( '*********', '*********', '*********', '*********', '*********', '*********', '60000' ) AND aa.sospecnumber <> '110151' ) 
	-- 排除50021产品 智能路由 （在第二部分汇总处理）
	AND ( aa.pospecnumber <> '50021' ) 
	-- 排除5003401 5G消息的157 费项（终端结算）
	AND ( aa.sospecnumber <> '5003401' OR aa.feetype <> '157' ) 
	-- 排除**************** 支付位士的1开头费项
	AND ( aa.sospecnumber <> '****************' OR aa.feetype NOT LIKE '1%' ) 
	-- 排除5001606 中间号
	AND aa.sospecnumber <> '5001606' 
	-- 只处理有效状态数据
	AND aa.STATUS = '0' 
UNION ALL

-- 第二部分：50021产品代码汇总数据处理
SELECT
	'1' sn,
	max(
	to_char ( aa.stream_id )) stream_id,    -- 取最大流水号
	aa.customernumber,
	aa.ordermode,
	aa.pospecnumber,
	decode( aa.soid, '', aa.soid, aa.sospecnumber ) sospecnumber,
	to_char ( aa.poid ) poid,
	to_char ( aa.soid ) soid,
	aa.dn,
	aa.accountid,
	aa.feetype,
	sum( aa.amount ) orgfee,               -- 金额汇总
	sum( aa.amount ) notaxfee,             -- 不含税金额汇总
	0 taxfee,                              -- 固定税费为0
	'9' taxrate,                           -- 固定税率9%
	aa.orgmonth,
	'202507' settlemonth,
	aa.orgmonth || '********' start_time,
	'1' phase,
	'BL' filesource,                       -- BL数据源标识
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	'' lcd,
	'' lce 
FROM
	sync_bl_settle_202507 aa 
WHERE
	aa.pospecnumber IN ( '50021' )   -- 只处理50021 智能路由
	AND aa.STATUS = '0'              -- 只处理有效状态数据
GROUP BY
	-- 按业务维度分组汇总
	orgmonth,          -- 原始月份
	customernumber,    -- 客户编号
	ordermode,         -- 订单模式
	pospecnumber,      -- 产品代码
	sospecnumber,      -- 服务代码
	poid,              -- 产品实例ID
	soid,              -- 服务实例ID
	dn,                -- 用户号码
	accountid,         -- 账户ID
	feetype            -- 费用类型