/*
 * 结算数据提取SQL系列 - 分区2数据处理 (101.SQL)
 * 
 * ======================== 系列文件差异对比 ========================
 * | 文件      | 分区条件                    | 结果字段 | 排除50022 | 说明           |
 * |-----------|---------------------------|----------|-----------|----------------|
 * | 101.SQL   | partid = '2'              | lce      | 否        | 分区2数据处理  |
 * | 102.SQL   | partid = '3'              | lcf      | 是        | 分区3数据处理  |
 * | 103.SQL   | partid = '0' OR IS NULL   | lcf      | 是        | 分区0/未分区   |
 * | 104.SQL   | partid = '1'              | lcf      | 是        | 分区1数据处理  |
 * =================================================================
 * 
 * 业务逻辑共同点：
 * - 都是结算数据提取SQL，按分区并行处理提高效率
 * - 都分两部分：普通产品数据和LP类型特殊产品数据
 * - 都使用相同的UNION ALL结构
 * - LP类型产品都使用list_price作为费用字段
 * 
 * 当前文件 (101.SQL) 业务说明：
 * 1. 第一部分：处理普通产品数据 (分区2)
 *    - 使用原始费用字段 (orgfee, notaxfee)
 *    - 排除特殊产品代码和LP类型产品
 *    - 不排除50022产品代码 (与其他文件的差异)
 * 
 * 2. 第二部分：处理LP类型特殊产品数据 (分区2)
 *    - 使用标价字段 (list_price) 作为费用
 *    - 只包含配置表中的LP类型产品
 *
 * 注意：两个UNION ALL查询不能合并，因为：
 * - 费用字段来源不同 (orgfee vs list_price)
 * - 业务逻辑互斥 (普通产品 vs 特殊产品)
 * - 保持逻辑清晰和维护性
 * 
 * 与其他文件差异：
 * - 101.SQL: 分区2, lce字段, 不排除50022  ← 当前文件
 * - 102.SQL: 分区3, lcf字段, 排除50022
 * - 103.SQL: 分区0/NULL, lcf字段, 排除50022
 * - 104.SQL: 分区1, lcf字段, 排除50022
 * 
 */

-- 第一部分：普通产品数据处理
SELECT
	'1' AS sn,
	tt.orgbid stream_id,
	tt.customernumber,
	tt.ordermode,
	tt.pospecnumber,
	decode( tt.soid, '', tt.soid, NULL, tt.soid, tt.sospecnumber ) sospecnumber,
	tt.poid,
	tt.soid,
	decode( tt.dn, '0', '', tt.dn ) dn,
	tt.accountid,
	tt.feetype,
	tt.orgfee,         -- 使用原始费用
	tt.notaxfee,       -- 使用原始不含税费用
	tt.taxfee,
	tt.taxrate,
	tt.orgmonth,
	'202507' settlemonth,
	decode( tt.adjmonth, '', tt.orgmonth, NULL, tt.orgmonth, adjmonth ) || '********' start_time,
	'1' phase,
	tt.datasource,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	tt.adjmonth,
	'' lce 
FROM
	sync_interface_bl_202507 tt 
WHERE
	tt.remark = '2'          -- 一批数据标识
	AND tt.STATUS = 0        -- 有效状态
	AND tt.partid = '2'      -- 分区标识
	AND (
		-- 排除特殊产品代码，但0102001产品的33、83费用类型除外
		tt.pospecnumber NOT IN ( '0102001', '*********', '*********', '50025' ) 
		OR (tt.pospecnumber = '0102001' AND tt.feetype NOT IN ( '33', '83' ))
	) 
	AND (
		-- 排除LP类型产品（这些在第二部分处理）
		tt.sospecnumber NOT IN ( SELECT tt.product_code FROM stlusers.STL_CONFIG_DB2F TT WHERE tt.type_nm = 'LP' AND tt.column_nm = 'product_code' ) 
		OR tt.sospecnumber IS NULL 
	) 
UNION ALL

-- 第二部分：LP类型特殊产品数据处理  
SELECT
	'1' AS sn,
	tt.orgbid stream_id,
	tt.customernumber,
	tt.ordermode,
	tt.pospecnumber,
	decode( tt.soid, '', tt.soid, NULL, tt.soid, tt.sospecnumber ) sospecnumber,
	tt.poid,
	tt.soid,
	decode( tt.dn, '0', '', tt.dn ) dn,
	tt.accountid,
	tt.feetype,
	tt.list_price orgfee,      -- LP类型产品使用标价作为费用
	tt.list_price notaxfee,    -- LP类型产品使用标价作为不含税费用
	tt.taxfee,
	tt.taxrate,
	tt.orgmonth,
	'202507' settlemonth,
	decode( tt.adjmonth, '', tt.orgmonth, NULL, tt.orgmonth, adjmonth ) || '********' start_time,
	'1' phase,
	tt.datasource,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	tt.adjmonth,
	'' lce 
FROM
	sync_interface_bl_202507 tt 
WHERE
	tt.remark = '2'          -- 一批数据标识
	AND tt.STATUS = 0        -- 有效状态  
	AND tt.partid = '2'      -- 分区标识
	AND tt.sospecnumber IN ( -- 只处理LP类型特殊产品
	SELECT
		tt.product_code 
	FROM
		stlusers.STL_CONFIG_DB2F TT 
	WHERE
	tt.type_nm = 'LP'        -- LP类型产品配置
	AND tt.column_nm = 'product_code')