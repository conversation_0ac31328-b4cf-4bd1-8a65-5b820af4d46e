/*
*	二批 ：对讲业务-实收（应收模拟实收）
*	暂估流程不考虑
*	50025	对讲业务
*
*/
SELECT
	'2' sn,
	bb.orgbid stream_id,
	bb.customernumber,
	bb.ordermode,
	bb.pospecnumber,
	decode( bb.soid, '', bb.soid, NULL, bb.soid, bb.sospecnumber ) sospecnumber,
	bb.poid,
	bb.soid,
	decode( bb.dn, '0', '', bb.dn ) dn,
	bb.accountid,
	feetype,
	bb.orgfee,
	bb.notaxfee,
	bb.taxfee,
	bb.taxrate,
	bb.orgmonth,
	bb.orgmonth paid_month,
	'202507' settlemonth,
	decode( bb.adjmonth, '', bb.orgmonth, NULL, bb.orgmonth, adjmonth ) || '********' start_time,
	'2' phase,
	bb.datasource,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	bb.adjmonth,
	'' space 
FROM
	sync_interface_bl_202507 bb
	LEFT JOIN (
	SELECT
		* 
	FROM
		stl_mnp_record_poc m 
	WHERE
		m.settlemonth = 202507 
	AND m.partid = substr( '202507', 5, 2 )) ss ON bb.dn = ss.member_code 
	AND bb.soid = ss.prod_order_id 
WHERE
	bb.ordermode IN ( '3', '4' ) 
	AND bb.pospecnumber = '50025' 
	AND bb.STATUS = 0 
	-- AND (					 -- 处理冲突 和 205 SQL形成互补
	-- 	bb.pospecnumber IN (	
	-- 	SELECT
	-- 		aa.offer_code 
	-- 	FROM
	-- 		stlusers.STL_CONFIG_DB2F aa 
	-- 	WHERE
	-- 		aa.type_nm = 'SIM' 
	-- 		AND aa.column_nm = 'offer_code' 
	-- 		AND '202507' BETWEEN aa.eff_month 
	-- 		AND aa.exp_month 
	-- 	) 
	-- 	OR bb.sospecnumber IN (
	-- 	SELECT
	-- 		aa.product_code 
	-- 	FROM
	-- 		stlusers.STL_CONFIG_DB2F aa 
	-- 	WHERE
	-- 		aa.type_nm = 'SIM' 
	-- 		AND aa.column_nm = 'product_code' 
	-- 		AND '202507' BETWEEN aa.eff_month 
	-- 		AND aa.exp_month
	-- 	)) 
UNION ALL
SELECT
	'2' sn,
	tt.orgbid stream_id,
	tt.customernumber,
	tt.ordermode,
	tt.pospecnumber,
	decode( tt.soid, '', tt.soid, NULL, tt.soid, tt.sospecnumber ) sospecnumber,
	tt.poid,
	tt.soid,
	decode( tt.dn, '0', '', tt.dn ) dn,
	tt.accountid,
	feetype,
	tt.orgfee,
	tt.notaxfee,
	tt.taxfee,
	tt.taxrate,
	tt.orgmonth,
	tt.paymonth paid_month,
	'202507' settlemonth,
	decode( tt.adjmonth, '', tt.orgmonth, NULL, tt.orgmonth, adjmonth ) || '********' start_time,
	'2' phase,
	tt.datasource,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	tt.adjmonth,
	'' space 
FROM
	stludr.sync_interface_ar_202507 tt
	LEFT JOIN ( SELECT * FROM stludr.stl_mnp_record_poc ) ss ON tt.dn = ss.member_code 
	AND tt.soid = ss.prod_order_id 
	AND ss.settlemonth = tt.orgmonth 
WHERE
	tt.ordermode = '1' 
	AND tt.pospecnumber = '50025' -- 对讲业务
	AND tt.STATUS = 0