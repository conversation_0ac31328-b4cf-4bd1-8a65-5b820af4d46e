/*
 * 结算数据提取SQL - 分区1数据处理 (104.SQL)
 * 
 * 业务说明：
 * 1. 第一部分：处理普通产品数据 (分区1)
 *    - 使用原始费用字段 (orgfee, notaxfee)
 *    - 排除特殊产品代码和LP类型产品
 *    - 比101.SQL多排除'50022'产品代码
 * 
 * 2. 第二部分：处理LP类型特殊产品数据 (分区1)
 *    - 使用标价字段 (list_price) 作为费用
 *    - 只包含配置表中的LP类型产品
 * 
 * 与其他文件差异：
 * - 101.SQL: 分区2, lce字段, 不排除50022
 * - 102.SQL: 分区3, lcf字段, 排除50022
 * - 103.SQL: 分区0/NULL, lcf字段, 排除50022
 * - 104.SQL: 分区1, lcf字段, 排除50022  ← 当前文件
 */

-- 第一部分：普通产品数据处理 (分区1)
SELECT
	'1' sn,
	tt.orgbid stream_id,
	tt.customernumber,
	tt.ordermode,
	tt.pospecnumber,
	decode( tt.soid, '', tt.soid, NULL, tt.soid, tt.sospecnumber ) sospecnumber,
	tt.poid,
	tt.soid,
	decode( tt.dn, '0', '', tt.dn ) dn,
	tt.accountid,
	tt.feetype,
	tt.orgfee,         -- 使用原始费用
	tt.notaxfee,       -- 使用原始不含税费用
	tt.taxfee,
	tt.taxrate,
	tt.orgmonth,
	'202507' settlemonth,
	decode( tt.adjmonth, '', tt.orgmonth, NULL, tt.orgmonth, adjmonth ) || '********' start_time,
	'1' phase,
	tt.datasource,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	tt.adjmonth,
	'' lcf             -- lcf字段标识 
FROM
	sync_interface_bl_202507 tt 
WHERE
	tt.remark = '2'          -- 一批数据标识
	AND tt.STATUS = 0        -- 有效状态
	AND tt.partid = '1'      -- 分区1标识
	AND (
		-- 排除特殊产品代码，包含50022，但0102001产品的33、83费用类型除外
		tt.pospecnumber NOT IN ( '0102001', '*********', '*********', '50025', '50022' ) 
		OR (
			tt.pospecnumber = '0102001' 
		AND tt.feetype NOT IN ( '33', '83' ))) 
	AND (
		-- 排除LP类型产品（这些在第二部分处理）
		tt.sospecnumber NOT IN ( SELECT tt.product_code FROM stlusers.STL_CONFIG_DB2F TT WHERE tt.type_nm = 'LP' AND tt.column_nm = 'product_code' ) 
		OR sospecnumber IS NULL 
	) 
UNION ALL

-- 第二部分：LP类型特殊产品数据处理 (分区1)
SELECT
	'1' sn,
	tt.orgbid stream_id,
	tt.customernumber,
	tt.ordermode,
	tt.pospecnumber,
	decode( tt.soid, '', tt.soid, NULL, tt.soid, tt.sospecnumber ) sospecnumber,
	tt.poid,
	tt.soid,
	decode( tt.dn, '0', '', tt.dn ) dn,
	tt.accountid,
	tt.feetype,
	tt.list_price orgfee,      -- LP类型产品使用标价作为费用
	tt.list_price notaxfee,    -- LP类型产品使用标价作为不含税费用
	tt.taxfee,
	tt.taxrate,
	tt.orgmonth,
	'202507' settlemonth,
	decode( tt.adjmonth, '', tt.orgmonth, NULL, tt.orgmonth, adjmonth ) || '********' start_time,
	'1' phase,
	tt.datasource,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	tt.adjmonth,
	'' lcf             -- lcf字段标识 
FROM
	sync_interface_bl_202507 tt 
WHERE
	tt.remark = '2'          -- 一批数据标识
	AND tt.STATUS = 0        -- 有效状态  
	AND tt.partid = '1'      -- 分区1标识
	AND tt.sospecnumber IN ( -- 只处理LP类型特殊产品
	SELECT tt.product_code FROM stlusers.STL_CONFIG_DB2F TT WHERE tt.type_nm = 'LP' AND tt.column_nm = 'product_code' )