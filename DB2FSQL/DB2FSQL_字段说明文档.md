# 结算数据提取SQL字段说明文档

## 文档概述

本文档基于 `DB2FSQL/101.SQL` 文件，详细说明结算数据提取查询中各个字段的含义、数据来源和业务逻辑。

## 数据源表

- **主表**: `sync_interface_bl_202507` - 同步接口账单数据表（202507月份）
- **配置表**: `stlusers.STL_CONFIG_DB2F` - 结算配置表

## 查询结果字段说明（按SELECT顺序）

| 序号 | 字段名 | 数据类型 | 数据来源 | 业务说明 |
|------|--------|----------|----------|----------|
| 1 | `sn` | VARCHAR | '1' | 序列号，固定值'1'，用于标识记录类型 |
| 2 | `stream_id` | VARCHAR | tt.orgbid | 流水ID，来源于原始账单ID |
| 3 | `customernumber` | VARCHAR | tt.customernumber | 客户编号，唯一标识客户 |
| 4 | `ordermode` | VARCHAR | tt.ordermode | 订单模式，标识订单的处理方式 |
| 5 | `pospecnumber` | VARCHAR | tt.pospecnumber | 产品规格编号，标识具体的产品类型 |
| 6 | `sospecnumber` | VARCHAR | decode(tt.soid, '', tt.soid, NULL, tt.soid, tt.sospecnumber) | 服务规格编号，当soid为空或NULL时使用sospecnumber |
| 7 | `poid` | VARCHAR | tt.poid | 产品实例ID，产品订购实例的唯一标识 |
| 8 | `soid` | VARCHAR | tt.soid | 服务实例ID，服务订购实例的唯一标识 |
| 9 | `dn` | VARCHAR | decode(tt.dn, '0', '', tt.dn) | 用户号码，当为'0'时转换为空字符串 |
| 10 | `accountid` | VARCHAR | tt.accountid | 账户ID，客户的计费账户标识 |
| 11 | `feetype` | VARCHAR | tt.feetype | 费项编码 |
| 12 | `orgfee` | DECIMAL | **普通产品**: tt.orgfee<br/>**LP类型**: tt.list_price | 原始费用金额<br/>• 普通产品使用原始费用<br/>• LP类型产品使用标价 |
| 13 | `notaxfee` | DECIMAL | **普通产品**: tt.notaxfee<br/>**LP类型**: tt.list_price | 不含税费用金额<br/>• 普通产品使用原始不含税费用<br/>• LP类型产品使用标价 |
| 14 | `taxfee` | DECIMAL | tt.taxfee | 税费金额，从原始数据直接获取 |
| 15 | `taxrate` | VARCHAR | tt.taxrate | 税率，从原始数据直接获取 |
| 16 | `orgmonth` | VARCHAR | tt.orgmonth | 原始月份，数据产生的原始月份 |
| 17 | `settlemonth` | VARCHAR | '202507' | 结算月份，固定为当前结算周期月份 |
| 18 | `start_time` | VARCHAR | decode(tt.adjmonth, '', tt.orgmonth, NULL, tt.orgmonth, adjmonth) &#124;&#124; '********' | 开始时间<br/>• 当调整月份为空或NULL时使用原始月份<br/>• 否则使用调整月份<br/>• 后缀'********'表示月初零时 |
| 19 | `phase` | VARCHAR | '1' | 阶段标识，固定值'1'，表示第一阶段处理 |
| 20 | `datasource` | VARCHAR | tt.datasource | 数据源标识，标识数据的来源系统 |
| 21 | `ndate` | VARCHAR | to_char(sysdate, 'yyyymmdd') | 当前日期，格式YYYYMMDD，数据处理日期 |
| 22 | `adjmonth` | VARCHAR | tt.adjmonth | 调整月份，用于费用调整的月份标识 |
| 23 | `lce` | VARCHAR | '' | 结果标识字段，空字符串，用于区分不同类型的处理结果 |

## 字段分类说明

### 基础标识字段
- **第1位**: `sn` - 序列号
- **第2位**: `stream_id` - 流水ID  
- **第19位**: `phase` - 阶段标识

### 客户相关字段  
- **第3位**: `customernumber` - 客户编号
- **第4位**: `ordermode` - 订单模式
- **第9位**: `dn` - 用户号码
- **第10位**: `accountid` - 账户ID

### 产品服务字段
- **第5位**: `pospecnumber` - 产品规格编号
- **第6位**: `sospecnumber` - 服务规格编号  
- **第7位**: `poid` - 产品实例ID
- **第8位**: `soid` - 服务实例ID

### 费用相关字段
- **第11位**: `feetype` - 费用类型
- **第12位**: `orgfee` - 原始费用金额
- **第13位**: `notaxfee` - 不含税费用金额  
- **第14位**: `taxfee` - 税费金额
- **第15位**: `taxrate` - 税率

### 时间相关字段
- **第16位**: `orgmonth` - 原始月份
- **第17位**: `settlemonth` - 结算月份
- **第18位**: `start_time` - 开始时间
- **第21位**: `ndate` - 当前日期
- **第22位**: `adjmonth` - 调整月份

### 数据源标识字段
- **第20位**: `datasource` - 数据源标识
- **第23位**: `lce` - 结果标识字段

## 业务逻辑说明

### 费用字段的差异化处理

#### 普通产品数据（第一部分UNION ALL）
```sql
tt.orgfee,     -- 使用原始费用
tt.notaxfee,   -- 使用原始不含税费用
```

#### LP类型特殊产品数据（第二部分UNION ALL）
```sql
tt.list_price orgfee,     -- 使用标价作为费用
tt.list_price notaxfee,   -- 使用标价作为不含税费用
```

### 关键业务规则

1. **产品代码排除规则**:
   - 排除特殊产品代码：'0102001', '*********', '*********', '50025'
   - 对于'0102001'产品，排除费用类型'33', '83'

2. **LP类型产品识别**:
   - 通过配置表 `stlusers.STL_CONFIG_DB2F` 识别
   - 条件：`type_nm = 'LP'` AND `column_nm = 'product_code'`

3. **数据状态过滤**:
   - `remark = '2'`：一批数据标识
   - `STATUS = 0`：有效状态
   - `partid = '2'`：分区2标识

### decode函数处理逻辑

#### dn字段处理
```sql
decode(tt.dn, '0', '', tt.dn)
```
- 当 dn = '0' 时，返回空字符串
- 否则返回原始 dn 值

#### sospecnumber字段处理
```sql
decode(tt.soid, '', tt.soid, NULL, tt.soid, tt.sospecnumber)
```
- 当 soid 为空字符串时，返回 soid
- 当 soid 为 NULL 时，返回 soid
- 否则返回 sospecnumber

#### start_time字段处理
```sql
decode(tt.adjmonth, '', tt.orgmonth, NULL, tt.orgmonth, adjmonth) || '********'
```
- 当 adjmonth 为空字符串或 NULL 时，使用 orgmonth
- 否则使用 adjmonth
- 拼接 '********' 表示月初零时

## 与系列文件的差异

本文档基于101.SQL（分区2处理），与其他文件的主要差异：

| 文件 | 分区标识 | 结果字段 | 特殊产品排除 |
|------|----------|----------|--------------|
| 101.SQL | partid = '2' | lce | 不排除50022 |
| 102.SQL | partid = '3' | lcf | 排除50022 |
| 103.SQL | partid = '0'/NULL | lcf | 排除50022 |
| 104.SQL | partid = '1' | lcf | 排除50022 |

## 注意事项

1. **数据完整性**: 两个UNION ALL查询确保了所有符合条件的数据都被处理
2. **费用处理**: LP类型产品使用标价而非原始费用，需要特别注意
3. **时间字段**: start_time字段的构造逻辑复杂，需要理解调整月份的优先级
4. **配置依赖**: LP类型产品的识别依赖于配置表，需要确保配置表数据的准确性

---

**文档版本**: 1.0  
**生成日期**: {{ 当前日期 }}  
**基于文件**: DB2FSQL/101.SQL  
**维护人员**: 开发团队