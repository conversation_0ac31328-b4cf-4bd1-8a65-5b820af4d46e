/*
*	二批 ：CDN-实收 和205.SQL 互补			暂估流程不考虑
*	50004	CDN  BBOSS
*/
SELECT
	'2' sn,
	tt.orgbid stream_id,
	tt.customernumber,
	tt.ordermode,
	tt.pospecnumber,
	decode( tt.soid, '', tt.soid, NULL, tt.soid, tt.sospecnumber ) sospecnumber,
	tt.poid,
	tt.soid,
	decode( tt.dn, '0', '', tt.dn ) dn,
	tt.accountid,
	tt.feetype,
	tt.orgfee,
	tt.notaxfee,
	tt.taxfee,
	tt.taxrate,
	tt.orgmonth,
	tt.paymonth paid_month,
	'202507' settlemonth,
	decode( tt.adjmonth, '', tt.orgmonth, NULL, tt.orgmonth, adjmonth ) || '********' start_time,
	'2' phase,
	'CD' datasource,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	tt.adjmonth,
	'' space 
FROM
	sync_interface_ar_202507 tt 
WHERE
	tt.remark <> '2' 
	AND tt.STATUS = 0 
	AND tt.pospecnumber = '50004' 
	AND tt.ordermode = '1'
UNION ALL
SELECT
	'2' sn,
	tt.orgbid stream_id,
	tt.customernumber,
	tt.ordermode,
	tt.pospecnumber,
	decode( tt.soid, '', tt.soid, NULL, tt.soid, tt.sospecnumber ) sospecnumber,
	tt.poid,
	tt.soid,
	decode( tt.dn, '0', '', tt.dn ) dn,
	tt.accountid,
	tt.feetype,
	tt.orgfee,
	tt.notaxfee,
	tt.taxfee,
	tt.taxrate,
	tt.orgmonth,
	tt.orgmonth paid_month,
	'202507' settlemonth,
	decode( tt.adjmonth, '', tt.orgmonth, NULL, tt.orgmonth, adjmonth ) || '********' start_time,
	'2' phase,
	'CD' datasource,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	tt.adjmonth,
	'' space 
FROM
	sync_interface_bl_202507 tt 
WHERE
	tt.remark <> '2' 
	AND tt.STATUS = 0 
	AND tt.pospecnumber = '50004' 
	AND tt.ordermode = '3'