/*
*	来自订购成员信息表
*	已下线，不使用
*/
SELECT
	'1' sn,
	stlusers.seq_bid.nextval stream_id,
	ll.customer_code,
	ll.order_mode,
	ll.product_code,
	ll.service_code,
	ll.prod_inst_id,
	ll.svc_inst_id,
	'' dn,
	ll.account_id,
	ll.feetype,
	ll.amount AS orgfee,
	ll.amount AS notaxfee,
	0 taxfee,
	ll.taxrate,
	ll.acct_month,
	'202507' settlemonth,
	ll.acct_month || '********' start_time,
	'1' phase,
	'BL' datasource,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	'' AS adjmonth,
	'' AS space 
FROM
	(
	SELECT
		tt.customer_code,
		tt.order_mode,
		tt.product_code,
		tt.service_code,
		tt.prod_inst_id,
		tt.svc_inst_id,
		tt.account_id,
		tt.taxrate,
		tt.feetype,
		tt.acct_month,
		sum( tt.fee ) AS amount 
	FROM
		stlusers.Interface_Sub_Member_202507_t tt 
	WHERE
		tt.STATUS = '0' 
	GROUP BY
		tt.customer_code,
		tt.order_mode,
		tt.product_code,
		tt.service_code,
		tt.prod_inst_id,
		tt.svc_inst_id,
		tt.account_id,
		tt.taxrate,
		tt.feetype,
	tt.acct_month 
	) ll