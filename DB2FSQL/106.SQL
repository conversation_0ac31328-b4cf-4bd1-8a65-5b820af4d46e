/* 
*	CP类结算数据提取，只涉及省间结算     ur_cpr_yyyymm_t 表     暂估不考虑
OFFER_CODE | 			product_code
4040104	企业手机报			 4010417
							4010418
							4010415
							4010416
							4010419
							4010420
							4010421
							4010422
							4010423
							4010424
							4010425
							4010426
							4010427
							4010428
							4010429
4040103	手机报统付版
4040105	企业阅读
4040108	
4040109	企业互联网电视
							110115 全网移动办公(OA)_彩信
60010   量子密话	

*/
SELECT
	'5' sn,
	tt.orgbid stream_id,
	tt.customernumber,
	tt.ordermode,
	tt.pospecnumber,
	decode( tt.soid, '', tt.soid, NULL, tt.soid, tt.sospecnumber ) sospecnumber,
	tt.poid,
	tt.soid,
	decode( tt.dn, '0', '', tt.dn ) dn,
	tt.accountid,
	tt.feetype,
	tt.orgfee,
	tt.notaxfee,
	tt.taxfee,
	tt.taxrate,
	tt.orgmonth,
	'' lcb,
	'202507' settlemonth,
	decode( tt.adjmonth, '', tt.orgmonth, NULL, tt.orgmonth, adjmonth ) || '********' start_time,
	'1' phase,
	tt.datasource filesource,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	tt.adjmonth,
	'' lca 
FROM
	sync_interface_bl_202507 tt 
WHERE
	tt.STATUS = 0 
	AND (
	tt.pospecnumber IN ( SELECT aa.offer_code FROM stlusers.STL_CONFIG_DB2F aa WHERE aa.type_nm = 'CP' AND aa.column_nm = 'offer_code' ) 
	OR tt.sospecnumber IN ( SELECT aa.product_code FROM stlusers.STL_CONFIG_DB2F aa WHERE aa.type_nm = 'CP' AND aa.column_nm = 'product_code' ))