/*
 * 结算数据提取SQL - 多数据源综合处理 (107.SQL)
 * 
 * ======================== 与101.SQL和108.SQL的对比分析 ========================
 * | 项目          | 101.SQL                    | 108.SQL                    | 107.SQL                    |
 * |---------------|----------------------------|----------------------------|----------------------------|
 * | UNION数量     | 2个                        | 3个                        | 5个                        |
 * | 数据源数量    | 1个 (sync_interface_bl)    | 3个                        | 4个                        |
 * | 主要特点      | 分区处理+LP类型            | 特殊业务补充               | 多数据源综合               |
 * | 关联查询      | 无                         | 无                         | 有LEFT JOIN               |
 * | 结果字段      | lce                        | lca,lcb                    | lca                        |
 * | 业务复杂度    | 中等                       | 中等                       | 高                         |
 * | 费项处理      | 标准处理                   | 标准处理                   | 包含费项映射               |
 * | 优化提示      | 无                         | 无                         | 有hint优化                 |
 * ==========================================================================
 * 
 * 业务说明：
 * 107.SQL是最复杂的综合处理脚本，包含5个不同的数据源和业务场景
 * 
 * 1. 第一部分：和对讲业务数据 + MNP记录关联
 * 2. 第二部分：接口表数据（排除50118）
 * 3. 第三部分：金额表数据（排除50118）
 * 4. 第四部分：SDWAN专线卫士数据
 * 5. 第五部分：SDWAN成员视频彩铃数据（含费项映射）
 * 
 * ======================== 互补关系说明 ========================
 * 与101.SQL的互补关系：
 * - 101.SQL排除50025产品代码（和对讲业务）
 * - 107.SQL第一部分专门处理50025产品代码
 * - 形成完整的业务数据覆盖
 * 
 * 与108.SQL的互补关系：
 * - 107.SQL第二、三部分排除50118产品代码（跨省专线卫士）
 * - 108.SQL第二、三部分专门处理50118产品代码
 * - 避免数据重复处理
 * 
 * 系列脚本协同工作模式：
 * - 101-104.SQL：按分区处理标准业务数据
 * - 105.SQL：处理BL结算数据
 * - 107.SQL：处理多数据源综合业务（排除部分特殊产品）
 * - 108.SQL：处理107.SQL中被排除的特殊产品数据
 * 
 * 独特特点总结：
 * - 最复杂结构：5个UNION ALL，处理最多样业务场景
 * - 唯一关联查询：使用LEFT JOIN关联MNP记录表
 * - 费项映射：第五部分包含复杂费项编码转换逻辑
 * - 性能优化：使用Oracle hint优化提示
 * - 多数据源覆盖：涵盖4个不同数据源表
 * ==========================================================
 */

-- 第一部分：和对讲业务数据处理（含MNP记录关联）
SELECT
	'1' sn,
	tt.orgbid stream_id,
	tt.customernumber,
	tt.ordermode,
	tt.pospecnumber,
	decode( tt.soid, '', tt.soid, NULL, tt.soid, tt.sospecnumber ) sospecnumber,
	tt.poid,
	tt.soid,
	decode( tt.dn, '0', '', tt.dn ) dn,
	tt.accountid,
	feetype,                   -- 费项编码
	tt.orgfee,                 -- 原始费用
	tt.notaxfee,               -- 不含税费用
	tt.taxfee,                 -- 税费
	tt.taxrate,                -- 税率
	tt.orgmonth,
	'202507' settlemonth,
	decode( tt.adjmonth, '', tt.orgmonth, NULL, tt.orgmonth, adjmonth ) || '********' start_time,
	'1' phase,
	tt.datasource,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	tt.adjmonth,
	'' lca                     -- lca字段标识 
FROM
	sync_interface_bl_202507 tt
	LEFT JOIN (				-- 调整为 inner join
		-- MNP（移动号码携带）记录子查询
		SELECT	* FROM stl_mnp_record_poc m 
		WHERE m.settlemonth = 202507      -- 结算月份过滤
		AND m.partid = substr( '202507', 5, 2 )) ss  -- 分区过滤，取后2位作为分区（07）
	ON tt.dn = ss.member_code             -- 通过用户号码关联
	AND tt.soid = ss.prod_order_id        -- 通过服务实例ID关联
WHERE
	tt.remark = '2'                       -- 一批数据标识
	AND tt.STATUS = 0                     -- 有效状态
	AND tt.pospecnumber = '50025'         -- 和对讲产品代码
UNION ALL

-- 第二部分：接口表数据处理（排除50118产品）
SELECT
/*+ no_index(tt idx_pospecnumber)*/     -- Oracle优化提示：不使用pospecnumber索引
	'1' sn,
	tt.orgbid stream_id,
	tt.customernumber,
	tt.ordermode,
	tt.pospecnumber,
	decode( tt.soid, '', tt.soid, NULL, tt.soid, tt.sospecnumber ) sospecnumber,
	tt.poid,
	tt.soid,
	decode( tt.dn, '0', '', tt.dn ) dn,
	tt.accountid,
	tt.feetype,                           -- 费项编码
	tt.orgfee,                            -- 原始费用
	tt.notaxfee,                          -- 不含税费用
	tt.taxfee,                            -- 税费
	tt.taxrate,                           -- 税率
	tt.orgmonth,
	'202507' settlemonth,
	decode( tt.adjmonth, '', tt.orgmonth, NULL, tt.orgmonth, adjmonth ) || '********' start_time,
	'1' phase,
	tt.datasource,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	tt.adjmonth,
	'' lca                                -- lca字段标识 
FROM
	int_interface_bl tt 
WHERE
	tt.remark = '2'                       -- 一批数据标识
	AND tt.orgmonth = '202507'            -- 原始月份过滤
	AND tt.STATUS = 0                     -- 有效状态
	AND tt.pospecnumber <> '50118'        -- 排除跨省专线卫士产品代码
UNION ALL

-- 第三部分：金额表数据处理（排除50118产品）
SELECT
/*+ no_index(tt idx_po_so)*/            -- Oracle优化提示：不使用po_so复合索引
	'1' sn,
	tt.orgbid stream_id,
	tt.customernumber,
	tt.ordermode,
	tt.pospecnumber,
	decode( tt.soid, '', tt.soid, NULL, tt.soid, tt.sospecnumber ) sospecnumber,
	tt.poid,
	tt.soid,
	decode( tt.dn, '0', '', tt.dn ) dn,
	tt.accountid,
	tt.feetype,                           -- 费项编码
	tt.orgfee,                            -- 原始费用
	tt.notaxfee,                          -- 不含税费用
	tt.taxfee,                            -- 税费
	tt.taxrate,                           -- 税率
	tt.orgmonth,
	'202507' settlemonth,
	tt.orgmonth || '********' start_time, -- 使用原始月份构造开始时间（无调整月份处理）
	'1' phase,
	tt.datasource,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	'' adjmonth,                          -- 调整月份为空
	'' lca                                -- lca字段标识 
FROM
	sync_interface_amount_202507 tt 
WHERE
	tt.remark = '2'                       -- 一批数据标识
	AND tt.STATUS = 0                     -- 有效状态
	AND tt.pospecnumber <> '50118'        -- 排除跨省专线卫士产品代码
UNION ALL

-- 第四部分：SDWAN专线卫士数据处理
SELECT
	'1' sn,
	tt.orgbid stream_id,
	tt.customernumber,
	tt.ordermode,
	tt.pospecnumber,
	decode( tt.soid, '', tt.soid, NULL, tt.soid, tt.sospecnumber ) sospecnumber,
	tt.poid,
	tt.soid,
	decode( tt.dn, '0', '', tt.dn ) dn,
	tt.accountid,
	tt.feetype,                           -- 费项编码
	tt.orgfee,                            -- 原始费用
	tt.notaxfee,                          -- 不含税费用
	tt.taxfee,                            -- 税费
	tt.taxrate,                           -- 税率
	tt.orgmonth,
	'202507' settlemonth,
	decode( tt.adjmonth, '', tt.orgmonth, NULL, tt.orgmonth, adjmonth ) || '********' start_time,
	'1' phase,
	tt.datasource,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	tt.adjmonth,
	'' lca                                -- lca字段标识 
FROM
	stludr.sync_interface_sdwan_202507 tt 
WHERE
	tt.sospecnumber = '****************' -- 专线卫士，优享版专线安全检测服务代码
	AND tt.STATUS = '0'                  -- 有效状态
	AND tt.remark = '2'                  -- 一批数据标识
UNION ALL

-- 第五部分：SDWAN成员视频彩铃数据处理（含费项映射）
SELECT
	'1' sn,
	tt.orgbid stream_id,
	tt.customernumber,
	tt.ordermode,
	tt.pospecnumber,
	decode( tt.soid, '', tt.soid, NULL, tt.soid, tt.sospecnumber ) sospecnumber,
	tt.poid,
	tt.soid,
	decode( tt.dn, '0', '', tt.dn ) dn,
	tt.accountid,
	-- 费项编码映射转换：将特定费项编码转换为标准编码
	decode( tt.feetype, '3682', '362', '3683', '363', '3684', '364', '3685', '365', '3865', '568' ) feetype,
	tt.orgfee,                            -- 原始费用
	tt.notaxfee,                          -- 不含税费用
	tt.taxfee,                            -- 税费
	tt.taxrate,                           -- 税率
	tt.orgmonth,
	'202507' settlemonth,
	decode( tt.adjmonth, '', tt.orgmonth, NULL, tt.orgmonth, adjmonth ) || '********' start_time,
	'1' phase,
	tt.datasource,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	tt.adjmonth,
	'' lca                                -- lca字段标识
FROM
	stludr.sync_interface_sdwan_202507 tt 
WHERE
	tt.sospecnumber = '910401'            -- 成员视频彩铃服务代码
	AND tt.STATUS = '0'                   -- 有效状态
	AND tt.remark = '2'                   -- 一批数据标识