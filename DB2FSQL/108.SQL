/*
 * 结算数据提取SQL - 特殊业务补充处理 (108.SQL)
 * 
 * ======================== 与105.SQL的对比分析 ========================
 * | 项目          | 105.SQL                    | 108.SQL                    |
 * |---------------|----------------------------|----------------------------|
 * | 数据源数量    | 1个 (sync_bl_settle)       | 3个 (多个数据源)           |
 * | 业务逻辑      | 排除特殊业务数据           | 专门处理特殊业务数据       |
 * | UNION结构     | 2个UNION ALL              | 3个UNION ALL               |
 * | 税率处理      | 6%/9%分别处理             | 统一6%税率                 |
 * | 结果字段      | lcd,lce                   | lca,lcb                    |
 * | 数据关系      | 主要业务流程              | 补充特殊业务流程           |
 * =================================================================
 * 
 * 业务说明：
 * 108.SQL专门处理105.SQL中被排除的特殊业务数据，形成互补关系
 * 
 * 1. 第一部分：BL结算表的特殊业务数据
 *    - 5G消息终端结算 (50034产品)
 *    - 支付卫士相关业务 (50024产品) 
 *    - 中间号业务 (50016产品)
 *    - 特定产品代码范围业务
 * 
 * 2. 第二部分：接口表的50118 跨省专线卫士产品数据
 *    - 数据源：int_interface_bl
 *    - 使用notaxfee字段作为费用
 * 
 * 3. 第三部分：金额表的50118 跨省专线卫士产品数据  
 *    - 数据源：sync_interface_amount
 *    - 使用notaxfee字段作为费用
 */

-- 第一部分：BL结算表的特殊业务数据处理
SELECT
	'1' sn,
	to_char ( b.stream_id ) stream_id,
	b.customernumber,
	b.ordermode,
	b.pospecnumber,
	decode( b.soid, '', b.soid, NULL, b.soid, b.sospecnumber ) sospecnumber,
	b.poid,
	b.soid,
	decode( b.dn, '0', '', b.dn ) dn,
	b.accountid,
	b.feetype,
	b.amount orgfee,           -- 使用原始金额
	b.amount notaxfee,         -- 使用原始金额作为不含税费用
	0,                         -- 固定税费为0
	'6',                       -- 固定税率6%
	b.orgmonth,
	'202507' settlemonth,
	b.orgmonth || '********' start_time,
	'1' phase,
	b.datasource,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	b.prov_cd,
	'' lca,                    -- lca字段标识
	'' lcb                     -- lcb字段标识 
FROM
	stludr.sync_bl_settle_202507 b 
WHERE
	-- 105.SQL中被排除的特殊业务数据，这里专门处理
	( b.pospecnumber = '50034' AND b.sospecnumber = '5003401' AND b.feetype = '157' )  -- 5G消息终端结算
	OR ( b.pospecnumber = '50024' AND b.sospecnumber = '****************' AND b.feetype LIKE '1%' )  -- 支付卫士1开头费项
	OR ( b.pospecnumber = '50016' AND b.sospecnumber = '5001606' )  -- 中间号业务
	OR ( b.pospecnumber BETWEEN '*********' AND '*********' OR b.pospecnumber = '60000' OR b.sospecnumber = '110151' )  -- 特定产品代码范围
UNION ALL

-- 第二部分：接口表的50118 跨省专线卫士产品数据处理
SELECT
	'1' sn,
	to_char ( b.orgbid ) stream_id,    -- 使用orgbid作为流水号
	b.customernumber,
	b.ordermode,
	b.pospecnumber,
	decode( b.soid, '', b.soid, NULL, b.soid, b.sospecnumber ) sospecnumber,
	b.poid,
	b.soid,
	decode( b.dn, '0', '', b.dn ) dn,
	b.accountid,
	b.feetype,
	b.notaxfee orgfee,             -- 使用不含税费用作为原始费用
	b.notaxfee notaxfee,           -- 使用不含税费用
	0,                             -- 固定税费为0
	'6',                           -- 固定税率6%
	b.orgmonth,
	'202507' settlemonth,
	b.orgmonth || '********' start_time,
	'1' phase,
	b.datasource,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	b.provcd,
	'' lca,                        -- lca字段标识
	'' lcb                         -- lcb字段标识 
FROM
	stludr.int_interface_bl b 
WHERE
	b.pospecnumber = '50118'     -- 处理50118 跨省专线卫士产品代码
	AND b.remark = '2'           -- 一批数据标识
UNION ALL

-- 第三部分：金额表的50118 跨省专线卫士产品数据处理
SELECT
	'1' sn,
	to_char ( b.orgbid ) stream_id,    -- 使用orgbid作为流水号
	b.customernumber,
	b.ordermode,
	b.pospecnumber,
	decode( b.soid, '', b.soid, NULL, b.soid, b.sospecnumber ) sospecnumber,
	b.poid,
	b.soid,
	decode( b.dn, '0', '', b.dn ) dn,
	b.accountid,
	b.feetype,
	b.notaxfee orgfee,             -- 使用不含税费用作为原始费用
	b.notaxfee notaxfee,           -- 使用不含税费用
	0,                             -- 固定税费为0
	'6',                           -- 固定税率6%
	b.orgmonth,
	'202507' settlemonth,
	b.orgmonth || '********' start_time,
	'1' phase,
	b.datasource,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	b.provcd,
	'' lca,                        -- lca字段标识
	'' lcb                         -- lcb字段标识 
FROM
	stludr.sync_interface_amount_202507 b 
WHERE
	b.pospecnumber = '50118'     -- 处理50118 跨省专线卫士产品代码
	AND b.remark = '2'           -- 一批数据标识