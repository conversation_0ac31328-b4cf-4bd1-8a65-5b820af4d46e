/*
 * 结算数据提取SQL - 二批实收数据处理 (205.SQL)    暂估不考虑
 * 
 * ======================== 与203.SQL的互补关系 ========================
 * | 项目          | 203.SQL                    | 205.SQL                    |
 * |---------------|----------------------------|----------------------------|
 * | 业务类型      | 二批应收                   | 二批实收                   |
 * | 数据关系      | 应收模拟                   | 实收处理                   |
 * | sn字段        | '1'                        | '2'                        |
 * | 数据源类型    | 账单接口表                 | 应收接口表+结算表          |
 * | 特殊字段      | lca,lcb                    | space                      |
 * | 汇总处理      | 无                         | 有（50021智能路由）        |
 * | 费项映射      | 无                         | 有（成员视频彩铃）         |
 * =================================================================
 * 
 * 业务说明：
 * 205.SQL专门处理二批实收数据，与203.SQL形成完整的应收实收处理体系
 * 
 * 1. 第一部分：应收接口数据（主要实收数据）
 * 2. 第二部分：账单接口数据（SIM类型产品）         ***应收模拟实收***
 * 3. 第三部分：SDWAN成员视频彩铃数据（含费项映射）  ***应收模拟实收***
 * 4. 第四部分：BL结算数据（排除50021）             ***应收模拟实收***
 * 5. 第五部分：BL结算数据（50021智能路由汇总）
 * 6. 第六部分：实收接口数据（按支付月份）
 * 
 * 关键特点：
 * - sn='2'标识实收数据
 * - paid_month字段区分支付月份
 * - 包含汇总处理和费项映射
 * - 应收模拟实收的完整实现
 */

-- 第一部分：实收接口数据处理（主要实收数据）
SELECT '2' sn,                           -- 实收数据标识
       tt.orgbid stream_id, 
       tt.customernumber, 
       tt.ordermode, 
       tt.pospecnumber, 
       decode(tt.soid,'',tt.soid,null, tt.soid,tt.sospecnumber) sospecnumber, 
       tt.poid, 
       tt.soid, 
       decode(tt.dn,'0','',tt.dn) dn, 
       tt.accountid, 
       tt.feetype,                        -- 费项编码
       tt.orgfee,                         -- 原始费用
       tt.notaxfee,                       -- 不含税费用
       tt.taxfee,                         -- 税费
       tt.taxrate,                        -- 税率
       tt.orgmonth, 
       tt.paymonth paid_month,            -- 支付月份（实收关键字段）
       '202507' settlemonth, 
       decode(tt.adjmonth,'', tt.orgmonth,null, tt.orgmonth, adjmonth) || '********' start_time, 
       '2' phase,                         -- 第二阶段标识
       tt.datasource, 
       to_char(sysdate,'yyyymmdd') ndate, 
       tt.adjmonth, 
       '' space                           -- space字段标识 
FROM stludr.sync_interface_ar_202507 tt 
WHERE tt.status = 0                       -- 有效状态
  AND tt.pospecnumber NOT IN (
      -- 排除CAR类型产品
      SELECT aa.offer_code 
      FROM stlusers.STL_CONFIG_DB2F aa 
      WHERE aa.type_nm='CAR'
  ) 
  AND (tt.pospecnumber NOT IN ('*********','01114001') OR tt.ordermode<>5) -- 排除农信通、400业务（受理模式5时）
  AND tt.pospecnumber NOT IN ('50025', '50004', '9200397') -- 排除和对讲、CDN业务

UNION ALL 

-- 第二部分：账单接口数据处理（SIM类型产品）
SELECT '2' sn,                           -- 实收数据标识
       bb.orgbid stream_id, 
       bb.customernumber, 
       bb.ordermode, 
       bb.pospecnumber, 
       decode(bb.soid,'',bb.soid,null, bb.soid,bb.sospecnumber) sospecnumber, 
       bb.poid, 
       bb.soid, 
       decode(bb.dn,'0','',bb.dn) dn, 
       bb.accountid, 
       bb.feetype,                        -- 费项编码
       bb.orgfee,                         -- 原始费用
       bb.notaxfee,                       -- 不含税费用
       bb.taxfee,                         -- 税费
       bb.taxrate,                        -- 税率
       bb.orgmonth, 
       bb.orgmonth paid_month,            -- 使用原始月份作为支付月份
       '202507' settlemonth, 
       decode(bb.adjmonth,'', bb.orgmonth,null, bb.orgmonth, adjmonth) || '********' start_time, 
       '2' phase,                         -- 第二阶段标识
       bb.datasource, 
       to_char(sysdate,'yyyymmdd') ndate, 
       bb.adjmonth, 
       '' space                           -- space字段标识 
FROM stludr.sync_interface_bl_202507 bb 
WHERE bb.ordermode IN ('3','4')          -- 受理模式3、4
  AND bb.status = 0                       -- 有效状态
  AND (bb.pospecnumber IN (
       -- SIM类型产品配置匹配（产品代码）
       SELECT aa.offer_code 
       FROM stl_config_db2f aa 
       WHERE aa.type_nm='SIM' 
         AND aa.column_nm='offer_code' 
         AND '202507' BETWEEN aa.eff_month AND aa.exp_month
       ) 
       OR bb.sospecnumber IN (
       -- SIM类型服务配置匹配（服务代码）
       SELECT aa.product_code 
       FROM stl_config_db2f aa 
       WHERE aa.type_nm='SIM' 
         AND aa.column_nm='product_code' 
         AND '202507' BETWEEN aa.eff_month AND aa.exp_month
       )) 
  AND (bb.pospecnumber <> '0102001' OR bb.soid IS NOT NULL) -- 排除特定条件的0102001产品
  AND bb.pospecnumber NOT IN ('*********', '*********', '50022', '50025', '50004') -- 排除特定产品 

UNION ALL

-- 第三部分：SDWAN成员视频彩铃数据处理（含费项映射）
SELECT '2' sn,                           -- 实收数据标识
       bb.orgbid stream_id, 
       bb.customernumber,  
       bb.ordermode,   
       bb.pospecnumber,   
       decode(bb.soid,'',bb.soid,null, bb.soid,bb.sospecnumber) sospecnumber, 
       bb.poid,   
       bb.soid,   
       decode(bb.dn,'0','',bb.dn) dn,  
       bb.accountid,    
       -- 费项编码映射转换：将特定费项编码转换为标准编码
       decode(bb.feetype,'3682','362','3683','363','3684','364','3685','365','3865','568') feetype,   
       bb.orgfee,                         -- 原始费用
       bb.notaxfee,                       -- 不含税费用
       bb.taxfee,                         -- 税费
       bb.taxrate,                        -- 税率
       bb.orgmonth,   
       bb.orgmonth paid_month,            -- 使用原始月份作为支付月份
       '202507' settlemonth, 
       decode(bb.adjmonth,'', bb.orgmonth,null, bb.orgmonth, adjmonth) || '********' start_time, 
       '2' phase,                         -- 第二阶段标识
       bb.datasource,   
       to_char(sysdate,'yyyymmdd') ndate,  
       bb.adjmonth,   
       '' space                           -- space字段标识  
FROM stludr.sync_interface_sdwan_202507 bb 
WHERE bb.ordermode IN ('1','3','5')      -- 受理模式1、3、5
  AND bb.status = 0                       -- 有效状态
  AND (bb.sospecnumber = (
      -- 成员视频彩铃服务代码匹配
      SELECT aa.product_code 
      FROM stl_config_db2f aa   
      WHERE aa.type_nm='SIM' 
        AND aa.product_code='910401'      -- 成员视频彩铃服务代码
        AND aa.column_nm='product_code'   
        AND '202507' BETWEEN aa.eff_month AND aa.exp_month
      )) 

UNION ALL 

-- 第四部分：BL结算数据处理（排除50021智能路由）
SELECT '2' sn,                           -- 实收数据标识
       to_char(dd.stream_id) stream_id, 
       dd.customernumber, 
       dd.ordermode, 
       dd.pospecnumber, 
       decode(dd.soid,'',dd.soid,null, dd.soid,dd.sospecnumber) sospecnumber, 
       to_char(dd.poid) poid, 
       to_char(dd.soid) soid, 
       dd.dn, 
       dd.accountid, 
       dd.feetype,                        -- 费项编码
       dd.amount orgfee,                  -- 使用金额作为原始费用
       dd.amount notaxfee,                -- 使用金额作为不含税费用
       0 taxfee,                          -- 固定税费为0
       '6' taxrate,                       -- 固定税率6%
       dd.orgmonth, 
       dd.orgmonth paid_month,            -- 使用原始月份作为支付月份
       '202507' settlemonth, 
       dd.orgmonth||'********' start_time, -- 使用原始月份构造开始时间（无调整月份处理）
       '2' phase,                         -- 第二阶段标识
       'AR' datasource,                   -- AR数据源标识
       to_char(sysdate,'yyyymmdd') ndate, 
       '' adjmonth,                       -- 调整月份为空
       '' space                           -- space字段标识 
FROM stludr.sync_bl_settle_202507 dd 
WHERE dd.pospecnumber <> '50021'          -- 排除智能路由（在第五部分单独处理）
  AND dd.ordermode = '3'                  -- 受理模式3
  AND (dd.pospecnumber IN (
      -- SIM类型产品配置匹配（产品代码）
      SELECT tt.offer_code 
      FROM stl_config_db2f tt 
      WHERE tt.type_nm='SIM' 
        AND tt.column_nm='offer_code' 
        AND '202507' BETWEEN tt.eff_month AND tt.exp_month
      ) 
      OR dd.sospecnumber IN (
      -- SIM类型服务配置匹配（服务代码）
      SELECT tt.product_code 
      FROM stl_config_db2f tt 
      WHERE tt.type_nm='SIM' 
        AND tt.column_nm='product_code' 
        AND '202507' BETWEEN tt.eff_month AND tt.exp_month
      )) 
  AND dd.status = '0'                     -- 有效状态 

UNION ALL 

-- 第五部分：BL结算数据处理（50021智能路由汇总）
SELECT '2' sn,                           -- 实收数据标识
       max(to_char(aa.stream_id)) stream_id, -- 取最大流水号
       aa.customernumber, 
       aa.ordermode, 
       aa.pospecnumber, 
       decode(aa.soid, '', aa.soid, null, aa.soid,aa.sospecnumber) sospecnumber, 
       to_char(aa.poid) poid, 
       to_char(aa.soid) soid, 
       aa.dn, 
       aa.accountid, 
       aa.feetype,                        -- 费项编码
       sum(aa.amount) orgfee,             -- 金额汇总作为原始费用
       sum(aa.amount) notaxfee,           -- 金额汇总作为不含税费用
       0 taxfee,                          -- 固定税费为0
       '9' taxrate,                       -- 固定税率9%
       aa.orgmonth , 
       aa.orgmonth paid_month,            -- 使用原始月份作为支付月份
       '202507' settlemonth, 
       aa.orgmonth || '********' start_time, -- 使用原始月份构造开始时间（无调整月份处理）
       '2' phase,                         -- 第二阶段标识
       'AR' datasource,                   -- AR数据源标识
       to_char(sysdate, 'yyyymmdd') ndate, 
       '' adjmonth,                       -- 调整月份为空
       '' space                           -- space字段标识 
FROM stludr.sync_bl_settle_202507 aa 
WHERE aa.pospecnumber = '50021'           -- 只处理智能路由产品
  AND aa.ordermode = '3'                  -- 受理模式3
--   注释掉的SIM配置匹配条件（避免处理冲突）
--   AND (aa.pospecnumber IN (    
--       SELECT tt.offer_code 
--       FROM stl_config_db2f tt 
--       WHERE tt.type_nm='SIM' 
--         AND tt.column_nm='offer_code' 
--         AND '202507' BETWEEN tt.eff_month AND tt.exp_month
--       ) 
--       OR aa.sospecnumber IN (
--       SELECT tt.product_code 
--       FROM stl_config_db2f tt 
--       WHERE tt.type_nm='SIM' 
--         AND tt.column_nm='product_code' 
--         AND '202507' BETWEEN tt.eff_month AND tt.exp_month
--       )) 
  AND aa.status = '0'                     -- 有效状态
-- 按业务维度分组汇总
GROUP BY orgmonth, customernumber, ordermode, pospecnumber, sospecnumber, poid, soid, dn, accountid, feetype 

UNION ALL 

-- 第六部分：实收接口数据处理（按支付月份）
SELECT '2' sn,                           -- 实收数据标识
       tt.orgbid stream_id, 
       tt.customernumber, 
       tt.ordermode, 
       tt.pospecnumber, 
       decode(tt.soid, '', tt.soid,null, tt.soid, tt.sospecnumber) sospecnumber, 
       tt.poid, 
       tt.soid, 
       decode(tt.dn, '0', '', tt.dn) dn, 
       tt.accountid, 
       tt.feetype,                        -- 费项编码
       tt.orgfee,                         -- 原始费用
       tt.notaxfee,                       -- 不含税费用
       tt.taxfee,                         -- 税费
       tt.taxrate,                        -- 税率
       tt.orgmonth, 
       tt.paymonth paid_month,            -- 支付月份（实收关键字段）
       '202507' settlemonth, 
       decode(tt.adjmonth,'', tt.orgmonth,null, tt.orgmonth, adjmonth) || '********' start_time, 
       '2' phase,                         -- 第二阶段标识
       tt.datasource, 
       to_char(sysdate, 'yyyymmdd') ndate, 
       tt.adjmonth, 
       '' space                           -- space字段标识
FROM stludr.int_interface_ar tt 
WHERE tt.paymonth = '202507';             -- 按支付月份过滤