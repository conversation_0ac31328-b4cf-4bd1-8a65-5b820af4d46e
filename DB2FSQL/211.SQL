/*
*	二批 ：CDN-应收 和203.SQL 互补			暂估流程不考虑
*	50004	CDN  BBOSS
*	9200397	CDN  EBOSS
*/
SELECT
	'1' sn,
	tt.orgbid stream_id,
	tt.customernumber,
	tt.ordermode,
	tt.pospecnumber,
	decode( tt.soid, '', tt.soid, NULL, tt.soid, tt.sospecnumber ) sospecnumber,
	tt.poid,
	tt.soid,
	decode( tt.dn, '0', '', tt.dn ) dn,
	tt.accountid,
	tt.feetype,
	tt.orgfee,
	tt.notaxfee,
	tt.taxfee,
	tt.taxrate,
	tt.orgmonth,
	'202507' settlemonth,
	decode( tt.adjmonth, '', tt.orgmonth, NULL, tt.orgmonth, adjmonth ) || '********' start_time,
	'2' phase,
	'CD' datasource,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	tt.adjmonth,
	'' space 
FROM
	sync_interface_bl_202507 tt 
WHERE
	tt.remark <> '2' 
	AND tt.STATUS = 0 
	AND tt.pospecnumber = '50004' 
UNION ALL
SELECT
	'1' sn,
	to_char ( aa.line_num ) stream_id,
	aa.eboss_customer_code,
	decode( aa.prov_code, '000', '1', '3' ) order_mode,
	aa.product_code,
	aa.service_code,
	aa.prod_order_id,
	aa.order_id,
	'' dn,
	0 accountid,
	decode( aa.customer_type, '2', '17', '16' ) feetype,
	round( aa.amount / 10 ) orgfee,
	round( aa.amount / 10 ) notaxfee,
	0 taxfee,
	to_char ( aa.tax_rate ) taxrate,
	to_char ( aa.acct_month ) orgmonth,
	'202507' settlemonth,
	aa.acct_month || '********' start_time,
	'2' phase,
	'CD' datasource,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	'' adjmonth,
	'' space 
FROM
	cust_prod_info aa 
WHERE
	aa.product_code = '9200397' 
	AND aa.acct_month = '202507' 
UNION ALL
SELECT
	'1' sn,
	'1' stream_id,
	bb.customer_code,
	bb.order_mode,
	bb.offer_code,
	bb.product_code,
	bb.offer_order_id,
	bb.product_order_id,
	'' dn,
	0 accountid,
	decode( bb.customer_type, '2', '117', '116' ) feetype,
	( bb.cmcc_fee + bb.oth_fee ) * decode( bb.offer_code, '50004', 100, 1 ),
	( bb.cmcc_fee + bb.oth_fee ) * decode( bb.offer_code, '50004', 100, 1 ),
	0 taxfee,
	'6' taxrate,
	bb.acct_month orgmonth,
	'202507' settlemonth,
	bb.acct_month || '********' start_time,
	'2' phase,
	'CD' datasource,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	'' adjmonth,
	'' space 
FROM
	stl_cdn_order_fee bb 
WHERE
	acct_month = '202507' 
UNION ALL
SELECT
	'1' sn,
	'1' stream_id,
	co.customer_code,
	co.order_mode,
	co.offer_code,
	co.product_code,
	co.offer_order_id,
	co.product_order_id,
	'' dn,
	0 accountid,
	decode( co.customer_type, '2', '117', '116' ) feetype,
	( co.cmcc_fee + co.oth_fee ) * decode( co.offer_code, '50004', 100, 1 ) orgfee,
	( co.cmcc_fee + co.oth_fee ) * decode( co.offer_code, '50004', 100, 1 ) notaxfee,
	0 taxfee,
	'6' taxrate,
	co.acct_month orgmonth,
	'202507' settlemonth,
	co.acct_month || '********' start_time,
	'2' phase,
	'CD' datasource,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	'' adjmonth,
	'' space 
FROM
	stl_cdn_order_fee_rif co 
WHERE
	acct_month = '202507' 
UNION ALL
SELECT
	'1' sn,
	'1' stream_id,
	co.customer_code,
	co.order_mode,
	co.offer_code,
	co.product_code,
	co.offer_order_id,
	co.product_order_id,
	'' dn,
	0 accountid,
	decode( co.customer_type, '2', '117', '116' ) feetype,
	( co.cmcc_fee + co.oth_fee ) * decode( co.offer_code, '50004', 100, 1 ) orgfee,
	( co.cmcc_fee + co.oth_fee ) * decode( co.offer_code, '50004', 100, 1 ) notaxfee,
	0 taxfee,
	'6' taxrate,
	co.acct_month orgmonth,
	'202507' settlemonth,
	co.acct_month || '********' start_time,
	'2' phase,
	'CD' datasource,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	'' adjmonth,
	'' space 
FROM
	stl_cdnappend_order_fee co 
WHERE
	acct_month = '202507'
UNION ALL
SELECT
	'1' sn,
	'1' stream_id,
	a.EC_CODE,
	TO_CHAR ( a.ORDER_MODE ) order_mode,
	a.offer_code,
	a.product_code,
	TO_CHAR ( a.OFFER_ORDER_ID ),
	TO_CHAR ( a.PRODUCT_ORDER_ID ),
	'' dn,
	0 accountid,
	a.FEE_TYPE feetype,
	cast(
	sum( a.DATA_VALUE ) AS DECIMAL ( 30, 0 )) orgfee,
	cast(
	sum( a.DATA_VALUE ) AS DECIMAL ( 30, 0 )) notaxfee,
	0 taxfee,
	'6' taxrate,
	a.acct_month orgmonth,
	'202507' settlemonth,
	a.acct_month || '********' start_time,
	'2' phase,
	'CD' datasource,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	'' adjmonth,
	'' space 
FROM
	CDN_VAS_INFO a 
WHERE
	acct_month = '202507' 
GROUP BY
	a.FEE_TYPE,
	a.acct_month,
	a.EC_CODE,
	a.ORDER_MODE,
	a.OFFER_CODE,
	a.PRODUCT_CODE,
	a.OFFER_ORDER_ID,
	a.PRODUCT_ORDER_ID,
	a.BUSI_TYPE 
UNION ALL
SELECT
	'1' sn,
	'1' stream_id,
	co.customer_code,
	co.order_mode,
	co.offer_code,
	co.product_code,
	co.offer_order_id,
	co.product_order_id,
	'' dn,
	0 accountid,
	co.charge_item feetype,
	( co.cmcc_fee + co.oth_fee ) * 100 orgfee,
	( co.cmcc_fee + co.oth_fee ) * 100 notaxfee,
	0 taxfee,
	'6' taxrate,
	co.acct_month orgmonth,
	'202507' settlemonth,
	co.acct_month || '********' start_time,
	'2' phase,
	'CD' phase,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	'' adjmonth,
	'' space 
FROM
	STL_HWCDN_ORDER_FEE co 
WHERE
	acct_month = '202507'