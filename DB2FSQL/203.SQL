/*
 * 结算数据提取SQL - 二批应收数据处理 (203.SQL)
 * 
 * ======================== 与101系列的差异对比 ========================
 * | 项目          | 101系列                    | 203.SQL                    |
 * |---------------|----------------------------|----------------------------|
 * | 处理批次      | 一批数据 (remark = '2')    | 二批数据 (remark <> '2')   |
 * | 业务类型      | 标准结算                   | 应收处理                   |
 * | UNION数量     | 2个                        | 5个                        |
 * | 数据源数量    | 1个                        | 4个                        |
 * | phase字段     | '1'                        | '2'                        |
 * | 特殊产品      | 排除特定产品               | 排除CAR、LP、特定产品      |
 * | 结果字段      | lce                        | lca,lcb                    |
 * =================================================================
 * 
 * 业务说明：
 * 203.SQL专门处理二批应收数据，与101系列形成互补
 * 
 * 1. 第一部分：同步接口账单数据（普通产品）
 * 2. 第二部分：同步接口账单数据（LP类型产品）
 * 3. 第三部分：接口账单数据
 * 4. 第四部分：金额接口数据
 * 5. 第五部分：SDWAN工业互联网数据
 * 
 * 关键过滤规则：
 * - 排除CAR类型产品（ordermode=5时）
 * - 排除特定产品代码：50025（和对讲业务）、50004（CDN）、9200397（CDN）
 * - LP类型产品使用list_price作为费用
 */

-- 第一部分：同步接口账单数据处理（普通产品）
SELECT
	'1' sn,
	tt.orgbid stream_id,
	tt.customernumber,
	tt.ordermode,
	tt.pospecnumber,
	decode( tt.soid, '', tt.soid, NULL, tt.soid, tt.sospecnumber ) sospecnumber,
	tt.poid,
	tt.soid,
	decode( tt.dn, '0', '', tt.dn ) dn,
	tt.accountid,
	tt.feetype,                           -- 费项编码
	tt.orgfee,                            -- 使用原始费用
	tt.notaxfee,                          -- 使用原始不含税费用
	tt.taxfee,                            -- 税费
	tt.taxrate,                           -- 税率
	tt.orgmonth,
	'202507' settlemonth,
	decode( tt.adjmonth, '', tt.orgmonth, NULL, tt.orgmonth, adjmonth ) || '********' start_time,
	'2' phase,                            -- 第二阶段标识
	tt.datasource,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	tt.adjmonth,
	'' lca                                -- lca字段标识 
FROM
	sync_interface_bl_202507 tt 
WHERE
	tt.remark <> '2'                      -- 二批数据标识（非一批数据）
	AND tt.STATUS = 0                     -- 有效状态
	AND ((
		-- CAR类型产品过滤：排除CAR类型或非受理模式5
		tt.pospecnumber NOT IN ( SELECT aa.offer_code FROM stlusers.STL_CONFIG_DB2F aa WHERE aa.type_nm = 'CAR' ) 
		OR tt.ordermode <> 5 
		) 
	) 
	AND (
		-- 排除LP类型产品（这些在第二部分处理）
		tt.sospecnumber NOT IN ( SELECT tt.product_code FROM stlusers.STL_CONFIG_DB2F TT WHERE tt.type_nm = 'LP' AND tt.column_nm = 'product_code' ) 
		OR sospecnumber IS NULL 
	) 
	-- 排除特定产品代码：和对讲业务、CDN
	AND tt.pospecnumber NOT IN ( '50025', '50004', '9200397' ) 
UNION ALL

-- 第二部分：同步接口账单数据处理（LP类型产品）
SELECT
	'1' sn,
	tt.orgbid stream_id,
	tt.customernumber,
	tt.ordermode,
	tt.pospecnumber,
	decode( tt.soid, '', tt.soid, NULL, tt.soid, tt.sospecnumber ) sospecnumber,
	tt.poid,
	tt.soid,
	decode( tt.dn, '0', '', tt.dn ) dn,
	tt.accountid,
	tt.feetype,                           -- 费项编码
	tt.list_price orgfee,                 -- LP类型产品使用标价作为费用
	tt.list_price notaxfee,               -- LP类型产品使用标价作为不含税费用
	tt.taxfee,                            -- 税费
	tt.taxrate,                           -- 税率
	tt.orgmonth,
	'202507' settlemonth,
	decode( tt.adjmonth, '', tt.orgmonth, NULL, tt.orgmonth, adjmonth ) || '********' start_time,
	'2' phase,                            -- 第二阶段标识
	tt.datasource,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	tt.adjmonth,
	'' lca                                -- lca字段标识 
FROM
	sync_interface_bl_202507 tt 
WHERE
	tt.remark <> '2'                      -- 二批数据标识（非一批数据）
	AND tt.STATUS = 0                     -- 有效状态
	-- 只处理LP类型特殊产品
	AND tt.sospecnumber IN ( SELECT tt.product_code FROM stlusers.STL_CONFIG_DB2F TT WHERE tt.type_nm = 'LP' AND tt.column_nm = 'product_code' ) 
UNION ALL

-- 第三部分：接口账单数据处理
SELECT
	'1' sn,
	tt.orgbid stream_id,
	tt.customernumber,
	tt.ordermode,
	tt.pospecnumber,
	decode( tt.soid, '', tt.soid, NULL, tt.soid, tt.sospecnumber ) sospecnumber,
	tt.poid,
	tt.soid,
	decode( tt.dn, '0', '', tt.dn ) dn,
	tt.accountid,
	tt.feetype,                           -- 费项编码
	tt.orgfee,                            -- 使用原始费用
	tt.notaxfee,                          -- 使用原始不含税费用
	tt.taxfee,                            -- 税费
	tt.taxrate,                           -- 税率
	tt.orgmonth,
	'202507' settlemonth,
	decode( tt.adjmonth, '', tt.orgmonth, NULL, tt.orgmonth, adjmonth ) || '********' start_time,
	'2' phase,                            -- 第二阶段标识
	tt.datasource,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	tt.adjmonth,
	'' lca                                -- lca字段标识 
FROM
	int_interface_bl tt 
WHERE
	tt.remark <> '2'                      -- 二批数据标识（非一批数据）
	AND tt.orgmonth = '202507'            -- 原始月份过滤
UNION ALL

-- 第四部分：金额接口数据处理
SELECT
	'1' sn,
	tt.orgbid stream_id,
	tt.customernumber,
	tt.ordermode,
	tt.pospecnumber,
	decode( tt.soid, '', tt.soid, NULL, tt.soid, tt.sospecnumber ) sospecnumber,
	tt.poid,
	tt.soid,
	decode( tt.dn, '0', '', tt.dn ) dn,
	tt.accountid,
	tt.feetype,                           -- 费项编码
	tt.orgfee,                            -- 使用原始费用
	tt.notaxfee,                          -- 使用原始不含税费用
	tt.taxfee,                            -- 税费
	tt.taxrate,                           -- 税率
	tt.orgmonth,
	'202507' settlemonth,
	tt.orgmonth || '********' start_time, -- 使用原始月份构造开始时间（无调整月份处理）
	'2' phase,                            -- 第二阶段标识
	tt.datasource,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	'' adjmonth,                          -- 调整月份为空
	'' lcb                                -- lcb字段标识 
FROM
	sync_interface_amount_202507 tt 
WHERE
	tt.remark <> '2'                      -- 二批数据标识（非一批数据）
	AND tt.STATUS = '0'                   -- 有效状态
UNION ALL

-- 第五部分：SDWAN工业互联网数据处理
SELECT
	'1' sn,
	tt.orgbid stream_id,
	tt.customernumber,
	tt.ordermode,
	tt.pospecnumber,
	decode( tt.soid, '', tt.soid, NULL, tt.soid, tt.sospecnumber ) sospecnumber,
	tt.poid,
	tt.soid,
	decode( tt.dn, '0', '', tt.dn ) dn,
	tt.accountid,
	tt.feetype,                           -- 费项编码
	tt.orgfee,                            -- 使用原始费用
	tt.notaxfee,                          -- 使用原始不含税费用
	tt.taxfee,                            -- 税费
	tt.taxrate,                           -- 税率
	tt.orgmonth,
	'202507' settlemonth,
	decode( tt.adjmonth, '', tt.orgmonth, NULL, tt.orgmonth, adjmonth ) || '********' start_time,
	'2' phase,                            -- 第二阶段标识
	tt.datasource,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	tt.adjmonth,
	'' lca                                -- lca字段标识
FROM
	sync_interface_sdwan_202507 tt 
WHERE
	tt.pospecnumber = '50097'             -- OnePower工业互联网平台产品代码
	AND tt.STATUS = '0'                   -- 有效状态