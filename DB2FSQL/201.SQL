/*
*	实收账单，代理商类 - 标准处理 (201.SQL)
*	已下线，不使用
*/
SELECT
	'7' sn,
	tt.orgbid stream_id,
	tt.customernumber,
	tt.ordermode,
	tt.pospecnumber,
	decode( tt.soid, '', tt.soid, NULL, tt.soid, tt.sospecnumber ) sospecnumber,
	tt.poid,
	tt.soid,
	decode( tt.dn, '0', '', tt.dn ) dn,
	tt.accountid,
	tt.feetype,
	tt.orgfee,
	tt.notaxfee,
	tt.taxfee,
	tt.taxrate,
	tt.orgmonth,
	tt.paymonth,
	'202507' settlemonth,
	tt.orgmonth || '********' start_time,
	'2' phase,
	tt.datasource,
	to_char ( sysdate, 'yyyymmdd' ) ndate,
	'' lca 
FROM
	sync_interface_ar_202507 tt 
WHERE
	tt.STATUS = 0 
	AND ((
			tt.soid IS NULL 
			AND tt.poid IN (
			SELECT
				aa.offer_order_id 
			FROM
				stlusers.STL_CONFIG_DB2F aa 
			WHERE
				aa.type_nm = 'SA' 
				AND tt.orgmonth BETWEEN aa.eff_month 
				AND aa.exp_month 
			)) 
		OR (
			tt.soid IS NOT NULL 
			AND tt.soid IN (
			SELECT
				aa.product_order_id 
			FROM
				stlusers.STL_CONFIG_DB2F aa 
			WHERE
				aa.type_nm = 'SA' 
				AND tt.orgmonth BETWEEN aa.eff_month 
			AND aa.exp_month 
	)))